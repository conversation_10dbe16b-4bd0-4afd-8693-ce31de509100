// 注册页面
import Api from "../../utils/api.js";

Page({
  data: {
    form: {
      userName: "",
      userPwd: "",
      confirmPwd: "",
      registerCode: "",
      email: "",
      phone: "",
    },
    loading: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查是否已登录
    const token = wx.getStorageSync("tokenKey");
    if (token) {
      wx.switchTab({
        url: "/pages/transportations/transportations",
      });
    }
  },

  /**
   * 用户名输入
   */
  onUserNameInput(e) {
    this.setData({
      "form.userName": e.detail.value.trim(),
    });
  },

  /**
   * 密码输入
   */
  onPasswordInput(e) {
    this.setData({
      "form.userPwd": e.detail.value,
    });
  },

  /**
   * 确认密码输入
   */
  onConfirmPasswordInput(e) {
    this.setData({
      "form.confirmPwd": e.detail.value,
    });
  },

  /**
   * 注册码输入
   */
  onRegisterCodeInput(e) {
    this.setData({
      "form.registerCode": e.detail.value.trim(),
    });
  },

  /**
   * 邮箱输入
   */
  onEmailInput(e) {
    this.setData({
      "form.email": e.detail.value.trim(),
    });
  },

  /**
   * 手机号输入
   */
  onPhoneInput(e) {
    this.setData({
      "form.phone": e.detail.value.trim(),
    });
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { userName, userPwd, confirmPwd, registerCode } = this.data.form;

    // 用户名验证
    if (!userName) {
      wx.showToast({
        title: "请输入用户名",
        icon: "none",
      });
      return false;
    }

    if (userName.length < 2) {
      wx.showToast({
        title: "用户名至少2个字符",
        icon: "none",
      });
      return false;
    }

    // 密码验证
    if (!userPwd) {
      wx.showToast({
        title: "请输入密码",
        icon: "none",
      });
      return false;
    }

    if (userPwd.length < 6) {
      wx.showToast({
        title: "密码长度不能少于6个字符",
        icon: "none",
      });
      return false;
    }

    // 确认密码验证
    if (!confirmPwd) {
      wx.showToast({
        title: "请确认密码",
        icon: "none",
      });
      return false;
    }

    if (userPwd !== confirmPwd) {
      wx.showToast({
        title: "两次密码输入不一致",
        icon: "none",
      });
      return false;
    }

    // 注册码验证
    if (!registerCode) {
      wx.showToast({
        title: "请输入注册码",
        icon: "none",
      });
      return false;
    }

    if (registerCode !== "anpinglu102") {
      wx.showToast({
        title: "注册码错误",
        icon: "none",
      });
      return false;
    }

    // 邮箱格式验证(如果填写了)
    if (this.data.form.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(this.data.form.email)) {
        wx.showToast({
          title: "邮箱格式不正确",
          icon: "none",
        });
        return false;
      }
    }

    // 手机号格式验证(如果填写了)
    if (this.data.form.phone) {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(this.data.form.phone)) {
        wx.showToast({
          title: "手机号格式不正确",
          icon: "none",
        });
        return false;
      }
    }

    return true;
  },

  /**
   * 注册处理
   */
  async onRegister() {
    if (this.data.loading) return;

    // 表单验证
    if (!this.validateForm()) {
      return;
    }

    this.setData({ loading: true });

    try {
      const { userName, userPwd, email, phone } = this.data.form;

      // 构建注册参数
      const registerParams = {
        userName,
        userPwd,
      };

      // 添加可选字段
      if (email) registerParams.email = email;
      if (phone) registerParams.phone = phone;

      // 调用注册API (复用JY-VUE3的后端接口)
      const response = await Api.register(registerParams);

      // 注册成功，后端返回token和用户信息
      if (response && response.data && response.data.token) {
        wx.showToast({
          title: "注册成功",
          icon: "success",
        });

        // 延迟跳转到登录页面
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: "注册失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("注册失败:", error);

      // 处理后端返回的错误信息
      let errorMessage = "注册失败，请重试";
      if (error.data && error.data.message) {
        errorMessage = error.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      wx.showToast({
        title: errorMessage,
        icon: "none",
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 跳转到登录页面
   */
  goToLogin() {
    wx.navigateBack();
  },
});
