<!-- 注册页面 -->
<view class="register-container">
  <view class="register-card">
    <!-- 标题 -->
    <view class="title">
      <text class="title-text">SAINGE</text>
      <text class="subtitle-text">用户注册</text>
    </view>
    
    <!-- 注册表单 -->
    <view class="form-container">
      <!-- 用户名输入 -->
      <view class="input-group">
        <view class="input-icon">
          <image src="/images/icon/user.png" class="icon"></image>
        </view>
        <input 
          type="text" 
          class="form-input" 
          placeholder="请输入用户名"
          value="{{form.userName}}"
          bindinput="onUserNameInput"
          maxlength="20"
        />
      </view>
      
      <!-- 密码输入 -->
      <view class="input-group">
        <view class="input-icon">
          <image src="/images/icon/password.png" class="icon"></image>
        </view>
        <input
          type="password"
          class="form-input"
          placeholder="请输入密码"
          value="{{form.userPwd}}"
          bindinput="onPasswordInput"
          maxlength="20"
        />
      </view>

      <!-- 确认密码输入 -->
      <view class="input-group">
        <view class="input-icon">
          <image src="/images/icon/password.png" class="icon"></image>
        </view>
        <input
          type="password"
          class="form-input"
          placeholder="请确认密码"
          value="{{form.confirmPwd}}"
          bindinput="onConfirmPasswordInput"
          maxlength="20"
        />
      </view>

      <!-- 注册码输入 -->
      <view class="input-group">
        <view class="input-icon">
          <image src="/images/icon/user.png" class="icon"></image>
        </view>
        <input
          type="text"
          class="form-input"
          placeholder="请输入注册码"
          value="{{form.registerCode}}"
          bindinput="onRegisterCodeInput"
          maxlength="20"
        />
      </view>

      <!-- 邮箱输入(可选) -->
      <view class="input-group">
        <view class="input-icon">
          <image src="/images/icon/user.png" class="icon"></image>
        </view>
        <input
          type="text"
          class="form-input"
          placeholder="邮箱(可选)"
          value="{{form.email}}"
          bindinput="onEmailInput"
          maxlength="50"
        />
      </view>

      <!-- 手机号输入(可选) -->
      <view class="input-group">
        <view class="input-icon">
          <image src="/images/icon/user.png" class="icon"></image>
        </view>
        <input
          type="number"
          class="form-input"
          placeholder="手机号(可选)"
          value="{{form.phone}}"
          bindinput="onPhoneInput"
          maxlength="11"
        />
      </view>
    </view>
    
    <!-- 按钮区域 -->
    <view class="button-container">
      <button 
        class="register-button" 
        bindtap="onRegister"
        disabled="{{loading}}"
        loading="{{loading}}"
      >
        {{loading ? '注册中...' : '注册'}}
      </button>
      
      <view class="login-link">
        已有账号？ 
        <text class="link-text" bindtap="goToLogin">立即登录</text>
      </view>
    </view>
  </view>
</view>
