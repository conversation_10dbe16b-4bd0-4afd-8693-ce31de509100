/* 注册页面样式 */
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 20rpx;
  box-sizing: border-box;
}

.register-card {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 32rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.08);
  padding: 60rpx 40rpx;
  box-sizing: border-box;
  animation: card-appear 0.5s ease-out;
}

@keyframes card-appear {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 标题区域 */
.title {
  text-align: center;
  margin-bottom: 60rpx;
}

.title-text {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2c9678;
  font-family: 'Helvetica', sans-serif;
  margin-bottom: 10rpx;
}

.subtitle-text {
  display: block;
  font-size: 28rpx;
  color: #666666;
  font-weight: 400;
}

/* 表单容器 */
.form-container {
  margin-bottom: 50rpx;
}

/* 输入组 */
.input-group {
  position: relative;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.input-group:focus-within {
  border-color: #2c9678;
  background-color: #ffffff;
  box-shadow: 0 0 0 6rpx rgba(44, 150, 120, 0.1);
}

.input-icon {
  width: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

.form-input {
  flex: 1;
  height: 88rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: transparent;
  border: none;
  outline: none;
  padding-right: 20rpx;
}

.form-input::placeholder {
  color: #999999;
}

/* 按钮容器 */
.button-container {
  text-align: center;
}

.register-button {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #2c9678 0%, #26a085 100%);
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  margin-bottom: 30rpx;
}

.register-button:not([disabled]):active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(44, 150, 120, 0.3);
}

.register-button[disabled] {
  opacity: 0.6;
  background: #cccccc;
}

/* 登录链接 */
.login-link {
  font-size: 26rpx;
  color: #666666;
  text-align: center;
}

.link-text {
  color: #2c9678;
  font-weight: 500;
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .register-card {
    width: 90%;
    padding: 50rpx 30rpx;
  }
  
  .title-text {
    font-size: 44rpx;
  }
  
  .subtitle-text {
    font-size: 26rpx;
  }
}
