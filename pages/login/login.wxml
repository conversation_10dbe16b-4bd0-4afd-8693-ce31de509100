<!-- 登录页面 -->
<view class="login-container" wx:if="{{!isLogin}}">
  <view class="login-card">
    <!-- 标题 -->
    <view class="title">
      <text class="title-text">SAINGE</text>
      <text class="subtitle-text">欢迎登录</text>
    </view>

    <!-- 登录表单 -->
    <view class="form-container">
      <!-- 用户名输入 -->
      <view class="input-group">
        <view class="input-icon">
          <image src="/images/icon/user.png" class="icon"></image>
        </view>
        <input
          type="text"
          class="form-input"
          placeholder="请输入用户名"
          value="{{userName}}"
          bindinput="onUserNameInput"
          disabled="{{loading}}"
        />
      </view>

      <!-- 密码输入 -->
      <view class="input-group">
        <view class="input-icon">
          <image src="/images/icon/password.png" class="icon"></image>
        </view>
        <input
          type="password"
          class="form-input"
          placeholder="请输入密码"
          value="{{userPwd}}"
          bindinput="inputPassword"
          disabled="{{loading}}"
        />
      </view>
    </view>

    <!-- 按钮区域 -->
    <view class="button-container">
      <button
        class="login-button"
        bindtap="onLogin"
        disabled="{{loading}}"
        loading="{{loading}}"
      >
        {{loading ? '登录中...' : '登录'}}
      </button>

      <view class="register-link">
        还没有账号？
        <text class="link-text" bindtap="goToRegister">立即注册</text>
      </view>
    </view>
  </view>
</view>

<!-- 已登录状态 -->
<view class="logged-in-container" wx:if="{{isLogin}}">
  <view class="logged-in-card">
    <text class="logged-in-text">已登录，正在跳转...</text>
  </view>
</view>
