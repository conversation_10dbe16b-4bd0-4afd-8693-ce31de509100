/* ==================== 页面容器 ==================== */
.transportation-container {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* ==================== 筛选器容器 ==================== */
.filter-container-custom {
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 24rpx;
  flex-shrink: 0;
}

.filter-row {
  display: flex;
  gap: 24rpx;
}

.filter-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}



.filter-label {
  font-size: 28rpx;
  color: #323233;
  font-weight: 500;
}

.dropdown-item {
  flex: 1;
  padding: 20rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 10rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  position: relative;
}

.dropdown-item::after {
  content: "▼";
  position: absolute;
  right: 15rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20rpx;
  color: #666;
}

/* ==================== 主要内容区域 ==================== */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden; /* 防止内容溢出 */
}

/* ==================== 列表容器 ==================== */
.list-container {
  flex: 1;
  height: 0; /* 关键：配合flex:1使用，确保scroll-view有明确高度 */
  background-color: #f5f5f5;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.list-item {
  margin-bottom: 20rpx;
  flex-shrink: 0; /* 防止列表项被压缩 */
  padding-left: 10rpx;
}

.list-item:first-child {
  margin-top: 20rpx;
}

/* ==================== 加载状态 ==================== */
.loading-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid #1989fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ==================== 错误状态 ==================== */
.error-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  padding: 40rpx;
}

.error-icon {
  font-size: 80rpx;
}

.error-text {
  font-size: 32rpx;
  color: #333;
  text-align: center;
}

.error-actions {
  margin-top: 20rpx;
}

.retry-btn {
  background: #1989fa;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.retry-btn:active {
  background: #0570d6;
}

/* ==================== 空状态 ==================== */
.empty-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 40rpx;
}

.empty-icon {
  font-size: 80rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  text-align: center;
}

/* ==================== 加载更多状态 ==================== */
.load-more-container {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
}

.load-more-content {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.load-more-text {
  font-size: 26rpx;
  color: #666;
}

.load-all-container {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
}

.load-all-text {
  font-size: 26rpx;
  color: #999;
}
