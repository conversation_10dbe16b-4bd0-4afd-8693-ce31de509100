// pages/transportations/transportations.js
import Api from "../../utils/api.js";

Page({
  data: {
    // 分页相关
    currentPage: 1,
    pageSize: 10,
    hasMore: true,

    // 加载状态
    loading: false,
    loadMore: false,
    loadAll: false,
    refreshing: false,

    // 数据列表
    list: [],

    // 筛选条件
    selectedSupplier: "",
    selectedStatus: "0",

    // 筛选选项
    supplierOptions: [{ text: "全部", value: "", icon: "" }],
    statusOptions: [
      { text: "全部", value: "全部", icon: "" },
      { text: "未到货", value: "0", icon: "" },
      { text: "已到货", value: "1", icon: "" },
    ],

    // 其他状态
    currentDate: new Date().getTime(),

    // 日期格式化函数
    formatter(type, value) {
      if (type === "year") {
        return `${value}年`;
      }
      if (type === "month") {
        return `${value}月`;
      }
      return value;
    },

    // 错误状态
    error: null,
    isEmpty: false,
  },

  onLoad: function () {
    // 初始化
    this.initPage();
  },

  onShow: function () {
    // 检查是否需要强制刷新（从货运详情页面返回）
    const app = getApp();

    if (app.globalData && app.globalData.needRefreshTransportations) {
      // 清除标记并强制刷新
      app.globalData.needRefreshTransportations = false;

      this.refreshData();
      return;
    }

    // 页面显示时检查是否需要刷新数据
    // 如果页面已经有数据且距离上次加载时间超过5分钟，则自动刷新
    const now = Date.now();
    if (!this.lastLoadTime || now - this.lastLoadTime > 5 * 60 * 1000) {
      console.log("定时刷新数据");
      this.refreshData();
    }
  },

  onUnload: function () {
    // 页面卸载时清理定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
  },

  /**
   * 初始化页面
   */
  initPage() {
    this.setData({
      currentPage: 1,
      hasMore: true,
      loading: false,
      loadMore: false,
      loadAll: false,
      list: [],
      error: null,
      isEmpty: false,
    });

    // 加载初始数据
    this.loadTransportationList(true);
    this.loadSupplierOptions();
  },

  /**
   * 页面触底事件
   */
  onReachBottom: function () {
    this.loadMoreTransportations();
  },

  /**
   * 加载更多数据
   */
  loadMoreTransportations() {
    if (this.data.loadMore || this.data.loadAll || !this.data.hasMore) {
      return;
    }

    this.setData({ loadMore: true });
    this.loadTransportationList(false);
  },

  /**
   * 加载货运单列表
   * @param {boolean} isRefresh 是否为刷新操作
   */
  async loadTransportationList(isRefresh = false) {
    // 防止重复请求
    if (this.data.loading) {
      return;
    }

    try {
      // 设置加载状态
      if (isRefresh) {
        this.setData({
          loading: true,
          refreshing: true,
          error: null,
        });
      } else {
        this.setData({
          loadMore: true,
          error: null,
        });
      }

      const params = {
        page: isRefresh ? 1 : this.data.currentPage,
        limit: this.data.pageSize,
        supplier: this.data.selectedSupplier,
        arrived:
          this.data.selectedStatus === "全部" ? "" : this.data.selectedStatus,
      };

      console.log("请求参数:", params);
      const res = await Api.getTransportationList(params);
      console.log("getTransportationList响应:", res);

      // 检查响应格式
      if (res?.data?.code === 200) {
        const newList = res.data.data || [];

        // 处理日期格式和计算天数
        newList.forEach((element) => {
          if (element.date_out) {
            element.date_out = this.formatDate(element.date_out);
          }
          if (element.date_arrived) {
            element.date_arrived = this.formatDate(element.date_arrived);
            element.days = this.calculateDays(
              element.date_arrived,
              element.date_out
            );
          } else {
            element.days = this.calculateDays(new Date(), element.date_out);
          }
        });

        // 更新数据
        const currentList = isRefresh ? [] : this.data.list;
        const updatedList = currentList.concat(newList);
        const hasMore = newList.length >= this.data.pageSize;
        const nextPage = isRefresh ? 2 : this.data.currentPage + 1;

        this.setData({
          list: updatedList,
          currentPage: nextPage,
          hasMore: hasMore,
          loadAll: !hasMore,
          isEmpty: updatedList.length === 0,
        });

        // 记录加载时间
        this.lastLoadTime = Date.now();
      } else {
        // 处理错误响应
        this.handleError("数据加载失败");
      }
    } catch (error) {
      console.error("获取货运单列表失败:", error);
      this.handleError("网络请求失败，请检查网络连接");
    } finally {
      // 清除加载状态
      this.setData({
        loading: false,
        loadMore: false,
        refreshing: false,
      });
    }
  },

  /**
   * 处理错误
   */
  handleError(message) {
    // 检查网络状态
    wx.getNetworkType({
      success: (res) => {
        if (res.networkType === "none") {
          message = "网络连接已断开，请检查网络设置";
        }
      },
    });

    this.setData({
      error: message,
      isEmpty: this.data.list.length === 0,
    });

    wx.showToast({
      title: message,
      icon: "none",
      duration: 2000,
    });
  },

  /**
   * 重试加载
   */
  retryLoad() {
    this.setData({ error: null });
    this.loadTransportationList(true);
  },

  /**
   * 手动刷新数据
   */
  manualRefresh() {
    wx.showLoading({
      title: "刷新中...",
      mask: true,
    });

    this.refreshData();

    setTimeout(() => {
      wx.hideLoading();
    }, 1000);
  },

  /**
   * 加载供应商选项列表
   */
  async loadSupplierOptions() {
    try {
      const res = await Api.getTransportationSupplierOptions();
      console.log("getTransportationSupplierOptions响应:", res);

      if (res?.data?.code === 200) {
        const supplierList = [
          {
            text: "全部",
            value: "",
            icon: "",
          },
        ];

        // 处理响应数据
        const suppliers = res.data.data;
        if (Array.isArray(suppliers)) {
          suppliers.forEach((element) => {
            supplierList.push({
              text: element,
              value: element,
              icon: "",
            });
          });
        }

        this.setData({
          supplierOptions: supplierList,
        });
      }
    } catch (error) {
      console.error("获取货运公司列表失败:", error);
      wx.showToast({
        title: "获取供应商列表失败",
        icon: "none",
      });
    }
  },

  /**
   * 防抖请求函数
   */
  debounceRequest(callback, delay = 300) {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    this.debounceTimer = setTimeout(() => {
      callback();
    }, delay);
  },

  /**
   * 供应商选择改变
   */
  onSupplierChange(e) {
    const selectedValue = e.detail;
    console.log("供应商选择改变:", selectedValue);

    // 防止递归更新，先检查值是否真的改变了
    if (this.data.selectedSupplier === selectedValue) {
      return;
    }

    this.setData({
      selectedSupplier: selectedValue,
    });

    // 使用防抖处理
    this.debounceRequest(() => {
      this.refreshData();
    });
  },

  /**
   * 到货状态选择改变
   */
  onStatusChange(e) {
    const selectedValue = e.detail;
    console.log("到货状态选择改变:", selectedValue);

    // 防止递归更新，先检查值是否真的改变了
    if (this.data.selectedStatus === selectedValue) {
      return;
    }

    this.setData({
      selectedStatus: selectedValue,
    });

    // 使用防抖处理
    this.debounceRequest(() => {
      this.refreshData();
    });
  },

  /**
   * 刷新数据
   */
  refreshData() {
    this.setData({
      currentPage: 1,
      hasMore: true,
      list: [],
      loadAll: false,
      error: null,
      isEmpty: false,
    });
    this.loadTransportationList(true);
  },

  /**
   * 阻止冒泡事件
   */
  onGetCode() {
    // 阻止事件冒泡，防止误触
  },

  // 格式化日期
  formatDate(date) {
    if (!date) return "";
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, "0");
    const day = String(d.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  },

  // 计算天数差
  calculateDays(endDate, startDate) {
    if (!endDate || !startDate) return 0;
    const end = new Date(endDate);
    const start = new Date(startDate);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  },

  // 打开货运单详情页
  async onGoToDetail(e) {
    // 获取点击的货运单数据
    const transportationItem = e.currentTarget.dataset.item;
    console.log("transportationItem:", transportationItem);

    if (!transportationItem) {
      wx.showToast({
        title: "获取货运单数据失败",
        icon: "none",
      });
      return;
    }

    // 尝试多种可能的ID字段名
    const transportationId =
      transportationItem.transportation_id ||
      transportationItem._id ||
      transportationItem.id;

    console.log("提取的ID:", transportationId);
    console.log("所有可能的ID字段:", {
      _id: transportationItem._id,
      transportation_id: transportationItem.transportation_id,
      id: transportationItem.id,
    });

    if (!transportationId) {
      wx.showToast({
        title: "获取货运单ID失败",
        icon: "none",
      });
      console.log("货运单对象的所有属性:", Object.keys(transportationItem));
      return;
    }

    wx.navigateTo({
      url: `/pages/transportation-detail/transportation-detail?id=${transportationId}`,
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log("下拉刷新，当前筛选条件:", {
      supplier: this.data.selectedSupplier,
      arrived: this.data.selectedStatus,
    });

    this.refreshData();

    // 延迟停止下拉刷新，确保用户能看到刷新动画
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 500);
  },

  /**
   * 重置筛选条件
   */
  resetFilters() {
    this.setData({
      selectedSupplier: "",
      selectedStatus: "0", // 默认为未到货
    });
    this.refreshData();
  },
});
