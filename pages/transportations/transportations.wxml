<view class="transportation-container">
  <!-- 筛选器 - 使用自定义下拉选择器 -->
  <view class="filter-container-custom">
    <view class="filter-row">
      <view class="filter-item">
        <z-custom-dropdown
          value="{{ selectedSupplier }}"
          options="{{ supplierOptions }}"
          placeholder="选择供应商"
          bind:change="onSupplierChange"
        />
      </view>
      <view class="filter-item">
        <z-custom-dropdown
          value="{{ selectedStatus }}"
          options="{{ statusOptions }}"
          placeholder="选择状态"
          bind:change="onStatusChange"
        />
      </view>
    </view>


  </view>

  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 初始加载状态 -->
    <view wx:if="{{ loading && list.length === 0 }}" class="loading-container">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <view class="loading-text">加载中...</view>
      </view>
    </view>

    <!-- 错误状态 -->
    <view wx:elif="{{ error }}" class="error-container">
      <view class="error-content">
        <view class="error-icon">⚠️</view>
        <view class="error-text">{{ error }}</view>
        <view class="error-actions">
          <button class="retry-btn" bindtap="retryLoad">重试</button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:elif="{{ isEmpty }}" class="empty-container">
      <view class="empty-content">
        <view class="empty-icon">📦</view>
        <view class="empty-text">暂无货运信息</view>
        <view class="empty-desc">请尝试调整筛选条件</view>
      </view>
    </view>

    <!-- 货运单列表 -->
    <scroll-view
      wx:else
      class="list-container"
      scroll-y
      bindscrolltolower="loadMoreTransportations"
      lower-threshold="100"
      refresher-enabled="{{ true }}"
      refresher-triggered="{{ refreshing }}"
      bindrefresherrefresh="onPullDownRefresh"
    >
      <view wx:for="{{list}}" wx:key="transportation_id" class="list-item">
        <z-transportation-info
          bindtap='onGoToDetail'
          bind:myevent="onGetCode"
          transportation="{{item}}"
          data-item="{{item}}"
        />
      </view>

      <!-- 加载更多状态 -->
      <view wx:if="{{ loadMore }}" class="load-more-container">
        <view class="load-more-content">
          <view class="loading-spinner small"></view>
          <view class="load-more-text">加载更多...</view>
        </view>
      </view>

      <!-- 没有更多数据 -->
      <view wx:elif="{{ loadAll && list.length > 0 }}" class="load-all-container">
        <view class="load-all-text">已加载全部数据</view>
      </view>
    </scroll-view>
  </view>
</view>