// pages/inventory-check-confirm/inventory-check-confirm.js
import Api from "../../utils/api.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 盘存数据
    warehouse: null,
    checkItems: [],

    // 统计数据
    totalItems: 0,
    surplusItems: 0,
    deficitItems: 0,
    normalItems: 0,

    // 状态
    submitting: false,

    // 日期选择器相关
    showDatePicker: false,
    selectedDate: "",
    minDate: new Date(2020, 0, 1).getTime(),
    maxDate: new Date().getTime(),
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.checkData) {
      try {
        const checkData = JSON.parse(decodeURIComponent(options.checkData));
        this.setData({
          warehouse: checkData.warehouse,
          checkItems: checkData.check_items || [],
          selectedDate: this.formatCurrentDate(),
        });
        
        this.calculateStatistics();
      } catch (error) {
        console.error("解析盘存数据失败:", error);
        wx.showToast({
          title: "数据解析失败",
          icon: "none",
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } else {
      wx.showToast({
        title: "缺少盘存数据",
        icon: "none",
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 计算统计数据
   */
  calculateStatistics() {
    const { checkItems } = this.data;
    let surplusItems = 0;
    let deficitItems = 0;
    let normalItems = 0;

    checkItems.forEach(item => {
      if (item.difference > 0) {
        surplusItems++;
      } else if (item.difference < 0) {
        deficitItems++;
      } else {
        normalItems++;
      }
    });

    this.setData({
      totalItems: checkItems.length,
      surplusItems,
      deficitItems,
      normalItems
    });
  },

  /**
   * 确认盘存
   */
  async confirmInventoryCheck() {
    if (this.data.submitting) return;

    const { selectedDate } = this.data;
    if (!selectedDate) {
      wx.showToast({
        title: "请选择盘存日期",
        icon: "none",
      });
      return;
    }

    // 显示日期选择器
    this.setData({
      showDatePicker: true
    });
  },

  /**
   * 日期选择器确认
   */
  onDatePickerConfirm(event) {
    const selectedDate = this.formatDate(new Date(event.detail));

    this.setData({
      showDatePicker: false,
      selectedDate: selectedDate
    });

    wx.showModal({
      title: "确认盘存",
      content: `盘存日期：${selectedDate}
仓库：${this.data.warehouse.name}
盘存项目：${this.data.checkItems.length}项
确定要执行盘存操作吗？`,
      success: async (res) => {
        if (res.confirm) {
          await this.submitInventoryCheck();
        }
      },
    });
  },

  /**
   * 日期选择器取消
   */
  onDatePickerCancel() {
    this.setData({
      showDatePicker: false
    });
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 格式化当前日期
   */
  formatCurrentDate() {
    return this.formatDate(new Date());
  },

  /**
   * 提交盘存数据
   */
  async submitInventoryCheck() {
    if (this.data.submitting) return;

    try {
      this.setData({ submitting: true });

      // 构建盘存数据
      const inventoryData = {
        warehouse_id: this.data.warehouse.warehouse_id,
        checks: this.data.checkItems.map(item => ({
          contents: item.contents,
          system_quantity: item.system_quantity,
          actual_quantity: item.actual_quantity
        })),
        operator: "小程序用户", // 可以从用户信息获取
        notes: "小程序盘存操作",
        operation_date: this.data.selectedDate, // 添加选择的操作日期
      };

      const response = await Api.warehouseInventoryCheck(inventoryData);

      if (response.data.code === 200) {
        wx.showToast({
          title: "盘存完成",
          icon: "success",
        });

        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          // 返回到仓库管理页面或盘存页面
          wx.navigateBack({
            delta: 2 // 返回两级，跳过盘存页面
          });
        }, 1500);
      } else {
        wx.showToast({
          title: response.data.message || "盘存失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("盘存操作失败:", error);
      wx.showToast({
        title: "盘存失败",
        icon: "none",
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 返回修改
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 获取差异类型文本
   */
  getDifferenceText(difference) {
    if (difference > 0) {
      return `盘盈 ${difference}包`;
    } else if (difference < 0) {
      return `盘亏 ${Math.abs(difference)}包`;
    } else {
      return "无差异";
    }
  },

  /**
   * 获取差异类型样式类
   */
  getDifferenceClass(difference) {
    if (difference > 0) {
      return "surplus";
    } else if (difference < 0) {
      return "deficit";
    } else {
      return "normal";
    }
  }
});
