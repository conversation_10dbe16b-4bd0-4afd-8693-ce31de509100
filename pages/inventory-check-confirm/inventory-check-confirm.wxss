/* pages/inventory-check-confirm/inventory-check-confirm.wxss */

.confirm-container {
  min-height: 95vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

/* 日期选择区域 */
/* .date-selection-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
} */

.date-selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.date-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.selected-date {
  font-size: 32rpx;
  color: #1989fa;
  font-weight: bold;
}

/* 头部信息样式 */
.header-section {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24rpx;
}

.warehouse-info {
  flex: 1;
  min-width: 0;
}

.warehouse-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.warehouse-address {
  font-size: 24rpx;
  color: #666;
}

.statistics-cards {
  display: flex;
  gap: 12rpx;
  flex-shrink: 0;
}

.stat-card {
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 16rpx 12rpx;
  text-align: center;
  border: 2rpx solid transparent;
  min-width: 80rpx;
}

.stat-card.surplus {
  background: #f0f9ff;
  border-color: #07c160;
}

.stat-card.deficit {
  background: #fff1f0;
  border-color: #ee0a24;
}

.stat-card.normal {
  background: #f6f6f6;
  border-color: #999;
}

.stat-number {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 6rpx;
}

.stat-card.surplus .stat-number {
  color: #07c160;
}

.stat-card.deficit .stat-number {
  color: #ee0a24;
}

.stat-card.normal .stat-number {
  color: #999;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
}

/* 明细区域样式 */
.details-section {
  flex: 1;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.details-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  overflow-y: auto;
  height: 70vh;
  border-radius: 12rpx;
}

.detail-item {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.item-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.clothing-info {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 8rpx;
  flex: 1;
  padding-right: 50rpx;
}

.clothing-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.difference-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  white-space: nowrap;
}

.difference-badge.surplus {
  background: #f0f9ff;
  color: #07c160;
  border: 1rpx solid #07c160;
}

.difference-badge.deficit {
  background: #fff1f0;
  color: #ee0a24;
  border: 1rpx solid #ee0a24;
}

.difference-badge.normal {
  background: #f6f6f6;
  color: #999;
  border: 1rpx solid #ddd;
}

.item-content {
  display: flex;
  align-items: center;
}

.quantity-comparison {
  display: flex;
  align-items: center;
  gap: 32rpx;
  width: 100%;
}

.quantity-item {
  flex: 1;
  display: flex;
  flex-direction: row;
  text-align: center;
  justify-content: center;
}

.quantity-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.quantity-value.actual {
  color: #1989fa;
}

.arrow-icon {
  font-size: 32rpx;
  color: #999;
  flex-shrink: 0;
}

/* 底部操作区域样式 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1rpx solid #eee;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  z-index: 100; /* 确保van-calendar在最上层 */
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.action-button-wrapper {
  flex: 1;
}

.action-button {
  width: 100% !important;
  border-radius: 12rpx !important;
  height: 80rpx;
  font-size: 28rpx;
}

.back-button {
  background: #f8f9fa !important;
  color: #666 !important;
  border: 1rpx solid #ddd !important;
}

.confirm-button {
  background: #1989fa !important;
  color: white !important;
  border: 1rpx solid #1989fa !important;
}
