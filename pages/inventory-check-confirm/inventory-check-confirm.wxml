<!-- pages/inventory-check-confirm/inventory-check-confirm.wxml -->
<view class="confirm-container">
  <!-- 头部信息 -->
  <view class="header-section">
    <view class="warehouse-info">
      <view class="warehouse-name">{{warehouse.name}}</view>
      <view class="warehouse-address">{{warehouse.address}}</view>
    </view>
    <view class="statistics-cards">
      <view class="stat-card">
        <view class="stat-number">{{totalItems}}</view>
        <view class="stat-label">总项目</view>
      </view>
      <view class="stat-card surplus">
        <view class="stat-number">{{surplusItems}}</view>
        <view class="stat-label">盘盈</view>
      </view>
      <view class="stat-card deficit">
        <view class="stat-number">{{deficitItems}}</view>
        <view class="stat-label">盘亏</view>
      </view>
      <view class="stat-card normal">
        <view class="stat-number">{{normalItems}}</view>
        <view class="stat-label">无差异</view>
      </view>
    </view>
  </view>
  <!-- 盘存明细 -->
  <view class="details-section">
    <view class="section-title">盘存明细</view>
    <view class="details-list">
      <view class="detail-item" wx:for="{{checkItems}}" wx:key="index">
        <view class="item-header">
          <view class="clothing-info">
            <view class="clothing-name">{{item.contents[0].name}}</view>
            <view class="pieces">{{item.contents[0].original_quantity}}件/包</view>
          </view>
          <view class="difference-badge {{item.difference > 0 ? 'surplus' : item.difference < 0 ? 'deficit' : 'normal'}}">
            <view wx:if="{{item.difference > 0}}">盘盈 {{item.difference}}包</view>
            <view wx:elif="{{item.difference < 0}}">盘亏 {{-item.difference}}包</view>
            <view wx:else>无差异</view>
          </view>
        </view>
        <view class="item-content">
          <view class="quantity-comparison">
            <view class="quantity-item">
              <view class="quantity-value">{{item.system_quantity}}包</view>
            </view>
            <van-icon name="arrow" class="arrow-icon" />
            <view class="quantity-item">
              <view class="quantity-value actual">{{item.actual_quantity}}包</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 日期选择区域 -->
  <!-- <view class="date-selection-card">
    <view class="date-selection-header">
      <view class="date-label">盘存日期：</view>
      <view class="selected-date">{{selectedDate}}</view>
    </view>
  </view> -->

  <!-- 底部操作区域 -->
  <view class="bottom-actions">
    <view class="action-button-wrapper">
      <van-button size="large" custom-class="action-button back-button" bind:click="goBack" disabled="{{submitting}}">
        返回修改
      </van-button>
    </view>
    <view class="action-button-wrapper">
      <van-button type="primary" size="large" custom-class="action-button confirm-button" loading="{{submitting}}" loading-text="盘存中..." bind:click="confirmInventoryCheck">
        确认盘存
      </van-button>
    </view>
  </view>
</view>

<!-- 日期选择器弹窗 -->
<van-calendar
  show="{{ showDatePicker }}"
  min-date="{{ minDate }}"
  max-date="{{ maxDate }}"
  bind:close="onDatePickerCancel"
  bind:confirm="onDatePickerConfirm"
  color="#2c9678"
  confirm-text="确认"
  confirm-disabled-text="确认"
/>