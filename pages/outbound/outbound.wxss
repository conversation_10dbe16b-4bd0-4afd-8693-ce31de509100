/* pages/outbound/outbound.wxss */

/* ==================== 基础布局 ==================== */
.outbound-container {
  margin-top: 10rpx;
  padding: 0 20rpx 160rpx 20rpx;
  background-color: #f5f5f5;
  height: 100vh;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  flex: 1;
  min-height: 0;
  overflow: visible; /* 改为visible，允许下拉提示显示 */
}

/* ==================== 搜索卡片 - 参照仓库日志样式 ==================== */
.search-section {
  background-color: #3563632c;
  box-shadow: 0 10px 6px -6px rgba(30, 30, 30, 0.1),
    12px 0 8px -8px rgba(50, 50, 50, 0.1);
  border-radius: 10rpx;
  overflow: visible; /* 改为visible，允许下拉提示显示 */
  flex-shrink: 0;
}

.search-header {
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-bottom: 1rpx solid #efefef;
  flex-shrink: 0;
}

.search-form {
  display: flex;
  gap: 12rpx;
  align-items: center;
  flex-wrap: wrap;
}

.search-form-item {
  flex: 1;
  min-width: 0;
}

/* 搜索输入框容器 */
.search-input-container {
  position: relative;
  z-index: 100; /* 确保容器有足够的层级 */
}



.search-form-item:nth-child(1) {
  flex: 2; /* 服装名称输入框占更大空间 */
}

.search-form-item:nth-child(2),
.search-form-item:nth-child(3) {
  flex: 0.8; /* 按钮 */
}

/* 服装名称输入框 */
.search-form-input {
  width: 100%;
  height: 80rpx; /* 增加高度，从60rpx改为80rpx */
  padding: 0 16rpx;
  font-size: 28rpx; /* 稍微增大字体 */
  color: #333;
  background: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e0e0e0;
  box-sizing: border-box;
  line-height: 80rpx; /* 添加行高确保文字垂直居中 */
}

.search-form-input:focus {
  border-color: #07c160;
  background: rgba(7, 193, 96, 0.05);
}

/* 搜索和重置按钮 */
.search-form-btn, .reset-form-btn {
  width: 100% !important;
  height: 80rpx !important; /* 匹配输入框高度 */
  border-radius: 8rpx !important;
  font-size: 28rpx !important; /* 匹配输入框字体大小 */
  font-weight: 600 !important;
}

.search-form-btn {
  background: linear-gradient(135deg, #07c160 0%, #05a050 100%) !important;
  border: none !important;
  color: white !important;
}

.reset-form-btn {
  background: #f5f5f5 !important;
  color: #666 !important;
  border: 1rpx solid #e0e0e0 !important;
}



/* ==================== 新的库存布局 ==================== */
.inventory-layout {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.inventory-panel {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1;
}

/* 已删除左右分栏布局相关样式 - 新交互流程不再需要 */

/* 已移除 panel-header, panel-title, panel-stats, stats-text 样式 */

/* 已删除仓库列表相关样式 - 新交互流程不再需要 */


/* ==================== 库存明细列表 ==================== */
.details-list {
  flex: 1;
  padding: 20rpx;
  min-height: 0;
  width: 100%;
  box-sizing: border-box;
  -ms-overflow-style: none;
  scrollbar-width: none;
  overflow-y: auto;
  overflow-x: hidden;
}

.details-list::-webkit-scrollbar {
  display: none;
}

.detail-item {
  background: white;
  border-radius: 10rpx;
  padding: 8rpx;
  margin-bottom: 8rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.item-content {
  margin-bottom: 0;
}

/* ==================== 仓库信息标题 ==================== */
.warehouse-header {
  background: #f0f8ff;
  border: 1rpx solid #d1ecf1;
  color: #333;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.warehouse-header .warehouse-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.warehouse-header .warehouse-address {
  font-size: 22rpx;
  color: #666;
  margin-top: 4rpx;
}

/* ==================== 服装信息布局 ==================== */
.item-main-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6rpx;
  padding: 8rpx 12rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  gap: 12rpx;
}

.clothing-info {
  flex: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0;
  gap: 4rpx;
}

.clothing-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.clothing-name.clickable {
  color: #1989fa;
  cursor: pointer;
  transition: color 0.3s;
}

.clothing-name.clickable:active {
  color: #0c7cd5;
}

.stock-info {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 80rpx;
}

.stock-formula {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
}

/* ==================== 操作区域 ==================== */
.item-actions-row {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-top: 6rpx;
  padding: 0 12rpx;
  background: #f0f9ff;
  border-radius: 8rpx;
  border: 1rpx solid #e0f2fe;
  min-height: 80rpx; /* 固定最小高度，防止按钮出现时位置移动 */
}

/* 固定位置的数量显示和控制组 */
.stock-quantity-group {
  display: flex;
  align-items: center;
  gap: 10rpx;
  flex: 1;
  min-width: 0;
}

.stock-display {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  align-items: center;
  flex-shrink: 0;
  min-width: 80rpx;
}

.stock-value {
  font-size: 30rpx;
  font-weight: 600;
  color: #ff9800;
  text-align: center;
}

.quantity-control {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-width: 200rpx; /* 为步进器预留固定空间 */
}

/* 出库按钮区域 */
.outbound-button-area {
  flex-shrink: 0;
  width: 120rpx; /* 固定宽度，保持布局稳定 */
  display: flex;
  justify-content: center;
  align-items: center;
}

.item-tips {
  text-align: center;
  padding: 16rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
}

.empty-tip {
  font-size: 24rpx;
  color: #999;
  font-weight: 600;
}

.empty-state {
  padding: 60rpx 0;
  text-align: center;
}

/* ==================== 多货物包裹卡片样式 ==================== */
.multi-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 12rpx 16rpx;
  background: #e3f2fd;
  border-radius: 8rpx;
}

.multi-item-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1989fa;
}

.multi-item-summary {
  font-size: 24rpx;
  color: #666;
}

.multi-item-list {
  margin-bottom: 10rpx;
}

.multi-item-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0rpx 12rpx;
  margin-bottom: 6rpx;
  background: white;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
}

.multi-item-row:last-child {
  margin-bottom: 0;
}

.multi-item-info {
  flex: 2;
  display: flex;
  align-items: center;
  gap: 12rpx;
  min-width: 0;
}

.multi-item-quantity {
  flex: 0 0 auto;
  font-size: 22rpx;
  color: #666;
  white-space: nowrap;
}

.multi-item-input {
  flex: 0 0 auto;
  width: 150rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.multi-input-field {
  text-align: center !important;
  font-size: 30rpx !important;
  font-weight: 600 !important;
  height: 40rpx !important;
  border: 2rpx solid #ddd !important;
  border-radius: 10rpx !important;
  box-sizing: border-box !important;
  background: #fff !important;
  color: #333 !important;
}

/* 移除van-field中van-cell的上下padding */
.multi-item-input .van-cell {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.multi-item-input .van-field__control {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.multi-item-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 12rpx;
  background: #f0f9ff;
  border-radius: 8rpx;
  border: 1rpx solid #e0f2fe;
  margin-top: 6rpx;
}

.package-calculation {
  flex: 1;
}

.calculation-display {
  font-size: 26rpx;
  font-weight: 600;
  color: #ff9800;
}

/* ==================== 底部操作栏 ==================== */
.bottom-fixed-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1rpx solid #eee;
  padding: 20rpx;
  z-index: 1000;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.bottom-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
  align-items: center;
}

.action-item {
  flex: 1;
  max-width: 300rpx;
}

.action-button {
  width: 100% !important;
  border-radius: 12rpx !important;
  height: 80rpx;
  font-size: 28rpx;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

/* 核对按钮特殊样式 */
.action-button[type="success"] {
  background: linear-gradient(135deg, #07c160 0%, #05a050 100%) !important;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3) !important;
  border: none !important;
  color: white !important;
}

.action-button[type="success"]:active {
  transform: translateY(2rpx) !important;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.4) !important;
}

/* ==================== 待出库清单弹窗 ==================== */
.cart-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

.popup-header {
  padding: 32rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  flex-shrink: 0;
  position: relative;
  z-index: 10;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.total-info {
  font-size: 24rpx;
  color: #666;
}

.cart-content {
  flex: 1;
  padding: 20rpx;
  padding-bottom: 80rpx;
  min-height: 0;
  width: 100%;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.cart-content::-webkit-scrollbar {
  display: none;
}

.empty-cart {
  padding: 60rpx 0;
  text-align: center;
}

.warehouse-group {
  margin-bottom: 32rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
}

.warehouse-header {
  background: #e3f2fd;
  padding: 20rpx 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.warehouse-summary {
  font-size: 24rpx;
  color: #666;
}

.warehouse-items {
  padding: 0 24rpx;
}

/* ==================== 待出库清单项目 ==================== */
.cart-item {
  display: flex;
  align-items: flex-start;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #eee;
  gap: 12rpx;
}

.cart-item:last-child {
  border-bottom: none;
}

.cart-item-content {
  flex: 1;
  min-width: 0;
}

.cart-main-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  padding: 8rpx 12rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  gap: 12rpx;
}

.cart-clothing-info {
  flex: 2;
  display: flex;
  align-items: center;
  min-width: 0;
}

.cart-clothing-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cart-clothing-name.clickable {
  color: #1989fa;
  cursor: pointer;
  transition: color 0.3s;
}

.cart-clothing-name.clickable:active {
  color: #0c7cd5;
}

.cart-stock-info {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 80rpx;
}

.cart-stock-formula {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
}

.cart-item-actions {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  width: 60rpx;
  padding-top: 8rpx;
}

.remove-icon {
  color: #ee0a24;
  cursor: pointer;
  font-size: 32rpx;
}

/* ==================== 弹窗样式 ==================== */
.warehouse-form {
  width: 600rpx;
  padding: 40rpx;
}

.form-title {
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  margin-bottom: 32rpx;
  color: #323233;
}

.form-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 32rpx;
}

/* 服装信息弹窗样式 */
.clothing-popup-container {
  width: 100%;
  background: transparent;
}

/* ==================== 购物车多货物包裹样式 ==================== */
.multi-cart-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 12rpx;
  margin-bottom: 0;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
  padding: 8rpx 12rpx;
  background: #e3f2fd;
  border-radius: 8rpx;
}

.cart-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1989fa;
}

.cart-package-info {
  font-size: 24rpx;
  color: #666;
}

.cart-contents-list {
  margin-bottom: 8rpx;
}

.cart-content-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6rpx 12rpx;
  margin-bottom: 4rpx;
  background: white;
  border-radius: 6rpx;
  border: 1rpx solid #e9ecef;
}

.cart-content-row:last-child {
  margin-bottom: 0;
}

.cart-content-name {
  flex: 1;
  font-size: 24rpx;
  color: #333;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cart-content-name.clickable {
  color: #1989fa;
  cursor: pointer;
  transition: color 0.3s;
}

.cart-content-name.clickable:active {
  color: #0c7cd5;
}

.cart-content-quantity {
  flex: 0 0 auto;
  font-size: 22rpx;
  color: #666;
  margin-left: 12rpx;
}

/* ==================== 统计信息样式 ==================== */
.stats-view {
  font-size: 24rpx;
  color: #1989fa;
  font-weight: 600;
  background: #e3f2fd;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.tip-view {
  font-size: 24rpx;
  color: #999;
}

/* ==================== 智能搜索建议样式 ==================== */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1rpx solid #e0e0e0;
  border-top: none;
  border-radius: 0 0 8rpx 8rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15); /* 增强阴影效果 */
  z-index: 9999; /* 大幅提高z-index，确保在最顶层 */
  max-height: 400rpx;
  overflow-y: auto;
}

.suggestion-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  gap: 12rpx;
  color: #999;
  font-size: 24rpx;
}

.suggestion-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx; /* 增加内边距 */
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
  min-height: 60rpx; /* 确保足够的点击区域 */
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:active {
  background-color: #f0f9f6; /* 使用品牌色的浅色版本 */
}

.suggestion-item:hover {
  background-color: #f8f9fa;
}

.suggestion-name {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ==================== 新的待出库清单列表样式 ==================== */
.outbound-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  padding: 0 20rpx;
}

.outbound-item {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  background: white;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
  gap: 16rpx;
  transition: all 0.3s;
}

.outbound-item:active {
  background-color: #f8f9fa;
}

.item-index {
  flex: 0 0 32rpx;
  width: 32rpx;
  height: 32rpx;
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #dee2e6;
  border-radius: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: 500;
}

.item-warehouse {
  flex: 0 0 120rpx;
  font-size: 24rpx;
  color: #666;
  text-align: center;
  word-break: break-all;
}

.item-clothing {
  flex: 1;
  min-width: 0;
}

.item-clothing .clothing-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  word-break: break-all;
  line-height: 1.4;
}

.single-mixed-clothing {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  word-break: break-all;
  line-height: 1.4;
}

.item-info {
  flex: 0 0 auto;
  text-align: right;
}

.outbound-formula {
  font-size: 24rpx;
  color: #666;
  line-height: 1.3;
  white-space: nowrap;
}

.item-actions {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

.item-actions .remove-icon {
  color: #ff4757;
  font-size: 32rpx;
  padding: 8rpx;
  border-radius: 50%;
  transition: all 0.3s;
}

.item-actions .remove-icon:active {
  background-color: rgba(255, 71, 87, 0.1);
  transform: scale(0.9);
}

/* ==================== 混合包裹样式（已简化，不再需要子项样式） ==================== */
