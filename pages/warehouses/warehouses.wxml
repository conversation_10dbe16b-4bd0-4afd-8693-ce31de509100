<!-- pages/warehouses/warehouses.wxml -->
<view class="warehouse-management-container" bind:tap="closeAllDropdowns">
  <!-- 主要内容 - 只有在数据初始化后才显示 -->
  <view wx:if="{{dataInitialized}}" class="main-content">
    <!-- 汇总统计卡片 -->
    <view class="summary-section">
      <view class="summary-card">
        <view class="summary-content">
          <view class="summary-item">
            <view class="summary-value">{{summaryData.totalPackages || 0}}</view>
            <view class="summary-label">总包数</view>
          </view>
          <view class="summary-item">
            <view class="summary-value">{{summaryData.totalPieces || 0}}</view>
            <view class="summary-label">总件数</view>
          </view>
          <view class="summary-item">
            <view class="summary-value">{{summaryData.lastUpdateDate || '--'}}</view>
            <view class="summary-label">最近更新</view>
          </view>
        </view>
      </view>
    </view>
    <!-- 管理功能菜单 -->
    <view class="management-section">
      <view class="menu-grid-horizontal">
        <view wx:for="{{managementMenus}}" wx:key="id" class="menu-item-horizontal" data-menu="{{item}}" bindtap="onManagementMenuTap">
          <view class="menu-icon-horizontal" style="background-color: {{item.color}};">
            <van-icon name="{{item.icon}}" size="40rpx" color="white" />
          </view>
          <view class="menu-label">{{item.label}}</view>
        </view>
      </view>
    </view>
    <!-- 仓库列表 -->
    <view class="warehouse-section">
      <view wx:if="{{warehouseList.length === 0}}" class="empty-state">
        <!-- 新建仓库按钮卡片 -->
        <view class="warehouse-mini-card add-warehouse-card" bindtap="onAddWarehouse">
          <view class="add-warehouse-content">
            <van-icon name="plus" size="40rpx" color="#3b82f6" />
            <view class="add-warehouse-text">新建仓库</view>
          </view>
        </view>
      </view>
      <view wx:else class="warehouse-scroll-container">
        <scroll-view class="warehouse-scroll" scroll-x show-scrollbar="{{false}}">
          <view class="warehouse-grid-horizontal">
            <view wx:for="{{warehouseList}}" wx:key="warehouse_id" class="warehouse-mini-card" data-warehouse="{{item}}" bindtap="onWarehouseDetail">
              <view class="warehouse-row-1">
                <!-- <view class="warehouse-mini-name">{{item.name}}</view> -->
                <view class="warehouse-mini-name">{{item.name}}</view>
                <view class="warehouse-mini-name">{{item.address}}</view>
              </view>
              <view class="warehouse-row-2">
                <view class="stats-text">{{item.stats.packages || 0}}包</view>
                <view class="stats-text">{{item.stats.pieces || 0}}件</view>
              </view>
            </view>
            <!-- 新建仓库按钮卡片 -->
            <view class="warehouse-mini-card add-warehouse-card" bindtap="onAddWarehouse">
              <view class="add-warehouse-content">
                <van-icon name="plus" size="40rpx" color="#3b82f6" />
                <view class="add-warehouse-text">新建仓库</view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
    <!-- 日历表组件 -->
    <view class="calendar-section">
      <calendar-table id="calendar-table" warehouse-id="{{null}}" show-query-button="{{true}}" bind:query="onCalendarQuery"></calendar-table>
    </view>
  </view>
  <!-- 待入库货运信息弹窗 -->
  <van-popup show="{{ showPendingInbound }}" position="center" round bind:close="closePendingInboundDialog">
    <view class="pending-inbound-dialog">
      <view class="dialog-title">待入库货运信息</view>
      <view wx:if="{{pendingTransportations.length === 0 && !loadingPending}}" class="empty-state">
        <van-empty description="暂无待入库货运" />
      </view>
      <scroll-view wx:else class="pending-scroll" scroll-y bindscrolltolower="loadMorePendingTransportations" lower-threshold="100">
        <view class="pending-list">
          <view wx:for="{{pendingTransportations}}" wx:key="transportation_id" class="pending-item" data-transportation="{{item}}" bindtap="onSelectTransportation">
            <view class="pending-row-1">
              <view class="supplier-name">{{item.supplier}}</view>
              <view class="package-count">{{item.total_package_quantity || 0}}包</view>
            </view>
            <view class="pending-row-2">
              <view class="date-info">
                <view class="date-label">发货：</view>
                <view class="date-value">{{item.formatted_date_out || '--'}}</view>
              </view>
              <view class="date-info">
                <view class="date-label">到货：</view>
                <view class="date-value">{{item.formatted_date_arrived || '--'}}</view>
              </view>
            </view>
          </view>
        </view>
        <view wx:if="{{loadingPending}}" class="loading-more">
          <van-loading type="spinner" size="20px" />
          <view>加载中...</view>
        </view>
        <view wx:if="{{!hasMorePending && pendingTransportations.length > 0}}" class="no-more">
          <view>到底了...</view>
        </view>
      </scroll-view>
    </view>
  </van-popup>
  <!-- 日期选择器 -->
  <van-calendar show="{{ showDatePicker }}" type="single" poppable="{{ true }}" show-confirm="{{ true }}" confirm-text="确认日期" bind:close="closeDatePicker" bind:confirm="onDateConfirm" color="#2c9678" min-date="{{ minDate }}" max-date="{{ maxDate }}" default-date="{{ currentDate }}" />
  <!-- 服装信息弹窗 -->
  <van-popup show="{{ showClothingInfo }}" round position="center" custom-style="width: 90%; max-width: 600rpx; border-radius: 16rpx;" bind:close="closeClothingInfo">
    <view class="clothing-popup-container">
      <z-clothing-info-card clothingInfo="{{ selectedClothingInfo.is_oem ? null : selectedClothingInfo }}" oemClothingInfo="{{ selectedClothingInfo.is_oem ? selectedClothingInfo : null }}" isOem="{{ selectedClothingInfo.is_oem }}" />
    </view>
  </van-popup>
</view>