// pages/warehouses/warehouses.js
import Api from "../../utils/api.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 仓库列表
    warehouseList: [],
    // 加载状态
    loading: false,
    // 数据是否已初始化
    dataInitialized: false,
    // 汇总统计数据
    summaryData: {
      totalPackages: 0,
      totalPieces: 0,
      lastUpdateDate: "--",
    },
    // 最后一次API响应数据
    lastApiResponse: null,
    // 新建/编辑仓库弹窗
    showAddWarehouse: false,
    showEditWarehouse: false,
    warehouseForm: {
      name: "",
      address: "",
      region: "",
      capacity: "",
      rent_price: "",
      rent_date: "",
      return_date: "",
      contact_person: "",
      contact_phone: "",
      remark: "",
    },
    // 待入库货运信息弹窗
    showPendingInbound: false,
    pendingTransportations: [],
    loadingPending: false,
    hasMorePending: true,
    pendingPage: 1,

    // 服装信息弹窗
    showClothingInfo: false,
    selectedClothingInfo: null,
    // 仓库管理功能菜单
    managementMenus: [
      {
        id: "inbound",
        label: "入库",
        icon: "add-square",
        color: "#5698c3",
        path: "/pages/inbound/inbound",
      },
      {
        id: "outbound",
        label: "出库",
        icon: "logistics",
        color: "#2c9678",
        path: "/pages/outbound/outbound",
      },
      {
        id: "transfer",
        label: "移库",
        icon: "exchange",
        color: "#ddc871",
        path: "/pages/transfer/transfer",
      },
      {
        id: "inventory",
        label: "盘存",
        icon: "records",
        color: "#806d9e",
        path: "/pages/inventory-check/inventory-check",
      },
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 首次加载时初始化数据
    this.loadWarehouses();
    this.loadSummaryData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时刷新仓库列表，以防从仓库详情页面返回时有新增或修改
    // 添加防抖处理，避免频繁刷新
    if (this._refreshTimer) {
      clearTimeout(this._refreshTimer);
    }

    this._refreshTimer = setTimeout(() => {
      console.log("onShow - 开始刷新数据");
      this.loadWarehouses();
      this.loadSummaryData();
      // 刷新calendar-table组件的数据
      this.refreshCalendarData();
    }, 100);
  },

  /**
   * 加载仓库列表 - 使用新的统一接口
   */
  async loadWarehouses() {
    try {
      // 避免重复加载
      if (this.data.loading) {
        console.log("正在加载中，跳过重复请求");
        return;
      }

      // 只有在数据未初始化时才显示loading状态
      if (!this.data.dataInitialized) {
        this.setData({ loading: true });
      }

      // 调用新的统一接口，一次性获取仓库列表和统计数据
      const response = await Api.getWarehousesWithStats({ status: "active" });

      if (response.data.code === 200) {
        const warehouses = response.data.data.list || [];

        console.log("获取到的仓库数据:", warehouses);

        this.setData({
          warehouseList: warehouses,
          dataInitialized: true,
        });
      } else {
        wx.showToast({
          title: response.data.message || "加载失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载仓库列表异常:", error);
      wx.showToast({
        title: "加载失败",
        icon: "none",
      });
    } finally {
      this.setData({
        loading: false,
        dataInitialized: true,
      });
    }
  },

  /**
   * 加载汇总统计数据 - 优化版本，使用后端聚合计算
   */
  async loadSummaryData() {
    try {
      // 使用新的全局库存汇总API，利用remaining_percentage字段进行后端聚合计算
      const response = await Api.getGlobalInventorySummary();

      if (response.data.code === 200) {
        const summaryData = response.data.data;

        // 格式化最近更新日期
        let lastUpdateDate = "--";
        if (summaryData.last_updated) {
          const date = new Date(summaryData.last_updated);
          lastUpdateDate = `${date.getFullYear()}-${String(
            date.getMonth() + 1
          ).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
        }

        this.setData({
          summaryData: {
            totalPackages: summaryData.total_packages || 0,
            totalPieces: summaryData.total_pieces || 0,
            lastUpdateDate,
          },
        });
      }
    } catch (error) {
      console.error("加载汇总数据失败:", error);
    }
  },









  /**
   * 格式化日期为 YYYY-MM-DD 格式
   */
  formatDate(dateString) {
    if (!dateString) return "--";

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "--";

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");

      return `${year}-${month}-${day}`;
    } catch (error) {
      console.error("日期格式化失败:", error);
      return "--";
    }
  },

  /**
   * 点击管理功能菜单
   */
  onManagementMenuTap(e) {
    const { menu } = e.currentTarget.dataset;

    if (menu.id === "inbound") {
      // 入库操作：弹窗显示待入库货运信息
      this.showPendingInboundDialog();
    } else {
      // 其他操作：直接跳转
      wx.navigateTo({
        url: menu.path,
      });
    }
  },

  /**
   * 显示待入库货运信息弹窗
   */
  async showPendingInboundDialog() {
    this.setData({
      showPendingInbound: true,
      pendingTransportations: [],
      pendingPage: 1,
      hasMorePending: true,
    });
    this.loadPendingTransportations();
  },

  /**
   * 加载待入库货运信息
   */
  async loadPendingTransportations() {
    if (this.data.loadingPending || !this.data.hasMorePending) return;

    try {
      this.setData({ loadingPending: true });

      const response = await Api.getPendingInboundTransportations({
        page: this.data.pendingPage,
        limit: 5,
        arrived: "1", // 已到货
        inbound_status: "0", // 未入库
      });

      if (response.data.code === 200) {
        const newTransportations = response.data.data || [];

        // 为每个货运信息添加发货包数统计和格式化日期
        for (let transportation of newTransportations) {
          // 格式化日期为 YYYY-MM-DD 格式
          transportation.formatted_date_out = this.formatDate(
            transportation.date_out
          );
          transportation.formatted_date_arrived = this.formatDate(
            transportation.date_arrived
          );
        }

        const allTransportations =
          this.data.pendingPage === 1
            ? newTransportations
            : [...this.data.pendingTransportations, ...newTransportations];

        this.setData({
          pendingTransportations: allTransportations,
          hasMorePending: newTransportations.length === 5,
          pendingPage: this.data.pendingPage + 1,
        });
      }
    } catch (error) {
      console.error("获取待入库货运信息失败:", error);
      wx.showToast({
        title: "获取数据失败",
        icon: "none",
      });
    } finally {
      this.setData({ loadingPending: false });
    }
  },

  /**
   * 加载更多待入库货运信息
   */
  loadMorePendingTransportations() {
    this.loadPendingTransportations();
  },

  /**
   * 关闭待入库弹窗
   */
  closePendingInboundDialog() {
    this.setData({ showPendingInbound: false });
  },

  /**
   * 选择货运信息进入入库页面
   */
  onSelectTransportation(e) {
    const { transportation } = e.currentTarget.dataset;
    this.closePendingInboundDialog();

    wx.navigateTo({
      url: `/pages/inbound/inbound?transportation_id=${transportation.transportation_id}`,
    });
  },

  /**
   * 查看仓库详情
   */
  onWarehouseDetail(e) {
    const { warehouse } = e.currentTarget.dataset;

    if (!warehouse || !warehouse.warehouse_id) {
      wx.showToast({
        title: "仓库信息不完整",
        icon: "none",
      });
      return;
    }

    // 将仓库数据传递给详情页面
    const warehouseData = {
      id: warehouse.warehouse_id,
      name: warehouse.name,
      address: warehouse.address,
      capacity: warehouse.capacity || "",
      rentPrice: warehouse.rent_price || "",
      rentDate: warehouse.rent_date || "",
      returnDate: warehouse.return_date || "",
    };

    wx.navigateTo({
      url: `/pages/warehouse-detail/warehouse-detail?warehouseData=${encodeURIComponent(
        JSON.stringify(warehouseData)
      )}`,
    });
  },

  /**
   * 新建仓库
   */
  onAddWarehouse() {
    wx.navigateTo({
      url: "/pages/warehouse-detail/warehouse-detail?mode=add",
    });
  },

  /**
   * 编辑仓库
   */
  onEditWarehouse(e) {
    const { warehouse } = e.currentTarget.dataset;
    this.setData({
      showEditWarehouse: true,
      warehouseForm: { ...warehouse },
    });
  },

  /**
   * 表单输入
   */
  onFormInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    this.setData({
      [`warehouseForm.${field}`]: value,
    });
  },

  /**
   * 保存仓库
   */
  async saveWarehouse() {
    const { warehouseForm, showEditWarehouse } = this.data;

    if (!warehouseForm.name || !warehouseForm.address) {
      wx.showToast({
        title: "请填写必要信息",
        icon: "none",
      });
      return;
    }

    try {
      let response;
      if (showEditWarehouse) {
        response = await Api.updateWarehouse(warehouseForm);
      } else {
        response = await Api.createWarehouse(warehouseForm);
      }

      if (response.data.code === 200) {
        wx.showToast({
          title: showEditWarehouse ? "更新成功" : "创建成功",
          icon: "success",
        });
        this.closeDialog();
        this.loadWarehouses();
      } else {
        wx.showToast({
          title: response.data.message || "操作失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("保存仓库失败:", error);
      wx.showToast({
        title: "操作失败",
        icon: "none",
      });
    }
  },

  /**
   * 关闭弹窗
   */
  closeDialog() {
    this.setData({
      showAddWarehouse: false,
      showEditWarehouse: false,
    });
  },







  /**
   * 刷新calendar-table组件的数据
   */
  refreshCalendarData() {
    try {
      // 获取calendar-table组件实例
      const calendarComponent = this.selectComponent('#calendar-table');
      if (calendarComponent && typeof calendarComponent.refreshData === 'function') {
        console.log("刷新calendar-table组件数据");
        calendarComponent.refreshData();
      } else {
        console.log("calendar-table组件未找到或refreshData方法不存在");
      }
    } catch (error) {
      console.error("刷新calendar-table数据失败:", error);
    }
  },
  /**
   * 关闭所有下拉菜单（处理页面点击事件）
   */
  closeAllDropdowns() {
    // 这个方法用于处理页面点击时关闭可能打开的下拉菜单
    // 目前主要是为了响应根容器的tap事件，避免控制台错误
    // 如果将来有下拉菜单组件，可以在这里添加关闭逻辑
  },
  /**
   * 处理calendar-table组件的查询事件
   */
  onCalendarQuery(e) {
    const { start_date, end_date, warehouse_id } = e.detail;
    console.log("日历查询事件:", { start_date, end_date, warehouse_id });

    // 跳转到日历日志页面查看查询结果
    wx.navigateTo({
      url: `/pages/calendar-logs/calendar-logs?start_date=${start_date}&end_date=${end_date}${warehouse_id ? `&warehouse_id=${warehouse_id}` : ''}`,
    });
  },
  /**
   * 点击服装名称 - 参照仓库详情页逻辑
   */
  async onClothingNameTap(e) {
    const { change } = e.currentTarget.dataset;

    console.log("仓库主页点击服装名称，change数据:", change);

    // 显示加载状态
    wx.showLoading({
      title: "加载中...",
      mask: true,
    });

    try {
      let clothingInfo;

      // 根据SKU规则判断服装类型：如果SKU包含"_"且前面是"oem"，则为OEM服装
      const sku = change.sku;
      const isOemClothing =
        sku && sku.includes("_") && sku.split("_")[0].toLowerCase() === "oem";

      if (isOemClothing) {
        // OEM服装：sku格式为 "oem_xxx"，提取后面的部分作为oem_clothing_id
        const oemClothingId = sku.split("_").slice(1).join("_");
        console.log("获取OEM服装信息，OEM ID:", oemClothingId);

        const response = await Api.getOemClothingInfo({
          oem_clothing_id: oemClothingId,
        });

        console.log("OEM服装API响应:", response);

        // 检查多种可能的响应格式
        let oemData = null;
        if (response.data) {
          if (response.data.code === 200) {
            oemData = response.data.data;
          } else if (response.data.oem_clothing_id) {
            oemData = response.data;
          }
        } else if (response.oem_clothing_id) {
          oemData = response;
        }

        if (oemData && oemData.oem_clothing_id) {
          clothingInfo = {
            oem_clothing_id: oemData.oem_clothing_id,
            oem_clothing_name: oemData.oem_clothing_name,
            oem_supplier: oemData.oem_supplier || "",
            classification: oemData.classification || "",
            price: oemData.price || 0,
            img: oemData.img || [],
            is_oem: true,
            size: oemData.size || "",
            style: oemData.style || "",
            order_quantity: oemData.order_quantity || 0,
            shipments: oemData.shipments || 0,
            in_pcs: oemData.in_pcs || 0,
          };
        } else {
          console.error("OEM服装API响应格式不正确:", response);
          throw new Error("获取OEM服装信息失败");
        }
      } else {
        // 普通服装：提取SKU中的后缀作为clothing_id
        console.log("获取普通服装信息，SKU:", sku);
        const clothingId = sku.split("_").slice(1).join("_");
        const response = await Api.getClothingInfo({
          clothing_id: clothingId,
        });

        console.log("普通服装API响应:", response);

        // 检查多种可能的响应格式
        let clothingData = null;
        if (response.data) {
          if (response.data.code === 200) {
            clothingData = response.data.data;
          } else if (response.data.clothing_id) {
            clothingData = response.data;
          }
        } else if (response.clothing_id) {
          clothingData = response;
        }

        if (clothingData && clothingData.clothing_id) {
          clothingInfo = {
            clothing_id: clothingData.clothing_id,
            clothing_name: clothingData.clothing_name,
            sku: clothingData.sku || sku,
            supplier: clothingData.supplier || "",
            price: clothingData.price || 0,
            img: clothingData.img || [],
            is_oem: false,
            group_classification: clothingData.group_classification || [],
            size: clothingData.size || "",
            style: clothingData.style || "",
            long_or_short_sleeve: clothingData.long_or_short_sleeve || "",
            pocket_type: clothingData.pocket_type || "",
            order_quantity: clothingData.order_quantity || 0,
            clipping_pcs: clothingData.clipping_pcs || 0,
            shipments: clothingData.shipments || 0,
          };
        } else {
          console.error("普通服装API响应格式不正确:", response);
          throw new Error("获取服装信息失败");
        }
      }

      console.log("构造的服装信息:", clothingInfo);

      this.setData({
        selectedClothingInfo: clothingInfo,
        showClothingInfo: true,
      });
    } catch (error) {
      console.error("获取服装详细信息失败:", error);
      wx.showToast({
        title: "获取服装信息失败",
        icon: "none",
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 关闭服装信息弹窗
   */
  closeClothingInfo() {
    this.setData({
      showClothingInfo: false,
      selectedClothingInfo: null,
    });
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 清理定时器
    if (this._refreshTimer) {
      clearTimeout(this._refreshTimer);
      this._refreshTimer = null;
    }
  },
});
