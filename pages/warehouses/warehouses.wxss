/* pages/warehouses/warehouses.wxss */

/* ==================== 基础布局 ==================== */
.warehouse-management-container {
  padding: 10rpx 20rpx 10rpx 20rpx;
  background-color: #f5f5f5;
  min-height: 95vh;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  max-height: 98vh;
}

/* ==================== 汇总统计卡片 ==================== */
.summary-card {
  background: linear-gradient(135deg, #667eea 0%, #2c9678 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  color: white;
}

.summary-content {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.summary-item {
  text-align: center;
  flex: 1;
}

.summary-value {
  font-size: 32rpx;
  font-weight: 700;
  margin-bottom: 6rpx;
}

.summary-label {
  font-size: 22rpx;
  opacity: 0.9;
}

/* ==================== 管理功能菜单 ==================== */
.management-section {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.menu-grid-horizontal {
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 20rpx;
}

.menu-item-horizontal {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  flex: 1;
  transition: all 0.3s ease;
}

.menu-item-horizontal:active {
  transform: scale(0.95);
}

.menu-icon-horizontal {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
}

.menu-label {
  font-size: 26rpx;
  font-weight: 500;
  color: #323233;
  text-align: center;
}

/* ==================== 仓库列表 ==================== */
.warehouse-section {
  background: white;
  border-radius: 16rpx;
  padding: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 水平滚动容器 */
.warehouse-scroll-container {
  width: 100%;
  overflow: hidden;
}

.warehouse-scroll {
  width: 100%;
  white-space: nowrap;
}

/* 水平网格布局 - 固定两行显示，支持动态缩放 */
.warehouse-grid-horizontal {
  display: inline-flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-content: flex-start;
  gap: calc(12rpx * var(--scale-factor, 1)); /* 动态间距 */
  height: calc(218rpx * var(--scale-factor, 1)); /* 动态高度，容纳2排卡片 */
  min-width: 100%;
  --scale-factor: 1; /* 默认缩放因子 */
}

/* 小屏幕适配 */
@media (max-width: 375px) {
  .warehouse-grid-horizontal {
    --scale-factor: 0.9;
  }
}

/* 大屏幕适配 */
@media (min-width: 414px) {
  .warehouse-grid-horizontal {
    --scale-factor: 1.1;
  }
}

.warehouse-mini-card {
  background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
  border-radius: calc(10rpx * var(--scale-factor, 1)); /* 动态圆角 */
  padding: calc(12rpx * var(--scale-factor, 1)); /* 动态内边距 */
  border: 1rpx solid #9bb7e5;
  box-shadow: 0 2rpx 4rpx rgba(89, 129, 195, 0.08),
    0 4rpx 8rpx rgba(89, 129, 195, 0.06), 0 8rpx 16rpx rgba(89, 129, 195, 0.04);
  transition: all 0.3s ease;
  height: calc(100rpx * var(--scale-factor, 1)); /* 动态高度 */
  width: calc(200rpx * var(--scale-factor, 1)); /* 动态宽度 */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex-shrink: 0; /* 防止压缩 */
  box-sizing: border-box;
}

.warehouse-mini-card:active {
  background: linear-gradient(135deg, #f9fafb 0%, #ecfdf5 100%);
  transform: scale(0.98);
  box-shadow: 0 1rpx 2rpx rgba(34, 197, 94, 0.1),
    0 2rpx 4rpx rgba(34, 197, 94, 0.08), 0 4rpx 8rpx rgba(34, 197, 94, 0.06);
}

.warehouse-row-1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: calc(8rpx * var(--scale-factor, 1)); /* 动态间距 */
  margin-bottom: calc(8rpx * var(--scale-factor, 1)); /* 动态边距 */
}

.warehouse-mini-name {
  font-size: calc(24rpx * var(--scale-factor, 1)); /* 动态字体大小 */
}

.address-tag {
  flex-shrink: 0;
}

.warehouse-row-2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-text {
  font-size: calc(20rpx * var(--scale-factor, 1)); /* 动态字体大小 */
  color: #323233;
  font-weight: 500;
}

/* 新建仓库卡片 */
.add-warehouse-card {
  background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%) !important;
  border: 2rpx dashed #6396e7 !important;
  box-shadow: 0 2rpx 4rpx rgba(59, 130, 246, 0.08),
    0 4rpx 8rpx rgba(59, 130, 246, 0.06), 0 8rpx 16rpx rgba(59, 130, 246, 0.04) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.add-warehouse-card:active {
  background: linear-gradient(135deg, #f9fafb 0%, #eff6ff 100%) !important;
  box-shadow: 0 1rpx 2rpx rgba(59, 130, 246, 0.1),
    0 2rpx 4rpx rgba(59, 130, 246, 0.08), 0 4rpx 8rpx rgba(59, 130, 246, 0.06) !important;
  transform: scale(0.98);
}

.add-warehouse-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: calc(8rpx * var(--scale-factor, 1)); /* 动态间距 */
}

.add-warehouse-text {
  font-size: calc(24rpx * var(--scale-factor, 1)); /* 动态字体大小 */
  color: #3b82f6;
  font-weight: 500;
}

/* ==================== 日历组件部分 ==================== */
.calendar-section {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: visible; /* 允许弹窗显示在容器外 */
  position: relative; /* 为弹窗提供定位上下文 */
  z-index: 1; /* 确保日历组件在合适的层级 */
}

/* 确保calendar-table组件内的弹窗有足够高的z-index */
.calendar-section calendar-table {
  position: relative;
  z-index: 10;
}





.warehouse-name {
  color: #646566;
  font-weight: 500;
  font-size: 22rpx;
}

/* 移库信息样式 */
.transfer-info {
  flex: 1;
}

.transfer-notes {
  font-size: 20rpx;
  color: #646566;
  font-weight: 500;
  line-height: 1.4;
}

/* 移库操作的仓库信息样式（保留兼容性） */
.transfer-warehouse-info {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 20rpx;
}

.transfer-arrow {
  color: #1989fa;
  font-weight: 600;
  font-size: 24rpx;
}

/* 服装信息弹窗容器 */
.clothing-popup-container {
  background: transparent;
  border-radius: 16rpx;
  overflow: hidden;
  max-width: 90vw;
  max-height: 80vh;
}

/* ==================== 弹窗样式 ==================== */
.pending-inbound-dialog {
  width: 600rpx;
  max-height: 800rpx;
  padding: 32rpx;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  margin-bottom: 24rpx;
  color: #323233;
}

.pending-scroll {
  max-height: 600rpx;
}

.pending-list {
  padding-bottom: 20rpx;
}

.pending-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 10rpx 20rpx;
  margin-bottom: 16rpx;
  border: 1rpx solid #ebedf0;
  transition: all 0.3s ease;
}

.pending-item:active {
  background: #f2f3f5;
  transform: scale(0.98);
}

.pending-row-1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.supplier-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

    text-align: center;
}

.package-count {
  font-size: 28rpx;
  color: #1989fa;
  font-weight: 600;
  flex-shrink: 0;
  text-align: center;
  flex: 1;
}

.pending-row-2 {
  display: flex;
  justify-content: space-between;
  align-items: center;

}

.date-info {
  display: flex;
  flex-direction: row;
    justify-content: center;
  align-items: center;
  flex: 1;
}

.date-label {
  font-size: 26rpx;
  color: #969799;
}

.date-value {
  font-size: 26rpx;
  color: #646566;
}

/* 加载更多和没有更多数据样式 */
.loading-more,
.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20rpx;
  margin-bottom: 40rpx;
  font-size: 24rpx;
  color: #969799;
  gap: 8rpx;
}

/* 汇总数据容器样式 */
.logs-summary-container {
  margin-top: 20rpx;
}

/* ==================== 空状态 ==================== */
.empty-state {
  padding: 60rpx 0;
  text-align: center;
}

/* ==================== 弹窗样式 ==================== */
.warehouse-form {
  width: 600rpx;
  padding: 40rpx;
}

.form-title {
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  margin-bottom: 32rpx;
  color: #323233;
}

.form-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 32rpx;
}

/* ==================== 日志汇总数据样式 ==================== */
/* .logs-summary {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #ebedf0;
} */


