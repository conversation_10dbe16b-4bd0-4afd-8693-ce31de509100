<!-- pages/inbound/inbound.wxml -->
<view class="inbound-container">
  <!-- 加载状态 -->
  <van-loading wx:if="{{loading}}" type="spinner" color="#1989fa" size="24px" vertical>
    加载中...
  </van-loading>
  <!-- 主要内容 -->
  <view wx:else class="main-content">
    <!-- 货运信息卡片 -->
    <view class="transportation-card">
      <view class="card-content">
        <view class="info-grid">
          <view class="info-item">
            <view class="label">供应商</view>
            <view class="value">{{transportationInfo.supplier}}</view>
          </view>
          <view class="info-item">
            <view class="label">发货日期</view>
            <view class="value">{{transportationInfo.date_out || ''}}</view>
          </view>
          <view class="info-item">
            <view class="label">总包数</view>
            <view class="value highlight">{{transportationInfo.total_package_quantity || 0}}包</view>
          </view>
          <view class="info-item">
            <view class="label">总件数</view>
            <view class="value highlight">{{transportationInfo.total_pcs || 0}}件</view>
          </view>
        </view>
      </view>
    </view>
    <!-- 左右分栏布局 -->
    <view class="split-layout">
      <!-- 左侧：仓库列表 -->
      <view class="left-panel">
        <view class="panel-header">
          <view class="panel-title">仓库列表</view>
        </view>
        <scroll-view class="warehouse-list" scroll-y show-scrollbar="{{false}}">
          <view wx:for="{{warehouseList}}" wx:key="warehouse_id" class="warehouse-item {{selectedWarehouse && selectedWarehouse.warehouse_id === item.warehouse_id ? 'selected' : ''}}">
            <view class="warehouse-info" data-warehouse="{{item}}" bind:tap="onSelectWarehouse">
              <view class="warehouse-name">{{item.name}}</view>
              <view class="warehouse-address">{{item.address}}</view>
            </view>
            <view class="warehouse-actions">
              <van-icon name="info-o" color="#666" size="16px" data-warehouse="{{item}}" bind:click="onEditWarehouse" class="edit-icon" />
            </view>
          </view>
        </scroll-view>
        <!-- 新建仓库按钮 -->
        <view class="add-warehouse-btn" bindtap="onAddWarehouse">
          <van-icon name="plus" size="40rpx" />
          <view>新建仓库</view>
        </view>
      </view>
      <!-- 右侧：到货明细列表 -->
      <view class="right-panel">
        <view class="panel-header">
          <!-- 服装名称筛选输入框直接放在标题区域 -->
          <view class="header-search-field">
            <view class="search-input-wrapper">
              <van-icon name="search" class="search-icon" />
              <input
                class="search-input"
                value="{{clothingNameFilter}}"
                placeholder="输入款号筛选"
                bindinput="onClothingNameFilterInput"
                maxlength="50"
                type="text"
                confirm-type="search" />
              <van-icon wx:if="{{clothingNameFilter}}" name="clear" class="clear-icon" bindtap="onClearFilter" />
            </view>
          </view>
          <van-button type="warning" size="small" bind:click="onInboundAllTap">一键全入库</van-button>
        </view>
        <view wx:if="{{filteredArrivalDetails.length === 0 && arrivalDetails.length === 0}}" class="empty-state">
          <van-empty description="暂无到货明细" />
        </view>
        <view wx:elif="{{filteredArrivalDetails.length === 0 && clothingNameFilter}}" class="empty-state">
          <van-empty description="未找到匹配的服装" />
        </view>
        <view wx:else class="details-list">
          <view wx:for="{{filteredArrivalDetails}}" wx:key="index" class="detail-item">
            <view class="item-content">
              <!-- 主要信息横向布局 -->
              <view class="item-main-info">
                <!-- 左侧：服装信息组 -->
                <view class="clothing-groups">
                  <view wx:for="{{item.clothingItems}}" wx:key="clothing_id" class="clothing-group">
                    <view class="clothing-name clickable" style="color: #000000;" data-clothing-info="{{item}}" data-is-oem="{{item.oem === '是' || item.oem === 'yes'}}" bindtap="onClothingNameTap">
                      {{item.clothing_name}}
                    </view>
                    <view class="piece-count">{{item.out_pcs}}件</view>
                  </view>
                </view>
                <!-- 右侧：包装信息 -->
                <view class="package-info">
                  <view class="package-formula">{{item.QUP}}*{{item.remainingQuantity}}</view>
                </view>
              </view>
              <!-- 操作区域：剩余包数、步进器、入库按钮在同一排 -->
              <view class="item-actions-row" wx:if="{{item.remainingQuantity > 0 && selectedWarehouse}}">
                <view class="remaining-info">
                  <view class="remaining-value {{item.remainingQuantity <= 0 ? 'completed-view' : 'remaining-view'}}">
                    {{item.remainingQuantity}}包
                  </view>
                </view>
                <view class="quantity-control">
                  <van-stepper value="{{item.selectedQuantity || 0}}" min="0" max="{{item.remainingQuantity}}" data-index="{{index}}" data-original-index="{{item.originalIndex}}" bind:change="onQuantityChange" />
                </view>
                <van-button type="primary" size="small" disabled="{{!item.selectedQuantity || item.selectedQuantity <= 0}}" data-detail="{{item}}" data-index="{{index}}" bind:click="onInboundTap">
                  入库
                </van-button>
              </view>
              <!-- 提示信息 -->
              <view class="item-tips" wx:if="{{item.remainingQuantity > 0 && !selectedWarehouse}}">
                <view class="tip-view">请先选择仓库</view>
              </view>
              <view class="item-tips" wx:if="{{item.remainingQuantity <= 0}}">
                <view class="completed-tip">已全部入库</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 底部固定操作栏 -->
  <view class="bottom-fixed-bar">
    <view class="bottom-actions">
      <!-- 待入库清单按钮 -->
      <view class="action-item">
        <van-button type="info" size="large" bind:click="toggleCart" class="action-button" disabled="{{inboundCart.length === 0}}">
          <van-icon name="shopping-cart-o" />
          待入库 ({{cartTotalPackages}}包)
        </van-button>
      </view>
      <!-- 核对按钮 -->
      <view class="action-item">
        <van-button type="success" size="large" bind:click="confirmInbound" class="action-button" disabled="{{inboundCart.length === 0}}">
          <van-icon name="passed" />
          核对
        </van-button>
      </view>
    </view>
  </view>
  <!-- 待入库清单浮窗 -->
  <van-popup show="{{showCart}}" position="bottom" bind:close="toggleCart" custom-style="height: 70vh;">
    <view class="cart-popup">
      <view class="popup-header">
        <view class="popup-title">待入库清单</view>
        <view class="header-actions">
          <view class="total-info">总计：{{cartTotalPackages}}包 / {{cartTotalPieces}}件</view>
          <van-button type="danger" size="small" bind:click="clearCart">清空</van-button>
        </view>
      </view>
      <view class="cart-content">
        <view wx:if="{{groupedCart.length === 0}}" class="empty-cart">
          <van-empty description="待入库清单为空" />
        </view>
        <view wx:else>
          <!-- 按仓库分组显示 -->
          <view wx:for="{{groupedCart}}" wx:key="warehouse_id" class="warehouse-group">
            <view class="warehouse-header">
              <view class="warehouse-name">{{item.warehouse_name}}</view>
              <view class="warehouse-summary">
                {{item.total_packages}}包 / {{item.total_pieces}}件
              </view>
            </view>
            <view class="warehouse-items">
              <view wx:for="{{item.items}}" wx:for-item="cartItem" wx:key="clothing_id" class="cart-item">
                <view class="cart-item-content">
                  <!-- 主要信息横向布局 - 参照到货明细格式 -->
                  <view class="cart-main-info">
                    <!-- 左侧：服装信息组 -->
                    <view class="cart-clothing-groups">
                      <view wx:for="{{cartItem.flatItems}}" wx:key="clothing_id" class="cart-clothing-group">
                        <view class="cart-clothing-name clickable" style="color: #000000;" data-clothing-info="{{item}}" data-is-oem="{{item.oem === '是' || item.oem === 'yes'}}" bindtap="onClothingNameTap">
                          {{item.clothing_name}}
                        </view>
                        <view class="cart-piece-count">
                          {{cartItem.flatItems.length > 1 ? item.out_pcs : (cartItem.inbound_quantity * cartItem.QUP)}}件
                        </view>
                      </view>
                    </view>
                    <!-- 右侧：包装信息 -->
                    <view class="cart-package-info">
                      <view class="cart-package-formula">
                        {{cartItem.QUP}}*{{cartItem.inbound_quantity}}
                      </view>
                    </view>
                  </view>
                  <view class="cart-style-tag" wx:if="{{cartItem.style}}">
                    <van-tag type="default" size="small">{{cartItem.style}}</van-tag>
                  </view>
                </view>
                <view class="cart-item-actions">
                  <!-- 数量调整 -->
                  <van-stepper value="{{cartItem.inbound_quantity}}" min="1" max="{{cartItem.max_quantity}}" data-cart-key="{{cartItem.cart_key}}" bind:change="onCartQuantityChange" />
                  <!-- 删除按钮 -->
                  <van-icon name="delete" color="#ee0a24" data-cart-key="{{cartItem.cart_key}}" bind:click="removeFromCart" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </van-popup>
  <!-- 服装信息弹窗 -->
  <van-popup show="{{showClothingInfo}}" position="center" bind:close="onCloseClothingInfo" custom-style="width: 90%; max-width: 600rpx; border-radius: 16rpx;">
    <clothing-info-card wx:if="{{selectedClothingInfo}}" clothing-info="{{selectedClothingInfo}}" oem-clothing-info="{{selectedClothingInfo}}" is-oem="{{isOemClothing}}" />
  </van-popup>
</view>