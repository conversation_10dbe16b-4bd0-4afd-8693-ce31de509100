// pages/transfer/transfer.js
import Api from "../../utils/api.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 仓库列表
    warehouseList: [],
    // 下拉框显示状态
    showFromDropdown: false,
    showToDropdown: false,
    // 源仓库
    fromWarehouse: null,
    // 目标仓库列表（支持多个目标仓库）
    targetWarehouses: [],
    // 当前选择的目标仓库（用于添加商品到购物车时指定）
    currentTargetWarehouse: null,
    // 显示目标仓库选择弹窗
    showTargetWarehouseModal: false,
    // 库存列表
    inventoryList: [],
    // 库存统计
    totalPackages: 0,
    totalPieces: 0,
    // 移库购物车（按目标仓库分组）
    transferCart: {},
    // 汇总的移库购物车（用于显示）
    groupedTransferCart: [],
    // 购物车统计
    cartTotalPackages: 0,
    cartTotalPieces: 0,
    // 显示购物车浮窗
    showCart: false,
    // 加载状态
    loading: false,
    // 搜索加载状态
    searchLoading: false,
    // 搜索关键词
    searchKeyword: "",
    // 分页信息
    page: 1,
    limit: 20,
    hasMore: true,
    // 待添加到购物车的商品信息（用于选择目标仓库）
    pendingTransferItem: null,
    // 是否为一键移库模式
    isBatchTransferMode: false,

    // 服装信息弹窗相关
    showClothingInfo: false,
    selectedClothingInfo: null,
    isOemClothing: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 延迟执行，避免过早调用
    setTimeout(() => {
      this.loadWarehouses();
    }, 100);
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    try {
      // 检查是否需要清空移库清单
      const app = getApp();
      if (app.globalData && app.globalData.shouldClearTransferList) {
        // 延迟执行，避免过早调用
        setTimeout(() => {
          // 清空移库清单 - 支持多仓库结构
          const emptyTransferCart = {};
          this.data.targetWarehouses.forEach(w => {
            emptyTransferCart[w.warehouse_id] = [];
          });

          this.setData({
            transferCart: emptyTransferCart,
            groupedTransferCart: [],
            cartTotalPackages: 0,
            cartTotalPieces: 0,
            showCart: false,
          });

          // 重新加载库存以更新数据
          if (this.data.fromWarehouse) {
            this.loadInventory();
          }

          // 清除标记
          app.globalData.shouldClearTransferList = false;
        }, 100);
      }
    } catch (error) {
      console.error("onShow error:", error);
    }
  },

  /**
   * 加载仓库列表
   */
  async loadWarehouses() {
    try {
      const response = await Api.getWarehouseList({ status: "active" });

      if (response.data.code === 200) {
        const warehouseList = response.data.data.list || [];

        // 为每个仓库添加选中状态标识
        const warehouseListWithSelection = warehouseList.map(warehouse => ({
          ...warehouse,
          isSelected: this.data.targetWarehouses.some(w => w.warehouse_id === warehouse.warehouse_id)
        }));

        this.setData({
          warehouseList: warehouseListWithSelection,
        });
      }
    } catch (error) {
      console.error("加载仓库列表失败:", error);
    }
  },

  /**
   * 更新仓库列表的选中状态
   */
  updateWarehouseListSelection() {
    const warehouseList = this.data.warehouseList.map(warehouse => ({
      ...warehouse,
      isSelected: this.data.targetWarehouses.some(w => w.warehouse_id === warehouse.warehouse_id)
    }));

    this.setData({
      warehouseList: warehouseList,
    });
  },

  /**
   * 切换移出仓库下拉框
   */
  toggleFromDropdown() {
    this.setData({
      showFromDropdown: !this.data.showFromDropdown,
      showToDropdown: false, // 关闭另一个下拉框
    });
  },

  /**
   * 切换移入仓库下拉框
   */
  toggleToDropdown() {
    this.setData({
      showToDropdown: !this.data.showToDropdown,
      showFromDropdown: false, // 关闭另一个下拉框
    });
  },

  /**
   * 选择移出仓库
   */
  selectFromWarehouse(e) {
    const { warehouse } = e.currentTarget.dataset;

    // 重置所有相关数据
    const emptyTransferCart = {};
    this.data.targetWarehouses.forEach(w => {
      emptyTransferCart[w.warehouse_id] = [];
    });

    this.setData({
      fromWarehouse: warehouse,
      showFromDropdown: false,
      inventoryList: [],
      transferCart: emptyTransferCart,
      groupedTransferCart: [],
      cartTotalPackages: 0,
      cartTotalPieces: 0,
      totalPackages: 0,
      totalPieces: 0,
      page: 1,
      hasMore: true,
    });

    this.loadInventory();
  },

  /**
   * 选择移入仓库（添加到目标仓库列表或取消选择）
   */
  selectToWarehouse(e) {
    const { warehouse } = e.currentTarget.dataset;

    // 检查是否与移出仓库相同
    if (
      this.data.fromWarehouse &&
      warehouse.warehouse_id === this.data.fromWarehouse.warehouse_id
    ) {
      wx.showToast({
        title: "移出和移入仓库不能相同",
        icon: "none",
      });
      return;
    }

    // 检查是否已经在目标仓库列表中
    const existingIndex = this.data.targetWarehouses.findIndex(
      w => w.warehouse_id === warehouse.warehouse_id
    );

    if (existingIndex >= 0) {
      // 已选中，执行取消选择逻辑
      this.unselectTargetWarehouse(warehouse.warehouse_id);
    } else {
      // 未选中，添加到目标仓库列表
      const newTargetWarehouses = [...this.data.targetWarehouses, warehouse];

      this.setData({
        targetWarehouses: newTargetWarehouses,
        showToDropdown: false,
      });

      // 初始化该仓库的购物车
      const newTransferCart = { ...this.data.transferCart };
      if (!newTransferCart[warehouse.warehouse_id]) {
        newTransferCart[warehouse.warehouse_id] = [];
      }

      this.setData({
        transferCart: newTransferCart,
      });

      this.updateCartData();
      this.updateWarehouseListSelection();
    }
  },

  /**
   * 取消选择目标仓库
   */
  unselectTargetWarehouse(warehouseId) {
    // 检查该仓库是否有移库数据
    const hasTransferData = this.data.transferCart[warehouseId] &&
                           this.data.transferCart[warehouseId].length > 0;

    if (hasTransferData) {
      // 有移库数据，需要确认
      const warehouse = this.data.targetWarehouses.find(w => w.warehouse_id === warehouseId);
      wx.showModal({
        title: "确认取消选择",
        content: `"${warehouse?.name || '该仓库'}"的移库清单中有数据，取消选择将清空对应的移库清单，确定继续吗？`,
        success: (res) => {
          if (res.confirm) {
            this.removeTargetWarehouseData(warehouseId);
          }
        },
      });
    } else {
      // 没有移库数据，直接移除
      this.removeTargetWarehouseData(warehouseId);
    }
  },

  /**
   * 移除目标仓库数据
   */
  removeTargetWarehouseData(warehouseId) {
    // 从目标仓库列表中移除
    const newTargetWarehouses = this.data.targetWarehouses.filter(
      w => w.warehouse_id !== warehouseId
    );

    // 从购物车中移除该仓库的数据
    const newTransferCart = { ...this.data.transferCart };
    delete newTransferCart[warehouseId];

    this.setData({
      targetWarehouses: newTargetWarehouses,
      transferCart: newTransferCart,
      showToDropdown: false, // 关闭下拉框
    });

    this.updateCartData();
    this.updateWarehouseListSelection();

    // 重新加载库存以更新数据
    if (this.data.fromWarehouse) {
      this.loadInventory();
    }
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡，防止关闭下拉框
  },

  /**
   * 关闭所有下拉框
   */
  closeAllDropdowns() {
    this.setData({
      showFromDropdown: false,
      showToDropdown: false,
    });
  },

  /**
   * 加载库存列表
   */
  async loadInventory(isLoadMore = false) {
    if (!this.data.fromWarehouse) return;

    try {
      // 只有在非搜索状态且非加载更多时才显示loading
      if (!this.data.searchLoading && !isLoadMore) {
        this.setData({ loading: true });
      }

      const params = {
        warehouse_id: this.data.fromWarehouse.warehouse_id,
        page: isLoadMore ? this.data.page : 1,
        limit: this.data.limit,
      };

      if (this.data.searchKeyword) {
        params.product_name = this.data.searchKeyword;
      }

      const response = await Api.getNewWarehouseInventory(params);
      console.log("获取到的库存数据621:", response.data);

      if (response.data.code === 200) {
        const newList = response.data.data.list || [];
        const summaryData = response.data.data.summary || {};
        const currentList = isLoadMore ? this.data.inventoryList : [];

        console.log("后端汇总数据:", summaryData);

        // 为每个库存项添加唯一ID和选择数量，使用 remaining_percentage 优化显示
        const processedList = newList.map((item, index) => ({
          ...item,
          unique_id: `${
            item.contents[0]?.sku || "unknown"
          }_${Date.now()}_${index}`,
          selectedQuantity: 0,
          // 使用 remaining_percentage 计算实际可移库包数
          effective_package_count: item.package_count_raw || item.package_count,
          // 标记包裹状态，便于前端显示
          is_partial: item.status_type === 'partially_shipped' || (item.remaining_percentage && item.remaining_percentage < 1),
        }));

        const updatedList = [...currentList, ...processedList];

        // 根据当前移库清单调整库存显示
        const adjustedList = this.adjustInventoryForTransferCart(updatedList);

        // 对于第一次加载，使用后端汇总数据；对于分页加载，使用前端计算
        let finalTotalPackages, finalTotalPieces;

        if (!isLoadMore) {
          // 第一次加载：使用后端汇总数据
          finalTotalPackages = Math.round((summaryData.total_packages || 0) * 100) / 100;
          finalTotalPieces = summaryData.total_pieces || 0;
          console.log("使用后端汇总数据:", { finalTotalPackages, finalTotalPieces });
        } else {
          // 分页加载：使用前端计算（这里只是为了保持兼容性，实际上分页时总数不应该变化）
          const currentPagePackages = adjustedList.slice(currentList.length).reduce(
            (sum, item) => sum + (item.effective_package_count || item.package_count || 0),
            0
          );
          const currentPagePieces = adjustedList.slice(currentList.length).reduce(
            (sum, item) => sum + (item.total_quantity || 0),
            0
          );
          finalTotalPackages = Math.round((this.data.totalPackages + currentPagePackages) * 100) / 100;
          finalTotalPieces = this.data.totalPieces + currentPagePieces;
        }

        this.setData({
          inventoryList: adjustedList,
          totalPackages: finalTotalPackages,
          totalPieces: finalTotalPieces,
          hasMore: newList.length >= this.data.limit,
          page: isLoadMore ? this.data.page + 1 : 2,
        });
      }
    } catch (error) {
      console.error("加载库存失败:", error);
      wx.showToast({
        title: "加载库存失败",
        icon: "none",
      });
    } finally {
      // 只有在非搜索状态时才重置loading
      if (!this.data.searchLoading) {
        this.setData({ loading: false });
      }
    }
  },

  /**
   * 搜索输入变化
   */
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value,
    });
  },

  /**
   * 搜索库存
   */
  async onSearch() {
    if (!this.data.fromWarehouse) {
      wx.showToast({
        title: "请先选择移出仓库",
        icon: "none",
      });
      return;
    }

    this.setData({
      searchLoading: true,
      page: 1,
      hasMore: true,
      inventoryList: [],
      totalPackages: 0,
      totalPieces: 0,
    });

    try {
      await this.loadInventory();
    } finally {
      this.setData({
        searchLoading: false,
      });
    }
  },

  /**
   * 重置搜索
   */
  async onResetSearch() {
    this.setData({
      searchKeyword: "",
      searchLoading: true,
      page: 1,
      hasMore: true,
      inventoryList: [],
      totalPackages: 0,
      totalPieces: 0,
    });

    try {
      await this.loadInventory();
    } finally {
      this.setData({
        searchLoading: false,
      });
    }
  },

  /**
   * 数量变化（步进器）
   */
  onQuantityChange(e) {
    const { inventoryIndex } = e.currentTarget.dataset;
    const quantity = parseFloat(e.detail) || 0;

    const inventoryList = this.data.inventoryList;
    inventoryList[inventoryIndex].selectedQuantity = quantity;

    this.setData({
      inventoryList: inventoryList,
    });
  },

  /**
   * 添加到移库购物车
   */
  addToTransferCart(e) {
    const { inventoryIndex } = e.currentTarget.dataset;
    const item = this.data.inventoryList[inventoryIndex];

    if (this.data.targetWarehouses.length === 0) {
      wx.showToast({
        title: "请先选择目标仓库",
        icon: "none",
      });
      return;
    }

    // 对于单货物包裹，检查是否有选择数量
    if (item.contents.length === 1) {
      if (!item.selectedQuantity || item.selectedQuantity <= 0) {
        wx.showToast({
          title: "请选择移库数量",
          icon: "none",
        });
        return;
      }
    }

    // 保存待处理的商品信息
    this.setData({
      pendingTransferItem: { item, inventoryIndex },
    });

    // 如果只有一个目标仓库，直接添加
    if (this.data.targetWarehouses.length === 1) {
      this.processPendingTransfer(this.data.targetWarehouses[0].warehouse_id);
    } else {
      // 多个目标仓库时，显示选择弹窗
      this.setData({
        showTargetWarehouseModal: true,
      });
    }
  },

  /**
   * 选择目标仓库进行移库
   */
  selectTargetForTransfer(e) {
    const { warehouseId } = e.currentTarget.dataset;

    // 检查是否为一键移库模式
    if (this.data.isBatchTransferMode) {
      // 一键移库模式：直接执行一键移库
      this.performBatchTransferToWarehouse(warehouseId);
    } else {
      // 普通移库模式：处理单个商品移库
      this.processPendingTransfer(warehouseId);
    }

    this.setData({
      showTargetWarehouseModal: false,
      isBatchTransferMode: false,
    });
  },

  /**
   * 执行一键移库到指定仓库
   */
  performBatchTransferToWarehouse(warehouseId) {
    // 检查是否与移出仓库相同
    if (this.data.fromWarehouse && warehouseId === this.data.fromWarehouse.warehouse_id) {
      wx.showToast({
        title: "移出和移入仓库不能相同",
        icon: "none",
      });
      return;
    }

    // 找到选择的仓库信息
    const selectedWarehouse = this.data.targetWarehouses.find(w => w.warehouse_id === warehouseId);
    if (!selectedWarehouse) {
      wx.showToast({
        title: "仓库信息错误",
        icon: "none",
      });
      return;
    }

    // 检查是否已经在目标仓库列表中，如果没有则添加
    const isAlreadySelected = this.data.targetWarehouses.some(w => w.warehouse_id === warehouseId);

    if (!isAlreadySelected) {
      const newTargetWarehouses = [...this.data.targetWarehouses, selectedWarehouse];

      this.setData({
        targetWarehouses: newTargetWarehouses,
      });

      // 初始化该仓库的购物车
      const newTransferCart = { ...this.data.transferCart };
      if (!newTransferCart[warehouseId]) {
        newTransferCart[warehouseId] = [];
      }

      this.setData({
        transferCart: newTransferCart,
      });

      this.updateCartData();
      this.updateWarehouseListSelection();
    }

    // 确认一键移库
    wx.showModal({
      title: "确认一键移库",
      content: `确定要将当前显示的所有库存（${this.data.totalPackages}包）添加到"${selectedWarehouse.name}"的移库清单吗？`,
      success: (res) => {
        if (res.confirm) {
          this.performBatchTransfer(warehouseId);
        }
      },
    });
  },

  /**
   * 处理待移库商品
   */
  processPendingTransfer(targetWarehouseId) {
    const { item, inventoryIndex } = this.data.pendingTransferItem;

    // 对于单货物包裹，检查是否有选择数量
    if (item.contents.length === 1) {
      // 根据选择的数量，从包裹中选择对应的包裹进行移库
      this.selectPackagesForTransfer(item, inventoryIndex, targetWarehouseId);
    } else {
      // 对于多货物包裹，直接移库整个包裹
      this.addMixedPackageToTransfer(item, inventoryIndex, targetWarehouseId);
    }

    // 清空待处理商品信息
    this.setData({
      pendingTransferItem: null,
    });
  },

  /**
   * 关闭目标仓库选择弹窗
   */
  closeTargetWarehouseModal() {
    this.setData({
      showTargetWarehouseModal: false,
      pendingTransferItem: null,
      isBatchTransferMode: false,
    });
  },

  /**
   * 选择包裹进行移库
   */
  selectPackagesForTransfer(item, inventoryIndex, targetWarehouseId) {
    const selectedQuantity = item.selectedQuantity;
    const availablePackages = item.package_codes || [];

    if (availablePackages.length === 0) {
      wx.showToast({
        title: "没有可用的包裹",
        icon: "none",
      });
      return;
    }

    // 使用 remaining_percentage 优化包裹选择：优先选择部分出库的包裹
    const packagesToTransfer = [];
    let remainingQuantity = selectedQuantity;

    // 如果有包裹状态信息，按 remaining_percentage 排序
    const sortedPackages = availablePackages.map(packageCode => ({
      packageCode,
      // 假设从后端获取的数据中包含每个包裹的状态信息
      remaining_percentage: item.remaining_percentage || 1,
      is_partial: item.is_partial || false
    })).sort((a, b) => {
      // 优先选择部分出库的包裹（remaining_percentage < 1）
      if (a.is_partial && !b.is_partial) return -1;
      if (!a.is_partial && b.is_partial) return 1;
      // 在同类包裹中，优先选择剩余量少的包裹
      return a.remaining_percentage - b.remaining_percentage;
    });

    for (const packageInfo of sortedPackages) {
      if (remainingQuantity <= 0) break;

      const packageCode = packageInfo.packageCode;

      // 检查包裹是否已在任何目标仓库的购物车中
      let isAlreadyInCart = false;
      for (const warehouseId in this.data.transferCart) {
        const cartItems = this.data.transferCart[warehouseId] || [];
        if (cartItems.some(cartItem => cartItem.package_code === packageCode)) {
          isAlreadyInCart = true;
          break;
        }
      }

      if (isAlreadyInCart) {
        continue; // 跳过已在购物车中的包裹
      }

      // 计算当前包裹的移库数量
      const packageQuantity = Math.min(remainingQuantity, packageInfo.remaining_percentage || 1);
      packagesToTransfer.push({
        packageCode,
        quantity: packageQuantity,
        remaining_percentage: packageInfo.remaining_percentage,
      });

      remainingQuantity -= packageQuantity;
    }

    if (packagesToTransfer.length === 0) {
      wx.showToast({
        title: "所选包裹已在移库清单中",
        icon: "none",
      });
      return;
    }

    // 添加选中的包裹到指定目标仓库的购物车
    const newCartItems = packagesToTransfer.map((pkg) => ({
      cart_key: pkg.packageCode,
      package_code: pkg.packageCode,
      sku: item.contents[0]?.sku || "",
      product_name: item.contents[0]?.name || "未知商品",
      quantity: item.contents[0]?.current_quantity || 0,
      package_count: pkg.quantity, // 实际的包裹数量（可以是小数）
      location_code: item.locations?.[0] || "",
      inbound_at: item.inbound_dates?.[0] || new Date(),
      target_warehouse_id: targetWarehouseId, // 添加目标仓库ID
    }));

    // 更新指定目标仓库的购物车
    const newTransferCart = { ...this.data.transferCart };
    if (!newTransferCart[targetWarehouseId]) {
      newTransferCart[targetWarehouseId] = [];
    }
    newTransferCart[targetWarehouseId] = [...newTransferCart[targetWarehouseId], ...newCartItems];

    this.setData({
      transferCart: newTransferCart,
    });

    this.updateCartData();

    // 更新库存列表中的可用包裹数量
    const inventoryList = this.data.inventoryList;
    inventoryList[inventoryIndex].selectedQuantity = 0;

    // 从可用包裹中移除已添加到购物车的包裹
    const transferredPackageCodes = packagesToTransfer.map(
      (pkg) => pkg.packageCode
    );
    const remainingPackages = inventoryList[
      inventoryIndex
    ].package_codes.filter(
      (packageCode) => !transferredPackageCodes.includes(packageCode)
    );
    inventoryList[inventoryIndex].package_codes = remainingPackages;
    inventoryList[inventoryIndex].package_count = remainingPackages.length;

    // 更新 effective_package_count：减去已移库的包数
    const totalTransferredPackages = packagesToTransfer.reduce(
      (sum, pkg) => sum + pkg.quantity,
      0
    );
    const currentEffectiveCount = inventoryList[inventoryIndex].effective_package_count || inventoryList[inventoryIndex].package_count || 0;
    inventoryList[inventoryIndex].effective_package_count = Math.max(0, currentEffectiveCount - totalTransferredPackages);

    this.setData({
      inventoryList: inventoryList,
    });

    // 计算实际添加的包裹数量（考虑小数）
    const totalAddedPackages = packagesToTransfer.reduce(
      (sum, pkg) => sum + pkg.quantity,
      0
    );
    const displayText =
      totalAddedPackages % 1 === 0
        ? `已添加${totalAddedPackages}包`
        : `已添加${totalAddedPackages.toFixed(2)}包`;

    wx.showToast({
      title: displayText,
      icon: "success",
      duration: 1000,
    });
    console.log("groupedTransferCart621",this.data.groupedTransferCart)
    console.log("transferCart111",this.data.transferCart)
  },

  /**
   * 添加多货物包裹到移库清单
   */
  addMixedPackageToTransfer(item, inventoryIndex, targetWarehouseId) {
    const availablePackages = item.package_codes || [];
    const effectivePackageCount = item.effective_package_count || item.package_count || 0;

    if (availablePackages.length === 0 || effectivePackageCount <= 0) {
      wx.showToast({
        title: "没有可用的包裹",
        icon: "none",
      });
      return;
    }

    // 检查哪些包裹还没有在任何目标仓库的购物车中
    const packagesToTransfer = [];

    for (let i = 0; i < availablePackages.length; i++) {
      const packageCode = availablePackages[i];

      // 检查包裹是否已在任何目标仓库的购物车中
      let isAlreadyInCart = false;
      for (const warehouseId in this.data.transferCart) {
        const cartItems = this.data.transferCart[warehouseId] || [];
        if (cartItems.some(cartItem => cartItem.package_code === packageCode)) {
          isAlreadyInCart = true;
          break;
        }
      }

      if (isAlreadyInCart) {
        continue; // 跳过已在购物车中的包裹
      }

      packagesToTransfer.push(packageCode);
    }

    if (packagesToTransfer.length === 0) {
      wx.showToast({
        title: "所有包裹已在移库清单中",
        icon: "none",
      });
      return;
    }

    // 计算每个包裹的实际包数（考虑 remaining_percentage）
    const totalPackageCodes = packagesToTransfer.length;
    const packageCountPerCode = totalPackageCodes > 0 ? effectivePackageCount / totalPackageCodes : effectivePackageCount;

    // 添加选中的包裹到指定目标仓库的购物车
    const newCartItems = packagesToTransfer.map((packageCode) => ({
      cart_key: packageCode,
      package_code: packageCode,
      sku: item.contents[0]?.sku || "",
      product_name: item.contents.map((c) => c.name).join(" + "),
      quantity: item.contents.reduce(
        (sum, c) => sum + (c.current_quantity || 0),
        0
      ),
      package_count: packageCountPerCode, // 使用计算出的实际包数
      remaining_percentage: item.remaining_percentage || 1, // 传递 remaining_percentage
      location_code: item.locations?.[0] || "",
      inbound_at: item.inbound_dates?.[0] || new Date(),
      target_warehouse_id: targetWarehouseId, // 添加目标仓库ID
    }));

    // 更新指定目标仓库的购物车
    const newTransferCart = { ...this.data.transferCart };
    if (!newTransferCart[targetWarehouseId]) {
      newTransferCart[targetWarehouseId] = [];
    }
    newTransferCart[targetWarehouseId] = [...newTransferCart[targetWarehouseId], ...newCartItems];

    this.setData({
      transferCart: newTransferCart,
    });

    this.updateCartData();

    // 更新库存列表中的可用包裹数量
    const inventoryList = this.data.inventoryList;

    // 从可用包裹中移除已添加到购物车的包裹
    const remainingPackages = inventoryList[inventoryIndex].package_codes.filter(
      (packageCode) => !packagesToTransfer.includes(packageCode)
    );
    inventoryList[inventoryIndex].package_codes = remainingPackages;
    inventoryList[inventoryIndex].package_count = remainingPackages.length;

    // 更新 effective_package_count：减去已移库的包数
    inventoryList[inventoryIndex].effective_package_count = Math.max(0, effectivePackageCount - (packageCountPerCode * packagesToTransfer.length));

    this.setData({
      inventoryList: inventoryList,
    });

    // 显示实际添加的包裹数量
    const totalAddedPackages = packageCountPerCode * packagesToTransfer.length;
    const displayText = totalAddedPackages % 1 === 0
      ? `已添加${totalAddedPackages}包`
      : `已添加${totalAddedPackages.toFixed(2)}包`;

    wx.showToast({
      title: displayText,
      icon: "success",
      duration: 1000,
    });
  },

  /**
   * 更新购物车数据 - 支持多仓库结构
   */
  updateCartData() {
    let cartTotalPackages = 0;
    let cartTotalPieces = 0;
    const allCartItems = [];

    // 遍历所有目标仓库的购物车
    for (const warehouseId in this.data.transferCart) {
      const cartItems = this.data.transferCart[warehouseId] || [];
      allCartItems.push(...cartItems);

      // 计算该仓库的统计数据
      cartItems.forEach(item => {
        const effectivePackageCount = item.remaining_percentage
          ? parseFloat(item.package_count) * item.remaining_percentage
          : parseFloat(item.package_count) || 1;
        cartTotalPackages += effectivePackageCount;
        cartTotalPieces += item.quantity || 0;
      });
    }

    // 四舍五入包裹数
    cartTotalPackages = Math.round(cartTotalPackages * 100) / 100;

    // 生成按仓库和classification_code分组的汇总清单
    const groupedCart = this.groupTransferCartItemsByWarehouse();

    this.setData({
      groupedTransferCart: groupedCart,
      cartTotalPackages,
      cartTotalPieces,
    });
  },

  /**
   * 按仓库和classification_code分组汇总待移库清单项目
   */
  groupTransferCartItemsByWarehouse() {
    const warehouseGroups = [];

    // 遍历每个目标仓库
    for (const warehouseId in this.data.transferCart) {
      const cartItems = this.data.transferCart[warehouseId] || [];
      if (cartItems.length === 0) continue;

      // 找到对应的仓库信息
      const warehouse = this.data.targetWarehouses.find(w => w.warehouse_id === warehouseId);
      if (!warehouse) continue;

      // 按classification_code分组该仓库的商品
      const classificationGroups = {};

      cartItems.forEach(item => {
        // 生成分组键：按SKU分组（classification_code的基础）
        const groupKey = item.sku;

        if (!classificationGroups[groupKey]) {
          classificationGroups[groupKey] = {
            sku: item.sku,
            product_name: item.product_name,
            total_quantity: 0,
            total_packages: 0,
            package_codes: [],
            location_code: item.location_code,
            inbound_at: item.inbound_at,
          };
        }

        // 累计数据
        classificationGroups[groupKey].total_quantity += item.quantity || 0;
        classificationGroups[groupKey].total_packages += item.package_count || 1;
        classificationGroups[groupKey].package_codes.push(item.package_code);
      });

      // 转换为数组并排序
      const sortedClassifications = Object.values(classificationGroups)
        .sort((a, b) => a.product_name.localeCompare(b.product_name))
        .map(group => ({
          ...group,
          total_packages_display: group.total_packages % 1 === 0
            ? group.total_packages.toString()
            : group.total_packages.toFixed(2),
        }));

      // 计算该仓库的汇总数据
      const warehouseTotalPackages = sortedClassifications.reduce(
        (sum, group) => sum + group.total_packages, 0
      );
      const warehouseTotalPieces = sortedClassifications.reduce(
        (sum, group) => sum + group.total_quantity, 0
      );

      warehouseGroups.push({
        warehouse_id: warehouseId,
        warehouse_name: warehouse.name,
        warehouse_address: warehouse.address,
        classifications: sortedClassifications,
        total_packages: warehouseTotalPackages,
        total_packages_display: warehouseTotalPackages % 1 === 0
          ? warehouseTotalPackages.toString()
          : warehouseTotalPackages.toFixed(2),
        total_pieces: warehouseTotalPieces,
      });
    }

    return warehouseGroups.sort((a, b) => a.warehouse_name.localeCompare(b.warehouse_name));
  },

  /**
   * 从购物车中移除整组商品（按仓库和SKU分组）
   */
  removeGroupFromCart(e) {
    const { warehouseId, sku } = e.currentTarget.dataset;

    wx.showModal({
      title: "确认移除",
      content: "确定要从移库清单中移除该商品吗？",
      success: (res) => {
        if (res.confirm) {
          const newTransferCart = { ...this.data.transferCart };

          if (newTransferCart[warehouseId]) {
            // 从指定仓库的购物车中移除该SKU的所有商品
            newTransferCart[warehouseId] = newTransferCart[warehouseId].filter(
              (item) => item.sku !== sku
            );

            // 如果该仓库的购物车为空，可以选择保留空数组或删除该键
            if (newTransferCart[warehouseId].length === 0) {
              // 保留空数组，便于后续添加商品
              // delete newTransferCart[warehouseId];
            }
          }

          this.setData({
            transferCart: newTransferCart,
          });

          this.updateCartData();

          // 重新加载库存数据以确保数据一致性
          this.loadInventory();

          // 检查是否还有商品在购物车中
          const hasItems = Object.values(newTransferCart).some(items => items.length > 0);
          this.setData({
            showCart: hasItems,
          });
        }
      },
    });
  },

  /**
   * 移除整个仓库的移库清单
   */
  removeWarehouseFromCart(e) {
    const { warehouseId } = e.currentTarget.dataset;
    const warehouse = this.data.targetWarehouses.find(w => w.warehouse_id === warehouseId);

    wx.showModal({
      title: "确认移除",
      content: `确定要移除"${warehouse?.name || '未知仓库'}"的所有移库商品吗？`,
      success: (res) => {
        if (res.confirm) {
          const newTransferCart = { ...this.data.transferCart };
          newTransferCart[warehouseId] = [];

          this.setData({
            transferCart: newTransferCart,
          });

          this.updateCartData();

          // 重新加载库存数据以确保数据一致性
          this.loadInventory();

          // 检查是否还有商品在购物车中
          const hasItems = Object.values(newTransferCart).some(items => items.length > 0);
          this.setData({
            showCart: hasItems,
          });
        }
      },
    });
  },



  /**
   * 切换购物车显示
   */
  toggleCart() {
    this.setData({
      showCart: !this.data.showCart,
    });
  },

  /**
   * 清空购物车
   */
  clearCart() {
    wx.showModal({
      title: "确认清空",
      content: "确定要清空所有移库清单吗？",
      success: (res) => {
        if (res.confirm) {
          // 清空所有仓库的购物车数据
          const emptyTransferCart = {};
          this.data.targetWarehouses.forEach(warehouse => {
            emptyTransferCart[warehouse.warehouse_id] = [];
          });

          this.setData({
            transferCart: emptyTransferCart,
            groupedTransferCart: [],
            cartTotalPackages: 0,
            cartTotalPieces: 0,
            showCart: false,
          });

          // 重新加载库存数据以确保数据一致性
          this.loadInventory();
        }
      },
    });
  },

  /**
   * 根据当前移库清单调整库存显示 - 支持多仓库结构
   */
  adjustInventoryForTransferCart(inventoryList) {
    const { transferCart } = this.data;

    // 检查是否有移库商品
    const hasItems = Object.values(transferCart).some(items => items && items.length > 0);
    if (!hasItems) return inventoryList;

    // 按SKU分组移库清单中的包裹，记录包裹数量信息
    const cartPackagesBySku = {};

    // 遍历所有目标仓库的购物车
    for (const warehouseId in transferCart) {
      const cartItems = transferCart[warehouseId] || [];
      cartItems.forEach((cartItem) => {
        const sku = cartItem.sku;
        if (!cartPackagesBySku[sku]) {
          cartPackagesBySku[sku] = {
            packageCodes: [],
            totalTransferredPackages: 0
          };
        }
        cartPackagesBySku[sku].packageCodes.push(cartItem.package_code);
        // 累计已移库的包数（考虑 remaining_percentage）
        const effectivePackageCount = cartItem.remaining_percentage
          ? cartItem.package_count * cartItem.remaining_percentage
          : cartItem.package_count || 1;
        cartPackagesBySku[sku].totalTransferredPackages += effectivePackageCount;
      });
    }

    // 调整库存明细，移除已在移库清单中的包裹并更新包数
    return inventoryList.map((item) => {
      const sku = item.contents[0]?.sku;
      if (sku && cartPackagesBySku[sku]) {
        // 从库存明细中移除已在移库清单中的包裹
        const remainingPackages = (item.package_codes || []).filter(
          (packageCode) => !cartPackagesBySku[sku].packageCodes.includes(packageCode)
        );

        // 使用 remaining_percentage 计算剩余包数
        const originalEffectiveCount = item.effective_package_count || item.package_count || 0;
        const transferredCount = cartPackagesBySku[sku].totalTransferredPackages;
        const remainingEffectiveCount = Math.max(0, originalEffectiveCount - transferredCount);

        return {
          ...item,
          package_codes: remainingPackages,
          package_count: remainingPackages.length,
          effective_package_count: remainingEffectiveCount,
        };
      }
      return item;
    });
  },



  /**
   * 一键移库 - 将当前库存列表中的所有包裹添加到移库清单
   */
  onBatchTransfer() {
    if (!this.data.fromWarehouse) {
      wx.showToast({
        title: "请先选择移出仓库",
        icon: "none",
      });
      return;
    }

    if (this.data.inventoryList.length === 0) {
      wx.showToast({
        title: "暂无库存可移库",
        icon: "none",
      });
      return;
    }

    // 如果没有选择目标仓库，或有多个目标仓库，显示仓库选择弹窗
    if (this.data.targetWarehouses.length === 0 || this.data.targetWarehouses.length > 1) {
      this.setData({
        isBatchTransferMode: true,
        showTargetWarehouseModal: true,
      });
      return;
    }

    // 如果只有一个目标仓库，直接确认移库
    wx.showModal({
      title: "确认一键移库",
      content: `确定要将当前显示的所有库存（${this.data.totalPackages}包）添加到移库清单吗？`,
      success: (res) => {
        if (res.confirm) {
          this.performBatchTransfer(this.data.targetWarehouses[0].warehouse_id);
        }
      },
    });
  },

  /**
   * 执行一键移库操作
   */
  performBatchTransfer(targetWarehouseId) {
    let addedPackageCount = 0; // 实际添加的包数（考虑部分包裹）
    const newCartItems = [];

    this.data.inventoryList.forEach((item) => {
      if (!item.package_codes || item.package_codes.length === 0) {
        return;
      }

      // 计算每个包裹的实际包数（考虑 remaining_percentage）
      const effectivePackageCount = item.effective_package_count || item.package_count || 0;
      const totalPackageCodes = item.package_codes.length;
      const packageCountPerCode = totalPackageCodes > 0 ? effectivePackageCount / totalPackageCodes : 1;

      // 检查哪些包裹还没有在任何目标仓库的购物车中
      item.package_codes.forEach((packageCode) => {
        let isAlreadyInCart = false;
        for (const warehouseId in this.data.transferCart) {
          const cartItems = this.data.transferCart[warehouseId] || [];
          if (cartItems.some(cartItem => cartItem.package_code === packageCode)) {
            isAlreadyInCart = true;
            break;
          }
        }

        if (isAlreadyInCart) {
          return; // 跳过已在购物车中的包裹
        }

        // 添加到新购物车项目
        const cartItem = {
          cart_key: packageCode,
          package_code: packageCode,
          sku: item.contents[0]?.sku || "",
          product_name:
            item.contents.length === 1
              ? item.contents[0]?.name || "未知商品"
              : item.contents.map((c) => c.name).join(" + "),
          quantity:
            item.contents.length === 1
              ? item.contents[0]?.current_quantity || 0
              : item.contents.reduce(
                  (sum, c) => sum + (c.current_quantity || 0),
                  0
                ),
          package_count: packageCountPerCode, // 使用计算出的实际包数
          remaining_percentage: item.remaining_percentage || 1, // 传递 remaining_percentage
          location_code: item.locations?.[0] || "",
          inbound_at: item.inbound_dates?.[0] || new Date(),
          target_warehouse_id: targetWarehouseId, // 添加目标仓库ID
        };

        newCartItems.push(cartItem);
        addedPackageCount += packageCountPerCode; // 累计实际包数
      });
    });

    if (newCartItems.length === 0) {
      wx.showToast({
        title: "所有包裹已在移库清单中",
        icon: "none",
      });
      return;
    }

    // 更新指定目标仓库的购物车
    const newTransferCart = { ...this.data.transferCart };
    if (!newTransferCart[targetWarehouseId]) {
      newTransferCart[targetWarehouseId] = [];
    }
    newTransferCart[targetWarehouseId] = [...newTransferCart[targetWarehouseId], ...newCartItems];

    this.setData({
      transferCart: newTransferCart,
    });

    this.updateCartData();

    // 更新库存列表，将已添加的包裹从库存中移除
    const updatedInventoryList = this.data.inventoryList.map((item) => {
      if (!item.package_codes || item.package_codes.length === 0) {
        return item;
      }

      // 找到这个库存项目中被添加到购物车的包裹
      const addedPackageCodes = newCartItems
        .filter(cartItem => cartItem.sku === (item.contents[0]?.sku || ""))
        .map(cartItem => cartItem.package_code);

      if (addedPackageCodes.length > 0) {
        // 从可用包裹中移除已添加的包裹
        const remainingPackages = item.package_codes.filter(
          packageCode => !addedPackageCodes.includes(packageCode)
        );

        // 计算移除的包数
        const effectivePackageCount = item.effective_package_count || item.package_count || 0;
        const totalPackageCodes = item.package_codes.length;
        const packageCountPerCode = totalPackageCodes > 0 ? effectivePackageCount / totalPackageCodes : 1;
        const removedPackageCount = addedPackageCodes.length * packageCountPerCode;

        return {
          ...item,
          package_codes: remainingPackages,
          package_count: remainingPackages.length,
          effective_package_count: Math.max(0, effectivePackageCount - removedPackageCount),
        };
      }

      return item;
    });

    this.setData({
      inventoryList: updatedInventoryList,
    });

    // 显示实际添加的包数
    const displayPackageCount = addedPackageCount % 1 === 0
      ? addedPackageCount.toString()
      : addedPackageCount.toFixed(2);

    wx.showToast({
      title: `已添加${displayPackageCount}包到移库清单`,
      icon: "success",
      duration: 1500,
    });
  },

  /**
   * 确认移库 - 跳转到移库确认页面
   */
  confirmTransfer() {
    const { transferCart, fromWarehouse, targetWarehouses } = this.data;

    // 检查是否有移库商品
    const hasItems = Object.values(transferCart).some(items => items.length > 0);
    if (!hasItems) {
      wx.showToast({
        title: "请先添加移库商品",
        icon: "none",
      });
      return;
    }

    if (targetWarehouses.length === 0) {
      wx.showToast({
        title: "请选择目标仓库",
        icon: "none",
      });
      return;
    }

    // 构建多仓库移库数据
    const transferData = {
      from_warehouse: fromWarehouse,
      target_warehouses: targetWarehouses,
      transfer_cart: transferCart,
      grouped_transfer_cart: this.data.groupedTransferCart,
      total_packages: this.data.cartTotalPackages,
      total_pieces: this.data.cartTotalPieces,
    };

    wx.navigateTo({
      url: `/pages/transfer-confirm/transfer-confirm?transferData=${encodeURIComponent(
        JSON.stringify(transferData)
      )}`,
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.setData({
      page: 1,
      hasMore: true,
    });
    this.loadInventory();
    wx.stopPullDownRefresh();
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadInventory(true);
    }
  },

  /**
   * 点击服装名称显示服装卡片
   */
  async onClothingNameTap(e) {
    const content = e.currentTarget.dataset.content;
    console.log("点击服装名称:", content);

    if (!content || !content.sku) {
      wx.showToast({
        title: "服装信息不完整",
        icon: "none",
      });
      return;
    }

    try {
      let detailInfo;
      // 根据SKU规则判断服装类型：如果SKU包含"_"且前面是"oem"，则为OEM服装
      const isOemClothing = content.sku && content.sku.includes("_") && content.sku.split("_")[0].toLowerCase() === "oem";

      if (isOemClothing) {
        // 获取OEM服装详细信息
        const oemClothingId = content.sku.split("_")[1];
        const response = await Api.getOemClothingInfo({
          oem_clothing_id: oemClothingId,
        });
        console.log("OEM服装API响应:", response);
        if (response.data) {
          detailInfo = response.data;
        }
      } else {
        // 获取普通服装详细信息
        const clothingId = content.clothing_id || content.sku;
        const response = await Api.getClothingInfo({
          clothing_id: clothingId,
        });
        console.log("普通服装API响应:", response);
        if (response.data) {
          detailInfo = response.data;
        }
      }

      if (detailInfo) {
        this.setData({
          selectedClothingInfo: detailInfo,
          isOemClothing: isOemClothing,
          showClothingInfo: true,
        });
      } else {
        wx.showToast({
          title: "获取服装信息失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("获取服装信息失败:", error);
      wx.showToast({
        title: "获取服装信息失败",
        icon: "none",
      });
    }
  },

  /**
   * 关闭服装信息弹窗
   */
  onCloseClothingInfo() {
    this.setData({
      showClothingInfo: false,
      selectedClothingInfo: null,
      isOemClothing: false,
    });
  },
});
