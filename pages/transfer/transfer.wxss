/* pages/transfer/transfer.wxss */

.transfer-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.main-content {
  padding: 20rpx;
}

/* 仓库选择器区域 */
.warehouse-selection-section {
  margin-bottom: 20rpx;
}

.warehouse-cards {
  display: flex;
  gap: 20rpx;
}

/* 自定义仓库选择器 */
.custom-warehouse-selector {
  flex: 1;
  position: relative;
  z-index: 10;
}

.selector-trigger {
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.selector-trigger.active {
  border-color: #1989fa;
  box-shadow: 0 4rpx 12rpx rgba(25, 137, 250, 0.2);
}

.custom-warehouse-selector.disabled .selector-trigger {
  background: #f5f5f5;
  border-color: #e0e0e0;
  cursor: not-allowed;
}

.custom-warehouse-selector.disabled .selector-text {
  color: #999;
}

.custom-warehouse-selector.disabled .selector-arrow {
  color: #ccc;
}

.selector-content {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.selector-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.selector-text {
  font-size: 28rpx;
  color: #1989fa;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.selector-arrow {
  font-size: 24rpx;
  color: #999;
  transition: transform 0.3s ease;
}

/* 下拉选项容器 */
.dropdown-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  margin-top: 8rpx; /* 与选择器留间隙 */
}

.dropdown-content {
  background: white;
  border-radius: 16rpx; /* 圆角 */
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid #e9ecef;
  overflow: hidden;
  max-height: 400rpx;
  overflow-y: auto;
}

/* 下拉选项 */
.dropdown-option {
  display: flex;
  flex-direction :row;
  justify-content:space-between;
  align-items:center;
  padding: 16rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
  background: white;
}

.dropdown-option:last-child {
  border-bottom: none;
}

.dropdown-option:hover {
  background: #f0f9ff;
}

.dropdown-option.selected {
  background: #f0f9ff;
  border-left: 4rpx solid #1989fa;
  color: #1989fa;
}

.dropdown-option {
  position: relative;
}

.option-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.option-info {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.selected-indicator {
  flex-shrink: 0;
  margin-left: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.option-name {
  font-size: 28rpx;
  font-weight: 600;
  color: inherit;

}

.option-address {
  font-size: 24rpx;
  color: #666;
}

/* 库存明细区域 */
.inventory-section {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #eee;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-stats {
  font-size: 24rpx;
  color: #666;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 20rpx;
}

.search-input-wrapper {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.search-input {
  flex: 1;
  height: 72rpx;
  padding: 0 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #f8f8f8;
}

.search-btn {
  height: 72rpx !important;
  padding: 0 24rpx !important;
}

.reset-btn {
  height: 72rpx !important;
  padding: 0 20rpx !important;
  border: 1rpx solid #ddd !important;
  color: #666 !important;
  background: white !important;
}

.batch-transfer-btn {
  height: 72rpx !important;
  padding: 0 20rpx !important;
  font-weight: 600 !important;
}

/* 加载状态 */
.loading-state {
  padding: 80rpx 20rpx;
  text-align: center;
}

/* 库存列表 - 参照出库页面样式 */
.details-list {
  max-height: calc(100vh - 500rpx); /* 为底部按钮留出更多空间 */
  min-height: 300rpx; /* 最小高度 */
  overflow-y: auto;
  margin-bottom: 20rpx; /* 与底部按钮区域留间隙 */
}

.details-list::-webkit-scrollbar {
  display: none;
}

.detail-item {
  background: white;
  border-radius: 10rpx;
  padding: 8rpx;
  margin-bottom: 8rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.item-content {
  margin-bottom: 0;
}

/* 服装信息布局 */
.item-main-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6rpx;
  padding: 8rpx 12rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  gap: 12rpx;
}

.clothing-info {
  flex: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0;
  gap: 4rpx;
}

.clothing-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.clothing-name.clickable {
  color: #1989fa;
  cursor: pointer;
  transition: color 0.3s;
}

.clothing-name.clickable:active {
  color: #0c7cd5;
}

.stock-info {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 80rpx;
}

.stock-formula {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
}

/* 操作区域 */
.item-actions-row {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-top: 6rpx;
  padding: 8rpx 12rpx;
  background: #f0f9ff;
  border-radius: 8rpx;
  border: 1rpx solid #e0f2fe;
  min-height: 80rpx; /* 固定最小高度，防止按钮出现时位置移动 */
}

/* 固定位置的数量显示和控制组 */
.stock-quantity-group {
  display: flex;
  align-items: center;
  gap: 10rpx;
  flex: 1;
  min-width: 0;
}

.stock-display {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  align-items: center;
  flex-shrink: 0;
  min-width: 80rpx;
}

.stock-value {
  font-size: 30rpx;
  font-weight: 600;
  color: #ff9800;
  text-align: center;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

/* 部分包裹标识 */
.partial-tag {
  font-size: 20rpx;
  color: #ff6b35;
  background: #fff3e0;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  border: 1rpx solid #ffcc80;
  font-weight: 500;
}

.quantity-control {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-width: 200rpx; /* 为步进器预留固定空间 */
}

/* 移库按钮区域 */
.transfer-button-area {
  flex-shrink: 0;
  width: 120rpx; /* 固定宽度，保持布局稳定 */
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 移库按钮固定宽度 */
.transfer-button-area .van-button {
  width: 120rpx !important;
  height: 60rpx !important;
  font-size: 24rpx !important;
  flex-shrink: 0 !important; /* 不缩小 */
}

.item-tips {
  text-align: center;
  padding: 16rpx;
}

.tip-view {
  font-size: 24rpx;
  color: #999;
}

.empty-tip {
  font-size: 24rpx;
  color: #999;
  font-weight: 600;
}

/* 多货物包裹卡片样式 */
.multi-item-list {
  margin-bottom: 10rpx;
}

.multi-item-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0rpx 12rpx;
  margin-bottom: 6rpx;
  background: white;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
}

.multi-item-row:last-child {
  margin-bottom: 0;
}

.multi-item-info {
  flex: 2;
  display: flex;
  align-items: center;
  gap: 12rpx;
  min-width: 0;
}

.multi-item-quantity {
  flex: 0 0 auto;
  font-size: 22rpx;
  color: #666;
  white-space: nowrap;
}

.multi-item-footer {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 8rpx 12rpx;
  background: #f0f9ff;
  border-radius: 8rpx;
  border: 1rpx solid #e0f2fe;
  margin-top: 6rpx;
  min-height: 80rpx; /* 与单货物包裹保持一致的高度 */
}

.package-calculation {
  flex: 1;
  min-width: 0;
}

.calculation-display {
  font-size: 26rpx;
  font-weight: 600;
  color: #ff9800;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

/* 空状态 */
.empty-state, .empty-prompt {
  padding: 80rpx 20rpx;
  text-align: center;
}

/* 底部固定操作栏 */
.bottom-fixed-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.bottom-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
  align-items: center;
}

.action-item {
  flex: 1;
  max-width: 300rpx;
}

.action-button {
  width: 100% !important;
  height: 88rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8rpx !important;
  border-radius: 12rpx !important;
  font-size: 28rpx !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

/* 核对按钮特殊样式 */
.action-button[type="success"] {
  background: linear-gradient(135deg, #07c160 0%, #05a050 100%) !important;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3) !important;
  border: none !important;
  color: white !important;
}

.action-button[type="success"]:active {
  transform: translateY(2rpx) !important;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.4) !important;
}

/* 购物车浮窗样式 */
.cart-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  padding: 24rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.total-info {
  font-size: 24rpx;
  color: #666;
}

.cart-content {
  flex: 1;
  overflow-y: auto;
  padding: 16rpx;
}

/* 仓库分组样式 */
.warehouse-group {
  margin-bottom: 24rpx;
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.warehouse-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #1989fa;
  color: white;
}

.warehouse-group-info {
  flex: 1;
  min-width: 0;
}

.warehouse-group-name {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.warehouse-group-stats {
  font-size: 24rpx;
  opacity: 0.9;
}

.remove-warehouse-icon {
  font-size: 32rpx;
  color: white;
  padding: 8rpx;
}

.warehouse-items {
  padding: 16rpx;
}

.cart-item {
  margin-bottom: 12rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  overflow: hidden;
}

.cart-item:last-child {
  margin-bottom: 0;
}

.cart-item-content {
  padding: 16rpx;
}

.cart-main-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cart-clothing-info {
  flex: 1;
  min-width: 0;
}

.cart-clothing-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.cart-clothing-details {
  font-size: 24rpx;
  color: #666;
}

.cart-item-actions {
  flex-shrink: 0;
  padding: 8rpx;
}

.remove-icon {
  font-size: 32rpx;
  color: #ff4757;
}

.empty-cart {
  padding: 80rpx 20rpx;
  text-align: center;
}

/* 目标仓库选择弹窗 */
.target-warehouse-modal {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #eee;
  background: #f8f9fa;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  font-size: 32rpx;
  color: #999;
  padding: 8rpx;
}

.modal-content {
  max-height: 400rpx;
  overflow-y: auto;
}

.warehouse-options {
  padding: 16rpx;
}

.warehouse-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 12rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.warehouse-option:last-child {
  margin-bottom: 0;
}

.warehouse-option:active {
  background: #e3f2fd;
  border-color: #1989fa;
}

.option-info {
  flex: 1;
  min-width: 0;
}

.option-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.option-address {
  font-size: 24rpx;
  color: #666;
}

.option-arrow {
  font-size: 24rpx;
  color: #1989fa;
}





