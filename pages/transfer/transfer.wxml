<!-- pages/transfer/transfer.wxml -->
<view class="transfer-container" bind:tap="closeAllDropdowns">
  <!-- 主要内容 -->
  <view class="main-content">
    <!-- 顶部仓库选择卡片 -->
    <view class="warehouse-selection-section">
      <view class="warehouse-cards">
        <!-- 移出仓库选择器 -->
        <view class="custom-warehouse-selector" catchtap="stopPropagation">
          <view class="selector-trigger {{showFromDropdown ? 'active' : ''}}" bind:tap="toggleFromDropdown">
            <view class="selector-content">
              <view class="selector-label">移出：</view>
              <view class="selector-text">{{fromWarehouse ? fromWarehouse.name : '请选择仓库'}}</view>
            </view>
            <van-icon name="{{showFromDropdown ? 'arrow-up' : 'arrow-down'}}" class="selector-arrow" />
          </view>
          <!-- 下拉选项 -->
          <view wx:if="{{showFromDropdown}}" class="dropdown-options">
            <view class="dropdown-content">
              <view wx:for="{{warehouseList}}" wx:key="warehouse_id" class="dropdown-option {{fromWarehouse && fromWarehouse.warehouse_id === item.warehouse_id ? 'selected' : ''}}" data-warehouse="{{item}}" bind:tap="selectFromWarehouse">
                <view class="option-name">{{item.name}}</view>
                <view class="option-address">{{item.address}}</view>
              </view>
            </view>
          </view>
        </view>
        <!-- 移入仓库选择器 -->
        <view class="custom-warehouse-selector {{!fromWarehouse ? 'disabled' : ''}}" catchtap="stopPropagation">
          <view class="selector-trigger {{showToDropdown ? 'active' : ''}}" bind:tap="{{fromWarehouse ? 'toggleToDropdown' : ''}}">
            <view class="selector-content">
              <view class="selector-label">移入：</view>
              <view class="selector-text">
                {{!fromWarehouse ? '先选移出仓库' : (targetWarehouses.length > 0 ? targetWarehouses.length + '个仓库' : '请选择仓库')}}
              </view>
            </view>
            <van-icon name="{{showToDropdown ? 'arrow-up' : 'arrow-down'}}" class="selector-arrow" />
          </view>
          <!-- 下拉选项 -->
          <view wx:if="{{showToDropdown}}" class="dropdown-options">
            <view class="dropdown-content">
              <view wx:for="{{warehouseList}}" wx:key="warehouse_id" class="dropdown-option {{item.isSelected ? 'selected' : ''}}" data-warehouse="{{item}}" bind:tap="selectToWarehouse">
                <view class="option-content">
                  <view class="option-info">
                    <view class="option-name">{{item.name}}</view>
                    <view class="option-address">{{item.address}}</view>
                  </view>
                  <!-- 选中状态指示器 -->
                  <view wx:if="{{item.isSelected}}" class="selected-indicator">
                    <van-icon name="success" color="#1989fa" size="16px" />
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 库存明细列表 -->
    <view wx:if="{{fromWarehouse}}" class="inventory-section">
      <view class="section-header">
        <view class="section-title">库存明细</view>
        <view class="section-stats">
          <view class="stats-view">{{totalPackages || 0}}包 / {{totalPieces || 0}}件</view>
        </view>
      </view>
      <!-- 搜索框 -->
      <view class="search-section">
        <view class="search-input-wrapper">
          <input class="search-input" value="{{searchKeyword}}" placeholder="输入服装名称搜索" bindinput="onSearchInput" bindconfirm="onSearch" />
          <van-button size="small" type="primary" bind:click="onSearch" custom-class="search-btn" loading="{{searchLoading}}" disabled="{{searchLoading}}">
            搜索
          </van-button>
          <van-button size="small" bind:click="onResetSearch" custom-class="reset-btn">
            重置
          </van-button>
          <van-button size="small" type="warning" bind:click="onBatchTransfer" custom-class="batch-transfer-btn" disabled="{{!fromWarehouse || inventoryList.length === 0}}">
            一键移库
          </van-button>
        </view>
      </view>
      <!-- 库存列表 -->
      <view wx:if="{{loading}}" class="loading-state">
        <van-loading type="spinner" color="#1989fa" size="24px" vertical>加载中...</van-loading>
      </view>
      <view wx:elif="{{inventoryList.length === 0}}" class="empty-state">
        <van-empty description="暂无库存数据" />
      </view>
      <view wx:else class="details-list">
        <view wx:for="{{inventoryList}}" wx:for-index="inventoryIndex" wx:key="unique_id" class="detail-item">
          <!-- 单货物包裹卡片 -->
          <view wx:if="{{item.contents.length === 1}}" class="item-content single-item-card">
            <!-- 主要信息横向布局 -->
            <view class="item-main-info">
              <!-- 左侧：服装信息 -->
              <view class="clothing-info">
                <view class="clothing-name clickable" style="{{item.contents[0].clothing_id ? 'color: #07c160' : 'color: #000'}}" data-content="{{item.contents[0]}}" bind:tap="onClothingNameTap">
                  {{item.contents[0].name || '未知服装'}}
                </view>
              </view>
              <!-- 右侧：库存信息 -->
              <view class="stock-info">
                <view class="stock-formula">
                  {{item.total_quantity || 0}}件   ({{item.contents[0].original_quantity || 0}}件/包)
                </view>
              </view>
            </view>
            <!-- 操作区域：库存数量、步进器、移库按钮在同一排 -->
            <view class="item-actions-row" wx:if="{{(item.effective_package_count || item.package_count) > 0 && fromWarehouse && targetWarehouses.length > 0}}">
              <!-- 固定位置的数量显示和控制组 -->
              <view class="stock-quantity-group">
                <view class="stock-display">
                  <view class="stock-value">
                    {{item.effective_package_count || item.package_count}}包
                    <text wx:if="{{item.is_partial}}" class="partial-tag">部分</text>
                  </view>
                </view>
                <view class="quantity-control">
                  <van-stepper value="{{item.selectedQuantity || 0}}" min="0" max="{{item.effective_package_count || item.package_count}}" step="1" data-inventory-index="{{inventoryIndex}}" bind:change="onQuantityChange" />
                </view>
              </view>
              <!-- 移库按钮区域 -->
              <view class="transfer-button-area">
                <van-button wx:if="{{item.selectedQuantity && item.selectedQuantity > 0}}" type="primary" size="small" data-inventory-index="{{inventoryIndex}}" bind:click="addToTransferCart">
                  移库
                </van-button>
              </view>
            </view>
            <!-- 提示信息 -->
            <view class="item-tips" wx:if="{{(item.effective_package_count || item.package_count) > 0 && (!fromWarehouse || targetWarehouses.length === 0)}}">
              <view class="tip-view">请先选择移出和移入仓库</view>
            </view>
            <view class="item-tips" wx:if="{{(item.effective_package_count || item.package_count) <= 0}}">
              <view class="empty-tip">暂无库存</view>
            </view>
          </view>
          <!-- 多货物包裹卡片 -->
          <view wx:else class="item-content">
            <!-- 货物列表 -->
            <view class="multi-item-list">
              <view wx:for="{{item.contents}}" wx:for-item="content" wx:for-index="contentIndex" wx:key="sku" class="multi-item-row">
                <!-- 左侧：服装信息 -->
                <view class="multi-item-info">
                  <view class="clothing-name clickable" style="{{content.clothing_id ? 'color: #07c160' : 'color: #000'}}" data-content="{{content}}" bind:tap="onClothingNameTap">
                    {{content.name}}
                  </view>
                  <view class="multi-item-quantity">{{content.current_quantity}}件</view>
                </view>
              </view>
            </view>
            <!-- 移库操作区域 -->
            <view class="multi-item-footer" wx:if="{{(item.effective_package_count || item.package_count) > 0 && fromWarehouse && targetWarehouses.length > 0}}">
              <!-- 固定位置的包数显示组 -->
              <view class="package-calculation">
                <view class="calculation-display">
                  可移库包数: {{item.effective_package_count || item.package_count || '0'}}包
                  <text wx:if="{{item.is_partial}}" class="partial-tag">部分</text>
                </view>
              </view>
              <!-- 移库按钮区域 -->
              <view class="transfer-button-area">
                <van-button wx:if="{{(item.effective_package_count || item.package_count) && (item.effective_package_count || item.package_count) > 0}}" type="primary" size="small" data-inventory-index="{{inventoryIndex}}" bind:click="addToTransferCart">
                  移库
                </van-button>
              </view>
            </view>
            <!-- 提示信息 -->
            <view class="item-tips" wx:if="{{(item.effective_package_count || item.package_count) > 0 && (!fromWarehouse || targetWarehouses.length === 0)}}">
              <view class="tip-view">请先选择移出和移入仓库</view>
            </view>
            <view class="item-tips" wx:if="{{(item.effective_package_count || item.package_count) <= 0}}">
              <view class="empty-tip">暂无库存</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 空状态提示 -->
    <view wx:if="{{!fromWarehouse}}" class="empty-prompt">
      <van-empty description="请先选择移出仓库" />
    </view>
  </view>
  <!-- 底部固定操作栏 -->
  <view class="bottom-fixed-bar">
    <view class="bottom-actions">
      <!-- 待移库清单按钮 -->
      <view class="action-item">
        <van-button type="info" size="large" bind:click="toggleCart" class="action-button" disabled="{{cartTotalPackages === 0}}">
          <van-icon name="shopping-cart-o" />
          待移库 ({{cartTotalPackages || 0}}包)
        </van-button>
      </view>
      <!-- 核对按钮 -->
      <view class="action-item">
        <van-button type="success" size="large" bind:click="confirmTransfer" class="action-button" disabled="{{targetWarehouses.length === 0 || cartTotalPackages === 0}}">
          <van-icon name="passed" />
          核对
        </van-button>
      </view>
    </view>
  </view>
  <!-- 待移库清单浮窗 -->
  <van-popup show="{{showCart}}" position="bottom" bind:close="toggleCart" custom-style="height: 70vh;">
    <view class="cart-popup">
      <view class="popup-header">
        <view class="popup-title">待移库清单</view>
        <view class="header-actions">
          <view class="total-info">总计：{{cartTotalPackages}}包 / {{cartTotalPieces}}件</view>
          <van-button type="danger" size="small" bind:click="clearCart">清空</van-button>
        </view>
      </view>
      <view class="cart-content">
        <view wx:if="{{groupedTransferCart.length === 0}}" class="empty-cart">
          <van-empty description="待移库清单为空" />
        </view>
        <view wx:else>
          <!-- 按仓库分组显示 -->
          <view wx:for="{{groupedTransferCart}}" wx:for-item="warehouseGroup" wx:key="warehouse_id" class="warehouse-group">
            <!-- 仓库标题 -->
            <view class="warehouse-group-header">
              <view class="warehouse-group-info">
                <view class="warehouse-group-name">{{warehouseGroup.warehouse_name}}</view>
                <view class="warehouse-group-stats">
                  {{warehouseGroup.total_packages_display}}包 / {{warehouseGroup.total_pieces}}件
                </view>
              </view>
              <van-icon name="delete-o" class="remove-warehouse-icon" data-warehouse-id="{{warehouseGroup.warehouse_id}}" bind:click="removeWarehouseFromCart" />
            </view>
            <!-- 该仓库的商品列表 -->
            <view class="warehouse-items">
              <view wx:for="{{warehouseGroup.classifications}}" wx:for-item="classification" wx:key="sku" class="cart-item">
                <view class="cart-item-content">
                  <view class="cart-main-info">
                    <!-- 左侧：服装信息 -->
                    <view class="cart-clothing-info">
                      <view class="cart-clothing-name">{{classification.product_name}}</view>
                      <view class="cart-clothing-details">
                        总件数：{{classification.total_quantity}}件 | 总包数：{{classification.total_packages_display}}包
                      </view>
                    </view>
                    <!-- 右侧：删除按钮 -->
                    <view class="cart-item-actions">
                      <van-icon name="close" data-warehouse-id="{{warehouseGroup.warehouse_id}}" data-sku="{{classification.sku}}" bind:click="removeGroupFromCart" class="remove-icon" />
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </van-popup>
  <!-- 目标仓库选择弹窗 -->
  <van-popup show="{{showTargetWarehouseModal}}" position="center" bind:close="closeTargetWarehouseModal" custom-style="width: 80%; max-height: 60vh; border-radius: 16rpx; overflow: hidden;">
    <view class="target-warehouse-modal">
      <view class="modal-header">
        <view class="modal-title">选择目标仓库</view>
        <van-icon name="cross" bind:click="closeTargetWarehouseModal" class="close-icon" />
      </view>
      <view class="modal-content">
        <view class="warehouse-options">
          <view wx:for="{{targetWarehouses}}" wx:key="warehouse_id" class="warehouse-option" data-warehouse-id="{{item.warehouse_id}}" bind:tap="selectTargetForTransfer">
            <view class="option-info">
              <view class="option-name">{{item.name}}</view>
              <view class="option-address">{{item.address}}</view>
            </view>
            <van-icon name="arrow" class="option-arrow" />
          </view>
        </view>
      </view>
    </view>
  </van-popup>
  <!-- 服装信息弹窗 -->
  <van-popup show="{{showClothingInfo}}" position="center" round bind:close="onCloseClothingInfo" custom-style="width: 90%; max-width: 600rpx; border-radius: 16rpx;">
    <clothing-info-card wx:if="{{selectedClothingInfo}}" clothingInfo="{{selectedClothingInfo}}" oemClothingInfo="{{selectedClothingInfo}}" isOem="{{isOemClothing}}" showDetailInfo="{{true}}" />
  </van-popup>
</view>