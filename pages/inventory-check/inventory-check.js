// pages/inventory-check/inventory-check.js
import Api from "../../utils/api.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 仓库相关
    warehouses: [],
    selectedWarehouse: null,
    showWarehouseDropdown: false,

    // 库存明细
    inventoryList: [],
    originalInventoryList: [], // 保存原始库存数据用于搜索
    loading: false,

    // 搜索相关
    searchKeyword: "",
    searchLoading: false,

    // 待盘存清单
    checkCart: [],
    cartTotalItems: 0,

    // 其他
    showCheckList: false,
    showCheckListPopup: false,

    // 库存统计
    inventoryStats: {
      totalPackages: 0,
      totalPieces: 0
    },

    // 服装信息弹窗
    showClothingInfo: false,
    clothingInfo: {},
    oemClothingInfo: {},
    isOem: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadWarehouses();
  },

  /**
   * 加载仓库列表
   */
  async loadWarehouses() {
    try {
      const response = await Api.getWarehouseList();
      if (response.data.code === 200) {
        this.setData({
          warehouses: response.data.data.list || []
        });
      }
    } catch (error) {
      console.error("加载仓库列表失败:", error);
      wx.showToast({
        title: "加载仓库失败",
        icon: "none",
      });
    }
  },

  /**
   * 切换仓库下拉框
   */
  toggleWarehouseDropdown() {
    this.setData({
      showWarehouseDropdown: !this.data.showWarehouseDropdown
    });
  },

  /**
   * 选择仓库
   */
  selectWarehouse(e) {
    const { warehouse } = e.currentTarget.dataset;
    this.setData({
      selectedWarehouse: warehouse,
      showWarehouseDropdown: false,
      inventoryList: [],
      checkCart: [],
      cartTotalItems: 0,
      showCheckList: false
    });

    // 加载库存明细
    this.loadInventory();
  },

  /**
   * 加载库存明细
   */
  async loadInventory() {
    if (!this.data.selectedWarehouse) return;

    try {
      this.setData({ loading: true });

      const response = await Api.getNewWarehouseInventory({
        warehouse_id: this.data.selectedWarehouse.warehouse_id,
        limit: 1000, // 设置足够大的limit以获取所有数据
      });

      if (response.data.code === 200) {
        const inventoryData = response.data.data.list || [];
        const summaryData = response.data.data.summary || {};
        console.log("原始库存数据:", inventoryData);
        console.log("后端汇总数据:", summaryData);

        // 按contents归类处理库存数据，参考移库页面逻辑
        const groupedInventory = this.groupInventoryByContents(inventoryData);
        console.log("按contents归类后的库存数据:", groupedInventory);

        // 使用后端汇总数据，而不是前端重新计算
        const inventoryStats = {
          totalPackages: Math.round((summaryData.total_packages || 0) * 100) / 100,
          totalPieces: summaryData.total_pieces || 0
        };
        console.log("使用后端汇总数据:", inventoryStats);

        this.setData({
          inventoryList: groupedInventory,
          originalInventoryList: groupedInventory, // 保存原始数据
          inventoryStats,
          loading: false,
        });
      } else {
        wx.showToast({
          title: response.data.message || "加载库存失败",
          icon: "none",
        });
        this.setData({
          inventoryList: [],
          loading: false,
        });
      }
    } catch (error) {
      console.error("加载库存失败:", error);
      wx.showToast({
        title: "加载库存失败",
        icon: "none",
      });
      this.setData({
        inventoryList: [],
        loading: false,
      });
    }
  },

  /**
   * 按contents归类处理库存数据
   */
  groupInventoryByContents(inventoryData) {
    const grouped = [];

    inventoryData.forEach(item => {
      // 为每个库存项添加盘存相关字段
      const inventoryItem = {
        ...item,
        actual_quantity: "", // 实际库存输入
        system_quantity: item.package_count || 0, // 账面库存
        is_checked: false // 是否已确认
      };

      grouped.push(inventoryItem);
    });

    return grouped;
  },

  /**
   * 计算库存统计
   */
  calculateInventoryStats(inventoryList) {
    let totalPackages = 0;
    let totalPieces = 0;

    inventoryList.forEach(item => {
      const packageCount = item.package_count || 0;
      const piecesPerPackage = item.contents[0]?.original_quantity || 0;

      totalPackages += packageCount;
      totalPieces += packageCount * piecesPerPackage;
    });

    return {
      totalPackages: Math.round(totalPackages * 100) / 100,
      totalPieces
    };
  },

  /**
   * 实际库存输入
   */
  onActualQuantityInput(e) {
    const { inventoryIndex } = e.currentTarget.dataset;
    const value = e.detail.value;

    const inventoryList = [...this.data.inventoryList];
    inventoryList[inventoryIndex].actual_quantity = value;

    this.setData({
      inventoryList
    });
  },

  /**
   * 确认盘存项目
   */
  confirmInventoryItem(e) {
    const { inventoryIndex } = e.currentTarget.dataset;
    const item = this.data.inventoryList[inventoryIndex];

    if (!item.actual_quantity && item.actual_quantity !== 0) {
      wx.showToast({
        title: "请输入实际库存",
        icon: "none",
      });
      return;
    }

    const actualQuantity = parseFloat(item.actual_quantity);
    if (isNaN(actualQuantity) || actualQuantity < 0) {
      wx.showToast({
        title: "请输入有效的库存数量",
        icon: "none",
      });
      return;
    }

    // 添加到待盘存清单
    const checkItem = {
      contents: item.contents,
      system_quantity: item.system_quantity,
      actual_quantity: actualQuantity,
      difference: actualQuantity - item.system_quantity,
      inventory_index: inventoryIndex
    };

    const checkCart = [...this.data.checkCart];

    // 检查是否已存在相同的项目
    const existingIndex = checkCart.findIndex(cartItem =>
      this.isSameContents(cartItem.contents, item.contents)
    );

    if (existingIndex >= 0) {
      // 更新现有项目
      checkCart[existingIndex] = checkItem;
    } else {
      // 添加新项目
      checkCart.push(checkItem);
    }

    // 更新库存列表中的状态
    const inventoryList = [...this.data.inventoryList];
    inventoryList[inventoryIndex].is_checked = true;

    this.setData({
      checkCart,
      inventoryList,
      cartTotalItems: checkCart.length,
      showCheckList: checkCart.length > 0
    });

    wx.showToast({
      title: "已添加到待盘存清单",
      icon: "success",
    });
  },

  /**
   * 判断两个contents是否相同
   */
  isSameContents(contents1, contents2) {
    if (contents1.length !== contents2.length) return false;

    return contents1.every(item1 =>
      contents2.some(item2 =>
        item1.sku === item2.sku && item1.name === item2.name && item1.original_quantity === item2.original_quantity  && item1.current_quantity === item2.current_quantity
      )
    );
  },

  /**
   * 从待盘存清单中移除项目
   */
  removeFromCheckCart(e) {
    const { cartIndex } = e.currentTarget.dataset;
    const checkCart = [...this.data.checkCart];
    const removedItem = checkCart[cartIndex];

    // 恢复库存列表中的状态
    const inventoryList = [...this.data.inventoryList];
    if (removedItem.inventory_index !== undefined) {
      inventoryList[removedItem.inventory_index].is_checked = false;
    }

    checkCart.splice(cartIndex, 1);

    this.setData({
      checkCart,
      inventoryList,
      cartTotalItems: checkCart.length,
      showCheckList: checkCart.length > 0
    });
  },

  /**
   * 清空待盘存清单
   */
  clearCheckCart() {
    // 恢复所有库存项的状态
    const inventoryList = this.data.inventoryList.map(item => ({
      ...item,
      is_checked: false
    }));

    this.setData({
      checkCart: [],
      inventoryList,
      cartTotalItems: 0,
      showCheckList: false
    });
  },

  /**
   * 搜索服装
   */
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 执行搜索 - 在当前库存中搜索
   */
  async performSearch() {
    if (!this.data.selectedWarehouse) {
      wx.showToast({
        title: "请先选择仓库",
        icon: "none",
      });
      return;
    }

    if (!this.data.searchKeyword.trim()) {
      // 如果搜索关键词为空，显示所有库存
      this.loadInventory();
      return;
    }

    try {
      this.setData({ searchLoading: true });

      // 在原始库存中搜索
      const keyword = this.data.searchKeyword.trim().toLowerCase();
      const allInventory = this.data.originalInventoryList;

      const filteredInventory = allInventory.filter(item => {
        // 搜索服装名称或SKU
        const clothingName = item.contents[0].name.toLowerCase();
        const sku = item.contents[0].sku.toLowerCase();
        return clothingName.includes(keyword) || sku.includes(keyword);
      });

      // 计算搜索结果的统计信息
      const inventoryStats = this.calculateInventoryStats(filteredInventory);

      this.setData({
        inventoryList: filteredInventory,
        inventoryStats,
        searchLoading: false
      });

      if (filteredInventory.length === 0) {
        wx.showToast({
          title: "未找到匹配的库存",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("搜索失败:", error);
      wx.showToast({
        title: "搜索失败",
        icon: "none",
      });
      this.setData({
        searchLoading: false
      });
    }
  },

  /**
   * 重置搜索
   */
  resetSearch() {
    this.setData({
      searchKeyword: ""
    });
    // 重新加载完整的库存列表
    this.loadInventory();
  },





  /**
   * 核对盘存清单
   */
  reviewCheckList() {
    if (this.data.checkCart.length === 0) {
      wx.showToast({
        title: "请先添加盘存项目",
        icon: "none",
      });
      return;
    }

    // 跳转到盘存确认页面
    const checkData = {
      warehouse: this.data.selectedWarehouse,
      check_items: this.data.checkCart
    };
    console.log("checkData622:", checkData);

    wx.navigateTo({
      url: `/pages/inventory-check-confirm/inventory-check-confirm?checkData=${encodeURIComponent(
        JSON.stringify(checkData)
      )}`,
    });
  },

  /**
   * 点击服装名称显示服装卡片
   */
  async onClothingNameTap(e) {
    const { sku, clothingName } = e.currentTarget.dataset;
    console.log("点击服装名称:", sku, clothingName);

    // 显示加载状态
    wx.showLoading({
      title: "加载中...",
      mask: true,
    });

    try {
      // 根据SKU规则判断服装类型：如果SKU包含"_"且前面是"oem"，则为OEM服装
      const isOemClothing = sku && sku.includes("_") && sku.split("_")[0].toLowerCase() === "oem";

      if (isOemClothing) {
        // 获取OEM服装信息
        const oemClothingId = sku.split("_")[1]; // 提取OEM服装ID
        const response = await Api.getOemClothingInfo({
          oem_clothing_id: oemClothingId,
        });

        if (response.data) {
          this.setData({
            showClothingInfo: true,
            oemClothingInfo: response.data,
            clothingInfo: {},
            isOem: true,
          });
        } else {
          wx.showToast({
            title: "未找到服装信息",
            icon: "none",
          });
        }
      } else {
        // 获取普通服装信息 - 去除CLO_前缀
        let clothingId = sku;
        if (sku.startsWith("CLO_")) {
          clothingId = sku.substring(4); // 去除"CLO_"前缀
        }

        const response = await Api.getClothingInfo({
          clothing_id: clothingId,
        });

        if (response.data) {
          this.setData({
            showClothingInfo: true,
            clothingInfo: response.data,
            oemClothingInfo: {},
            isOem: false,
          });
        } else {
          wx.showToast({
            title: "未找到服装信息",
            icon: "none",
          });
        }
      }
    } catch (error) {
      console.error("获取服装信息失败:", error);
      wx.showToast({
        title: "获取服装信息失败",
        icon: "none",
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 关闭服装信息弹窗
   */
  onCloseClothingInfo() {
    this.setData({
      showClothingInfo: false,
      clothingInfo: {},
      oemClothingInfo: {},
      isOem: false,
    });
  },

  /**
   * 切换待盘存清单弹窗
   */
  toggleCheckList() {
    this.setData({
      showCheckListPopup: !this.data.showCheckListPopup
    });
  },

  /**
   * 关闭所有下拉框
   */
  closeAllDropdowns() {
    this.setData({
      showWarehouseDropdown: false
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时可以刷新数据
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    if (this.data.selectedWarehouse) {
      this.loadInventory();
    }
    wx.stopPullDownRefresh();
  }
})