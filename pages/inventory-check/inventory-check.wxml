<!-- pages/inventory-check/inventory-check.wxml -->
<view class="inventory-check-container" bind:tap="closeAllDropdowns">
  <!-- 主要内容 -->
  <view class="main-content">
    <!-- 仓库选择器 -->
    <view class="warehouse-selection-section">
      <view class="custom-warehouse-selector" catchtap="stopPropagation">
        <view class="selector-trigger {{showWarehouseDropdown ? 'active' : ''}}" bind:tap="toggleWarehouseDropdown">
          <view class="selector-content">
            <view class="selector-label">仓库：</view>
            <view class="selector-text">
              {{selectedWarehouse ? selectedWarehouse.name : '请选择仓库'}}
            </view>
          </view>
          <van-icon name="{{showWarehouseDropdown ? 'arrow-up' : 'arrow-down'}}" class="selector-arrow" />
        </view>
        <!-- 仓库下拉列表 -->
        <view class="dropdown-list {{showWarehouseDropdown ? 'show' : ''}}" wx:if="{{showWarehouseDropdown}}">
          <view class="dropdown-item" wx:for="{{warehouses}}" wx:key="warehouse_id" data-warehouse="{{item}}" bind:tap="selectWarehouse">
            <view class="option-content">
              <view class="option-info">
                <view class="option-name">{{item.name}}</view>
                <view class="option-address">{{item.address}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 库存明细 -->
    <view class="inventory-section" wx:if="{{selectedWarehouse}}">
      <view class="section-title">
        库存明细
        <view class="inventory-stats" wx:if="{{inventoryStats.totalPackages > 0}}">
          {{inventoryStats.totalPackages}}包 / {{inventoryStats.totalPieces}}件
        </view>
      </view>
      <!-- 搜索区域 -->
      <view class="search-section">
        <view class="search-header">
          <view class="search-form">
            <!-- 服装名称输入框 -->
            <view class="search-form-item">
              <input class="search-form-input" placeholder="输入服装名称搜索" value="{{searchKeyword}}" bindinput="onSearchInput" />
            </view>
            <!-- 搜索按钮 -->
            <view class="search-form-item">
              <van-button size="small" type="primary" bind:click="performSearch" loading="{{searchLoading}}" custom-class="search-form-btn">
                搜索
              </van-button>
            </view>
            <!-- 重置按钮 -->
            <view class="search-form-item">
              <van-button size="small" bind:click="resetSearch" custom-class="reset-form-btn">
                重置
              </van-button>
            </view>
          </view>
        </view>
      </view>
      <!-- 加载状态 -->
      <view class="loading-container" wx:if="{{loading}}">
        <van-loading type="spinner" />
        <text class="loading-text">加载中...</text>
      </view>
      <!-- 库存列表 -->
      <view class="inventory-list" wx:else>
        <view class="inventory-item" wx:for="{{inventoryList}}" wx:key="index" wx:for-index="inventoryIndex">
          <view class="item-header">
            <view class="clothing-info">
              <view class="clothing-name-row">
                <view class="clothing-name clickable" data-sku="{{item.contents[0].sku}}" data-clothing-name="{{item.contents[0].name}}" bind:tap="onClothingNameTap">
                  {{item.contents[0].name}}
                </view>
                <view class="pieces">{{item.contents[0].original_quantity}}件/包</view>
                <view class="stock-summary">
                  <text class="label">账面库存:</text>
                  <text class="value">{{item.system_quantity}}包</text>
                </view>
              </view>
            </view>
          </view>
          <view class="item-content">
            <view class="actual-stock-row">
              <text class="label">实际库存:</text>
              <input class="actual-input" type="digit" placeholder="输入实际库存" value="{{item.actual_quantity}}" data-inventory-index="{{inventoryIndex}}" bindinput="onActualQuantityInput" />
              <text class="unit">包</text>
            </view>
            <view class="action-area">
              <van-button wx:if="{{!item.is_checked}}" type="primary" size="small" data-inventory-index="{{inventoryIndex}}" bind:click="confirmInventoryItem">
                确定
              </van-button>
              <view wx:else class="checked-status">
                <van-icon name="success" color="#07c160" />
                <text>已添加</text>
              </view>
            </view>
          </view>
        </view>
        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{inventoryList.length === 0 && !loading}}">
          <van-empty description="暂无库存数据" />
        </view>
      </view>
    </view>
  </view>
  <!-- 底部固定操作栏 -->
  <view class="bottom-fixed-bar" wx:if="{{selectedWarehouse}}">
    <view class="bottom-actions">
      <!-- 待盘存清单按钮 -->
      <view class="action-item">
        <van-button type="info" size="large" bind:click="toggleCheckList" class="action-button" disabled="{{cartTotalItems === 0}}">
          <van-icon name="shopping-cart-o" />
          待盘存 ({{cartTotalItems}}项)
        </van-button>
      </view>
      <!-- 核对按钮 -->
      <view class="action-item">
        <van-button type="success" size="large" bind:click="reviewCheckList" class="action-button" disabled="{{cartTotalItems === 0}}">
          <van-icon name="passed" />
          核对
        </van-button>
      </view>
    </view>
  </view>
  <!-- 服装信息弹窗 -->
  <van-popup show="{{showClothingInfo}}" round position="center" custom-style="width: 90%; max-width: 600rpx; border-radius: 16rpx;" bind:close="onCloseClothingInfo">
    <view class="clothing-popup-container">
      <clothing-info-card wx:if="{{!isOem}}" clothingInfo="{{clothingInfo}}" isOem="{{false}}" />
      <clothing-info-card wx:else oemClothingInfo="{{oemClothingInfo}}" isOem="{{true}}" />
    </view>
  </van-popup>
  <!-- 待盘存清单浮窗 -->
  <van-popup show="{{showCheckListPopup}}" position="bottom" bind:close="toggleCheckList" custom-style="height: 70vh;">
    <view class="cart-popup">
      <view class="popup-header">
        <view class="popup-title">待盘存清单</view>
        <view class="header-actions">
          <van-button size="small" bind:click="clearCheckCart" type="danger">清空</van-button>
          <van-button size="small" bind:click="toggleCheckList">关闭</van-button>
        </view>
      </view>
      <scroll-view class="cart-content" scroll-y>
        <view wx:if="{{checkCart.length === 0}}" class="empty-cart">
          <van-empty description="暂无待盘存项目" />
        </view>
        <view wx:else>
          <view class="cart-item" wx:for="{{checkCart}}" wx:key="index" wx:for-index="cartIndex">
            <view class="cart-item-content">
              <view class="cart-item-info">
                <view class="cart-clothing-details">
                  <view class="cart-clothing-name">{{item.contents[0].name}}</view>
                  <view class="cart-pieces">{{item.contents[0].original_quantity}}件/包</view>
                </view>
                <view class="cart-stock-info">
                  <view class="cart-stock-formula">
                    账面: {{item.system_quantity}}包 → 实际: {{item.actual_quantity}}包
                  </view>
                  <view class="cart-difference {{item.difference > 0 ? 'surplus' : item.difference < 0 ? 'deficit' : 'normal'}}">
                    <text wx:if="{{item.difference > 0}}">盘盈 {{item.difference}}包</text>
                    <text wx:elif="{{item.difference < 0}}">盘亏 {{-item.difference}}包</text>
                    <text wx:else>无差异</text>
                  </view>
                </view>
              </view>
              <view class="cart-item-actions">
                <van-icon name="close" data-cart-index="{{cartIndex}}" bind:click="removeFromCheckCart" class="remove-icon" />
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </van-popup>
</view>