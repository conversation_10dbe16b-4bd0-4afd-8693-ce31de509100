/* pages/inventory-check/inventory-check.wxss */

.inventory-check-container {
  min-height: 95vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.main-content {
  flex: 1;
}

/* 仓库选择器样式 */
.warehouse-selection-section {
  margin-bottom: 20rpx;
}

.custom-warehouse-selector {
  position: relative;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.selector-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.selector-trigger.active {
  background-color: #f0f9ff;
  border-color: #1989fa;
}

.selector-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.selector-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 16rpx;
  font-weight: 500;
}

.selector-text {
  font-size: 28rpx;
  color: #666;
  flex: 1;
}

.selector-arrow {
  font-size: 32rpx;
  color: #999;
  transition: transform 0.3s ease;
}

.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 400rpx;
  overflow-y: auto;
  margin-top: 8rpx;
  opacity: 0;
  transform: translateY(-10rpx);
  transition: all 0.3s ease;
}

.dropdown-list.show {
  opacity: 1;
  transform: translateY(0);
}

.dropdown-item {
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item:active {
  background: #e3f2fd;
  border-color: #1989fa;
}

.option-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-info {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.option-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex-shrink: 0;
}

.option-address {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  min-width: 0;
  text-align: left;
}

/* 搜索区域样式 - 参照操作日志样式 */
.search-section {
  margin-bottom: 20rpx;
}

.search-header {
  background: white;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-form {
  display: flex;
  gap: 12rpx;
  align-items: center;
  flex-wrap: wrap;
}

.search-form-item {
  flex: 1;
  min-width: 0;
}

.search-form-item:first-child {
  flex: 2;
}

.search-form-input {
  width: 100%;
  height: 60rpx;
  padding: 0 16rpx;
  font-size: 26rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e0e0e0;
  box-sizing: border-box;
}

.search-form-input:focus {
  border-color: #07c160;
  background: rgba(7, 193, 96, 0.05);
}

/* 搜索和重置按钮 - 参照操作日志样式 */
.search-form-btn,
.reset-form-btn {
  width: 100% !important;
  height: 60rpx !important;
  border-radius: 8rpx !important;
  font-size: 26rpx !important;
  font-weight: 600 !important;
}

.search-form-btn {
  background: linear-gradient(135deg, #07c160 0%, #05a050 100%) !important;
  border: none !important;
  color: white !important;
}

.reset-form-btn {
  background: #f5f5f5 !important;
  color: #666 !important;
  border: 1rpx solid #e0e0e0 !important;
}

/* 区域标题样式 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.inventory-stats {
  font-size: 24rpx;
  color: #666;
  font-weight: normal;
}

.clear-btn {
  margin-left: auto;
}

/* 库存列表样式 */
.inventory-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  overflow-y: auto;
  height: 69vh;
  border-radius: 12rpx;
}

.inventory-item {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.item-header {
  margin-bottom: 16rpx;
}

.clothing-info {
  display: flex;
  flex-direction: column;
}

.clothing-name-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16rpx;
}

.clothing-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex-shrink: 0;
}

.clothing-name.clickable {
  color: #1989fa;
  font-size: 32rpx;
}

.clothing-name.clickable:active {
  color: #0570c9;
}

.stock-summary {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 32rpx;
  flex: 1;
  justify-content: flex-end;
}

.stock-summary .label {
  color: #333;
}

.stock-summary .value {
  color: #e1824e;
  font-weight: 500;
}

.stock-summary .pieces {
  color: #999;
}

.item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20rpx;
}

.actual-stock-row {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.actual-stock-row .label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
}

.actual-input {
  flex: 1;
  max-width: 200rpx;
  height: 60rpx;
  padding: 0 16rpx;
  border: 2rpx solid #eee;
  border-radius: 6rpx;
  font-size: 28rpx;
  background: #f8f9fa;
  text-align: center;
}

.actual-input:focus {
  border-color: #1989fa;
  background: white;
}

.unit {
  font-size: 24rpx;
  color: #999;
}

.action-area {
  flex-shrink: 0;
  min-width: 120rpx;
  display: flex;
  justify-content: center;
}

.checked-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #07c160;
}

/* 待盘存清单样式 */
.check-cart-section {
  margin-top: 32rpx;
}

.check-cart-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.check-cart-item {
  background: white;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.item-info {
  flex: 1;
}

.quantity-info {
  display: flex;
  gap: 20rpx;
  margin-top: 8rpx;
  font-size: 24rpx;
}

.system {
  color: #666;
}

.actual {
  color: #333;
}

.difference {
  font-weight: 500;
}

.difference.surplus {
  color: #07c160;
}

.difference.deficit {
  color: #ee0a24;
}

.difference.normal {
  color: #999;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  background: white;
  border-radius: 12rpx;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

/* 空状态样式 */
.empty-state {
  background: white;
  border-radius: 12rpx;
  padding: 80rpx 0;
}

/* 底部操作区域样式 */
.bottom-fixed-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1rpx solid #eee;
  padding: 20rpx;
  z-index: 1000;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.bottom-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
  align-items: center;
}

.action-item {
  flex: 1;
  max-width: 300rpx;
}

.action-button {
  width: 100% !important;
  border-radius: 12rpx !important;
  height: 80rpx;
  font-size: 28rpx;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

/* 核对按钮特殊样式 */
.action-button[type="success"] {
  background: linear-gradient(135deg, #07c160 0%, #05a050 100%) !important;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3) !important;
  border: none !important;
  color: white !important;
}

.action-button[type="success"]:active {
  transform: translateY(2rpx) !important;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.4) !important;
}

/* 服装信息弹窗样式 */
.clothing-popup-container {
  padding: 20rpx;
  max-height: 80vh;
  overflow-y: auto;
}

/* 待盘存清单弹窗样式 */
.cart-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #eee;
  background: #f8f9fa;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 16rpx;
}

.cart-content {
  flex: 1;
  padding: 20rpx;
}

.empty-cart {
  padding: 80rpx 0;
  text-align: center;
}

.cart-item {
  background: white;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
}

.cart-item-content {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
}

.cart-item-info {
  flex: 1;
  min-width: 0;
}

.cart-clothing-details {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 8rpx;
}

.cart-clothing-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  word-break: break-all;
}

.cart-stock-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 8rpx;
}

.cart-stock-formula {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.cart-difference {
  font-size: 24rpx;
  font-weight: 600;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
  width: fit-content;
}

.cart-difference.surplus {
  color: #07c160;
  background: #f0f9ff;
  border: 1rpx solid #07c160;
}

.cart-difference.deficit {
  color: #ee0a24;
  background: #fff1f0;
  border: 1rpx solid #ee0a24;
}

.cart-difference.normal {
  color: #999;
  background: #f6f6f6;
  border: 1rpx solid #ddd;
}

.cart-item-actions {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  width: 120rpx;
  height: 100rpx;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
}

.remove-icon {
  color: #ee0a24;
  cursor: pointer;
  font-size: 32rpx;
}
