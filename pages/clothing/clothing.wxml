<!-- 搜索区域容器 -->
<view class="search-container {{hasSearchResults ? 'search-moved-up' : ''}}">
  <!-- 使用自定义搜索栏组件 -->
  <z-search-bar
    showYearStepper="{{true}}"
    year="{{yearNumber}}"
    searchValue="{{search}}"
    placeholder="搜索服装"
    minYear="{{2020}}"
    maxYear="{{2030}}"
    disabled="{{loading}}"
    bind:yearChange="onYearChange"
    bind:searchInput="onSearchInput"
    bind:search="onGoToSearch"
  ></z-search-bar>

  <!-- 按钮组 -->
  <view class="button-group">
    <view class="comprehensive-search-btn" bindtap="goToComprehensiveSearch">
      汇总查询
    </view>
    <view class="reset-btn" bindtap="onReset">
      重置
    </view>
    <view class="comprehensive-search-btn" bindtap="onSearch">
      搜索
    </view>
  </view>

</view>

<!-- 搜索结果容器 -->
<view class="search-results {{hasSearchResults ? 'show-results' : ''}}">
  <scroll-view
    class="scroll-container"
    scroll-y="{{true}}"
    enhanced="{{true}}"
    show-scrollbar="{{false}}"
    enable-back-to-top="{{true}}"
  >
    <!-- 普通服装列表 -->
    <view wx:for="{{clothingList}}" wx:key="index" class="clothing-item">
      <clothing-info-card
        clothingInfo="{{item}}"
        isOem="{{false}}"
        bind:cardTap="onClothingCardTap"
        bind:changePrice="onChangePrice">
      </clothing-info-card>
    </view>

    <!-- OEM服装列表 -->
    <view wx:for="{{oemClothingList}}" wx:key="index" class="clothing-item">
      <clothing-info-card
        oemClothingInfo="{{item}}"
        isOem="{{true}}"
        bind:cardTap="onOemClothingCardTap"
        bind:changePrice="onChangePrice">
      </clothing-info-card>
    </view>

    <!-- 底部安全区域 -->
    <view class="bottom-safe-area"></view>
  </scroll-view>
</view>


<!-- 使用自定义加载状态组件 -->
<z-loading-state
  loading="{{loading}}"
  loadAll="{{false}}"
  loadingText="搜索中..."
></z-loading-state>
