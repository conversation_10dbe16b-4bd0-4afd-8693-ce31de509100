/* 搜索区域 */
.search-container {
  background: rgb(255, 255, 255);
  padding: 24rpx;
  display: flex;
  flex-direction: column;

  position: relative;
  z-index: 10;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center top;
  border-radius: 0 0 24rpx 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(190, 190, 190, 0.15);
}

/* 按钮组 */
.button-group {
  display: flex;
  gap: 12rpx;
  margin-top: 12rpx;
  flex-wrap: wrap;
}

/* 汇总查询按钮 */
.comprehensive-search-btn {
  flex: 1;
  background: linear-gradient(135deg, #2c96788d 0%, #34a085 100%);
  color: #ffffff;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 600;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(44, 150, 120, 0.3);
  transition: all 0.3s ease;
}

.comprehensive-search-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 12rpx rgba(44, 150, 120, 0.2);
}

/* 重置按钮 */
.reset-btn {
  flex: 1;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  color: #6c757d;
  border: 2rpx solid #dee2e6;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 600;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.reset-btn:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* 搜索区域上移状态 */
.search-container.search-moved-up {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  padding: 16rpx 24rpx;
  border-radius: 0;
  box-shadow: 0 4rpx 20rpx rgba(209, 209, 209, 0.2);
  backdrop-filter: blur(10rpx);
}

/* 示例数据控制按钮 */
.demo-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16rpx;
  margin-top: 12rpx;
  padding: 8rpx 0;
  flex-wrap: wrap;
}

/* 搜索结果容器 */
.search-results {
  opacity: 0;
  transform: translateY(30rpx);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 5;
}

.search-results.show-results {
  opacity: 1;
  transform: translateY(0);
}

/* 滚动容器 */
.scroll-container {
  height: 100vh;
  padding-top: 228rpx; /* 为固定的搜索栏留出空间 */
  padding-left: 24rpx;
  padding-right: 24rpx;
  box-sizing: border-box;
}

/* 底部安全区域 */
.bottom-safe-area {
  height: 100rpx;
  width: 100%;
}

/* 服装项 */
.clothing-item {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateY(30rpx);
  margin-bottom: 24rpx;
}

.clothing-item:nth-child(1) {
  animation-delay: 0.1s;
}
.clothing-item:nth-child(2) {
  animation-delay: 0.2s;
}
.clothing-item:nth-child(3) {
  animation-delay: 0.3s;
}
.clothing-item:nth-child(4) {
  animation-delay: 0.4s;
}
.clothing-item:nth-child(5) {
  animation-delay: 0.5s;
}
.clothing-item:nth-child(n + 6) {
  animation-delay: 0.6s;
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 空状态 */
.empty-container {
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-actions {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-top: 20rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.empty-hint {
  font-size: 24rpx;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60rpx;
  color: #999;
  font-size: 28rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .button-group {
    flex-direction: column;
    gap: 8rpx;
  }

  .comprehensive-search-btn,
  .reset-btn,
  .search-btn {
    flex: none;
    min-height: 80rpx;
    font-size: 28rpx;
  }
}
