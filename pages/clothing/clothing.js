// pages/clothing/clothing.js
import Api from "../../utils/api.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    search: "",
    year: "",
    yearNumber: new Date().getFullYear(), // 数字类型的年份，用于步进器
    clothingList: [],
    oemClothingList: [],
    loading: false,
    hasSearchResults: false, // 是否有搜索结果
    showImagePreview: false,
    previewImages: [],
    currentImageIndex: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    const currentYear = new Date().getFullYear();
    this.setData({
      year: currentYear.toString(),
      yearNumber: currentYear,
    });
  },




  /**
   * 搜索按钮点击事件
   */
  onSearch() {
    this.performSearch();
  },

  /**
   * 搜索服装
   */
  async onGoToSearch() {
    this.performSearch();
  },

  /**
   * 执行搜索操作
   */
  async performSearch() {
    if (this.data.search.trim() === "") {
      wx.showToast({
        title: "请输入搜索内容",
        icon: "none",
      });
      return;
    }

    this.setData({ loading: true });

    try {
      const params = {
        clothing_name: this.data.search,
        clothing_year: this.data.year + '年',
      };

      // 搜索普通服装
      const clothingRes = await Api.searchClothing(params);
      console.log("普通服装搜索结果:", clothingRes);
      let clothingList = [];

      if (clothingRes.data && clothingRes.data.data && clothingRes.data.data.clothingList) {
        clothingList = clothingRes.data.data.clothingList;
        clothingList.forEach((item) => {
          this.processClothingItem(item, 'normal');
        });
      }

      // 搜索OEM服装
      const oemParams = {
        oem_clothing_name: this.data.search,
        oem_clothing_year: this.data.year + '年',
      };
      const oemClothingRes = await Api.searchOemClothing(oemParams);
      console.log("OEM服装搜索结果:", oemClothingRes);
      let oemClothingList = [];

      if (oemClothingRes.data && oemClothingRes.data.data && oemClothingRes.data.data.oemClothingList) {
        oemClothingList = oemClothingRes.data.data.oemClothingList;
        oemClothingList.forEach((item) => {
          this.processClothingItem(item, 'oem');
        });
      }

      const hasResults = clothingList.length > 0 || oemClothingList.length > 0;

      this.setData({
        clothingList: clothingList,
        oemClothingList: oemClothingList,
        hasSearchResults: hasResults,
      });

      if (!hasResults) {
        wx.showToast({
          title: "未找到相关服装",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("搜索服装失败:", error);
      wx.showToast({
        title: "搜索失败",
        icon: "none",
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 处理服装项数据
   */
  processClothingItem(item, type) {
    let imgList = [];
    let totalPcs, shipments;

    if (type === 'oem') {
      totalPcs = item.in_pcs || 0;
      shipments = item.shipments || 0;
    } else {
      totalPcs = item.clipping_pcs || 0;
      shipments = item.shipments || 0;
    }

    // 计算进度百分比
    const percent = totalPcs > 0 ? (shipments / totalPcs) * 100 : 0;
    item.percent = Math.min(Math.max(percent, 0), 100);
    item.percentWidth = Math.min(Math.max(percent * 5.5, 190), 550); // 用于进度条宽度

    // 处理图片 - 统一使用img字段
    if (item.img && Array.isArray(item.img)) {
      item.img.forEach((img) => {
        if (img && img.url) {
          imgList.push(img.url);
        }
      });
    }
    // 兼容旧的字段名
    else if (item.clothing_img && Array.isArray(item.clothing_img)) {
      item.clothing_img.forEach((img) => {
        if (img && img.url) {
          imgList.push(img.url);
        }
      });
    } else if (item.oem_clothing_img && Array.isArray(item.oem_clothing_img)) {
      item.oem_clothing_img.forEach((img) => {
        if (img && img.url) {
          imgList.push(img.url);
        }
      });
    }

    item.imgList = imgList;
    item.hasImages = imgList.length > 0;
  },

  /**
   * 输入搜索内容 - 适配 Vant 搜索组件
   */
  onSearchInput(e) {
    this.setData({
      search: e.detail,
    });
  },

  /**
   * 年份改变 - 适配 Vant 步进器组件
   */
  onYearChange(e) {
    console.log("年份改变:", e.detail);
    const newYear = e.detail;
    this.setData({
      year: newYear.toString(),
      yearNumber: newYear,
    });
  },

  /**
   * 增加年份 (保留兼容性)
   */
  increaseYear() {
    const currentYear = parseInt(this.data.year);
    const newYear = currentYear + 1;
    if (newYear <= 2030) { // 设置最大年份限制
      this.setData({
        year: newYear.toString(),
      });
    }
  },

  /**
   * 减少年份 (保留兼容性)
   */
  decreaseYear() {
    const currentYear = parseInt(this.data.year);
    const newYear = currentYear - 1;
    if (newYear >= 2020) { // 设置最小年份限制
      this.setData({
        year: newYear.toString(),
      });
    }
  },

  /**
   * 预览图片
   */
  onPreviewImage(e) {
    const { images, index } = e.currentTarget.dataset;
    if (images && images.length > 0) {
      wx.previewImage({
        urls: images,
        current: images[index || 0],
      });
    }
  },

  /**
   * 普通服装卡片点击事件
   */
  onClothingCardTap(e) {
    const { info } = e.detail;
    console.log("点击普通服装卡片:", info);
    // 可以在这里添加跳转到详情页面的逻辑
  },

  /**
   * OEM服装卡片点击事件
   */
  onOemClothingCardTap(e) {
    const { info } = e.detail;
    console.log("点击OEM服装卡片:", info);
    // 可以在这里添加跳转到详情页面的逻辑
  },

  /**
   * 修改价格
   */
  async onChangePrice(e) {
    const { info, isOem } = e.detail;

    wx.showModal({
      title: "修改价格",
      editable: true,
      placeholderText: "请输入新价格",
      success: async (res) => {
        if (res.confirm && res.content) {
          const newPrice = parseFloat(res.content);
          if (isNaN(newPrice) || newPrice < 0) {
            wx.showToast({
              title: "请输入有效价格",
              icon: "none",
            });
            return;
          }

          try {
            const params = {
              id: isOem ? info.oem_clothing_id : info.clothing_id,
              type: isOem ? 'oemClothing' : 'clothing',
              price: newPrice,
            };

            const result = await Api.changePrice(params);

            if (result.data && result.data.code === 200) {
              // 更新本地数据
              if (isOem) {
                const updatedList = this.data.oemClothingList.map((clothing) => {
                  if (clothing.oem_clothing_id === info.oem_clothing_id) {
                    clothing.price = newPrice;
                  }
                  return clothing;
                });
                this.setData({ oemClothingList: updatedList });
              } else {
                const updatedList = this.data.clothingList.map((clothing) => {
                  if (clothing.clothing_id === info.clothing_id) {
                    clothing.price = newPrice;
                  }
                  return clothing;
                });
                this.setData({ clothingList: updatedList });
              }

              wx.showToast({
                title: "价格修改成功",
                icon: "success",
              });
            } else {
              wx.showToast({
                title: "价格修改失败",
                icon: "none",
              });
            }
          } catch (error) {
            console.error("修改价格失败:", error);
            wx.showToast({
              title: "价格修改失败",
              icon: "none",
            });
          }
        }
      },
    });
  },

  /**
   * 重置按钮点击事件
   */
  onReset() {
    const currentYear = new Date().getFullYear();
    this.setData({
      search: "",
      year: currentYear.toString(),
      yearNumber: currentYear,
      clothingList: [],
      oemClothingList: [],
      hasSearchResults: false,
    });
    wx.showToast({
      title: "已重置",
      icon: "success",
      duration: 1000
    });
  },

  /**
   * 清空搜索结果
   */
  onClearSearch() {
    this.setData({
      search: "",
      clothingList: [],
      oemClothingList: [],
      hasSearchResults: false,
    });
  },

  /**
   * 跳转到综合搜索页面
   */
  goToComprehensiveSearch() {
    wx.navigateTo({
      url: '/pages/comprehensive-search/comprehensive-search'
    });
  },



  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 可以在这里添加下拉刷新逻辑
    wx.stopPullDownRefresh();
  },



  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
});
