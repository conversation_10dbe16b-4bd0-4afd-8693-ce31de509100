/* pages/calendar-logs/calendar-logs.wxss */

.page-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20rpx;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 24rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.header-title {
  align-items: center;
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
}

.header-subtitle {
  font-size: 28rpx;
  color: #666;
  background: #f0f9f6;
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  display: inline-block;
}

/* 日志查询区域 */
.logs-section {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

/* 页面信息头部 */
.page-info-header {
  background: #f8f9fa;
  padding: 20rpx 32rpx;
  border-bottom: 2rpx solid #e9ecef;
}

/* 操作类型按钮组头部 */
.operation-buttons-header {
  background: white;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #e9ecef;
}

.operation-buttons {
  display: flex;
  gap: 16rpx;
  justify-content: space-around;
}

.operation-btn {
  flex: 1;
  text-align: center;
  padding: 10rpx 16rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

/* 入库按钮选中状态 - 蓝色 */
.operation-btn.active[data-type="inbound"] {
  background: #5698c3;
  border-color: #5698c3;
  color: white;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(86, 152, 195, 0.3);
}

/* 出库按钮选中状态 - 绿色 */
.operation-btn.active[data-type="outbound"] {
  background: #2c9678;
  border-color: #2c9678;
  color: white;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(44, 150, 120, 0.3);
}

/* 移库按钮选中状态 - 黄色 */
.operation-btn.active[data-type="transfer"] {
  background: #ddc871;
  border-color: #ddc871;
  color: white;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(221, 200, 113, 0.3);
}

/* 盘存按钮选中状态 - 紫色 */
.operation-btn.active[data-type="inventory"] {
  background: #806d9e;
  border-color: #806d9e;
  color: white;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(128, 109, 158, 0.3);
}

.operation-btn:active {
  transform: translateY(2rpx);
}

/* 数据展示选项卡 */
.data-tabs-header {
  background: white;
  padding: 0 32rpx;
  border-bottom: 2rpx solid #e9ecef;
}

.data-tabs {
  display: flex;
  gap: 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.data-tab {
  flex: 1;
  text-align: center;
  padding: 16rpx 12rpx;
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.data-tab.active {
  color: #2c9678;
  font-weight: 600;
}

.data-tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #2c9678;
  border-radius: 2rpx;
}

.data-tab:active {
  background: #f8f9fa;
}

/* 来源信息显示样式 */
.source-info-item {
  display: flex;
  align-items: center;
  background: #f0f9f6;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  border: 2rpx solid #2c9678;
  max-width: 400rpx;
}

.source-info-label {
  font-size: 26rpx;
  color: #2c9678;
  font-weight: 600;
  white-space: nowrap;
}

.source-info-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 300rpx;
}

.source-info-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60rpx;
}

/* 日志列表 */
.logs-list {
  height: calc(100vh - 240rpx);
  padding-bottom: 20rpx;
}

.empty-state {
  padding: 120rpx 40rpx;
  text-align: center;
}

.log-item {
  border-bottom: 2rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.log-item:last-child {
  border-bottom: none;
}

.log-item:hover {
  background: #fafafa;
}

/* 紧凑布局样式 */
.log-item-content-compact {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  gap: 16rpx;
  min-height: 40rpx;
}

/* 序号列样式 */
.serial-number {
  width: 60rpx;
  text-align: center;
  font-size: 24rpx;
  color: #999;
  font-weight: 500;
  flex-shrink: 0;
}

/* 按仓库汇总 - 简约紧凑设计 */
.warehouse-compact-item {
  padding: 0 12rpx;
  margin: 16rpx;
  background: white;
  border-radius: 16rpx;
  border: 1rpx solid #2c967877;
  overflow: hidden;
}

.warehouse-compact-header {
  margin-top: 10rpx;
  display: flex;
  align-items: center;
  padding: 12rpx 16rpx;
  border-radius: 16rpx 16rpx 0 0;
  background: #fafafa;
  border-bottom: 1rpx solid #f0f0f0;
  gap: 12rpx;
}

.warehouse-serial {
  width: 40rpx;
  text-align: center;
  font-size: 22rpx;
  color: #999;
  font-weight: 500;
  flex-shrink: 0;
}

.warehouse-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8rpx;
  justify-content: space-between;
}

.warehouse-title {
  font-size: 26rpx;
  color: #333;
  font-weight: 600;
}
.warehouse-packages {
  font-size: 28rpx;
  color: #c45a65;
  font-weight: 400;
  margin-right: 80rpx;
}

.clothing-compact-list {
  padding: 0;
}

.clothing-compact-item {
  border-bottom: 1rpx solid #f8f8f8;
  transition: background-color 0.2s ease;
}

.clothing-compact-item:last-child {
  border-bottom: none;
}

.clothing-compact-item:active {
  background: #f8f9fa;
}

.clothing-compact-content {
  display: flex;
  align-items: center;
  padding: 10rpx 16rpx 10rpx 68rpx; /* 左侧对齐序号 */
  gap: 12rpx;
  min-height: 44rpx;
}

.clothing-compact-name {
  flex: 2;
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.3;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.clothing-compact-name.clickable {
  color: #2c9678;
  cursor: pointer;
}

.clothing-compact-packages {
  flex: 1;
  text-align: right;
  flex-shrink: 0;
}

.clothing-compact-pieces {
  flex: 1;
  text-align: right;
  flex-shrink: 0;
}

/* 通用文本样式 */
.pieces-text,
.packages-text {
  font-size: 26rpx;
  font-weight: 400;
}

.pieces-text {
  color: #333;
  font-weight: 600;
}

.packages-text {
  color: #333;
}

/* 按服装汇总 - 简化设计 */
.clothing-simple-item {
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.clothing-simple-item:last-child {
  border-bottom: none;
}

.clothing-simple-item:active {
  background: #fafafa;
}

.clothing-simple-content {
  display: flex;
  align-items: center;
  padding: 14rpx 24rpx;
  gap: 16rpx;
  min-height: 48rpx;
}

.clothing-simple-left {
  flex: 2;
  min-width: 0;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.clothing-simple-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.3;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.clothing-simple-name.clickable {
  color: #2c9678;
  cursor: pointer;
}

.clothing-simple-packages {
  flex: 1;
  text-align: right;
  flex-shrink: 0;
}

.clothing-simple-pieces {
  flex: 1;
  text-align: center;
  flex-shrink: 0;
}

/* 简单汇总文本样式 */
.simple-packages-text,
.simple-pieces-text {
  font-size: 26rpx;
}

.simple-packages-text {
  color: #333;
  font-weight: 400;
}

.simple-pieces-text {
  color: #333;
  font-weight: 600;
}

/* 左侧区域：服装信息 */
.left-section {
  flex: 2;
  min-width: 0;
}

.clothing-info-compact {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 4rpx;
}

.clothing-info-compact:last-child {
  margin-bottom: 0;
}

.clothing-name-compact {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  word-break: break-all;
  line-height: 1.3;
  max-width: 240rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.clothing-name-compact.clickable {
  color: #2c9678;
  cursor: pointer;
}

.quantity-compact {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}

.package-compact {
  color: #999;
  font-size: 22rpx;
  margin-left: 4rpx;
}

/* 中间区域：仓库信息 */
.middle-section {
  flex: 1;
  min-width: 0;
}

.warehouse-compact {
  font-size: 24rpx;
  color: #666;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 160rpx;
}

/* 右侧区域：日期和标签 */
.right-section {
  flex: 2;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  text-align: center;
}

.date-compact {
  font-size: 22rpx;
  color: #999;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.bottom-text {
  text-align: center;
  color: #999;
  font-size: 24rpx;
  margin-top: 20rpx;
  padding-bottom: 20rpx;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  gap: 16rpx;
  color: #666;
  font-size: 28rpx;
}

/* 右滑删除按钮样式 */
.delete-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 130rpx;
  height: 100%;
  background-color: #c45a65;
  color: white;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 0;
  transition: background-color 0.2s ease;
}

.delete-button:active {
  background-color: #a94952;
}

/* 服装信息卡片弹窗样式 */
.clothing-card-popup {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  max-height: 80vh;
}





.popup-content {
  padding: 32rpx;
  max-height: calc(80vh - 120rpx);
  overflow-y: auto;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .page-container {
    padding: 16rpx;
  }

  .operation-buttons-header {
    padding: 20rpx 24rpx;
  }

  .operation-buttons {
    gap: 12rpx;
  }

  .operation-btn {
    padding: 16rpx 12rpx;
    font-size: 26rpx;
  }

  .data-tabs-header {
    padding: 12rpx 24rpx 0;
  }

  .data-tab {
    padding: 12rpx 8rpx;
    font-size: 24rpx;
  }

  .source-info-item {
    max-width: 300rpx;
    padding: 12rpx 20rpx;
  }

  .source-info-text {
    max-width: 200rpx;
    font-size: 24rpx;
  }

  .log-item-content-compact {
    padding: 12rpx 20rpx;
    gap: 12rpx;
  }

  .warehouse-compact-header {
    padding: 10rpx 14rpx;
    gap: 10rpx;
  }

  .warehouse-serial {
    width: 36rpx;
    font-size: 20rpx;
  }

  .warehouse-title {
    font-size: 24rpx;
  }

  .warehouse-count {
    font-size: 18rpx;
  }

  .clothing-compact-content {
    padding: 8rpx 14rpx 8rpx 60rpx;
    gap: 10rpx;
    min-height: 40rpx;
  }

  .clothing-compact-name {
    font-size: 22rpx;
  }

  .pieces-text,
  .packages-text {
    font-size: 22rpx;
  }

  .clothing-simple-content {
    padding: 12rpx 20rpx;
    gap: 12rpx;
    min-height: 44rpx;
  }

  .clothing-simple-name {
    font-size: 24rpx;
  }

  .simple-packages-text,
  .simple-pieces-text {
    font-size: 22rpx;
  }

  .serial-number {
    width: 50rpx;
    font-size: 22rpx;
  }

  .clothing-card-popup {
    max-height: 85vh;
  }

  .popup-content {
    padding: 24rpx;
    max-height: calc(85vh - 120rpx);
  }

  .logs-list {
    height: calc(100vh - 300rpx);
  }
}
