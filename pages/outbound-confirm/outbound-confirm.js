// pages/outbound-confirm/outbound-confirm.js
import Api from "../../utils/api.js";
import TempStorage from "../../utils/storage.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 出库单数据
    orderData: null,
    // 按仓库分组的出库项
    groupedItems: [],
    // 加载状态
    loading: false,
    // 提交状态
    submitting: false,
    // 服装详情弹窗
    showClothingInfo: false,
    selectedClothingInfo: null,
    // 当前日期
    currentDate: "",
    // 日期选择器相关
    showDatePicker: false,
    selectedDate: "",
    minDate: new Date(2020, 0, 1).getTime(),
    maxDate: new Date().getTime(),
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { orderData } = options;

    if (orderData) {
      try {
        const parsedData = JSON.parse(decodeURIComponent(orderData));
        console.log("接收到的完整出库数据:", parsedData);

        // 直接使用传递过来的完整数据结构
        this.setData({
          orderData: parsedData,
          groupedItems: parsedData.warehouses || [],
          currentDate: this.formatCurrentDate(),
          selectedDate: this.formatCurrentDate(),
        });
      } catch (error) {
        console.error("解析出库数据失败:", error);
        wx.showToast({
          title: "数据错误",
          icon: "none",
        });
        wx.navigateBack();
      }
    } else {
      wx.showToast({
        title: "缺少订单数据",
        icon: "none",
      });
      wx.navigateBack();
    }
  },

  /**
   * 返回修改
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 如果页面卸载时还没有成功提交，不清除临时数据
    // 这样用户重新进入出库页面时可以恢复数据
    console.log("出库确认页面卸载，保留临时数据以便恢复");
  },

  /**
   * 确认出库
   */
  async confirmOutbound() {
    const { orderData, selectedDate } = this.data;
    console.log("格式化显示当前页面数据orderData:",orderData);
    if (
      !orderData ||
      !orderData.warehouses ||
      orderData.warehouses.length === 0
    ) {
      wx.showToast({
        title: "出库数据不完整",
        icon: "none",
      });
      return;
    }

    if (!selectedDate) {
      wx.showToast({
        title: "请选择出库日期",
        icon: "none",
      });
      return;
    }

    // 显示日期选择器
    this.setData({
      showDatePicker: true
    });
  },

  /**
   * 日期选择器确认
   */
  onDatePickerConfirm(event) {
    const selectedDate = this.formatDate(new Date(event.detail));
    const { orderData } = this.data;

    this.setData({
      showDatePicker: false,
      selectedDate: selectedDate
    });

    wx.showModal({
      title: "确认出库",
      content: `出库日期：${selectedDate}
总计：${parseFloat(orderData.total_packages).toFixed(2)}包，${orderData.total_pieces}件
确定要将这批货物出库吗？`,
      success: async (res) => {
        if (res.confirm) {
          await this.submitOutboundOrder();
        }
      },
    });
  },

  /**
   * 日期选择器取消
   */
  onDatePickerCancel() {
    this.setData({
      showDatePicker: false
    });
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 格式化当前日期
   */
  formatCurrentDate() {
    return this.formatDate(new Date());
  },

  /**
   * 提交出库单 - 使用统一批量出库接口
   */
  async submitOutboundOrder() {
    try {
      this.setData({ submitting: true });

      const { orderData } = this.data;

      // 构建批量出库数据
      const batchOutboundData = {
        warehouses: orderData.warehouses,
        total_packages: orderData.total_packages,
        total_pieces: orderData.total_pieces,
        operator: wx.getStorageSync("userInfo")?.userName || "unknown",
        notes: "微信小程序批量出库操作",
        operation_date: this.data.selectedDate, // 添加选择的操作日期
      };

      console.log('发送批量出库请求:', batchOutboundData);

      // 调用统一批量出库接口
      const response = await Api.warehouseBatchOutbound(batchOutboundData);

      if (response.data && response.data.code === 200) {
        wx.showToast({
          title: "出库成功",
          icon: "success",
        });

        // 设置全局标记，通知仓库页面清空待出库清单
        const app = getApp();
        if (!app.globalData) {
          app.globalData = {};
        }
        app.globalData.shouldClearOutboundList = true;
        console.log("已设置全局标记 shouldClearOutboundList = true");

        // 清除出库临时数据
        TempStorage.clearOutboundCart();
        console.log("出库成功，已清除临时数据");

        // 返回仓库管理页面
        setTimeout(() => {
          wx.navigateBack({
            delta: 2, // 返回两级页面
          });
        }, 1500);
      } else {
        wx.showToast({
          title: response.data?.message || "出库失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("出库失败:", error);
      wx.showToast({
        title: "出库失败",
        icon: "none",
      });
    } finally {
      this.setData({ submitting: false });
    }
  },



  /**
   * 获取服装信息用于弹窗 - 从contents构建
   */
  getClothingInfoFromContent(content) {
    if (!content) return null;
    return this.buildClothingInfo(content);
  },

  /**
   * 构建服装信息对象
   */
  buildClothingInfo(content) {
    if (!content) return null;

    if (content.is_oem) {
      return {
        oem_clothing_id: content.oem_clothing_id || content.clothing_id,
        oem_clothing_name: content.name,
        oem_supplier: content.supplier || content.oem_supplier || '',
        classification: content.classification || '',
        price: content.price || 0,
        img: content.img || [],
        is_oem: true,
        size: content.size || '',
        style: content.style || '',
        order_quantity: content.order_quantity || 0,
        shipments: content.shipments || 0,
        in_pcs: content.in_pcs || 0
      };
    } else {
      return {
        clothing_id: content.clothing_id,
        clothing_name: content.name,
        sku: content.sku,
        supplier: content.supplier || '',
        price: content.price || 0,
        img: content.img || [],
        is_oem: false,
        group_classification: content.group_classification || [],
        size: content.size || '',
        style: content.style || '',
        order_quantity: content.order_quantity || 0,
        shipments: content.shipments || 0
      };
    }
  },

  /**
   * 格式化当前日期
   */
  formatCurrentDate() {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  },

  /**
   * 点击服装内容
   */
  async onContentTap(e) {
    const { content } = e.currentTarget.dataset;
    const clothingInfo = this.getClothingInfoFromContent(content);
    await this.fetchAndShowClothingDetail(clothingInfo);
  },

  /**
   * 获取并显示服装详细信息 - 统一处理方法
   */
  async fetchAndShowClothingDetail(clothingInfo) {
    if (!clothingInfo) {
      wx.showToast({
        title: '服装信息不完整',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    try {
      let detailedClothingInfo;

      if (clothingInfo.is_oem) {
        const response = await Api.getOemClothingInfo({
          oem_clothing_id: clothingInfo.oem_clothing_id
        });
        detailedClothingInfo = this.parseOemClothingResponse(response);
      } else {
        const response = await Api.getClothingInfo({
          clothing_id: clothingInfo.clothing_id
        });
        detailedClothingInfo = this.parseClothingResponse(response);
      }

      this.setData({
        selectedClothingInfo: detailedClothingInfo,
        showClothingInfo: true,
      });

    } catch (error) {
      wx.showToast({
        title: '获取服装信息失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 解析OEM服装API响应
   */
  parseOemClothingResponse(response) {
    let oemData = null;
    if (response.data?.code === 200) {
      oemData = response.data.data;
    } else if (response.data?.oem_clothing_id) {
      oemData = response.data;
    } else if (response.oem_clothing_id) {
      oemData = response;
    }

    if (!oemData?.oem_clothing_id) {
      throw new Error('获取OEM服装信息失败');
    }

    return {
      oem_clothing_id: oemData.oem_clothing_id,
      oem_clothing_name: oemData.oem_clothing_name,
      oem_supplier: oemData.oem_supplier || '',
      classification: oemData.classification || '',
      price: oemData.price || 0,
      img: oemData.img || [],
      is_oem: true,
      size: oemData.size || '',
      style: oemData.style || '',
      order_quantity: oemData.order_quantity || 0,
      shipments: oemData.shipments || 0,
      in_pcs: oemData.in_pcs || 0
    };
  },

  /**
   * 解析普通服装API响应
   */
  parseClothingResponse(response) {
    let clothingData = null;
    if (response.data?.code === 200) {
      clothingData = response.data.data;
    } else if (response.data?.clothing_id) {
      clothingData = response.data;
    } else if (response.clothing_id) {
      clothingData = response;
    }

    if (!clothingData?.clothing_id) {
      throw new Error('获取服装信息失败');
    }

    return {
      clothing_id: clothingData.clothing_id,
      clothing_name: clothingData.clothing_name,
      sku: clothingData.sku || '',
      supplier: clothingData.supplier || '',
      price: clothingData.price || 0,
      img: clothingData.img || [],
      is_oem: false,
      group_classification: clothingData.group_classification || [],
      size: clothingData.size || '',
      style: clothingData.style || '',
      long_or_short_sleeve: clothingData.long_or_short_sleeve || '',
      pocket_type: clothingData.pocket_type || '',
      order_quantity: clothingData.order_quantity || 0,
      clipping_pcs: clothingData.clipping_pcs || 0,
      shipments: clothingData.shipments || 0
    };
  },

  /**
   * 关闭服装信息弹窗
   */
  onCloseClothingInfo() {
    this.setData({
      showClothingInfo: false,
      selectedClothingInfo: null,
    });
  },
});
