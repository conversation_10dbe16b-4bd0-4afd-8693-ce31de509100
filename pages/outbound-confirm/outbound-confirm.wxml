<!-- pages/outbound-confirm/outbound-confirm.wxml -->
<view class="confirm-container">
  <!-- 出库确认单卡片 -->
  <view class="order-card">
    <!-- 抬头：左边出库确认单，右边出库日期 -->
    <view class="card-header">
      <view class="title">出库: {{orderData.total_packages}}包 / {{orderData.total_pieces}}件</view>
      <view class="date">{{selectedDate}}</view>
    </view>
  </view>
  <!-- 出库明细 -->
  <view class="details-section">
    <view wx:for="{{groupedItems}}" wx:key="warehouse_id" class="warehouse-group">
      <view class="warehouse-header">
        <view class="warehouse-info">
          <view class="warehouse-name">{{item.warehouse_name}}</view>
          <van-tag type="primary" size="small">{{item.warehouse_address}}</van-tag>
        </view>
        <view class="warehouse-summary">
          <view class="summary-view">{{item.total_packages}}包 / {{item.total_pieces}}件</view>
        </view>
      </view>

      <view class="warehouse-items">
        <view wx:for="{{item.items}}" wx:for-item="outboundItem" wx:key="classification_code" class="outbound-item">
          <!-- 单货物包裹 -->
          <view wx:if="{{outboundItem.package_type === 'single'}}" class="item-content">
            <view class="item-main-info">
              <view class="clothing-name clickable" data-content="{{outboundItem.contents[0]}}" bindtap="onContentTap">
                {{outboundItem.clothing_name}}
              </view>
              <view class="outbound-formula">
                {{outboundItem.contents[0].original_quantity}}件/包 × {{outboundItem.package_count}}包 = {{outboundItem.total_pieces}}件
              </view>
            </view>
          </view>
          <!-- 多货物包裹 -->
          <view wx:else class="item-content multi-item">
            <view class="multi-contents">
              <view wx:for="{{outboundItem.contents}}" wx:for-item="content" wx:key="sku" class="content-row">
                <view class="content-name clickable" data-content="{{content}}" bindtap="onContentTap">
                  {{content.name}}
                </view>
                <view class="content-formula">{{content.outbound_quantity}}件</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <view class="action-button-wrapper">
      <van-button size="large" custom-class="action-button back-button" bind:click="goBack" disabled="{{submitting}}">
        返回修改
      </van-button>
    </view>
    <view class="action-button-wrapper">
      <van-button type="primary" size="large" custom-class="action-button confirm-button" loading="{{submitting}}" loading-view="出库中..." bind:click="confirmOutbound">
        确认出库
      </van-button>
    </view>
  </view>
</view>

<!-- 日期选择器弹窗 -->
<van-calendar
  show="{{ showDatePicker }}"
  min-date="{{ minDate }}"
  max-date="{{ maxDate }}"
  bind:close="onDatePickerCancel"
  bind:confirm="onDatePickerConfirm"
  color="#2c9678"
  confirm-text="确认"
  confirm-disabled-text="确认"
/>

<!-- 服装信息弹窗 -->
<van-popup show="{{ showClothingInfo }}" round position="center" custom-style="width: 90%; max-width: 600rpx; border-radius: 16rpx;" bind:close="onCloseClothingInfo">
  <view class="clothing-popup-container">
    <z-clothing-info-card
      clothingInfo="{{ selectedClothingInfo.is_oem ? null : selectedClothingInfo }}"
      oemClothingInfo="{{ selectedClothingInfo.is_oem ? selectedClothingInfo : null }}"
      isOem="{{ selectedClothingInfo.is_oem }}" />
  </view>
</van-popup>
<!-- 加载状态 -->
<van-loading wx:if="{{ loading }}" type="spinner" />