/* pages/outbound-confirm/outbound-confirm.wxss */

/* ==================== 基础布局 ==================== */
.confirm-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx 20rpx 160rpx 20rpx;
  box-sizing: border-box;
}


.date-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.selected-date {
  font-size: 32rpx;
  color: #1989fa;
  font-weight: bold;
}

/* ==================== 订单信息卡片 ==================== */
.order-card {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 24rpx 32rpx;
  background: linear-gradient(135deg, #1989fa 0%, #0c7cd5 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
}

.date {
  font-size: 26rpx;
  font-weight: 500;
}

.card-row {
  padding: 20rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.label {
  font-size: 26rpx;
  color: #666;
}

.value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.comparison-row {
  padding: 20rpx 32rpx;
  display: flex;
  justify-content: center;
}

.data-card {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  border: 2rpx solid #e9ecef;
}

.outbound-card {
  border-color: #1989fa;
  background: #e3f2fd;
}

.card-title {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.data-item:last-child {
  margin-bottom: 0;
}

.data-label {
  font-size: 24rpx;
  color: #666;
}

.data-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.highlight {
  color: #1989fa;
}

/* ==================== 明细列表 ==================== */
.details-section {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  
  /* 可以滚动 */
  overflow-y: auto;
  overflow-x: hidden;
  height: 81vh; 
}

.section-header {
  padding: 24rpx 32rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #eee;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.warehouse-group {
  margin-bottom: 0;
}

.warehouse-header {
  background: #e3f2fd;
  padding: 20rpx 32rpx;
  border-bottom: 1rpx solid #ddd;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.warehouse-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.warehouse-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.warehouse-summary {
  display: flex;
  align-items: center;
}

.summary-text {
  font-size: 24rpx;
  font-weight: 600;
  color: #1989fa;
  background: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid #1989fa;
}

.warehouse-items {
  padding: 0 32rpx;
}

/* ==================== 包裹类型标签样式 ==================== */
.package-type-tag {
  margin-bottom: 8rpx;
}

.outbound-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.outbound-item:last-child {
  border-bottom: none;
}

.item-content {
  margin-bottom: 0;
}

.item-main-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 14rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  gap: 16rpx;
}

.clothing-info {
  flex: 2;
  display: flex;
  align-items: center;
  min-width: 0;
}

.clothing-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.clothing-name.clickable {
  color: #1989fa;
  cursor: pointer;
  transition: color 0.3s;
}

.clothing-name.clickable:active {
  color: #0c7cd5;
}

.outbound-info {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 80rpx;
}

.outbound-formula {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
}

/* ==================== 多货物包裹样式 ==================== */
.multi-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 16rpx;
}

.multi-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
  padding: 12rpx 16rpx;
  background: #e3f2fd;
  border-radius: 8rpx;
}

.multi-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1989fa;
}

.multi-summary {
  font-size: 24rpx;
  color: #666;
}

.multi-contents {
  margin-bottom: 0;
}

.content-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 16rpx;
  margin-bottom: 6rpx;
  background: white;
  border-radius: 6rpx;
  border: 1rpx solid #e9ecef;
}

.content-row:last-child {
  margin-bottom: 0;
}

.content-name {
  flex: 1;
  font-size: 24rpx;
  color: #333;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.content-name.clickable {
  color: #1989fa;
  cursor: pointer;
  transition: color 0.3s;
}

.content-name.clickable:active {
  color: #0c7cd5;
}

.content-formula {
  flex: 0 0 auto;
  font-size: 22rpx;
  color: #666;
  margin-left: 12rpx;
  white-space: nowrap;
}

/* ==================== 底部操作栏 ==================== */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1rpx solid #eee;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  z-index: 100; /* 确保van-calendar在最上层 */
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.action-button-wrapper {
  flex: 1;
}

.action-button {
  width: 100% !important;
  border-radius: 12rpx !important;
  height: 80rpx;
  font-size: 28rpx;
}

.back-button {
  background: #f8f9fa !important;
  color: #666 !important;
  border: 1rpx solid #ddd !important;
}

.confirm-button {
  background: #1989fa !important;
  color: white !important;
  border: 1rpx solid #1989fa !important;
}

/* ==================== 服装信息弹窗 ==================== */
.clothing-popup-container {
  background: transparent;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 750rpx) {
  .card-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
  }

  .comparison-row {
    padding: 20rpx;
  }

  .data-card {
    width: 100%;
  }

  .item-main-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }

  .outbound-info {
    align-self: flex-end;
  }

  .bottom-actions {
    flex-direction: column;
    gap: 12rpx;
  }
}
