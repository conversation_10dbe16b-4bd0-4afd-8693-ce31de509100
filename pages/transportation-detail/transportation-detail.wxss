/* ==================== 页面容器 ==================== */
.page-container {
  background: #f5f5f5;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* ==================== 顶部操作栏 ==================== */
.header-section {
  padding: 20rpx;
  background: #f5f5f5;
  flex-shrink: 0;
}

/* ==================== 货运信息卡片（照搬货运信息页面样式） ==================== */
.card {
  width: calc(100% - 16rpx);
  height: 220rpx;
  border-radius: 5px;
  background-color: #3563632c;
  box-shadow: 0 10px 6px -6px rgba(30, 30, 30, 0.1),
    12px 0 8px -8px rgba(50, 50, 50, 0.1);
  margin: 8rpx;
  position: relative;
}

.transportation {
  position: relative;
  margin-left: 30rpx;
  margin-right: 30rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.detail {
  width: 100%;
  height: 140rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.a {
  flex: 3;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.b {
  display: flex;
  flex-direction: row;
  justify-content:flex-start;
  gap: 8rpx;
}

.supplier {
  margin-top: 10rpx;
  margin-bottom: 15rpx;
  margin-left: 10rpx;
  width: 180rpx;
  font-size: 35rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.weight {
  width: auto;
}

.pieces {
  width: auto;
}

.total_package_quantity {
  flex: 2;
  margin-top: 10rpx;
  width: 220rpx;
  font-size: 60rpx;
  text-align: right;
  flex-shrink: 0;
}

.date {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
  gap: 8rpx;
}

.status-switch-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6rpx;
  min-width: 80rpx;
  flex-shrink: 0;
}

.status-switch {
  transform: scale(0.8);
  flex-shrink: 0;
  margin-top: 2rpx;
}

.status-text {
  font-size: 20rpx;
  color: #666;
  text-align: center;
  white-space: nowrap;
  line-height: 1.2;
}

/* ==================== 明细列表区域 ==================== */
.details-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
  padding: 0 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 8rpx;
  flex-shrink: 0;
}

.section-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

.section-subtitle {
  font-size: 28rpx;
  color: #666;
}

/* ==================== 明细列表 ==================== */
.details-list {
  flex: 1;
  height: 0;
  padding: 0 0 20rpx 0;
  box-sizing: border-box;
}

.detail-item {
  background: white;
  border-radius: 12rpx;
  padding: 12rpx;
  margin-bottom: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #e9ecef;
  transition: all 0.3s;
}

.detail-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.item-content {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

/* ==================== 包裹序号 ==================== */
.package-index {
  flex-shrink: 0;
  width: 50rpx;
  height: 50rpx;
  background: #e3f2fd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.index-number {
  font-size: 26rpx;
  font-weight: 600;
  color: #1976d2;
}

/* ==================== 服装信息布局（参照入库页面） ==================== */
.item-main-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;

  border-radius: 8rpx;
  gap: 12rpx;
}

.clothing-groups {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  min-width: 0;
}

.clothing-group {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 16rpx;
  min-height: 40rpx;
}

.clothing-name {
  font-size: 32rpx;
  font-weight: 600;
  line-height: 40rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  margin-left: 24rpx; /* 服装名称往右移动1个字符 */
}

.clothing-name.clickable {
  cursor: pointer;
  transition: color 0.3s;
}

.clothing-name.clickable:active {
  opacity: 0.7;
}

.piece-count {
  font-size: 28rpx;
  font-weight: 600;
  color: #1989fa;
  white-space: nowrap;
  line-height: 40rpx;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-right: 72rpx; /* 总件数位置往左移3个字符 */
}

.package-info {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 100rpx;
}

.package-formula {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  line-height: 40rpx;
  display: flex;
  align-items: center;
}

/* ==================== 底部占位 ==================== */
.bottom-placeholder {
  padding: 20rpx 0;
  text-align: center;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999;
}

/* ==================== 服装信息弹窗 ==================== */
.clothing-popup-container {
  width: 100%;
  min-width: 600rpx;
  max-width: 90vw;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-sizing: border-box;
}

/* 确保弹窗内的组件样式正常 */
.clothing-popup-container .clothing-card {
  margin: 0 !important;
  box-shadow: none !important;
  border-radius: 16rpx !important;
  background: transparent !important;
}

/* 弹窗内卡片容器的内边距调整 */
.clothing-popup-container .card-container {
  padding: 32rpx !important;
}
