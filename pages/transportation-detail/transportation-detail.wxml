<view class="page-container">
  <!-- 顶部货运信息卡片 -->
  <view class="header-section">
    <view class="card">
      <view class="transportation">
        <view class="detail">
          <view class="a">
            <view class="supplier">{{transportationInfo.supplier || '未设置'}}</view>
            <view class="b">
              <van-tag color="#685e4899" wx:if="{{transportationInfo.weight}}" type="primary" size="large" class="weight">
                {{transportationInfo.weight}} kg
              </van-tag>
              <van-tag color="#7aa386" type="primary" size="large" class="pieces">
                {{transportationInfo.total_pcs || 0}}件
              </van-tag>
            </view>
          </view>
          <view class="total_package_quantity">{{totalPackages}}包</view>
        </view>
        <view class="date">
          <van-tag type="primary" color="#2775b699" size="large">{{transportationInfo.date_out || '未设置'}}</van-tag>
          <van-tag type="primary" color="#f47a5599" size="large">{{transportationInfo.days || 0}}天</van-tag>
          <view class="status-switch-container">
            <van-switch
              class="status-switch"
              checked="{{ checked }}"
              active-color="#07c160"
              disabled="{{ arrivalSwitchDisabled }}"
              bind:change="onChangeWitch"
            />
            <view class="status-text">{{checked ? '已到货' : '未到货'}}</view>
          </view>
        </view>
      </view>

      <van-calendar
        show="{{ showDate }}"
        type="single"
        poppable="{{ true }}"
        show-confirm="{{ true }}"
        confirm-text="确认日期"
        bind:close="onCloseDatePopup"
        bind:confirm="onConfirm"
        color="#2c9678"
        min-date="{{ minDate }}"
        max-date="{{ maxDate }}"
        default-date="{{ currentDate }}"
      />
    </view>
  </view>

  <!-- 货运明细列表 -->
  <view class="details-section">
    <view class="section-header">
      <view class="section-title">货运明细</view>
      <view class="section-subtitle">共 {{totalPackages}} 个包裹 / {{transportationInfo.total_pcs || 0}} 件衣服</view>
    </view>

    <scroll-view class="details-list" scroll-y>
      <view wx:for="{{transportationDetail}}" wx:key="index" class="detail-item">
        <view class="item-content">
          <!-- 包裹序号 -->
          <view class="package-index">
            <view class="index-number">{{item.index + 1}}</view>
          </view>

          <!-- 主要信息横向布局 - 参照入库页面格式 -->
          <view class="item-main-info">
            <!-- 左侧：服装信息组 -->
            <view class="clothing-groups">
              <view wx:for="{{item.items}}" wx:key="clothing_id" wx:for-item="clothing" class="clothing-group">
                <view
                  class="clothing-name clickable"
                  style="{{clothing.img && clothing.img.length > 0 ? 'color: #07c160' : 'color: #000'}}"
                  data-clothing_id="{{clothing.clothing_id}}"
                  data-oem="{{clothing.oem}}"
                  data-img="{{clothing.img}}"
                  bindtap="onClothingNameTap"
                  bindlongpress="onClothingLongPress">
                  {{clothing.clothing_name}}
                </view>
                <view class="piece-count">{{clothing.out_pcs}}件</view>
              </view>
            </view>
            <!-- 右侧：包装信息 -->
            <view class="package-info">
              <view class="package-formula">{{item.QUP}}*{{item.package_quantity}}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部占位 -->
      <view class="bottom-placeholder">
        <view class="placeholder-text">到底了...</view>
      </view>
    </scroll-view>
  </view>
</view>

<!-- 服装信息弹窗 -->
<van-popup show="{{ showClothingInfo }}" round position="center" custom-style="max-width: 90vw; max-height: 80vh; padding: 0; background: transparent;" bind:close="onCloseClothingInfo">
  <view class="clothing-popup-container">
    <z-clothing-info-card clothingInfo="{{clothingInfo}}" oemClothingInfo="{{oemClothingInfo}}" isOem="{{oem}}"></z-clothing-info-card>
  </view>
</van-popup>
