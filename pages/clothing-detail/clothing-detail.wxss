/* 服装详情页面样式 */
.clothing-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 服装信息卡片 */
.clothing-info-card {
  margin-bottom: 20rpx;
}

/* ==================== 选项卡样式 ==================== */

/* 选项卡卡片 */
.tabs-card {
  background-color: #3563632c;
  box-shadow: 0 10px 6px -6px rgba(30, 30, 30, 0.1),
    12px 0 8px -8px rgba(50, 50, 50, 0.1);
  border-radius: 10rpx;
  overflow: hidden;
  height: 70vh;
  display: flex;
  flex-direction: column;
}

/* 选项卡头部 */
.tabs-header {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1rpx solid #efefef;
  flex-shrink: 0;
}

.tab-item {
  font-size: 20rpx;
  flex: 1;
  padding: 16rpx 20rpx;
  text-align: center;
  position: relative;
  cursor: pointer;
}

.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: #2c9678;
}

.tab-text {
  font-size: 26rpx;
  font-weight: 500;
  color: #666;
}

.tab-item.active .tab-text {
  color: #2c9678;
  font-weight: 600;
}

/* 选项卡内容 */
.tabs-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* ==================== 库存明细样式 ==================== */

.inventory-header {
  padding: 10rpx 30rpx;
  border-bottom: 1rpx solid #efefef;
  flex-shrink: 0;
}

.inventory-stats {
  text-align: center;
}

.stats-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c9678;
}

.inventory-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 30rpx;
}

.inventory-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 10rpx 16rpx;
  margin: 6rpx 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #e9ecef;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.inventory-item:last-child {
  margin-bottom: 0;
}

.warehouse-info {
  flex: 2;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

}

.warehouse-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
  flex: 1;
}

.warehouse-address {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  
}

.stock-details {
  flex: 2;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center; 
}

.stock-info {
  font-size: 26rpx;
  font-weight: 600;
  color: #2c9678;
  text-align: right;
  flex: 1;
}

.detail-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  text-align: right;
  flex: 1;
}

/* ==================== 日志样式 ==================== */

.logs-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 30rpx;
}

/* 日历表组件样式 */
.calendar-section {
  padding: 0;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
  margin-top: 16rpx;
}



/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}


