// pages/clothing-detail/clothing-detail.js
import Api from "../../utils/api.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 服装信息
    clothingInfo: null,
    isOem: false,

    // 选项卡
    activeTab: "inventory", // inventory: 库存明细, logs: 仓库日志

    // 库存明细
    inventoryList: [],
    loadingInventory: false,
    totalPackages: 0,
    totalPieces: 0,

    // 加载状态
    loading: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { clothingId, oemClothingId, isOem, hasData } = options;

    if (!clothingId && !oemClothingId) {
      wx.showToast({
        title: "参数错误",
        icon: "none",
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({
      isOem: isOem === "true",
      clothingId: clothingId,
      oemClothingId: oemClothingId,
      hasData: hasData === "true",
    });

    // 检查是否有传递的服装数据
    if (hasData === "true") {
      const globalData = getApp().globalData.clothingDetailInfo;
      if (globalData && globalData.clothingInfo) {
        // 使用传递的服装数据，无需重新请求
        this.setData({
          clothingInfo: globalData.clothingInfo,
          isOem: globalData.isOem,
        });

        // 清除全局数据，避免内存泄漏
        getApp().globalData.clothingDetailInfo = null;

        console.log("使用传递的服装数据，跳过服装详情请求");
      } else {
        // 如果全局数据不存在，仍然请求服装详情
        this.loadClothingDetail();
      }
    } else {
      // 没有传递数据，正常请求服装详情
      this.loadClothingDetail();
    }

    // 加载库存明细
    this.loadInventoryDetail();
  },

  /**
   * 加载服装详情信息
   */
  async loadClothingDetail() {
    this.setData({ loading: true });
    console.log("this.data.isOem", this.data);
    try {
      let result;

      if (this.data.isOem) {
        result = await Api.getOemClothingDetail({
          oem_clothing_id: this.data.oemClothingId,
        });
        console.log("result", result);
      } else {
        result = await Api.getClothingDetail({
          clothing_id: this.data.clothingId,
        });
        console.log("result", result);
      }

      if (result.data && result.data.code === 200) {
        console.log("result.data.data._doc", result.data.data);
        const clothingInfo = result.data.data;

        // 处理服装信息
        this.processClothingInfo(clothingInfo);

        this.setData({
          clothingInfo: clothingInfo,
        });
      } else {
        wx.showToast({
          title: result.data?.message || "加载失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载服装详情失败:", error);
      wx.showToast({
        title: "加载失败",
        icon: "none",
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 处理服装信息
   */
  processClothingInfo(clothingInfo) {
    // 处理图片列表
    let imgList = [];
    if (clothingInfo.img && Array.isArray(clothingInfo.img)) {
      clothingInfo.img.forEach((img) => {
        if (img && img.url) {
          imgList.push(img.url);
        }
      });
    }
    clothingInfo.imgList = imgList;
    clothingInfo.hasImages = imgList.length > 0;

    // 计算进度百分比
    let totalPcs, shipments;
    if (this.data.isOem) {
      totalPcs = clothingInfo.in_pcs || 0;
      shipments = clothingInfo.shipments || 0;
    } else {
      totalPcs = clothingInfo.clipping_pcs || 0;
      shipments = clothingInfo.shipments || 0;
    }

    const percent = totalPcs > 0 ? (shipments / totalPcs) * 100 : 0;
    clothingInfo.percent = Math.min(Math.max(percent, 0), 100);
    clothingInfo.percentWidth = Math.min(Math.max(percent * 5.5, 190), 550);
  },

  /**
   * 加载库存明细
   */
  async loadInventoryDetail() {
    this.setData({ loadingInventory: true });

    try {
      const params = this.data.isOem
        ? { oem_clothing_id: this.data.oemClothingId }
        : { clothing_id: this.data.clothingId };

      const result = await Api.getClothingInventory(params);

      if (result.data && result.data.code === 200) {
        const inventoryList = result.data.data || [];

        // 计算总计数据
        let totalPackages = 0;
        let totalPieces = 0;

        inventoryList.forEach((item) => {
          // 有小数就保留 2 位，否则不显示小数
          item.total_packages = +item.total_packages.toFixed(
            item.total_packages % 1 === 0 ? 0 : 2
          );
          totalPackages += item.total_packages || 0;
          totalPieces += item.total_pieces || 0;
          totalPackages = +totalPackages.toFixed(
            totalPackages % 1 === 0 ? 0 : 2
          );

        });

        this.setData({
          inventoryList: inventoryList,
          totalPackages: totalPackages,
          totalPieces: totalPieces,
        });
      } else {
        wx.showToast({
          title: result.data?.message || "加载库存失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载库存明细失败:", error);
      wx.showToast({
        title: "加载库存失败",
        icon: "none",
      });
    } finally {
      this.setData({ loadingInventory: false });
    }
  },

  /**
   * 切换选项卡
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
  },

  /**
   * 处理calendar-table组件的查询事件
   */
  onCalendarQuery(e) {
    const { start_date, end_date, clothing_id, oem_clothing_id } = e.detail;
    const currentClothingId = this.data.isOem
      ? this.data.oemClothingId
      : this.data.clothingId;
    console.log("服装详情页日历查询事件:", {
      start_date,
      end_date,
      clothing_id,
      oem_clothing_id,
      currentClothingId,
      isOem: this.data.isOem,
    });

    // 构建查询参数
    let queryParams = `start_date=${start_date}&end_date=${end_date}`;

    if (this.data.isOem) {
      // OEM服装：使用oem_clothing_id参数
      queryParams += `&oem_clothing_id=${
        oem_clothing_id || currentClothingId
      }&isOem=true`;
    } else {
      // 普通服装：使用clothing_id参数
      queryParams += `&clothing_id=${
        clothing_id || currentClothingId
      }&isOem=false`;
    }

    // 跳转到日历日志页面查看查询结果
    wx.navigateTo({
      url: `/pages/calendar-logs/calendar-logs?${queryParams}`,
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 设置导航栏标题
    const title = this.data.isOem ? "OEM服装详情" : "服装详情";
    wx.setNavigationBarTitle({
      title: title,
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 刷新数据
    this.loadClothingDetail();
    this.loadInventoryDetail();

    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
});
