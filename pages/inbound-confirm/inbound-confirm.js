// pages/inbound-confirm/inbound-confirm.js
import Api from "../../utils/api.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 入库单数据
    orderData: null,
    // 按仓库分组的入库项
    groupedItems: [],
    // 加载状态
    loading: false,
    // 提交状态
    submitting: false,
    // 服装详情弹窗
    showClothingInfo: false,
    clothingInfo: {},
    oemClothingInfo: {},
    isOem: false,
    inbound_status: "",
    // 日期选择器相关
    showDatePicker: false,
    selectedDate: "",
    minDate: new Date(2020, 0, 1).getTime(),
    maxDate: new Date().getTime(),
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    //解析options
    const parsedOptions = JSON.parse(decodeURIComponent(options.orderData));
    console.log("解析后的页面参数666:", parsedOptions);
    const { orderData } = options;

    if (orderData) {
      try {
        const parsedData = JSON.parse(decodeURIComponent(orderData));
        console.log("解析后的订单数据:", parsedData);
        const groupedItems = this.getGroupedItems(parsedData);
        this.setData({
          orderData: parsedData,
          groupedItems: groupedItems,
          inbound_status: parsedData.transportation_info?.inbound_status || "",
          selectedDate: this.formatCurrentDate(),
        });
      } catch (error) {
        console.error("解析订单数据失败:", error);
        wx.showToast({
          title: "数据错误",
          icon: "none",
        });
        wx.navigateBack();
      }
    } else {
      wx.showToast({
        title: "缺少订单数据",
        icon: "none",
      });
      wx.navigateBack();
    }
  },

  /**
   * 返回修改
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 确认入库
   */
  async confirmInbound() {
    const { orderData, selectedDate } = this.data;

    if (
      !orderData ||
      !orderData.inbound_items ||
      orderData.inbound_items.length === 0
    ) {
      wx.showToast({
        title: "入库数据不完整",
        icon: "none",
      });
      return;
    }

    if (!selectedDate) {
      wx.showToast({
        title: "请选择入库日期",
        icon: "none",
      });
      return;
    }

    // 显示日期选择器
    this.setData({
      showDatePicker: true
    });
  },

  /**
   * 日期选择器确认
   */
  onDatePickerConfirm(event) {
    const selectedDate = this.formatDate(new Date(event.detail));
    const { orderData } = this.data;

    this.setData({
      showDatePicker: false,
      selectedDate: selectedDate
    });

    wx.showModal({
      title: "确认入库",
      content: `入库日期：${selectedDate}
总计：${orderData.total_packages}包，${orderData.total_pieces}件
确定要将这批货物入库吗？`,
      success: async (res) => {
        if (res.confirm) {
          await this.submitInboundOrder();
        }
      },
    });
  },

  /**
   * 日期选择器取消
   */
  onDatePickerCancel() {
    this.setData({
      showDatePicker: false
    });
  },



  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 格式化当前日期
   */
  formatCurrentDate() {
    return this.formatDate(new Date());
  },

  /**
   * 提交入库单
   */
  async submitInboundOrder() {
    try {
      this.setData({ submitting: true });

      const { orderData } = this.data;
      console.log("准备提交的订单数据1759:", JSON.stringify(orderData.inbound_items, null, 2));

      // 构造入库单数据 - 使用扁平化数据结构
      const flattenedItems = this.flattenInboundItems(orderData.inbound_items);
      const inboundOrderData = {
        transportation_id: orderData.transportation_id,
        inbound_items: flattenedItems,
        total_packages: orderData.total_packages,
        total_pieces: orderData.total_pieces,
        operator: wx.getStorageSync("userInfo")?.userName || "unknown",
        inbound_status: this.checkInboundStatus(),
        operation_date: this.data.selectedDate, // 添加选择的操作日期
      };

      console.log(
        "准备提交的入库单数据:",
        JSON.stringify(inboundOrderData, null, 2)
      );
      console.log("扁平化后的入库项:", JSON.stringify(flattenedItems, null, 2));

      // 调用API创建入库单
      const response = await Api.createInboundOrder(inboundOrderData);

      if (response.data.code === 200) {
        const result = response.data.data || {};
        const actualCreated = result.actual_created || 0;
        const totalRequested = result.total_requested || 0;
        console.log("入库结果:", result);

        if (actualCreated === 0) {
          wx.showToast({
            title: "所有包裹已存在",
            icon: "none",
            duration: 2000
          });
        } else if (actualCreated < totalRequested) {
          wx.showToast({
            title: `部分入库成功(${actualCreated}/${totalRequested})`,
            icon: "success",
            duration: 2000
          });
        } else {
          wx.showToast({
            title: "入库成功",
            icon: "success",
          });
        }

        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          // 返回到货运详情页面或仓库页面
          wx.navigateBack({
            delta: 2, // 返回两级页面
          });
        }, 2000);
      } else {
        wx.showToast({
          title: response.data.message || "入库失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("提交入库单失败:", error);
      wx.showToast({
        title: "入库失败",
        icon: "none",
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 点击服装名称
   */
  async onClothingNameTap(e) {
    const { clothingInfo, isOem } = e.currentTarget.dataset;
    console.log("点击服装名称:", clothingInfo, isOem);

    try {
      if (isOem) {
        // 显示 OEM 服装详情
        const res = await Api.getOemClothingInfo({
          oem_clothing_id: clothingInfo.clothing_id,
        });
        console.log("OEM服装信息:", res);

        this.setData({
          showClothingInfo: true,
          oemClothingInfo: res.data,
          clothingInfo: {},
          isOem: true,
        });
      } else {
        // 显示普通服装详情
        const res = await Api.getClothingInfo({
          clothing_id: clothingInfo.clothing_id,
        });
        console.log("普通服装信息:", res);

        this.setData({
          showClothingInfo: true,
          clothingInfo: res.data,
          oemClothingInfo: {},
          isOem: false,
        });
      }
    } catch (error) {
      console.error("获取服装信息失败:", error);
      wx.showToast({
        title: "获取服装信息失败",
        icon: "none",
      });
    }
  },

  /**
   * 关闭服装信息弹窗
   */
  onCloseClothingInfo() {
    this.setData({
      showClothingInfo: false,
      clothingInfo: {},
      oemClothingInfo: {},
    });
  },

  /**
   * 将包裹数据扁平化为单个服装项
   */
  flattenInboundItems(inboundItems) {
    const flattenedItems = [];

    inboundItems.forEach((item) => {
      // 检查是否有flatItems数据
      if (!item.flatItems || item.flatItems.length === 0) {
        console.warn("入库项缺少flatItems数据:", item);
        return;
      }

      // 使用扁平化数据（新项目不需要兼容旧数据结构）
      item.flatItems.forEach((flatItem) => {
        flattenedItems.push({
          warehouse_id: item.warehouse_id,
          transportation_id: item.transportation_id,
          series_number: item.series_number,
          clothing_id: flatItem.clothing_id,
          clothing_name: flatItem.clothing_name,
          oem: flatItem.oem,
          out_pcs: flatItem.out_pcs,
          pieces_per_package: flatItem.pieces_per_package || item.QUP,
          // 对于混合包裹，直接使用该服装的实际件数
          inbound_pcs: flatItem.out_pcs,
          package_quantity: item.inbound_quantity,
          supplier: flatItem.supplier || "",
        });
      });
    });

    console.log("扁平化处理结果:", flattenedItems);
    return flattenedItems;
  },

  /**
   * 按仓库分组显示入库项
   */
  getGroupedItems(orderData) {
    if (!orderData || !orderData.inbound_items) {
      return [];
    }

    const grouped = {};
    orderData.inbound_items.forEach((item) => {
      const warehouseId = item.warehouse_id;
      if (!grouped[warehouseId]) {
        grouped[warehouseId] = {
          warehouse_id: warehouseId,
          warehouse_name: item.warehouse_name,
          items: [],
          total_packages: 0,
          total_pieces: 0,
        };
      }
      grouped[warehouseId].items.push(item);
      grouped[warehouseId].total_packages += item.inbound_quantity;
      grouped[warehouseId].total_pieces += item.total_inbound_pieces;
    });

    return Object.values(grouped);
  },

  /**
   * 通过准备入库+已入库的总件数判断inbound_status
   */
  checkInboundStatus() {
    const { orderData } = this.data;
    const { already_inbound_pieces, total_pieces } = orderData;
    if (
      already_inbound_pieces + total_pieces >=
      orderData.transportation_info.total_pcs
    ) {
      return "full_inbound";
    } else if (already_inbound_pieces > 0) {
      return "partial_inbound";
    } else {
      return "not_inbound";
    }
  },
});
