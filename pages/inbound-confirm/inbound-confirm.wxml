<!-- pages/inbound-confirm/inbound-confirm.wxml -->
<view class="confirm-container">
  <!-- 订单信息卡片 -->
  <view class="order-card">
    <!-- 第一行：入库确认单、发货编码 -->
    <view class="card-header">
      <view class="title">入库确认单</view>
      <van-tag type="primary">{{orderData.transportation_id}}</van-tag>
    </view>
    <!-- 第二行：供应商、发货日期 -->
    <view class="card-row">
      <view class="info-item">
        <view class="label">供应商：</view>
        <view class="value">{{orderData.transportation_info.supplier}}</view>
      </view>
      <view class="info-item">
        <view class="label">发货日期：</view>
        <view class="value">{{orderData.transportation_info.date_out}}</view>
      </view>
    </view>
    <!-- 第三行：到货数据 vs 已入库 vs 准备入库对比 -->
    <view class="comparison-row">
      <!-- 左边：到货总包数和总件数 -->
      <view class="data-card arrival-card">
        <view class="card-title">到货</view>
        <view class="data-item">
          <view class="data-label">包数：</view>
          <view class="data-value">
            {{orderData.transportation_info.total_package_quantity || 0}}包
          </view>
        </view>
        <view class="data-item">
          <view class="data-label">件数：</view>
          <view class="data-value">{{orderData.transportation_info.total_pcs || 0}}件</view>
        </view>
      </view>
      <!-- 中间：已入库数据（如果有的话） -->
      <view class="data-card already-inbound-card" wx:if="{{orderData.already_inbound_packages > 0 || orderData.already_inbound_pieces > 0}}">
        <view class="card-title">已入库</view>
        <view class="data-item">
          <view class="data-label">包数：</view>
          <view class="data-value warning">{{orderData.already_inbound_packages || 0}}包</view>
        </view>
        <view class="data-item">
          <view class="data-label">件数：</view>
          <view class="data-value warning">{{orderData.already_inbound_pieces || 0}}件</view>
        </view>
      </view>
      <!-- 右边：准备入库总包数和总件数 -->
      <view class="data-card inbound-card">
        <view class="card-title">准备入库</view>
        <view class="data-item">
          <view class="data-label">包数：</view>
          <view class="data-value highlight">{{orderData.total_packages}}包</view>
        </view>
        <view class="data-item">
          <view class="data-label">件数：</view>
          <view class="data-value highlight">{{orderData.total_pieces}}件</view>
        </view>
      </view>
    </view>
  </view>
  <!-- 按仓库分组的入库明细 -->
  <view class="details-section">
    <view class="section-header">
      <view class="section-title">入库明细</view>
      <view class="selected-date">{{selectedDate}}</view>
    </view>
    <view wx:for="{{groupedItems}}" wx:key="warehouse_id" class="warehouse-group">
      <!-- 仓库信息 - 参照待入库清单仓库抬头格式，信息放在一排 -->
      <view class="warehouse-header">
        <view class="warehouse-name">{{item.warehouse_name}}</view>
        <view class="warehouse-summary">{{item.total_packages}}包 / {{item.total_pieces}}件</view>
        <van-tag type="success" size="small">仓库</van-tag>
      </view>
      <!-- 该仓库的入库项目 -->
      <view class="warehouse-items">
        <view wx:for="{{item.items}}" wx:for-item="detail" wx:key="index" class="detail-item">
          <view class="item-content">
            <!-- 主要信息横向布局 - 参照待入库清单格式 -->
            <view class="item-main-info">
              <!-- 左侧：服装信息组 -->
              <view class="clothing-groups">
                <!-- 显示所有服装 -->
                <view wx:for="{{detail.flatItems}}" wx:key="clothing_id" class="clothing-group">
                  <view class="clothing-name clickable" style="{{item.img && item.img.length > 0 ? 'color: #07c160' : 'color: #000'}}" data-clothing-info="{{item}}" data-is-oem="{{item.oem === '是' || item.oem === 'yes'}}" bindtap="onClothingNameTap">
                    {{item.clothing_name}}
                  </view>
                  <view class="piece-count">{{detail.flatItems.length > 1 ? item.out_pcs : (detail.inbound_quantity * detail.QUP)}}件</view>
                </view>
              </view>
              <!-- 右侧：包装信息 -->
              <view class="package-info">
                <text class="package-formula">{{detail.QUP}}*{{detail.inbound_quantity}}</text>
              </view>
            </view>
            <!-- 入库信息区域 -->
            <view class="item-actions-row" wx:if="{{detail.style}}">
              <view class="inbound-info">
                <view class="inbound-label">款式</view>
                <view class="inbound-value">{{detail.style}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <view class="action-button-wrapper">
      <van-button size="large" custom-class="action-button back-button" bind:click="goBack" disabled="{{submitting}}">
        返回修改
      </van-button>
    </view>
    <view class="action-button-wrapper">
      <van-button type="primary" size="large" custom-class="action-button confirm-button" loading="{{submitting}}" loading-text="入库中..." bind:click="confirmInbound">
        确认入库
      </van-button>
    </view>
  </view>
</view>
<!-- 日期选择器弹窗 -->
<van-calendar
  show="{{ showDatePicker }}"
  min-date="{{ minDate }}"
  max-date="{{ maxDate }}"
  bind:close="onDatePickerCancel"
  bind:confirm="onDatePickerConfirm"
  color="#2c9678"
  confirm-text="确认"
  confirm-disabled-text="确认"
/>

<!-- 服装信息弹窗 -->
<van-popup show="{{ showClothingInfo }}" round position="center" custom-style="max-width: 90vw; max-height: 80vh; padding: 0; background: transparent;" bind:close="onCloseClothingInfo">
  <view class="clothing-popup-container">
    <z-clothing-info-card clothingInfo="{{clothingInfo}}" oemClothingInfo="{{oemClothingInfo}}" isOem="{{isOem}}"></z-clothing-info-card>
  </view>
</van-popup>