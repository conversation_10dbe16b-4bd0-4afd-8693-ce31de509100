/* pages/inbound-confirm/inbound-confirm.wxss */
.confirm-container {
  padding: 20rpx;

  background-color: #f5f5f5;
  min-height: 95vh;
}


.selected-date {
  font-size: 32rpx;
  color: #1989fa;
  font-weight: bold;
}

/* 订单信息卡片 */
.order-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 第二行：供应商、发货日期 */
.card-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  flex: 1;
}

.label {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
}

/* 第三行：数据对比 */
.comparison-row {
  display: flex;
  gap: 8rpx;
  flex-wrap: nowrap;
}

.data-card {
  flex: 1;
  min-width: 180rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 12rpx;
  border: 2rpx solid #e9ecef;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.arrival-card {
  border-color: #ffc107;
  background: #fff8e1;
}

.already-inbound-card {
  border-color: #ff9800;
  background: #fff3e0;
}

.inbound-card {
  border-color: #28a745;
  background: #e8f5e8;
}

.card-title {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  margin-bottom: 8rpx;
  font-weight: bold;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6rpx;
  gap: 4rpx;
}

.data-item:last-child {
  margin-bottom: 0;
}

.data-label {
  font-size: 24rpx;
  color: #666;
  flex-shrink: 0;
  white-space: nowrap;
}

.data-value {
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
  text-align: right;
  word-wrap: break-word;
  overflow-wrap: break-word;
  line-height: 1.3;
  min-width: 0;
}

.highlight {
  color: #1989fa;
  font-weight: bold;
}

.warning {
  color: #ff9800;
  font-weight: bold;
}

/* 明细区域 */
.details-section {
  overflow-y: auto;
  overflow-x: hidden;
  -ms-overflow-style: none;
  scrollbar-width: none;
  max-height: 63vh;
  min-height: 40vh;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 仓库分组 */
.warehouse-group {
  margin-bottom: 24rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  overflow: hidden;
}

.warehouse-header {
  background: #f8f9fa;
  padding: 10rpx 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2rpx solid #f0f0f0;
  gap: 16rpx;
}

.warehouse-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  flex-shrink: 0;
}

.warehouse-summary {
  font-size: 26rpx;
  color: #666;
  flex: 1;
  text-align: center;
}

/* 仓库内的项目 */
.warehouse-items {
  background: white;
}

.detail-item {
  background: white;
  border-radius: 10rpx;
  padding: 12rpx;
  margin: 12rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.item-content {
  margin-bottom: 0;
}

/* ==================== 服装信息布局（参照待入库清单） ==================== */
.item-main-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 14rpx;
  border-radius: 8rpx;
  gap: 16rpx;
}

.clothing-groups {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
  min-width: 0;
}

.clothing-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10rpx;
}

.clothing-name {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.clothing-name.clickable {
  color: #1989fa;
  cursor: pointer;
  transition: color 0.3s;
}

.clothing-name.clickable:active {
  color: #0c7cd5;
}

.piece-count {
  font-size: 24rpx;
  font-weight: 600;
  color: #1989fa;
  white-space: nowrap;
}

.package-info {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 80rpx;
}

.package-formula {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
}

/* ==================== 入库信息区域（参照操作区域样式） ==================== */
.item-actions-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12rpx;
  margin-top: 10rpx;
  padding: 10rpx 14rpx;
  background: #e8f5e8;
  border-radius: 8rpx;
  border: 1rpx solid #c8e6c9;
}

.inbound-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  align-items: center;
  flex: 1;
  min-width: 60rpx;
}

.inbound-label {
  font-size: 20rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.inbound-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}

/* 底部操作区 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx 40rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 40rpx;
  z-index: 100; /* 确保van-calendar在最上层 */
}

.action-button-wrapper {
  flex: 1;
}

.action-button {
  width: 100% !important;
  padding: 20rpx 30rpx !important;
  border-radius: 12rpx !important;
  font-size: 32rpx !important;
  font-weight: bold !important;
}

.back-button {
  border: 2rpx solid #ddd !important;
  color: #666 !important;
  background: white !important;
}

.confirm-button {
  background: #1989fa !important;
  border: 1rpx solid #1989fa !important;
}

/* ==================== 服装信息弹窗 ==================== */
.clothing-popup-container {
  width: 100%;
  min-width: 600rpx;
  max-width: 90vw;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-sizing: border-box;
}

/* 确保弹窗内的组件样式正常 */
.clothing-popup-container .clothing-card {
  margin: 0 !important;
  box-shadow: none !important;
  border-radius: 16rpx !important;
  background: transparent !important;
}

/* 弹窗内卡片容器的内边距调整 */
.clothing-popup-container .card-container {
  padding: 32rpx !important;
}
