/* pages/warehouse-detail/warehouse-detail.wxss */

.warehouse-detail-container {
  background-color: #f5f5f5;
  height: 96vh;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* ==================== 查看模式样式 ==================== */

/* 仓库信息卡片 - 查看模式 */
.warehouse-info-card {
  background-color: #3563632c;
  box-shadow: 0 10px 6px -6px rgba(30, 30, 30, 0.1),
    12px 0 8px -8px rgba(50, 50, 50, 0.1);
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  flex-shrink: 0;
}

.info-content {
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.95);
}

/* 信息行布局 */
.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  gap: 16rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

/* 第一行：仓库名称、地址、编辑图标 */
.warehouse-name {
  font-size: 40rpx;
  font-weight: 600;
  color: #2c9678;
  flex-shrink: 0;
}

.address-tag {
  flex: 1;
  margin: 0 16rpx;
}


.edit-btn {
  padding: 8rpx;
  border-radius: 50%;
  background: rgba(7, 193, 96, 0.1);
  flex-shrink: 0;
}

.edit-btn:active {
  background: rgba(7, 193, 96, 0.2);
}

/* 第二行：租金、容量 */
.info-item {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  flex: 1;
}

.info-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 第三行：日期信息 */
.date-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  flex: 1;
}

.date-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.date-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.days-info {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  background: rgba(7, 193, 96, 0.1);
  border-radius: 20rpx;
  flex-shrink: 0;
}

.days-value {
  font-size: 24rpx;
  color: #2c9678;
  font-weight: 600;
}

/* ==================== 编辑模式样式 ==================== */

/* 仓库信息卡片 - 编辑模式 */
.warehouse-edit-card {
  background-color: #3563632c;
  box-shadow: 0 10px 6px -6px rgba(30, 30, 30, 0.1),
    12px 0 8px -8px rgba(50, 50, 50, 0.1);
  border-radius: 10rpx;
  overflow: hidden;
  height: 90vh;
  display: flex;
  flex-direction: column;
}

.edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-bottom: 1rpx solid #efefef;
  flex-shrink: 0;
}

.edit-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.cancel-btn {
  padding: 8rpx;
  border-radius: 50%;
  background: rgba(153, 153, 153, 0.1);
}

.cancel-btn:active {
  background: rgba(153, 153, 153, 0.2);
}

.edit-content {
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  flex: 1;
  overflow: hidden;
}

/* 表单区域 */
.form-section {
  margin-bottom: 40rpx;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  padding-bottom: 12rpx;
  border-bottom: 2rpx solid #07c160;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #efefef;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex-shrink: 0;
}

.form-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  padding: 12rpx 16rpx;
  background: rgba(245, 245, 245, 0.5);
  border-radius: 8rpx;
  border: 1rpx solid #e0e0e0;
}

.form-input:focus {
  border-color: #07c160;
  background: rgba(7, 193, 96, 0.05);
}

/* 日期选择器样式 */
.date-picker-compact {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 12rpx;
  background: rgba(245, 245, 245, 0.5);
  border-radius: 6rpx;
  border: 1rpx solid #e0e0e0;
  cursor: pointer;
  height: 60rpx;
}

.date-picker-compact:active {
  background: rgba(7, 193, 96, 0.05);
  border-color: #07c160;
}

.date-text {
  font-size: 26rpx;
  color: #333;
}

.date-text.placeholder {
  color: #999;
}

.date-arrow {
  font-size: 18rpx;
  color: #666;
  margin-left: 8rpx;
}

/* 编辑模式操作按钮 */
.edit-actions-bar {
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  border-top: 1rpx solid #efefef;
  display: flex;
  justify-content: center;
  flex-shrink: 0;
}

.button-container {
  display: flex;
  gap: 40rpx;
  width: 100%;
  max-width: 600rpx;
  justify-content: center;
}

.action-button {
  flex: 1;
  min-width: 200rpx;
  height: 88rpx !important;
  border-radius: 10rpx !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15) !important;
  transition: all 0.3s ease !important;
}

.save-button {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%) !important;
  border: none !important;
  color: white !important;
}

.save-button:active {
  transform: translateY(2rpx) !important;
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.4) !important;
}

.delete-button {
  background: linear-gradient(135deg, #ff7875 0%, #f5222d 100%) !important;
  border: none !important;
  color: white !important;
}

.delete-button:active {
  transform: translateY(2rpx) !important;
  box-shadow: 0 2rpx 8rpx rgba(255, 120, 117, 0.4) !important;
}

/* ==================== 选项卡样式 ==================== */

/* 选项卡卡片 */
.tabs-card {
  background-color: #3563632c;
  box-shadow: 0 10px 6px -6px rgba(30, 30, 30, 0.1),
    12px 0 8px -8px rgba(50, 50, 50, 0.1);
  border-radius: 10rpx;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 选项卡头部 */
.tabs-header {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1rpx solid #efefef;
  flex-shrink: 0;
}

.tab-item {
  font-size: 20rpx;
  flex: 1;
  padding: 16rpx 20rpx;
  text-align: center;
  position: relative;
  cursor: pointer;
}

.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: #2c9678;
}

.tab-text {
  font-size: 26rpx;
  font-weight: 500;
  color: #666;
}

.tab-item.active .tab-text {
  color: #2c9678;
  font-weight: 600;
}

/* 选项卡内容 */
.tabs-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* ==================== 库存明细样式 ==================== */

.inventory-header {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #efefef;
  flex-shrink: 0;
}

.inventory-stats {
  text-align: center;
}

.stats-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c9678;
}

.inventory-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 30rpx;
}

.inventory-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 10rpx 16rpx;
  margin: 16rpx 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #e9ecef;
}

.inventory-item:last-child {
  margin-bottom: 0;
}

.item-detail {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.clothing-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  text-align: left;
}

.stock-info {
  font-size: 26rpx;
  font-weight: 600;
  color: #2c9678;
  flex: 1;
  text-align: right;
}

.detail-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
  text-align: right;
}

.multi-item {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.contents-list {
  display: flex;
  flex-direction: column;
  flex: 2;
}

.content-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.content-quantity {
  font-size: 26rpx;
  font-weight: 600;
  color: #2c9678;
  flex: 1;
  text-align: right;
}

/* ==================== 日志样式 ==================== */

/* 日志搜索区域 - 参考盘存页面样式 */
.logs-search {
  flex-shrink: 0;
  margin-bottom: 10rpx;
}

.search-header {
  background: white;
  border-radius: 12rpx;
  padding: 20rpx;
  margin: 20rpx 20rpx 0rpx 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-form {
  display: flex;
  gap: 12rpx;
  align-items: center;
  flex-wrap: wrap;
}

.search-form-item {
  flex: 1;
  min-width: 0;
}

.search-form-item:last-child {
  flex: 0 0 auto;
}

.search-form-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 60rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e0e0e0;
  box-sizing: border-box;
}

.search-form-picker:active {
  border-color: #07c160;
  background: rgba(7, 193, 96, 0.05);
}

.search-form-text {
  color: #333;
}

.search-form-text.placeholder {
  color: #999;
}

/* 重置按钮 - 参考盘存页面样式 */
.reset-form-btn {
  width: 100% !important;
  height: 60rpx !important;
  border-radius: 8rpx !important;
  font-size: 26rpx !important;
  font-weight: 600 !important;
  background: #f5f5f5 !important;
  color: #666 !important;
  border: 1rpx solid #e0e0e0 !important;
}

/* 操作类型下拉选择器 - 参考盘存页面样式 */
.operation-type-selector {
  position: relative;
  z-index: 10;
}

.selector-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 60rpx;
  padding: 0 16rpx;
  font-size: 26rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e0e0e0;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.selector-trigger.active {
  background-color: #f0f9ff;
  border-color: #07c160;
}

.selector-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.selector-text {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

/* 下拉选项 */
.dropdown-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  margin-top: 4rpx;
  max-height: 300rpx;
  overflow: hidden;
}

.dropdown-content {
  max-height: 300rpx;
  overflow-y: auto;
}

.dropdown-option {
  padding: 16rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.dropdown-option:last-child {
  border-bottom: none;
}

.dropdown-option:hover {
  background-color: #f8f9fa;
}

.dropdown-option:active {
  background: #e3f2fd;
}

.dropdown-option.selected {
  background-color: #f0f9ff;
}

.option-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-text {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.selected-indicator {
  flex-shrink: 0;
  margin-left: 16rpx;
}

/* 日志列表 */
.logs-list {
  flex: 1;
  overflow-y: auto;
}

/* 日志卡片 - 更紧凑的设计 */
.log-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  margin: 6rpx 20rpx;
  border: 1rpx solid #e9ecef;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
}

.log-item:last-child {
  margin-bottom: 0;
}

/* 日志内容 - 紧凑布局 */
.log-contents {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  flex: 3;
}

.change-list {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  flex: 2;
}

.content-change {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  flex: 2;
}

.change-name {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  font-weight: 500;
}

.change-quantity {
  font-size: 26rpx;
  font-weight: 600;
  flex-shrink: 0;
}

.package-quantity {
  font-size: 26rpx;
  color: #666;
  flex-shrink: 0;
  margin-left: 16rpx;
  flex: 1;
  text-align: right;
}

.change-quantity.positive {
  color: #429c15;
}

.change-quantity.negative {
  color: #dc4244;
}

.log-type {
  flex: 1;
  flex-shrink: 0;
  text-align: right;
}

.summary-stat {
  font-size: 26rpx;
  color: #666;
  border-radius: 4rpx;
  flex-shrink: 0;
  flex: 1;
  text-align: right;
}

/* 汇总内容区域 */
.summary-contents {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  flex: 2;
}

/* ==================== 通用状态样式 ==================== */

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 20rpx;
  gap: 16rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #666;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 20rpx;
}

/* 服装名称点击样式 */
.clothing-name.clickable {
  color: #1989fa;
  cursor: pointer;
  transition: color 0.3s;
}

.clothing-name.clickable:active {
  color: #0c7cd5;
}

.content-name.clickable {
  color: #1989fa;
  cursor: pointer;
  transition: color 0.3s;
}

.content-name.clickable:active {
  color: #0c7cd5;
}



/* 左滑删除样式 */
.log-swipe-item {
  margin-bottom: 12rpx;
}



.delete-button {
  width: 60px;
  height: 100%;
  background: #c45a65 !important;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  border-radius: 12rpx;
}
