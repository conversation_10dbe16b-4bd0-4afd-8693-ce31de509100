<!-- pages/warehouse-detail/warehouse-detail.wxml -->
<view class="warehouse-detail-container" bind:tap="closeAllDropdowns">
  <!-- 仓库信息卡片 - 查看模式 -->
  <view wx:if="{{ !editMode }}" class="warehouse-info-card">
    <view class="info-content">
      <!-- 第一行：仓库名称、地址、编辑图标 -->
      <view class="info-row">
        <view class="warehouse-name">{{ warehouseForm.name || '未命名仓库' }}</view>
        <van-tag wx:if="{{ warehouseForm.address }}" size="medium" class="address-tag" color="#2c9678">
          {{ warehouseForm.address }}
        </van-tag>
        <van-icon name="edit" color="#2c9678" size="20px" bind:click="enterEditMode" class="edit-btn" />
      </view>
      <!-- 第二行：租金、容量 -->
      <view class="info-row">
        <view class="info-item">
          <view class="info-label">租金</view>
          <view class="info-value">
            {{ warehouseForm.rentPrice ? warehouseForm.rentPrice + ' 卢布/月' : '--' }}
          </view>
        </view>
        <view class="info-item">
          <view class="info-label">容量</view>
          <view class="info-value">
            {{ warehouseForm.capacity ? warehouseForm.capacity + ' 包' : '--' }}
          </view>
        </view>
      </view>
      <!-- 第三行：租赁日期、天数、退租日期 -->
      <view class="info-row">
        <view class="date-info">
          <view class="date-label">租赁日期</view>
          <view class="date-value">{{ warehouseForm.rentDate || '--' }}</view>
        </view>
        <view wx:if="{{ warehouseForm.rentDate && warehouseForm.returnDate }}" class="days-info">
          <view class="days-value">
            {{ calculateDays(warehouseForm.rentDate, warehouseForm.returnDate) }}天
          </view>
        </view>
        <view class="date-info">
          <view class="date-label">退租日期</view>
          <view class="date-value">{{ warehouseForm.returnDate || '--' }}</view>
        </view>
      </view>
    </view>
  </view>
  <!-- 仓库信息编辑模式 -->
  <view wx:else class="warehouse-edit-card">
    <view class="edit-header">
      <view class="edit-title">{{ warehouseId ? '编辑仓库' : '新建仓库' }}</view>
      <view class="edit-actions">
        <van-icon name="cross" color="#999" size="20px" bind:click="cancelEdit" class="cancel-btn" />
      </view>
    </view>
    <view class="edit-content">
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>
        <view class="form-group">
          <view class="form-item">
            <text class="form-label">仓库名称</text>
            <input class="form-input" value="{{ warehouseForm.name }}" placeholder="请输入仓库名称" data-field="name" bindinput="onFormInput" />
          </view>
          <view class="form-item">
            <text class="form-label">仓库地址</text>
            <input class="form-input" value="{{ warehouseForm.address }}" placeholder="请输入仓库地址" data-field="address" bindinput="onFormInput" />
          </view>
          <view class="form-item">
            <text class="form-label">仓库容量</text>
            <input class="form-input" value="{{ warehouseForm.capacity }}" placeholder="预计可堆放包数" type="number" data-field="capacity" bindinput="onFormInput" />
          </view>
        </view>
      </view>
      <!-- 租赁信息 -->
      <view class="form-section">
        <view class="section-title">租赁信息</view>
        <view class="form-group">
          <view class="form-item">
            <text class="form-label">租赁价格</text>
            <input class="form-input" value="{{ warehouseForm.rentPrice }}" placeholder="请输入租赁价格" type="number" data-field="rentPrice" bindinput="onFormInput" />
          </view>
          <view class="form-item" bindtap="showRentDatePicker">
            <text class="form-label">租赁日期</text>
            <view class="date-picker-compact">
              <view class="date-text {{ warehouseForm.rentDate ? '' : 'placeholder' }}">
                {{ warehouseForm.rentDate || '请选择租赁日期' }}
              </view>
              <text class="date-arrow">▼</text>
            </view>
          </view>
          <view class="form-item" bindtap="showReturnDatePicker">
            <text class="form-label">退租日期</text>
            <view class="date-picker-compact">
              <view class="date-text {{ warehouseForm.returnDate ? '' : 'placeholder' }}">
                {{ warehouseForm.returnDate || '请选择退租日期' }}
              </view>
              <text class="date-arrow">▼</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 编辑模式操作按钮 -->
    <view class="edit-actions-bar">
      <view class="button-container">
        <van-button size="large" bindtap="saveWarehouse" loading="{{ saving }}" round type="primary" custom-class="action-button save-button">
          {{ warehouseId ? '保存修改' : '创建仓库' }}
        </van-button>
        <van-button wx:if="{{ warehouseId }}" size="large" bindtap="deleteWarehouse" loading="{{ deleting }}" round type="danger" custom-class="action-button delete-button">
          删除仓库
        </van-button>
      </view>
    </view>
  </view>
  <!-- 选项卡卡片 - 只在查看模式下显示 -->
  <view wx:if="{{ !editMode && warehouseId }}" class="tabs-card">
    <!-- 选项卡头部 -->
    <view class="tabs-header">
      <view class="tab-item {{ activeTab === 'inventory' ? 'active' : '' }}" data-tab="inventory" bindtap="onTabChange">
        <view class="tab-text">库存明细</view>
      </view>
      <view class="tab-item {{ activeTab === 'logs' ? 'active' : '' }}" data-tab="logs" bindtap="onTabChange">
        <view class="tab-text">日志</view>
      </view>
    </view>
    <!-- 选项卡内容 -->
    <view class="tabs-content">
      <!-- 库存明细选项卡 -->
      <view wx:if="{{ activeTab === 'inventory' }}" class="tab-content">
        <view class="inventory-header">
          <view class="inventory-stats">
            <view class="stats-text">总计：{{totalPackages || 0}}包 / {{totalPieces || 0}}件</view>
          </view>
        </view>
        <view wx:if="{{ loadingInventory }}" class="loading-state">
          <van-loading type="spinner" />
          <view class="loading-text">加载中...</view>
        </view>
        <view wx:elif="{{ inventoryList.length === 0 }}" class="empty-state">
          <van-empty description="暂无库存数据" />
        </view>
        <scroll-view wx:else class="inventory-list" scroll-y>
          <view wx:for="{{inventoryList}}" wx:key="unique_id" class="inventory-item">
            <!-- 单货物包裹 -->
            <view wx:if="{{item.contents.length === 1}}" class="single-item">
              <view class="item-detail">
                <view class="clothing-name clickable" style="{{item.contents[0].clothing_id ? 'color: #07c160' : 'color: #000'}}" data-content="{{item.contents[0]}}" bind:tap="onClothingNameTap">
                  {{item.contents[0].name || '未知服装'}}
                </view>
                <view class="stock-info">{{item.availableTotalQuantity || 0}}件</view>
                <view class="detail-value">{{item.availablePackageCount || 0}}包</view>
                <view class="detail-value">{{item.contents[0].original_quantity || 0}}件/包</view>
              </view>
            </view>
            <!-- 混合包裹 -->
            <view wx:else class="multi-item">
              <view class="contents-list">
                <view wx:for="{{item.contents}}" wx:for-item="content" wx:key="clothing_id" class="content-item">
                  <view class="content-name clickable" style="{{content.clothing_id ? 'color: #07c160' : 'color: #000'}}" data-content="{{content}}" bind:tap="onClothingNameTap">
                    {{content.name}}
                  </view>
                  <view class="content-quantity">{{content.available_quantity || 0}}件</view>
                </view>
              </view>
              <view class="detail-value">{{item.availablePackageCount || 0}}包</view>
              <view class="detail-value">{{item.totalOriginalQuantity || 0}}件/包</view>
            </view>
          </view>
        </scroll-view>
      </view>
      <!-- 日志选项卡 -->
      <view wx:if="{{ activeTab === 'logs' }}" class="tab-content">
        <!-- 日历表组件 -->
        <view class="calendar-section">
          <calendar-table
            id="calendar-table"
            warehouse-id="{{warehouseId}}"
            show-query-button="{{true}}"
            bind:query="onCalendarQuery">
          </calendar-table>
        </view>
      </view>
    </view>
  </view>
</view>
<!-- 加载状态 -->
<van-loading wx:if="{{ loading }}" type="spinner" />
<!-- 租赁日期选择器 -->
<van-popup show="{{ showRentDatePicker }}" position="bottom" bind:close="hideRentDatePicker">
  <van-datetime-picker type="date" value="{{ rentDatePickerValue }}" bind:confirm="onRentDateConfirm" bind:cancel="hideRentDatePicker" title="选择租赁日期" />
</van-popup>
<!-- 退租日期选择器 -->
<van-popup show="{{ showReturnDatePicker }}" position="bottom" bind:close="hideReturnDatePicker">
  <van-datetime-picker type="date" value="{{ returnDatePickerValue }}" bind:confirm="onReturnDateConfirm" bind:cancel="hideReturnDatePicker" title="选择退租日期" />
</van-popup>
<!-- 日志日期选择器 -->
<van-calendar show="{{ showDatePicker }}" type="single" poppable="{{ true }}" show-confirm="{{ true }}" confirm-text="确认日期" bind:close="hideDatePicker" bind:confirm="onDateConfirm" color="#2c9678" min-date="{{ minDate }}" max-date="{{ maxDate }}" default-date="{{ currentDate }}" />
<!-- 服装信息弹窗 -->
<van-popup show="{{showClothingInfo}}" position="center" round bind:close="onCloseClothingInfo" custom-style="width: 90%; max-width: 600rpx; border-radius: 16rpx;">
  <clothing-info-card wx:if="{{selectedClothingInfo}}" clothingInfo="{{selectedClothingInfo}}" oemClothingInfo="{{selectedClothingInfo}}" isOem="{{isOemClothing}}" showDetailInfo="{{true}}" />
</van-popup>