// pages/warehouse-detail/warehouse-detail.js
import Api from "../../utils/api.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 仓库信息
    warehouseId: null,
    warehouseForm: {
      name: "",
      address: "",
      capacity: "",
      rentPrice: "",
      rentDate: "",
      returnDate: "",
    },

    // 状态
    loading: false,
    saving: false,
    deleting: false,
    editMode: false, // 编辑模式控制
    // 日期选择器相关
    showRentDatePicker: false,
    showReturnDatePicker: false,
    rentDatePickerValue: new Date().getTime(),
    returnDatePickerValue: new Date().getTime(),

    // 选项卡相关
    activeTab: "inventory", // 默认显示库存明细

    // 库存明细相关
    inventoryList: [],
    totalPackages: 0,
    totalPieces: 0,
    loadingInventory: false,

    // 日志相关
    logsList: [],
    loadingLogs: false,
    isDetailMode: true, // true为明细模式，false为汇总模式
    originalLogsList: [], // 保存原始明细数据
    logQuery: {
      date: "",
      operation_type: "",
      operation_type_text: "",
    },
    logsSummary: null,

    // 操作类型选项
    operationTypes: [
      { value: "", name: "全部" },
      { value: "inbound", name: "入库" },
      { value: "outbound", name: "出库" },
      { value: "transfer_out", name: "移出" },
      { value: "transfer_in", name: "移入" },
      { value: "inventory_surplus", name: "盘盈" },
      { value: "inventory_deficit", name: "盘亏" },
    ],
    showOperationDropdown: false,
    showDatePicker: false,
    currentDate: new Date().getTime(),
    // van-calendar 日期范围配置
    minDate: new Date(2020, 0, 1).getTime(), // 2020年1月1日
    maxDate: new Date().getTime(), // 当前日期

    // 服装信息弹窗相关
    showClothingInfo: false,
    selectedClothingInfo: null,
    isOemClothing: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.warehouseData) {
      try {
        const warehouse = JSON.parse(decodeURIComponent(options.warehouseData));
        this.setData({
          warehouseId: warehouse.warehouse_id || warehouse.id,
          warehouseForm: {
            name: warehouse.name || "",
            address: warehouse.address || "",
            capacity: warehouse.capacity || "",
            rentPrice: warehouse.rent_price || warehouse.rentPrice || "",
            rentDate:
              this.formatDateOnly(warehouse.rent_date || warehouse.rentDate) ||
              "",
            returnDate:
              this.formatDateOnly(
                warehouse.return_date || warehouse.returnDate
              ) || "",
          },
          editMode: false, // 有数据时默认为查看模式
        });

        // 初始化日志查询日期为今天
        this.initLogQuery();

        // 加载库存明细（默认选项卡）
        this.loadWarehouseInventory();
      } catch (error) {
        console.error("解析仓库数据失败:", error);
        wx.showToast({
          title: "数据错误",
          icon: "none",
        });
      }
    } else {
      // 新建仓库时直接进入编辑模式
      this.setData({
        editMode: true,
      });
    }
  },

  /**
   * 选项卡切换
   */
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab,
    });

    // 根据选项卡加载对应数据
    if (tab === "inventory" && this.data.inventoryList.length === 0) {
      this.loadWarehouseInventory();
    } else if (tab === "logs" && this.data.logsList.length === 0) {
      this.loadLogs(true);
    }
  },

  /**
   * 加载仓库库存明细
   */
  async loadWarehouseInventory() {
    if (!this.data.warehouseId) return;

    try {
      this.setData({ loadingInventory: true });

      const response = await Api.getNewWarehouseInventory({
        warehouse_id: this.data.warehouseId,
        limit: 1000, // 设置足够大的limit以获取所有数据
      });

      if (response.data.code === 200) {
        const inventoryData = response.data.data.list || [];
        const summaryData = response.data.data.summary || {};
        console.log("原始库存数据:", inventoryData);
        console.log("后端汇总数据:", summaryData);

        // 处理库存数据，参考出库页面的处理逻辑
        const processedInventory = this.processInventoryData(inventoryData);
        console.log("处理后的库存数据:", processedInventory);

        // 直接使用后端返回的汇总数据，而不是前端重新计算
        const totalPackages = summaryData.total_packages || 0;
        const totalPieces = summaryData.total_pieces || 0;

        console.log("使用后端汇总数据:", { totalPackages, totalPieces });

        this.setData({
          inventoryList: processedInventory,
          totalPackages: Math.round(totalPackages * 100) / 100,
          totalPieces: totalPieces,
        });
      }
    } catch (error) {
      console.error("加载库存明细失败:", error);
      wx.showToast({
        title: "加载库存失败",
        icon: "none",
      });
    } finally {
      this.setData({ loadingInventory: false });
    }
  },

  /**
   * 处理库存数据
   */
  processInventoryData(inventoryData) {
    return inventoryData.map((item, index) => {
      return {
        ...item,
        unique_id: `${this.data.warehouseId}_${
          item.classification_code
        }_${Date.now()}_${index}`,
        classification_code: item.classification_code,
        contents: item.contents.map((content) => ({
          ...content,
          available_quantity: content.current_quantity,
          total_original_quantity: content.original_quantity,
        })),
        availablePackageCount: item.package_count,
        availableTotalQuantity: item.total_quantity,
        totalOriginalQuantity: item.contents.reduce(
          (sum, content) => sum + content.original_quantity,
          0
        ),
      };
    });
  },

  /**
   * 表单输入处理
   */
  onFormInput(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;

    this.setData({
      [`warehouseForm.${field}`]: value,
    });
  },

  /**
   * 保存仓库信息
   */
  async saveWarehouse() {
    const { warehouseForm, warehouseId } = this.data;

    // 表单验证
    if (!warehouseForm.name || !warehouseForm.address) {
      wx.showToast({
        title: "请填写完整信息",
        icon: "none",
      });
      return;
    }

    try {
      this.setData({ saving: true });

      // 构造API请求数据
      const warehouseData = {
        name: warehouseForm.name,
        address: warehouseForm.address,
        capacity: warehouseForm.capacity || "",
        rent_price: warehouseForm.rentPrice || "",
        rent_date: warehouseForm.rentDate || "",
        return_date: warehouseForm.returnDate || "",
        status: "active",
      };

      let response;
      if (warehouseId) {
        // 更新现有仓库
        response = await Api.updateWarehouse({
          warehouse_id: warehouseId,
          ...warehouseData,
        });
      } else {
        // 创建新仓库
        response = await Api.createWarehouse(warehouseData);
      }

      if (response.data && response.data.code === 200) {
        wx.showToast({
          title: "保存成功",
          icon: "success",
        });

        // 如果是新建仓库，设置warehouseId并切换到查看模式
        if (!warehouseId) {
          const newWarehouseId =
            response.data.data?.warehouse_id || response.data.data?._id;
          this.setData({
            warehouseId: newWarehouseId,
            editMode: false,
          });

          // 通知货物入库页面刷新仓库列表
          this.notifyInboundPageRefresh();
        } else {
          // 如果是编辑现有仓库，切换到查看模式
          this.setData({
            editMode: false,
          });
        }
      } else {
        wx.showToast({
          title: response.data?.message || "保存失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("保存失败:", error);
      wx.showToast({
        title: "保存失败",
        icon: "none",
      });
    } finally {
      this.setData({ saving: false });
    }
  },

  /**
   * 删除仓库
   */
  deleteWarehouse() {
    wx.showModal({
      title: "确认删除",
      content: `确定要删除仓库"${this.data.warehouseForm.name}"吗？此操作不可恢复。`,
      success: async (res) => {
        if (res.confirm) {
          await this.executeDelete();
        }
      },
    });
  },

  /**
   * 执行删除操作
   */
  async executeDelete() {
    try {
      this.setData({ deleting: true });

      // 调用真实API软删除仓库（标记为inactive）
      const response = await Api.updateWarehouse({
        warehouse_id: this.data.warehouseId,
        status: "inactive",
      });

      if (response.data && response.data.code === 200) {
        wx.showToast({
          title: "删除成功",
          icon: "success",
        });

        // 通知货物入库页面刷新仓库列表并清除选中状态
        this.notifyInboundPageRefresh(true);

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: response.data?.message || "删除失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("删除失败:", error);
      wx.showToast({
        title: "删除失败",
        icon: "none",
      });
    } finally {
      this.setData({ deleting: false });
    }
  },

  /**
   * 通知货物入库页面刷新仓库列表
   */
  notifyInboundPageRefresh(isDelete = false) {
    try {
      // 获取所有页面栈
      const pages = getCurrentPages();

      // 查找货物入库页面
      const inboundPage = pages.find(
        (page) => page.route === "pages/inbound/inbound"
      );

      if (inboundPage && inboundPage.loadWarehouses) {
        // 保存当前选中的仓库ID（如果不是删除操作）
        const currentWarehouseId = isDelete ? null : this.data.warehouseId;

        // 如果是删除操作，先清除选中状态
        if (isDelete && inboundPage.clearSelectedWarehouse) {
          inboundPage.clearSelectedWarehouse();
        }

        // 调用货物入库页面的刷新方法
        inboundPage.loadWarehouses();

        // 如果不是删除操作，恢复选中状态
        if (
          !isDelete &&
          currentWarehouseId &&
          inboundPage.restoreSelectedWarehouse
        ) {
          setTimeout(() => {
            inboundPage.restoreSelectedWarehouse(currentWarehouseId);
          }, 100);
        }
      }
    } catch (error) {
      console.error("通知货物入库页面刷新失败:", error);
    }
  },

  /**
   * 显示租赁日期选择器
   */
  showRentDatePicker() {
    // 如果已有租赁日期，设置为当前值
    if (this.data.warehouseForm.rentDate) {
      const date = new Date(this.data.warehouseForm.rentDate);
      this.setData({
        rentDatePickerValue: date.getTime(),
        showRentDatePicker: true,
      });
    } else {
      this.setData({
        showRentDatePicker: true,
      });
    }
  },

  /**
   * 隐藏租赁日期选择器
   */
  hideRentDatePicker() {
    this.setData({
      showRentDatePicker: false,
    });
  },

  /**
   * 确认租赁日期
   */
  onRentDateConfirm(e) {
    const date = new Date(e.detail);
    const formattedDate = this.formatDate(date);

    this.setData({
      "warehouseForm.rentDate": formattedDate,
      showRentDatePicker: false,
    });
  },

  /**
   * 显示退租日期选择器
   */
  showReturnDatePicker() {
    // 如果已有退租日期，设置为当前值
    if (this.data.warehouseForm.returnDate) {
      const date = new Date(this.data.warehouseForm.returnDate);
      this.setData({
        returnDatePickerValue: date.getTime(),
        showReturnDatePicker: true,
      });
    } else {
      this.setData({
        showReturnDatePicker: true,
      });
    }
  },

  /**
   * 隐藏退租日期选择器
   */
  hideReturnDatePicker() {
    this.setData({
      showReturnDatePicker: false,
    });
  },

  /**
   * 确认退租日期
   */
  onReturnDateConfirm(e) {
    const date = new Date(e.detail);
    const formattedDate = this.formatDate(date);

    this.setData({
      "warehouseForm.returnDate": formattedDate,
      showReturnDatePicker: false,
    });
  },

  /**
   * 格式化日期为 YYYY-MM-DD 格式
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  },

  /**
   * 格式化日期为 YYYY-MM-DD 格式（只显示日期部分）
   */
  formatDateOnly(dateInput) {
    if (!dateInput) return "";

    try {
      let date;
      if (typeof dateInput === "string") {
        // 如果是字符串，先解析
        date = new Date(dateInput);
      } else if (dateInput instanceof Date) {
        date = dateInput;
      } else {
        return "";
      }

      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return "";
      }

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    } catch (error) {
      console.error("日期格式化失败:", error);
      return "";
    }
  },

  /**
   * 进入编辑模式
   */
  enterEditMode() {
    this.setData({
      editMode: true,
    });
  },

  /**
   * 取消编辑
   */
  cancelEdit() {
    if (this.data.warehouseId) {
      // 如果是编辑现有仓库，返回查看模式
      this.setData({
        editMode: false,
      });
    } else {
      // 如果是新建仓库，返回上一页
      wx.navigateBack();
    }
  },

  /**
   * 初始化日志查询
   */
  initLogQuery() {
    // 设置默认日期为今天
    const today = new Date();
    const dateStr = `${today.getFullYear()}-${String(
      today.getMonth() + 1
    ).padStart(2, "0")}-${String(today.getDate()).padStart(2, "0")}`;

    this.setData({
      "logQuery.date": dateStr,
      currentDate: today.getTime(),
      isDetailMode: true, // 初始化时设为明细模式
    });
  },

  /**
   * 加载日志数据
   */
  async loadLogs(reset = false) {
    if (this.data.loadingLogs) return;
    if (!this.data.warehouseId) return;

    try {
      this.setData({ loadingLogs: true });

      const { logQuery } = this.data;

      const params = {
        page: 1,
        limit: 1000,
        warehouse_id: this.data.warehouseId, // 只查询当前仓库的日志
      };

      if (logQuery.date) {
        params.start_date = logQuery.date;
        params.end_date = logQuery.date;
      }

      if (logQuery.operation_type) {
        params.operation_type = logQuery.operation_type;
      }

      // 使用明细日志API
      const response = await Api.getWarehouseOperationLogsDetail(params);

      console.log("API响应:", response);

      if (response.data && response.data.code === 200) {
        const logs = response.data.data?.list || response.data.data || [];
        console.log("获取到的日志数据:", logs);

        // 处理日志数据
        const processedLogs = logs.map((log) => {
          return {
            ...log,
            id: log.id || log._id,
            type_text: this.getOperationTypeText(
              log.type || log.operation_type
            ),
            contents_changes: log.contents_changes || [],
          };
        });

        console.log("处理后的日志数据:", processedLogs);

        this.setData({
          logsList: processedLogs,
          originalLogsList: processedLogs, // 保存原始明细数据
        });

        // 如果当前是汇总模式，自动生成汇总数据
        if (!this.data.isDetailMode) {
          this.generateSummaryData();
        }
      } else {
        console.error("API响应错误:", response);
        this.setData({
          logsList: [],
        });
      }
    } catch (error) {
      console.error("加载日志数据失败:", error);
      wx.showToast({
        title: "加载日志失败",
        icon: "none",
      });
    } finally {
      this.setData({ loadingLogs: false });
    }
  },

  /**
   * 获取操作类型文本
   */
  getOperationTypeText(type) {
    const typeMap = {
      inbound: "入库",
      outbound: "出库",
      transfer_out: "移出",
      transfer_in: "移入",
      inventory_surplus: "盘盈",
      inventory_deficit: "盘亏",
    };
    return typeMap[type] || type;
  },

  /**
   * 计算两个日期之间的天数
   */
  calculateDays(startDate, endDate) {
    if (!startDate || !endDate) return 0;

    const start = new Date(startDate);
    const end = new Date(endDate);
    const timeDiff = end.getTime() - start.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

    return daysDiff > 0 ? daysDiff : 0;
  },

  /**
   * 显示日期选择器
   */
  onSelectDate() {
    this.setData({ showDatePicker: true });
  },

  /**
   * 隐藏日期选择器
   */
  hideDatePicker() {
    this.setData({ showDatePicker: false });
  },

  /**
   * 确认选择日期
   */
  onDateConfirm(e) {
    // van-calendar 返回的是Date对象
    const selectedDate = e.detail;
    const date = new Date(selectedDate);
    const dateStr = this.formatDate(date);

    this.setData({
      "logQuery.date": dateStr,
      currentDate: date.getTime(),
      showDatePicker: false,
    });

    // 自动触发查询
    this.onQueryLogs();
  },

  /**
   * 切换操作类型下拉框
   */
  toggleOperationDropdown() {
    this.setData({
      showOperationDropdown: !this.data.showOperationDropdown,
    });
  },

  /**
   * 选择操作类型
   */
  selectOperationType(e) {
    const { operation } = e.currentTarget.dataset;
    this.setData({
      "logQuery.operation_type": operation.value,
      "logQuery.operation_type_text": operation.name,
      showOperationDropdown: false,
    });

    // 自动触发查询
    this.onQueryLogs();
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 关闭所有下拉框
   */
  closeAllDropdowns() {
    this.setData({
      showOperationDropdown: false,
    });
  },

  /**
   * 查询日志
   */
  onQueryLogs() {
    this.setData({
      logsList: [],
    });
    this.loadLogs(true);
  },

  /**
   * 重置日志查询
   */
  onResetLogs() {
    // 重置为今天日期
    const today = new Date();
    const dateStr = this.formatDate(today);

    this.setData({
      "logQuery.date": dateStr,
      "logQuery.operation_type": "",
      "logQuery.operation_type_text": "",
      currentDate: today.getTime(),
      logsList: [],
      isDetailMode: true, // 重置时回到明细模式
    });

    this.loadLogs(true);
  },

  /**
   * 切换汇总/明细模式
   */
  onToggleMode() {
    const isDetailMode = this.data.isDetailMode;

    if (isDetailMode) {
      // 当前是明细模式，切换到汇总模式
      this.generateSummaryData();
    } else {
      // 当前是汇总模式，切换到明细模式
      this.setData({
        isDetailMode: true,
        logsList: this.data.originalLogsList,
      });
    }
  },

  /**
   * 生成汇总数据
   */
  generateSummaryData() {
    const originalLogs = this.data.originalLogsList;
    if (!originalLogs || originalLogs.length === 0) {
      this.setData({
        isDetailMode: false,
        logsList: [],
      });
      return;
    }

    // 按操作方向分组，再按classification_code汇总
    const summaryMap = new Map();

    originalLogs.forEach((log) => {
      const operationType = log.type;
      const classificationCode = log.classification_code || "未知分类";

      // 创建分组键：操作方向_分类码
      const groupKey = `${operationType}_${classificationCode}`;

      if (!summaryMap.has(groupKey)) {
        summaryMap.set(groupKey, {
          id: `summary_${groupKey}_${Date.now()}`,
          type: operationType,
          type_text: this.getOperationTypeText(operationType),
          classification_code: classificationCode,
          date: log.date,
          warehouse_id: log.warehouse_id,
          total_packages: 0,
          total_quantity: 0,
          package_quantity_change: 0,
          operation_count: 0,
          contents_changes: [],
        });
      }

      const summary = summaryMap.get(groupKey);
      summary.operation_count += 1;
      summary.total_packages += 1; // 每个日志记录代表一个包裹操作

      // 汇总contents_changes
      if (log.contents_changes && log.contents_changes.length > 0) {
        log.contents_changes.forEach((change) => {
          summary.total_quantity += Math.abs(change.quantity_change);
          summary.package_quantity_change += Math.abs(
            Math.round(change.package_quantity_change * 100) / 100
          );
          // 查找是否已有相同产品的汇总
          const existingChange = summary.contents_changes.find(
            (c) => c.product_name === change.product_name
          );

          if (existingChange) {
            existingChange.quantity_change += Math.abs(change.quantity_change);
          } else {
            summary.contents_changes.push({
              product_name: change.product_name,
              quantity_change: Math.abs(change.quantity_change),
            });
          }
        });
      }
    });

    // 转换为数组并排序
    const summaryList = Array.from(summaryMap.values()).sort((a, b) => {
      // 先按操作类型排序，再按分类码排序
      if (a.type !== b.type) {
        return a.type.localeCompare(b.type);
      }
      return a.classification_code.localeCompare(b.classification_code);
    });
    console.log("生成的汇总数据:", summaryList);

    this.setData({
      isDetailMode: false,
      logsList: summaryList,
    });
  },

  /**
   * 点击服装名称显示服装卡片
   */
  async onClothingNameTap(e) {
    const content = e.currentTarget.dataset.content;
    console.log("点击服装名称:", content);

    if (!content || !content.sku) {
      wx.showToast({
        title: "服装信息不完整",
        icon: "none",
      });
      return;
    }

    try {
      let detailInfo;
      // 根据SKU规则判断服装类型：如果SKU包含"_"且前面是"oem"，则为OEM服装
      const isOemClothing =
        content.sku &&
        content.sku.includes("_") &&
        content.sku.split("_")[0].toLowerCase() === "oem";

      if (isOemClothing) {
        // 获取OEM服装详细信息
        const oemClothingId = content.sku.split("_")[1];
        const response = await Api.getOemClothingInfo({
          oem_clothing_id: oemClothingId,
        });
        console.log("OEM服装API响应:", response);
        if (response.data) {
          detailInfo = response.data;
        }
      } else {
        // 获取普通服装详细信息
        const clothingId = content.clothing_id || content.sku;
        const response = await Api.getClothingInfo({
          clothing_id: clothingId,
        });
        console.log("普通服装API响应:", response);
        if (response.data) {
          detailInfo = response.data;
        }
      }

      if (detailInfo) {
        this.setData({
          selectedClothingInfo: detailInfo,
          isOemClothing: isOemClothing,
          showClothingInfo: true,
        });
      } else {
        wx.showToast({
          title: "获取服装信息失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("获取服装信息失败:", error);
      wx.showToast({
        title: "获取服装信息失败",
        icon: "none",
      });
    }
  },

  /**
   * 关闭服装信息弹窗
   */
  onCloseClothingInfo() {
    this.setData({
      showClothingInfo: false,
      selectedClothingInfo: null,
      isOemClothing: false,
    });
  },

  /**
   * 处理calendar-table组件的查询事件
   */
  onCalendarQuery(e) {
    const { start_date, end_date, warehouse_id } = e.detail;
    console.log("仓库详情页日历查询事件:", { start_date, end_date, warehouse_id });

    // 跳转到日历日志页面查看查询结果
    wx.navigateTo({
      url: `/pages/calendar-logs/calendar-logs?start_date=${start_date}&end_date=${end_date}&warehouse_id=${warehouse_id || this.data.warehouseId}`,
    });
  },

  /**
   * 处理swipe-cell点击事件
   */
  onSwipeCellClick(e) {
    const position = e.detail;
    console.log("swipe-cell点击位置:", position);
    // 如果点击的是外部区域，关闭所有打开的swipe-cell
    if (position === "outside" || position === "cell") {
    }
  },

  /**
   * 删除日志（冲正）
   */
  onDeleteLog(e) {
    const logId = e.currentTarget.dataset.logId;
    const logItem = e.currentTarget.dataset.logItem;

    console.log("删除日志:", logId, logItem);

    wx.showModal({
      title: "确认删除",
      content: `确定要删除这条${
        logItem.type_text || logItem.type
      }日志吗？此操作将进行冲正处理，不可撤销。`,
      confirmText: "确认删除",
      cancelText: "取消",
      success: (res) => {
        if (res.confirm) {
          this.executeDeleteLog(logId);
        }
      },
    });
  },

  /**
   * 执行删除日志操作
   */
  async executeDeleteLog(logId) {
    try {
      wx.showLoading({
        title: "处理中...",
        mask: true,
      });

      const response = await Api.reverseOperationLog({
        log_id: logId,
        operator_name: "小程序用户", // 可以从用户信息中获取
      });

      if (response.data && response.data.code === 200) {
        wx.showToast({
          title: "删除成功",
          icon: "success",
        });

        // 重新加载日志数据
        this.loadLogs(true);

        // 如果当前显示的是库存明细选项卡，也需要刷新库存明细
        if (this.data.activeTab === "inventory") {
          this.loadWarehouseInventory();
        }
      } else {
        wx.showToast({
          title: response.data?.message || "删除失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("删除日志失败:", error);
      wx.showToast({
        title: "删除失败",
        icon: "none",
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 通知仓库主页刷新数据
    try {
      const pages = getCurrentPages();
      const warehousesPage = pages.find(
        (page) => page.route === "pages/warehouses/warehouses"
      );

      if (warehousesPage && warehousesPage.loadWarehouses) {
        // 延迟刷新，确保页面已经返回
        setTimeout(() => {
          warehousesPage.loadWarehouses();
          warehousesPage.loadSummaryData();
        }, 100);
      }
    } catch (error) {
      console.error("通知仓库主页刷新失败:", error);
    }
  },
});
