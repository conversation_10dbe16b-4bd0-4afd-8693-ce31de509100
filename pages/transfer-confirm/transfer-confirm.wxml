<!-- pages/transfer-confirm/transfer-confirm.wxml -->
<view class="transfer-confirm-container">
  <!-- 加载状态 -->
  <van-loading wx:if="{{loading}}" type="spinner" color="#1989fa" size="24px" vertical>
    加载中...
  </van-loading>
  <!-- 主要内容 -->
  <view wx:else class="main-content">
    <!-- 移库信息卡片 -->
    <view class="transfer-info-card">
      <view class="card-header">
        <view class="card-title">移出仓库：</view>
                <view class="warehouse-info from-warehouse">
          <view class="warehouse-name">{{transferData.from_warehouse.name}}</view>
          <view class="warehouse-address">{{transferData.from_warehouse.address}}</view>
        </view>
        <view class="transfer-date">{{currentDate}}</view>
      </view>
    </view>
    <!-- 移库明细 -->
    <view class="transfer-details-card">
      <view class="card-header">
        <view class="card-title">移库明细</view>
        <view class="summary-info">
          总计：{{transferData.total_packages_display}}包 / {{transferData.total_pieces}}件
        </view>
      </view>
      <view class="details-list">
        <!-- 按仓库分组显示移库明细 -->
        <view wx:for="{{transferData.grouped_transfer_items}}" wx:for-item="warehouseGroup" wx:key="warehouse_id" class="warehouse-detail-group">
          <!-- 仓库标题 -->
          <view class="warehouse-detail-header">
            <view class="warehouse-detail-title">
              <van-icon name="shop-o" class="warehouse-icon" />
              <view class="warehouse-name-address">
                <text class="warehouse-name">{{warehouseGroup.warehouse_name}}</text>
                <text class="warehouse-address">{{warehouseGroup.warehouse_address}}</text>
              </view>
            </view>
            <view class="warehouse-detail-stats">
              {{warehouseGroup.total_packages_display}}包 / {{warehouseGroup.total_pieces}}件
            </view>
          </view>

          <!-- 该仓库的商品明细 -->
          <view class="warehouse-items-detail">
            <view wx:for="{{warehouseGroup.classifications}}" wx:for-item="classification" wx:key="sku" class="detail-item">
              <view class="item-info">
                <view class="item-header">
                  <view class="product-name">{{classification.product_name}}</view>
                </view>
                <view class="item-details">
                  <view class="quantity-info">
                    总件数：{{classification.total_quantity}}件 | 总包数：{{classification.total_packages_display}}包
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 日期选择区域 -->
  <view class="date-selection-card">
    <view class="date-selection-header">
      <view class="date-label">移库日期：</view>
      <view class="selected-date">{{selectedDate}}</view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <view class="action-button-wrapper">
      <van-button size="large" custom-class="action-button back-button" bind:click="goBack" disabled="{{submitting}}">
        返回修改
      </van-button>
    </view>
    <view class="action-button-wrapper">
      <van-button type="primary" size="large" custom-class="action-button confirm-button" loading="{{submitting}}" loading-text="移库中..." bind:click="confirmTransfer">
        确认移库
      </van-button>
    </view>
  </view>
</view>

<!-- 日期选择器弹窗 -->
<van-calendar
  show="{{ showDatePicker }}"
  min-date="{{ minDate }}"
  max-date="{{ maxDate }}"
  bind:close="onDatePickerCancel"
  bind:confirm="onDatePickerConfirm"
  color="#2c9678"
  confirm-text="确认"
  confirm-disabled-text="确认"
/>