// pages/transfer-confirm/transfer-confirm.js
import Api from "../../utils/api.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 移库单数据
    transferData: null,
    // 加载状态
    loading: false,
    // 提交状态
    submitting: false,
    // 当前日期
    currentDate: "",
    // 日期选择器相关
    showDatePicker: false,
    selectedDate: "",
    minDate: new Date(2020, 0, 1).getTime(),
    maxDate: new Date().getTime(),
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { transferData } = options;

    if (transferData) {
      try {
        const parsedData = JSON.parse(decodeURIComponent(transferData));
        console.log("解析后的移库数据:", parsedData);

        // 格式化显示数据
        const formattedData = this.formatTransferData(parsedData);
        console.log("格式化后的移库数据:", formattedData);

        this.setData({
          transferData: formattedData,
          currentDate: this.formatCurrentDate(),
          selectedDate: this.formatCurrentDate(),
        });
      } catch (error) {
        console.error("解析移库数据失败:", error);
        wx.showToast({
          title: "数据错误",
          icon: "none",
        });
        wx.navigateBack();
      }
    } else {
      wx.showToast({
        title: "缺少移库数据",
        icon: "none",
      });
      wx.navigateBack();
    }
  },

  /**
   * 返回修改
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 确认移库
   */
  async confirmTransfer() {
    const { transferData, selectedDate } = this.data;
    console.log("当前页面数据:", this.data);
    if (
      !transferData ||
      !transferData.grouped_transfer_cart ||
      transferData.grouped_transfer_cart.length === 0
    ) {
      wx.showToast({
        title: "移库数据不完整",
        icon: "none",
      });
      return;
    }

    if (!selectedDate) {
      wx.showToast({
        title: "请选择移库日期",
        icon: "none",
      });
      return;
    }

    // 显示日期选择器
    this.setData({
      showDatePicker: true
    });
  },

  /**
   * 日期选择器确认
   */
  onDatePickerConfirm(event) {
    const selectedDate = this.formatDate(new Date(event.detail));
    const { transferData } = this.data;

    this.setData({
      showDatePicker: false,
      selectedDate: selectedDate
    });

    // 构建确认信息
    const warehouseNames = transferData.target_warehouses.map(w => w.name).join('、');
    const confirmContent = `移库日期：${selectedDate}
从"${transferData.from_warehouse.name}"移至"${warehouseNames}"
总计：${transferData.total_packages_display}包，${transferData.total_pieces}件
确定要执行移库操作吗？`;

    wx.showModal({
      title: "确认移库",
      content: confirmContent,
      success: async (res) => {
        if (res.confirm) {
          await this.submitTransferOrder();
        }
      },
    });
  },

  /**
   * 日期选择器取消
   */
  onDatePickerCancel() {
    this.setData({
      showDatePicker: false
    });
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 格式化当前日期
   */
  formatCurrentDate() {
    return this.formatDate(new Date());
  },

  /**
   * 提交移库单
   */
  async submitTransferOrder() {
    try {
      this.setData({ submitting: true });

      const { transferData } = this.data;

      // 构建多仓库移库数据
      const transferOrders = [];

      // 遍历每个目标仓库，为每个仓库创建一个移库单
      for (const warehouseGroup of transferData.grouped_transfer_cart) {
        const targetWarehouse = transferData.target_warehouses.find(
          w => w.warehouse_id === warehouseGroup.warehouse_id
        );

        if (!targetWarehouse) continue;

        // 收集该仓库的所有包裹代码
        const packageCodes = [];
        for (const classification of warehouseGroup.classifications) {
          packageCodes.push(...classification.package_codes);
        }

        const transferOrderData = {
          package_codes: packageCodes,
          from_warehouse_id: transferData.from_warehouse.warehouse_id,
          to_warehouse_id: warehouseGroup.warehouse_id,
          operator: wx.getStorageSync("userInfo")?.userName || "unknown",
          notes: `微信小程序移库操作：从${transferData.from_warehouse.name}移至${targetWarehouse.name}`,
          operation_date: this.data.selectedDate, // 添加选择的操作日期
        };

        transferOrders.push(transferOrderData);
      }

      // 并行执行所有移库操作
      const transferPromises = transferOrders.map(orderData =>
        Api.warehouseTransfer(orderData)
      );

      const responses = await Promise.all(transferPromises);

      // 检查所有移库操作是否成功
      const failedTransfers = responses.filter(response => response.data.code !== 200);

      if (failedTransfers.length === 0) {
        wx.showToast({
          title: "移库成功",
          icon: "success",
        });

        // 设置全局标记，通知移库页面清空待移库清单
        const app = getApp();
        if (!app.globalData) {
          app.globalData = {};
        }
        app.globalData.shouldClearTransferList = true;

        // 返回移库页面
        setTimeout(() => {
          wx.navigateBack({
            delta: 2, // 返回两级页面
          });
        }, 1500);
      } else {
        wx.showToast({
          title: `部分移库失败，${failedTransfers.length}个仓库移库失败`,
          icon: "none",
        });
      }
    } catch (error) {
      console.error("移库失败:", error);
      wx.showToast({
        title: "移库失败",
        icon: "none",
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 格式化移库数据显示
   */
  formatTransferData(data) {
    // 格式化总包裹数
    const totalPackages = parseFloat(data.total_packages) || 0;
    const formattedTotalPackages = totalPackages % 1 === 0
      ? totalPackages.toString()
      : totalPackages.toFixed(2);

    // 使用已经分组好的数据，只需要格式化显示
    const groupedTransferCart = data.grouped_transfer_cart || [];

    return {
      ...data,
      total_packages_display: formattedTotalPackages,
      grouped_transfer_items: groupedTransferCart // 使用多仓库分组数据
    };
  },



  /**
   * 格式化当前日期
   */
  formatCurrentDate() {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  },
});
