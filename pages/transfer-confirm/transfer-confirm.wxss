/* pages/transfer-confirm/transfer-confirm.wxss */

.transfer-confirm-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.main-content {
  padding: 20rpx;
}

/* 日期选择区域 */
.date-selection-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.date-selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.date-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.selected-date {
  font-size: 32rpx;
  color: #1989fa;
  font-weight: bold;
}

/* 移库信息卡片 */
.transfer-info-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.transfer-date {
  font-size: 24rpx;
  color: #666;
}

.transfer-route {
  display: flex;
  align-items: center;
  justify-content: center;
}

.warehouse-info {
  text-align: center;
}

.warehouse-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b35;
}

.warehouse-address {
  font-size: 24rpx;
  color: #999;
}



/* 移库明细卡片 */
.transfer-details-card {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  height:  75vh;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.summary-info {
  font-size: 24rpx;
  color: #1989fa;
  font-weight: 600;
}

.details-list {
  margin-top: 20rpx;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 仓库分组样式 */
.warehouse-detail-group {
  margin-bottom: 24rpx;
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #e9ecef;
}

.warehouse-detail-group:last-child {
  margin-bottom: 0;
}

.warehouse-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 20rpx;
  background: #1989fa;
  color: white;
}

.warehouse-detail-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.warehouse-icon {
  font-size: 24rpx;
}

.warehouse-name-address {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.warehouse-name-address .warehouse-name {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
}

.warehouse-name-address .warehouse-address {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

.warehouse-detail-stats {
  font-size: 24rpx;
  opacity: 0.9;
}

.warehouse-items-detail {
  padding: 16rpx;
}

.detail-item {
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
  border-left: 4rpx solid #1989fa;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.product-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.package-code {
  font-size: 24rpx;
  color: #666;
  background: #e8f4fd;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.item-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.quantity-info {
  font-size: 24rpx;
  color: #1989fa;
  font-weight: 600;
}

.package-codes {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
  word-break: break-all;
  background: #f0f9ff;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  border: 1rpx solid #e1f5fe;
}

.location-info {
  font-size: 22rpx;
  color: #999;
}


/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1rpx solid #eee;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  z-index: 100; /* 确保van-calendar在最上层 */
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.action-button-wrapper {
  flex: 1;
}

.action-button {
  width: 100% !important;
  border-radius: 12rpx !important;
  height: 80rpx;
  font-size: 28rpx;
}

.back-button {
  background: #f8f9fa !important;
  color: #666 !important;
  border: 1rpx solid #ddd !important;
}

.confirm-button {
  background: #1989fa !important;
  color: white !important;
  border: 1rpx solid #1989fa !important;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .bottom-actions {
    flex-direction: column;
    gap: 12rpx;
  }
}
