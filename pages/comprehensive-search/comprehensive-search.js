// pages/comprehensive-search/comprehensive-search.js
import Api from "../../utils/api.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 第一个卡片：本厂/外加工选择
    factoryType: "", // 'own' 或 'oem'

    // 第二个卡片：年份选择
    selectedYear: "",
    yearOptions: [],

    // 第三个卡片：袖型选择 (可多选)
    selectedSleeveTypes: [], // 选中的袖型数组
    sleeveButtonStates: {
      // 袖型按钮状态
      long: false,
      short: false,
    },
    sleeveCardSelected: false, // 袖型卡片是否有选中项

    // 第四个卡片：供应商选择
    selectedSuppliers: [],
    supplierOptions: [],
    supplierButtonStates: {}, // 存储每个供应商按钮的选中状态
    supplierCardSelected: false, // 供应商卡片是否有选中项

    // 第五个卡片：布料分类选择
    selectedClassifications: [],
    classificationOptions: [],
    classificationButtonStates: {}, // 存储每个布料分类按钮的选中状态
    classificationCardSelected: false, // 布料分类卡片是否有选中项

    // 第六个卡片：款式选择
    selectedStyles: [],
    styleOptions: [],
    styleButtonStates: {}, // 存储每个款式按钮的选中状态
    styleCardSelected: false, // 款式卡片是否有选中项

    // 动态查询参数
    queryParams: {},

    // 加载状态
    loading: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.loadYearOptions();
  },

  /**
   * 加载年份选项
   */
  async loadYearOptions() {
    try {
      // 调用API获取服装年份
      const result = await Api.getClothingYears();
      console.log("获取到的年份数据：", result);

      if (result.data && result.data.data.years) {
        this.setData({
          yearOptions: result.data.data.years,
        });
        console.log("年份选项加载成功:", result.data.years);
      } else {
        wx.showToast({
          title: "年份数据加载失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载年份失败:", error);
      wx.showToast({
        title: "年份加载失败",
        icon: "none",
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 选择工厂类型
   */
  onFactoryTypeSelect(e) {
    const type = e.currentTarget.dataset.type;
    console.log("选择工厂类型:", type);

    this.setData({
      factoryType: type,
    });

    // 重置依赖选项
    this.resetDependentOptions();

    // 检查是否可以查询下一级选项
    this.checkAndLoadNextOptions();
  },

  /**
   * 选择年份
   */
  onYearSelect(e) {
    const year = e.currentTarget.dataset.year;
    console.log("选择年份:", year);

    this.setData({
      selectedYear: year,
    });

    // 重置依赖选项
    this.resetDependentOptions();

    // 检查是否可以查询下一级选项
    this.checkAndLoadNextOptions();
  },

  /**
   * 选择袖型 (多选)
   */
  onSleeveTypeSelect(e) {
    const type = e.currentTarget.dataset.type;
    const selectedSleeveTypes = [...this.data.selectedSleeveTypes];
    const sleeveButtonStates = { ...this.data.sleeveButtonStates };

    // 切换按钮状态
    sleeveButtonStates[type] = !sleeveButtonStates[type];

    // 根据按钮状态更新选中数组
    if (sleeveButtonStates[type]) {
      if (selectedSleeveTypes.indexOf(type) === -1) {
        selectedSleeveTypes.push(type);
      }
    } else {
      const index = selectedSleeveTypes.indexOf(type);
      if (index > -1) {
        selectedSleeveTypes.splice(index, 1);
      }
    }

    // 检查袖型卡片是否有选中项
    const sleeveCardSelected = selectedSleeveTypes.length > 0;

    console.log("选择袖型:", selectedSleeveTypes);
    console.log("袖型按钮状态:", sleeveButtonStates);
    console.log("袖型卡片已选中:", sleeveCardSelected);

    this.setData({
      selectedSleeveTypes,
      sleeveButtonStates,
      sleeveCardSelected,
    });

    // 更新查询参数
    this.updateQueryParams();

    // 本厂模式选择袖型后，加载供应商选项
    if (this.data.factoryType === "own" && this.data.selectedYear) {
      console.log("本厂模式选择袖型后，开始加载供应商选项");
      this.loadSupplierOptions();
    }
  },

  /**
   * 检查是否可以查询下一级选项
   */
  checkAndLoadNextOptions() {
    // 只有工厂类型和年份都选择了才能查询下一级选项
    if (this.data.factoryType && this.data.selectedYear) {
      console.log("工厂类型和年份都已选择，开始级联查询");

      // 如果是外加工，直接加载供应商选项（跳过袖型）
      if (this.data.factoryType === "oem") {
        console.log("外加工模式，直接加载供应商选项");
        this.loadSupplierOptions();
      } else {
        console.log("本厂模式，等待袖型选择");
        // 本厂模式需要等待袖型选择后再加载供应商选项
      }
    } else {
      console.log("工厂类型或年份未完全选择，等待用户选择");
    }
  },

  /**
   * 更新查询参数
   */
  updateQueryParams() {
    const queryParams = {
      factoryType: this.data.factoryType,
      year: this.data.selectedYear,
      sleeveTypes: this.data.selectedSleeveTypes,
      suppliers: this.data.selectedSuppliers,
      classifications: this.data.selectedClassifications,
      styles: this.data.selectedStyles,
    };

    this.setData({ queryParams });
    console.log("更新查询参数:", queryParams);
  },

  /**
   * 解析API返回的数据结构
   */
  parseApiResponse(result) {
    let optionsData = null;
    if (result.data && result.data.data) {
      // 如果数据结构是 { data: { data: { ... } } }
      optionsData = result.data.data;
    } else if (result.data) {
      // 如果数据结构是 { data: { ... } }
      optionsData = result.data;
    }
    return optionsData;
  },

  /**
   * 加载供应商选项
   */
  async loadSupplierOptions() {
    // 检查前置条件
    if (!this.data.factoryType || !this.data.selectedYear) {
      console.log("前置条件不满足，无法加载供应商选项");
      return;
    }

    // 本厂模式还需要检查袖型选择
    if (
      this.data.factoryType === "own" &&
      this.data.selectedSleeveTypes.length === 0
    ) {
      console.log("本厂模式需要先选择袖型");
      return;
    }

    try {
      const params = {
        factoryType: this.data.factoryType,
        year: this.data.selectedYear,
      };

      // 如果是本厂模式，添加袖型参数
      if (this.data.factoryType === "own") {
        console.log("前端袖型数据详情:");
        console.log("- selectedSleeveTypes:", this.data.selectedSleeveTypes);
        console.log("- 类型:", typeof this.data.selectedSleeveTypes);
        console.log(
          "- 是否为数组:",
          Array.isArray(this.data.selectedSleeveTypes)
        );
        console.log("- 长度:", this.data.selectedSleeveTypes.length);
        this.data.selectedSleeveTypes.forEach((item, index) => {
          console.log(`- [${index}]:`, item, "类型:", typeof item);
        });

        params.sleeveTypes = this.data.selectedSleeveTypes;
      }

      console.log("加载供应商选项参数:", params);
      console.log("参数JSON:", JSON.stringify(params));

      const result = await Api.getSupplierOptions(params);
      console.log("获取到的供应商选项数据：", result);

      const optionsData = this.parseApiResponse(result);
      console.log("解析后的供应商选项数据：", optionsData);

      if (optionsData && optionsData.suppliers) {
        const suppliers = optionsData.suppliers;
        console.log("提取的供应商数据:", suppliers);

        // 初始化按钮状态
        const supplierButtonStates = {};
        suppliers.forEach((supplier) => {
          supplierButtonStates[supplier] = false;
        });

        this.setData({
          supplierOptions: suppliers,
          supplierButtonStates,
          // 重置后续选项
          selectedSuppliers: [],
          supplierCardSelected: false,
          classificationOptions: [],
          classificationButtonStates: {},
          selectedClassifications: [],
          classificationCardSelected: false,
          styleOptions: [],
          styleButtonStates: {},
          selectedStyles: [],
          styleCardSelected: false,
        });

        console.log("供应商选项加载成功:", suppliers);
      } else {
        wx.showToast({
          title: "供应商选项数据加载失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载供应商选项失败:", error);
      wx.showToast({
        title: "供应商选项加载失败",
        icon: "none",
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 加载布料分类选项
   */
  async loadClassificationOptions() {
    // 检查前置条件
    if (
      !this.data.factoryType ||
      !this.data.selectedYear ||
      this.data.selectedSuppliers.length === 0
    ) {
      console.log("前置条件不满足，无法加载布料分类选项");
      return;
    }

    try {
      const params = {
        factoryType: this.data.factoryType,
        year: this.data.selectedYear,
        suppliers: this.data.selectedSuppliers,
      };

      // 如果是本厂模式，添加袖型参数
      if (this.data.factoryType === "own") {
        params.sleeveTypes = this.data.selectedSleeveTypes;
      }

      console.log("加载布料分类选项参数:", params);

      const result = await Api.getClassificationOptions(params);
      console.log("获取到的布料分类选项数据：", result);

      const optionsData = this.parseApiResponse(result);
      console.log("解析后的布料分类选项数据：", optionsData);

      if (optionsData && optionsData.classifications) {
        const classifications = optionsData.classifications.sort(); // 按文字名称排序
        console.log("提取的布料分类数据:", classifications);

        // 初始化按钮状态
        const classificationButtonStates = {};
        classifications.forEach((classification) => {
          classificationButtonStates[classification] = false;
        });

        this.setData({
          classificationOptions: classifications,
          classificationButtonStates,
          // 重置后续选项
          selectedClassifications: [],
          classificationCardSelected: false,
          styleOptions: [],
          styleButtonStates: {},
          selectedStyles: [],
          styleCardSelected: false,
        });

        console.log("布料分类选项加载成功:", classifications);
      } else {
        wx.showToast({
          title: "布料分类选项数据加载失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载布料分类选项失败:", error);
      wx.showToast({
        title: "布料分类选项加载失败",
        icon: "none",
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 加载款式选项
   */
  async loadStyleOptions() {
    // 检查前置条件
    if (
      !this.data.factoryType ||
      !this.data.selectedYear ||
      this.data.selectedSuppliers.length === 0 ||
      this.data.selectedClassifications.length === 0
    ) {
      console.log("前置条件不满足，无法加载款式选项");
      return;
    }

    try {
      const params = {
        factoryType: this.data.factoryType,
        year: this.data.selectedYear,
        suppliers: this.data.selectedSuppliers,
        classifications: this.data.selectedClassifications,
      };

      // 如果是本厂模式，添加袖型参数
      if (this.data.factoryType === "own") {
        params.sleeveTypes = this.data.selectedSleeveTypes;
      }

      console.log("加载款式选项参数:", params);

      const result = await Api.getStyleOptions(params);
      console.log("获取到的款式选项数据：", result);

      const optionsData = this.parseApiResponse(result);
      console.log("解析后的款式选项数据：", optionsData);

      if (optionsData && optionsData.styles) {
        const styles = optionsData.styles.sort(); // 按文字名称排序
        console.log("提取的款式数据:", styles);

        // 初始化按钮状态
        const styleButtonStates = {};
        styles.forEach((style) => {
          styleButtonStates[style] = false;
        });

        this.setData({
          styleOptions: styles,
          styleButtonStates,
          // 重置选中状态
          selectedStyles: [],
          styleCardSelected: false,
        });

        console.log("款式选项加载成功:", styles);
      } else {
        wx.showToast({
          title: "款式选项数据加载失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载款式选项失败:", error);
      wx.showToast({
        title: "款式选项加载失败",
        icon: "none",
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 选择供应商
   */
  onSupplierSelect(e) {
    const supplier = e.currentTarget.dataset.supplier;
    const selectedSuppliers = [...this.data.selectedSuppliers];
    const supplierButtonStates = { ...this.data.supplierButtonStates };

    // 切换按钮状态
    supplierButtonStates[supplier] = !supplierButtonStates[supplier];

    // 根据按钮状态更新选中数组
    if (supplierButtonStates[supplier]) {
      if (selectedSuppliers.indexOf(supplier) === -1) {
        selectedSuppliers.push(supplier);
      }
    } else {
      const index = selectedSuppliers.indexOf(supplier);
      if (index > -1) {
        selectedSuppliers.splice(index, 1);
      }
    }

    // 检查供应商卡片是否有选中项
    const supplierCardSelected = selectedSuppliers.length > 0;

    console.log("选择供应商:", selectedSuppliers);
    console.log("供应商按钮状态:", supplierButtonStates);
    console.log("供应商卡片已选中:", supplierCardSelected);

    this.setData({
      selectedSuppliers,
      supplierButtonStates,
      supplierCardSelected,
    });

    // 更新查询参数
    this.updateQueryParams();

    // 如果有选中的供应商，加载布料分类选项
    if (selectedSuppliers.length > 0) {
      console.log("供应商已选择，开始加载布料分类选项");
      this.loadClassificationOptions();
    } else {
      console.log("没有选中供应商，清空后续选项");
      // 清空后续选项
      this.setData({
        classificationOptions: [],
        classificationButtonStates: {},
        selectedClassifications: [],
        classificationCardSelected: false,
        styleOptions: [],
        styleButtonStates: {},
        selectedStyles: [],
        styleCardSelected: false,
      });
    }
  },

  /**
   * 选择布料分类
   */
  onClassificationSelect(e) {
    const classification = e.currentTarget.dataset.classification;
    const selectedClassifications = [...this.data.selectedClassifications];
    const classificationButtonStates = {
      ...this.data.classificationButtonStates,
    };

    // 切换按钮状态
    classificationButtonStates[classification] =
      !classificationButtonStates[classification];

    // 根据按钮状态更新选中数组
    if (classificationButtonStates[classification]) {
      if (selectedClassifications.indexOf(classification) === -1) {
        selectedClassifications.push(classification);
      }
    } else {
      const index = selectedClassifications.indexOf(classification);
      if (index > -1) {
        selectedClassifications.splice(index, 1);
      }
    }

    // 检查布料分类卡片是否有选中项
    const classificationCardSelected = selectedClassifications.length > 0;

    console.log("选择布料分类:", selectedClassifications);
    console.log("布料分类按钮状态:", classificationButtonStates);
    console.log("布料分类卡片已选中:", classificationCardSelected);

    this.setData({
      selectedClassifications,
      classificationButtonStates,
      classificationCardSelected,
    });

    // 更新查询参数
    this.updateQueryParams();

    // 如果有选中的布料分类，加载款式选项
    if (selectedClassifications.length > 0) {
      console.log("布料分类已选择，开始加载款式选项");
      this.loadStyleOptions();
    } else {
      console.log("没有选中布料分类，清空款式选项");
      // 清空款式选项
      this.setData({
        styleOptions: [],
        styleButtonStates: {},
        selectedStyles: [],
        styleCardSelected: false,
      });
    }
  },

  /**
   * 选择款式
   */
  onStyleSelect(e) {
    const style = e.currentTarget.dataset.style;
    const selectedStyles = [...this.data.selectedStyles];
    const styleButtonStates = { ...this.data.styleButtonStates };

    // 切换按钮状态
    styleButtonStates[style] = !styleButtonStates[style];

    // 根据按钮状态更新选中数组
    if (styleButtonStates[style]) {
      if (selectedStyles.indexOf(style) === -1) {
        selectedStyles.push(style);
      }
    } else {
      const index = selectedStyles.indexOf(style);
      if (index > -1) {
        selectedStyles.splice(index, 1);
      }
    }

    // 检查款式卡片是否有选中项
    const styleCardSelected = selectedStyles.length > 0;

    console.log("选择款式:", selectedStyles);
    console.log("款式按钮状态:", styleButtonStates);
    console.log("款式卡片已选中:", styleCardSelected);

    this.setData({
      selectedStyles,
      styleButtonStates,
      styleCardSelected,
    });

    // 更新查询参数
    this.updateQueryParams();
  },

  /**
   * 重置依赖选项
   */
  resetDependentOptions() {
    this.setData({
      selectedSleeveTypes: [],
      sleeveButtonStates: {
        long: false,
        short: false,
      },
      sleeveCardSelected: false,
      selectedSuppliers: [],
      selectedClassifications: [],
      selectedStyles: [],
      supplierOptions: [],
      classificationOptions: [],
      styleOptions: [],
      supplierButtonStates: {},
      classificationButtonStates: {},
      styleButtonStates: {},
      supplierCardSelected: false,
      classificationCardSelected: false,
      styleCardSelected: false,
      queryParams: {},
    });
  },

  /**
   * 重置所有选择
   */
  onReset() {
    wx.showModal({
      title: "确认重置",
      content: "确定要重置所有选择条件吗？",
      success: (res) => {
        if (res.confirm) {
          this.setData({
            factoryType: "",
            selectedYear: "",
            selectedSleeveTypes: [],
            sleeveButtonStates: {
              long: false,
              short: false,
            },
            sleeveCardSelected: false,
            selectedSuppliers: [],
            selectedClassifications: [],
            selectedStyles: [],
            supplierOptions: [],
            classificationOptions: [],
            styleOptions: [],
            supplierButtonStates: {},
            classificationButtonStates: {},
            styleButtonStates: {},
            supplierCardSelected: false,
            classificationCardSelected: false,
            styleCardSelected: false,
            queryParams: {},
          });

          console.log("=== 重置完成 ===");
          console.log("所有选择条件已清空");

          wx.showToast({
            title: "已重置",
            icon: "success",
            duration: 1500,
          });
        }
      },
    });
  },

  /**
   * 执行搜索
   */
  onSearch() {
    // 检查必填条件
    if (!this.data.factoryType) {
      wx.showToast({
        title: "请选择工厂类型",
        icon: "none",
      });
      return;
    }

    if (!this.data.selectedYear) {
      wx.showToast({
        title: "请选择年份",
        icon: "none",
      });
      return;
    }

    // 本厂模式需要选择袖型，外加工模式不需要
    if (
      this.data.factoryType === "own" &&
      this.data.selectedSleeveTypes.length === 0
    ) {
      wx.showToast({
        title: "请选择袖型",
        icon: "none",
      });
      return;
    }

    const searchParams = this.data.queryParams;

    console.log("=== 执行搜索 ===");
    console.log("当前查询参数:", JSON.stringify(searchParams, null, 2));
    console.log("工厂类型:", searchParams.factoryType);
    console.log("年份:", searchParams.year);
    console.log("袖型:", searchParams.sleeveTypes);
    console.log("供应商:", searchParams.suppliers);
    console.log("布料分类:", searchParams.classifications);
    console.log("款式:", searchParams.styles);

    // 跳转到汇总查询结果页面，传递查询参数
    const queryString = encodeURIComponent(JSON.stringify(searchParams));
    wx.navigateTo({
      url: `/pages/summary-results/summary-results?params=${queryString}`,
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
});
