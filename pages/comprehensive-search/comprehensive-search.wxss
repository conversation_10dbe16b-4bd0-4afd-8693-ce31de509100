/* pages/comprehensive-search/comprehensive-search.wxss */

/* ==================== 基础布局 ==================== */
.container {
  padding: 24rpx;

  height: 100vh;
  box-sizing: border-box;
}

/* ==================== 搜索卡片样式 ==================== */
.search-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.search-card:hover {
  transform: translateY(-2rpx);
}

.card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c9678;
  margin-bottom: 20rpx;
  padding-bottom: 12rpx;
  border-bottom: 2rpx solid rgba(44, 150, 120, 0.2);
}

/* ==================== 按钮组样式 ==================== */
.button-group {
  display: flex;
  gap: 16rpx;
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  padding-bottom: 8rpx;
  -webkit-overflow-scrolling: touch;
}

.button-group.multi-line {
  justify-content: flex-start;
  flex-wrap: nowrap;
}

/* 两行布局样式 - 专门用于供应商、布料分类、款式卡片 */
.button-group.two-rows {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  overflow-y:scroll;
  white-space: normal;
  padding-bottom: 0;
  height: auto; /* 容器高度自适应 */
  min-height:70rpx; /* 最小高度确保单行显示 */
  max-height: 160rpx; /* 最大高度限制为2行 */
  align-content: flex-start;
  justify-content: flex-start;
  -webkit-overflow-scrolling: auto;
}

.button-group.four-rows {

  max-height: 320rpx; /* 最大高度限制为4行 */
}

/* 两行布局中的按钮样式调整 */
.button-group.two-rows .selection-button {
  flex: 0 0 auto; /* 不拉伸，不收缩，自动宽度 */
  flex-shrink: 0;
  white-space: nowrap; /* 防止文字换行 */
  min-width: auto; /* 移除最小宽度限制 */
  max-width: calc(100% - 12rpx); /* 防止单个按钮超出容器 */
  word-break: keep-all; /* 防止文字断行 */
  overflow: hidden; /* 防止内容溢出 */
  text-overflow: ellipsis; /* 超长文字显示省略号 */
}

/* 隐藏滚动条但保持滚动功能 */
.button-group::-webkit-scrollbar {
  display: none;
}

/* ==================== 选择按钮样式 ==================== */
.selection-button {
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  user-select: none;
  min-width: 120rpx;
  flex-shrink: 0;
  white-space: nowrap;

  /* 默认状态 */
  background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 100%);
  color: #5f6368;
  border: 2rpx solid #e8eaed;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.selection-button:active {
  transform: scale(0.98);
}

/* 激活状态 */
.selection-button.active {
  background: linear-gradient(135deg, #2c9678 0%, #34a085 100%);
  color: #ffffff;
  border: 2rpx solid #2c9678;
  box-shadow: 0 4rpx 16rpx rgba(44, 150, 120, 0.3);
  transform: translateY(-1rpx);
}

/* 多选按钮特殊样式 */
.selection-button.multi-select {
  position: relative;
  overflow: hidden;
}

.selection-button.multi-select::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.selection-button.multi-select.active::before {
  left: 100%;
}

/* ==================== 操作按钮样式 ==================== */
.action-buttons {
  display: flex;
  gap: 24rpx;
  margin-top: 40rpx;
  padding: 0 20rpx;
}

.reset-button,
.search-button {
  flex: 1;
  padding: 20rpx 0;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  user-select: none;
}

.reset-button {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  color: #6c757d;
  border: 2rpx solid #dee2e6;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.reset-button:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.search-button {
  background: linear-gradient(135deg, #2c9678 0%, #34a085 100%);
  color: #ffffff;
  border: 2rpx solid #2c9678;
  box-shadow: 0 6rpx 20rpx rgba(44, 150, 120, 0.4);
}

.search-button:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(44, 150, 120, 0.3);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 750rpx) {
  .selection-button {
    min-width: 100rpx;
    padding: 14rpx 20rpx;
    font-size: 24rpx;
  }

  .button-group {
    gap: 12rpx;
  }
}

/* ==================== 底部安全区域 ==================== */

