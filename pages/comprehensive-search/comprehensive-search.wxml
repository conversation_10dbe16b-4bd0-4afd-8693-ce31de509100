<!-- pages/comprehensive-search/comprehensive-search.wxml -->
<scroll-view class="container" scroll-y="{{true}}" enhanced="{{true}}" show-scrollbar="{{false}}">
  <!-- 第一个卡片：本厂/外加工选择 -->
  <view class="search-card">
    <view class="button-group">
      <view class="selection-button {{factoryType === 'own' ? 'active' : ''}}" data-type="own" bindtap="onFactoryTypeSelect">
        本厂
      </view>
      <view class="selection-button {{factoryType === 'oem' ? 'active' : ''}}" data-type="oem" bindtap="onFactoryTypeSelect">
        外加工
      </view>
    </view>
  </view>
  <!-- 第二个卡片：年份选择 -->
  <view class="search-card">
    <view class="button-group multi-line">
      <view wx:for="{{yearOptions}}" wx:key="*this" class="selection-button {{selectedYear === item ? 'active' : ''}}" data-year="{{item}}" bindtap="onYearSelect">
        {{item}}
      </view>
    </view>
  </view>
  <!-- 第三个卡片：袖型选择 (可多选) - 只在本厂时显示 -->
  <view class="search-card" wx:if="{{factoryType === 'own'}}">
    <view class="button-group">
      <view class="selection-button multi-select {{sleeveButtonStates['long'] ? 'active' : ''}}" data-type="long" bindtap="onSleeveTypeSelect">
        长袖
      </view>
      <view class="selection-button multi-select {{sleeveButtonStates['short'] ? 'active' : ''}}" data-type="short" bindtap="onSleeveTypeSelect">
        短袖
      </view>
    </view>
  </view>
  <!-- 第四个卡片：供应商选择 -->
  <view class="search-card">
    <view class="card-title">供应商 (可多选)</view>
    <view class="button-group two-rows">
      <view wx:for="{{supplierOptions}}" wx:key="*this" class="selection-button multi-select {{supplierButtonStates[item] ? 'active' : ''}}" data-supplier="{{item}}" bindtap="onSupplierSelect">
        {{item}}
      </view>
    </view>
  </view>
  <!-- 第五个卡片：布料分类选择 -->
  <view class="search-card">
    <view class="card-title">布料分类 (可多选)</view>
    <view class="button-group two-rows four-rows">
      <view wx:for="{{classificationOptions}}" wx:key="*this" class="selection-button multi-select {{classificationButtonStates[item] ? 'active' : ''}}" data-classification="{{item}}" bindtap="onClassificationSelect">
        {{item}}
      </view>
    </view>
  </view>
  <!-- 第六个卡片：款式选择 -->
  <view class="search-card">
    <view class="card-title">款式 (可多选)</view>
    <view class="button-group two-rows ">
      <view wx:for="{{styleOptions}}" wx:key="*this" class="selection-button multi-select {{styleButtonStates[item] ? 'active' : ''}}" data-style="{{item}}" bindtap="onStyleSelect">
        {{item}}
      </view>
    </view>
  </view>
  <!-- 操作按钮 -->
  <view class="action-buttons">
    <view class="reset-button" bindtap="onReset">重置</view>
    <view class="search-button" bindtap="onSearch">查询</view>
  </view>
  <!-- 底部安全区域 -->
  <view class="bottom-safe-area"></view>
</scroll-view>