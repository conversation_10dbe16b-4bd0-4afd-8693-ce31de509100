// pages/summary-results/summary-results.js
import Api from "../../utils/api.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 搜索参数
    searchParams: {},
    
    // 服装列表数据
    clothingList: [],
    
    // 选中状态
    allSelected: false,
    selectedCount: 0,
    selectedSummary: {
      cutting_quantity: 0,
      inbound_quantity: 0,
      shipped_quantity: 0,
      arrived_quantity: 0,
      inventory_quantity: 0
    },
    
    // 袖型文本显示
    sleeveTypesText: '',
    
    // 加载状态
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.params) {
      try {
        const searchParams = JSON.parse(decodeURIComponent(options.params));
        console.log("接收到的搜索参数:", searchParams);
        
        this.setData({
          searchParams: searchParams,
          sleeveTypesText: this.formatSleeveTypes(searchParams.sleeveTypes)
        });
        
        // 加载汇总数据
        this.loadSummaryResults();
      } catch (error) {
        console.error("解析搜索参数失败:", error);
        wx.showToast({
          title: "参数解析失败",
          icon: "none"
        });
      }
    }
  },

  /**
   * 格式化袖型显示文本
   */
  formatSleeveTypes(sleeveTypes) {
    if (!sleeveTypes || sleeveTypes.length === 0) {
      return '';
    }
    
    const typeMap = {
      'long': '长袖',
      'short': '短袖'
    };
    
    return sleeveTypes.map(type => typeMap[type] || type).join('、');
  },

  /**
   * 加载汇总查询结果
   */
  async loadSummaryResults() {
    try {
      this.setData({ loading: true });
      
      console.log("开始加载汇总查询结果，参数:", this.data.searchParams);
      
      const result = await Api.getSummaryResults(this.data.searchParams);
      console.log("汇总查询结果:", result);
      
      if (result.data && result.data.data) {
        const clothingList = result.data.data.map(item => ({
          ...item,
          selected: false // 初始化选中状态
        }));

        this.setData({
          clothingList: clothingList
        });

        console.log("汇总数据加载成功，共", clothingList.length, "条记录");
        console.log("数据样例:", clothingList.slice(0, 2)); // 打印前两条数据用于调试
      } else {
        wx.showToast({
          title: "数据加载失败",
          icon: "none"
        });
      }
    } catch (error) {
      console.error("加载汇总查询结果失败:", error);
      wx.showToast({
        title: "数据加载失败",
        icon: "none"
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 全选/取消全选
   */
  onSelectAll() {
    const allSelected = !this.data.allSelected;
    const clothingList = this.data.clothingList.map(item => ({
      ...item,
      selected: allSelected
    }));
    
    this.setData({
      allSelected: allSelected,
      clothingList: clothingList
    });
    
    // 更新选中汇总
    this.updateSelectedSummary();
  },

  /**
   * 选中/取消选中单个项目
   */
  onSelectItem(e) {
    const index = e.currentTarget.dataset.index;
    const clothingList = [...this.data.clothingList];
    
    clothingList[index].selected = !clothingList[index].selected;
    
    // 检查是否全选
    const allSelected = clothingList.every(item => item.selected);
    
    this.setData({
      clothingList: clothingList,
      allSelected: allSelected
    });
    
    // 更新选中汇总
    this.updateSelectedSummary();
  },

  /**
   * 更新选中项汇总统计
   */
  updateSelectedSummary() {
    const selectedItems = this.data.clothingList.filter(item => item.selected);
    const selectedCount = selectedItems.length;
    
    const summary = selectedItems.reduce((acc, item) => {
      acc.cutting_quantity += item.cutting_quantity || 0;
      acc.inbound_quantity += item.inbound_quantity || 0;
      acc.shipped_quantity += item.shipped_quantity || 0;
      acc.arrived_quantity += item.arrived_quantity || 0;
      acc.inventory_quantity += item.inventory_quantity || 0;
      return acc;
    }, {
      cutting_quantity: 0,
      inbound_quantity: 0,
      shipped_quantity: 0,
      arrived_quantity: 0,
      inventory_quantity: 0
    });
    
    this.setData({
      selectedCount: selectedCount,
      selectedSummary: summary
    });
    
    console.log("选中汇总更新:", {
      selectedCount: selectedCount,
      summary: summary
    });
  },

  /**
   * 点击服装名称跳转到详情页面
   */
  onClothingDetail(e) {
    const item = e.currentTarget.dataset.item;
    console.log("点击服装详情:", item);

    // 根据工厂类型构建不同的参数
    if (this.data.searchParams.factoryType === 'own') {
      // 本厂服装详情页面
      const params = {
        clothingId: item.clothing_id || item._id,
        isOem: 'false',
        hasData: 'false'  // 改为 false，让详情页面重新请求数据
      };

      const queryString = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&');

      wx.navigateTo({
        url: `/pages/clothing-detail/clothing-detail?${queryString}`
      });
    } else {
      // 外加工服装详情页面
      // 检查可能的 ID 字段
      const oemClothingId = item.oem_clothing_id || item.clothing_id || item._id;

      if (!oemClothingId) {
        console.error("外加工服装 ID 不存在:", item);
        wx.showToast({
          title: "服装ID不存在",
          icon: "none"
        });
        return;
      }

      const params = {
        oemClothingId: oemClothingId,
        isOem: 'true',
        hasData: 'true'  // 改为 false，让详情页面重新请求数据
      };

      const queryString = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&');

      console.log("外加工服装跳转参数:", params);

      wx.navigateTo({
        url: `/pages/clothing-detail/clothing-detail?${queryString}`
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadSummaryResults();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
});
