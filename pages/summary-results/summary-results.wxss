/* pages/summary-results/summary-results.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

/* 卡片通用样式 */
.search-params-card,
.summary-card {
  width: 680rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
}

.params-content {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 12rpx;
  align-items: center;
}

/* van-tag 自定义样式 */
.params-content van-tag {
  --tag-primary-color: #2c9678;
  --tag-font-size: 24rpx;
  --tag-border-radius: 8rpx;
  --tag-padding: 8rpx 16rpx;
}

/* 汇总统计内容 */
.summary-content {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.summary-item {
  display: flex;
  align-items: center;
  min-width: 200rpx;
}

.summary-label {
  font-size: 28rpx;
  color: #666666;
  margin-right: 8rpx;
}

.summary-value {
  font-size: 28rpx;
  color: #2c9678;
  font-weight: bold;
}

/* 表格容器 */
.table-container {
  width: 720rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  max-height: 80vh;
}

/* 表格头部 */
.table-header {
  display: flex;
  background-color: #f8f9fa;
  border-bottom: 2rpx solid #e9ecef;
  padding: 12rpx 0;
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 表格单元格通用样式 */
.header-cell,
.table-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #333333;
  padding: 0 8rpx;
  text-align: center;
  line-height: 1.2;
}

.header-cell {
  font-weight: bold;
}

/* 单元格尺寸定义 */
.checkbox-cell {
  width: 60rpx;
  flex-shrink: 0;
}

.name-cell {
  flex: 1;
  min-width: 100rpx;
}

.header-cell:not(.checkbox-cell):not(.name-cell),
.table-cell:not(.checkbox-cell):not(.name-cell) {
  flex: 1;
  min-width: 100rpx;
}

/* 外加工模式下的数量列宽度调整 */
.table-container[data-factory-type="oem"]
  .header-cell:not(.checkbox-cell):not(.name-cell):not(.price-cell),
.table-container[data-factory-type="oem"]
  .table-cell:not(.checkbox-cell):not(.name-cell):not(.price-cell) {
  flex: 0.75;
  min-width: 75rpx;
}

/* 外加工模式下单价列的特殊宽度 */
.table-container[data-factory-type="oem"] .price-cell {
  flex: 0.6;
  min-width: 60rpx;
}

/* 表格内容 */
.table-content {
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
  padding: 12rpx 0;
  align-items: center;
}

.table-row:hover {
  background-color: #dddddd;

}

.table-row:last-child {
  margin-bottom: 20rpx;
}

.bottom-text {
  text-align: center;
  color: #999999;
  font-size: 24rpx;
  margin-top: 20rpx;
  margin-bottom: 30rpx;
}

.clothing-name {
  color: #2c9678;
  cursor: pointer;
}

/* 加载和空数据状态 */
.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  font-size: 28rpx;
  color: #999999;
}

/* 复选框样式调整 */
checkbox {
  transform: scale(0.8);
  margin: 0;
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
  .summary-content {
    flex-direction: column;
  }

  .summary-item {
    min-width: auto;
  }

  .header-cell,
  .table-cell {
    font-size: 22rpx;
    padding: 0 6rpx;
  }

  .checkbox-cell {
    width: 70rpx;
  }

  .name-cell {
    min-width: 140rpx;
  }

  /* 外加工模式下小屏幕的特殊调整 */
  .table-container[data-factory-type="oem"]
    .header-cell:not(.checkbox-cell):not(.name-cell):not(.price-cell),
  .table-container[data-factory-type="oem"]
    .table-cell:not(.checkbox-cell):not(.name-cell):not(.price-cell) {
    flex: 0.7;
    min-width: 65rpx;
  }

  .table-container[data-factory-type="oem"] .price-cell {
    flex: 0.5;
    min-width: 50rpx;
  }
}
