<!-- pages/summary-results/summary-results.wxml -->
<view class="container">
  <!-- 搜索参数卡片 -->
  <view class="search-params-card">
    <view class="params-content">
      <van-tag type="primary" size="medium">{{searchParams.factoryType === 'own' ? '本厂' : '外加工'}}</van-tag>
      <van-tag type="primary" size="medium">{{searchParams.year}}</van-tag>
      <van-tag type="primary" size="medium" wx:if="{{searchParams.sleeveTypes && searchParams.sleeveTypes.length > 0}}">{{sleeveTypesText}}</van-tag>
      <van-tag type="primary" size="medium" wx:if="{{searchParams.suppliers && searchParams.suppliers.length > 0}}">{{searchParams.suppliers}}</van-tag>
      <van-tag type="primary" size="medium" wx:if="{{searchParams.classifications && searchParams.classifications.length > 0}}">{{searchParams.classifications}}</van-tag>
      <van-tag type="primary" size="medium" wx:if="{{searchParams.styles && searchParams.styles.length > 0}}">{{searchParams.styles}}</van-tag>
    </view>
  </view>
  <!-- 汇总统计卡片 -->
  <view class="summary-card">
    <view class="summary-content">
      <view class="summary-item">
        <view class="summary-label">选中：</view>
        <view class="summary-value">{{selectedCount}} 款</view>
      </view>
      <view class="summary-item" wx:if="{{searchParams.factoryType === 'own'}}">
        <view class="summary-label">裁剪数：</view>
        <view class="summary-value">{{selectedSummary.cutting_quantity || 0}}</view>
      </view>
      <view class="summary-item" wx:if="{{searchParams.factoryType === 'oem'}}">
        <view class="summary-label">入库数：</view>
        <view class="summary-value">{{selectedSummary.inbound_quantity || 0}}</view>
      </view>
      <view class="summary-item">
        <view class="summary-label">发货数：</view>
        <view class="summary-value">{{selectedSummary.shipped_quantity || 0}}</view>
      </view>
      <view class="summary-item">
        <view class="summary-label">到货数：</view>
        <view class="summary-value">{{selectedSummary.arrived_quantity || 0}}</view>
      </view>
      <view class="summary-item">
        <view class="summary-label">库存数：</view>
        <view class="summary-value">{{selectedSummary.inventory_quantity || 0}}</view>
      </view>
    </view>
  </view>
  <!-- 数据表格 -->
  <view class="table-container" data-factory-type="{{searchParams.factoryType}}">
    <!-- 表格头部 -->
    <view class="table-header">
      <view class="header-cell checkbox-cell">
        <checkbox checked="{{allSelected}}" bindtap="onSelectAll"></checkbox>
      </view>
      <view class="header-cell name-cell">服装名称</view>
      <view class="header-cell price-cell" wx:if="{{searchParams.factoryType === 'oem'}}">单价</view>
      <view class="header-cell" wx:if="{{searchParams.factoryType === 'own'}}">裁剪数</view>
      <view class="header-cell" wx:if="{{searchParams.factoryType === 'oem'}}">入库数</view>
      <view class="header-cell">发货数</view>
      <view class="header-cell">到货数</view>
      <view class="header-cell">库存数</view>
    </view>
    <!-- 表格内容 -->
    <scroll-view class="table-content" >
      <view class="table-row" wx:for="{{clothingList}}" wx:key="index">
        <view class="table-cell checkbox-cell">
          <checkbox checked="{{item.selected}}" data-index="{{index}}" bindtap="onSelectItem"></checkbox>
        </view>
        <view class="table-cell name-cell" data-item="{{item}}" bindtap="onClothingDetail">
          <view class="clothing-name">{{item.clothing_name}}</view>
        </view>
        <view class="table-cell price-cell" wx:if="{{searchParams.factoryType === 'oem'}}">
          {{item.price || "-"}}
        </view>
        <view class="table-cell" wx:if="{{searchParams.factoryType === 'own'}}">
          {{item.cutting_quantity || 0}}
        </view>
        <view class="table-cell" wx:if="{{searchParams.factoryType === 'oem'}}">
          {{item.inbound_quantity || 0}}
        </view>
        <view class="table-cell">{{item.shipped_quantity || 0}}</view>
        <view class="table-cell">{{item.arrived_quantity || 0}}</view>
        <view class="table-cell">{{item.inventory_quantity || 0}}</view>
      </view>
      <view class="bottom-text">
        到底了...
      </view>
    </scroll-view>
  </view>
  <!-- 空数据提示 -->
  <view class="empty-container" wx:if="{{!loading && clothingList.length === 0}}">
    <view>暂无数据</view>
  </view>
</view>