import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/auth/Login.vue'),
      meta: { requiresAuth: false, title: '登录' },
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('@/views/auth/Register.vue'),
      meta: { requiresAuth: false, title: '注册' },
    },
    {
      path: '/',
      name: 'Home',
      component: () => import('@/views/Home.vue'),
      meta: { requiresAuth: true, title: '首页' },

      children: [
        {
          path: 'fabric/info',
          name: 'FabricsInfo',
          component: () => import('@/views/fabric/FabricInfo.vue'),
          meta: { requiresAuth: true, title: '布料信息' },
        },
        {
          path: 'fabric/stock-in',
          name: 'FabricsStockIn',
          component: () => import('@/views/fabric/FabricStockIn.vue'),
          meta: { requiresAuth: true, title: '布料入库' },
        },
        {
          path: 'staff/info',
          name: 'StaffInfo',
          component: () => import('@/views/staff/StaffInfo.vue'),
          meta: { requiresAuth: true, title: '员工管理' },
        },
        {
          path: 'clothing/info',
          name: 'ClothingInfo',
          component: () => import('@/views/clothing/ClothingInfo.vue'),
          meta: { requiresAuth: true, title: '服装信息' },
        },
        {
          path: 'fabric/group-info',
          name: 'FabricGroupInfo',
          component: () => import('@/views/fabric/FabricGroupInfo.vue'),
          meta: { requiresAuth: true, title: '布料组信息' },
        },
        {
          path: 'oem-clothing/info',
          name: 'OemClothingInfo',
          component: () => import('@/views/oemClothing/OemClothingInfo.vue'),
          meta: { requiresAuth: true, title: 'OEM服装信息' },
        },
        {
          path: 'oem-clothing/incoming',
          name: 'OemClothingIncoming',
          component: () => import('@/views/oemClothing/OemClothingIncoming.vue'),
          meta: { requiresAuth: true, title: 'OEM服装入库' },
        },
        {
          path: 'work/info',
          name: 'WorkInfo',
          component: () => import('@/views/work/WorkInfo.vue'),
          meta: { requiresAuth: true, title: '工序信息' },
        },
        {
          path: 'division-work/info',
          name: 'DivisionWorkInfo',
          component: () => import('@/views/divisionWork/DivisionWorkInfo.vue'),
          meta: { requiresAuth: true, title: '分工信息' },
        },
        {
          path: 'transportation/info',
          name: 'TransportationInfo',
          component: () => import('@/views/transportation/TransportationInfo.vue'),
          meta: { requiresAuth: true, title: '发货信息' },
        },
        // 物料管理相关路由
        {
          path: 'material/info',
          name: 'MaterialInfo',
          component: () => import('@/views/material/MaterialInfo.vue'),
          meta: { requiresAuth: true, title: '物料信息管理' },
        },
        {
          path: 'material/category',
          name: 'MaterialCategory',
          component: () => import('@/views/materialCategory/MaterialCategoryInfo.vue'),
          meta: { requiresAuth: true, title: '物料分类管理' },
        },
      ],
    },
    // 404页面
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/Home.vue'),
      meta: { requiresAuth: false, title: '页面不存在' },
    },
  ],
})

// 全局前置守卫
router.beforeEach(async (to, _from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 金印` : '金印'

  const userStore = useUserStore()
  const token = localStorage.getItem('token')

  // 如果有token但没有用户信息，则获取用户信息
  if (token && !userStore.userInfo) {
    try {
      await userStore.getUserInfoAction()
    } catch (error) {
      console.error('获取用户信息失败:', error)
      userStore.logout()
      ElMessage.error('登录状态已失效，请重新登录')
      next('/login')
      return
    }
  }

  // 需要登录但未登录
  if (to.meta.requiresAuth && !userStore.token) {
    ElMessage.warning('请先登录')
    next('/login')
    return
  }

  // 已登录但访问登录/注册页
  if (to.path === '/login' || to.path === '/register') {
    if (userStore.token) {
      next('/')
      return
    }
  }

  next()
})

export default router
