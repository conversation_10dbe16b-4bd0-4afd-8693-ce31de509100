import { Injectable } from '@nestjs/common'
import { JwtService } from '@nestjs/jwt'
import { UsersService } from '../users/users.service'
import { CreateUserDto } from '../users/dto/create-user.dto'
import { LoginDto } from './dto/login.dto'

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService
  ) {}

  async register(createUserDto: CreateUserDto) {
    try {
      const user = await this.usersService.create(createUserDto)
      return this.generateToken(user)
    } catch (error) {
      console.error('注册错误:', error)
      throw error
    }
  }

  async login(loginDto: LoginDto) {
    try {
      const user = await this.usersService.validateUser(loginDto.userName, loginDto.userPwd)
      return this.generateToken(user)
    } catch (error) {
      console.error('登录错误:', error)
      throw error
    }
  }

  async getUserById(userId: string) {
    return this.usersService.findById(userId)
  }

  private generateToken(user: any) {
    const payload = {
      sub: user.id,
      userName: user.userName,
    }

    return {
      token: this.jwtService.sign(payload),
      user: {
        id: user.id,
        userName: user.userName,
      },
    }
  }
}
