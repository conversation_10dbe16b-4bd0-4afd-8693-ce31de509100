import { Module } from '@nestjs/common'
import { ModelsModule } from '../../models/models.module'
import { OemClothingIncomingController } from './oem-clothing-incoming.controller'
import { OemClothingIncomingService } from './oem-clothing-incoming.service'

@Module({
  imports: [ModelsModule],
  controllers: [OemClothingIncomingController],
  providers: [OemClothingIncomingService],
  exports: [OemClothingIncomingService],
})
export class OemClothingIncomingModule {}
