import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger'
import { OemClothingIncomingService } from './oem-clothing-incoming.service'
import {
  CreateOemClothingIncomingDto,
  UpdateOemClothingIncomingDto,
  QueryOemClothingIncomingDto,
  CreateOemClothingIncomingDetailDto,
  UpdateOemClothingIncomingDetailDto,
} from './dto'

@ApiTags('OEM服装入库管理')
@Controller('/oem-clothing-incoming')
export class OemClothingIncomingController {
  constructor(private readonly oemClothingIncomingService: OemClothingIncomingService) {}

  @Post()
  @ApiOperation({ summary: '创建OEM服装入库' })
  create(@Body() createOemClothingIncomingDto: CreateOemClothingIncomingDto) {
    return this.oemClothingIncomingService.create(createOemClothingIncomingDto)
  }

  @Get()
  @ApiOperation({ summary: '获取OEM服装入库列表' })
  findAll(@Query() queryOemClothingIncomingDto: QueryOemClothingIncomingDto) {
    return this.oemClothingIncomingService.findAll(queryOemClothingIncomingDto)
  }

  @Get('year-options')
  @ApiOperation({ summary: '获取年份选项列表' })
  @ApiQuery({ name: 'supplier', required: false, description: '供应商过滤，多个用逗号分隔' })
  async getYearOptions(@Query('supplier') suppliers: string) {
    const years = await this.oemClothingIncomingService.getYearOptions(suppliers)
    console.log('获取到的年份数据133：', years)
    return { data: { years } }
  }

  @Get('supplier-options')
  @ApiOperation({ summary: '获取供应商选项列表' })
  @ApiQuery({ name: 'year', required: false, description: '年份过滤，多个用逗号分隔' })
  async getSupplierOptions(@Query('year') years: string) {
    const suppliers = await this.oemClothingIncomingService.getSupplierOptions(years)
    console.log('获取到的供应商数据：', suppliers)
    return { data: { suppliers } }
  }

  @Get(':id')
  @ApiOperation({ summary: '获取OEM服装入库详情' })
  @ApiParam({ name: 'id', description: 'OEM服装入库ID' })
  findOne(@Param('id') id: string) {
    return this.oemClothingIncomingService.findOne(id)
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新OEM服装入库' })
  @ApiParam({ name: 'id', description: 'OEM服装入库ID' })
  update(
    @Param('id') id: string,
    @Body() updateOemClothingIncomingDto: UpdateOemClothingIncomingDto
  ) {
    return this.oemClothingIncomingService.update(id, updateOemClothingIncomingDto)
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除OEM服装入库' })
  @ApiParam({ name: 'id', description: 'OEM服装入库ID' })
  remove(@Param('id') id: string) {
    return this.oemClothingIncomingService.remove(id)
  }

  @Post('detail')
  @ApiOperation({ summary: '创建OEM服装入库明细' })
  createDetail(@Body() createOemClothingIncomingDetailDto: CreateOemClothingIncomingDetailDto) {
    return this.oemClothingIncomingService.createDetail(createOemClothingIncomingDetailDto)
  }

  @Post('detail/batch/:incomingId')
  @ApiOperation({ summary: '批量创建OEM服装入库明细' })
  @ApiParam({ name: 'incomingId', description: '入库ID' })
  createDetailBatch(
    @Param('incomingId') incomingId: string,
    @Body() details: Omit<CreateOemClothingIncomingDetailDto, 'incoming_id'>[]
  ) {
    return this.oemClothingIncomingService.createDetailBatch(incomingId, details)
  }

  @Get('detail/incoming/:incomingId')
  @ApiOperation({ summary: '获取指定入库的所有明细' })
  @ApiParam({ name: 'incomingId', description: '入库ID' })
  findAllDetailsByIncomingId(@Param('incomingId') incomingId: string) {
    return this.oemClothingIncomingService.findAllDetailsByIncomingId(incomingId)
  }

  @Get('detail/oem-clothing/:oemClothingId')
  @ApiOperation({ summary: '获取指定OEM服装的入库明细' })
  @ApiParam({ name: 'oemClothingId', description: 'OEM服装ID' })
  findAllDetailsByOemClothingId(@Param('oemClothingId') oemClothingId: string) {
    return this.oemClothingIncomingService.findAllDetailsByOemClothingId(oemClothingId)
  }

  @Get('detail/:id')
  @ApiOperation({ summary: '获取入库明细详情' })
  @ApiParam({ name: 'id', description: '入库明细ID' })
  findOneDetail(@Param('id') id: string) {
    return this.oemClothingIncomingService.findOneDetail(id)
  }

  @Patch('detail/:id')
  @ApiOperation({ summary: '更新入库明细' })
  @ApiParam({ name: 'id', description: '入库明细ID' })
  updateDetail(
    @Param('id') id: string,
    @Body() updateOemClothingIncomingDetailDto: UpdateOemClothingIncomingDetailDto
  ) {
    return this.oemClothingIncomingService.updateDetail(id, updateOemClothingIncomingDetailDto)
  }

  @Delete('detail/:id')
  @ApiOperation({ summary: '删除入库明细' })
  @ApiParam({ name: 'id', description: '入库明细ID' })
  removeDetail(@Param('id') id: string) {
    return this.oemClothingIncomingService.removeDetail(id)
  }
}
