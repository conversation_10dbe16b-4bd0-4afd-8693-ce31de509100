import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsOptional, IsNumber, IsArray, IsDate } from 'class-validator'
import { Type, Transform } from 'class-transformer'

export class QueryOemClothingIncomingDto {
  @ApiProperty({ description: '页码', required: false })
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => (value ? parseInt(value) : 1))
  page?: number = 1

  @ApiProperty({ description: '每页数量', required: false })
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => (value ? parseInt(value) : 10))
  limit?: number = 10

  @ApiProperty({ description: 'OEM服装入库编号', required: false })
  @IsString()
  @IsOptional()
  oem_clothing_incoming_id?: string

  @ApiProperty({ description: 'OEM服装入库年份', required: false })
  @IsString()
  @IsOptional()
  oem_clothing_incoming_year?: string

  @ApiProperty({ description: 'OEM服装入库年份列表', required: false, type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Type(() => String)
  oem_clothing_incoming_years?: string[]

  @ApiProperty({ description: '入库日期开始', required: false })
  @IsDate()
  @IsOptional()
  @Type(() => Date)
  date_in_start?: Date

  @ApiProperty({ description: '入库日期结束', required: false })
  @IsDate()
  @IsOptional()
  @Type(() => Date)
  date_in_end?: Date

  @ApiProperty({ description: '供应商', required: false })
  @IsString()
  @IsOptional()
  supplier?: string

  @ApiProperty({ description: '供应商列表', required: false, type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Type(() => String)
  suppliers?: string[]

  
}
