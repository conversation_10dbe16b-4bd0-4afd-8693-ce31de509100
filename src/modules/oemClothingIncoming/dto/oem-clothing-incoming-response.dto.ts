import { ApiProperty } from '@nestjs/swagger'
import { Expose, Type } from 'class-transformer'
import { IsArray, IsDate, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator'

/**
 * OEM服装入库响应 DTO
 */
export class OemClothingIncomingResponseDto {
  @Expose()
  _id: string

  @Expose()
  @IsString()
  oem_clothing_incoming_id: string

  @Expose()
  @IsString()
  oem_clothing_incoming_year: string

  @Expose()
  @IsDate()
  date_in: Date

  @Expose()
  @IsString()
  supplier: string

  @Expose()
  @IsString()
  @IsOptional()
  remark?: string

  @Expose()
  @IsDate()
  @IsOptional()
  createTime?: Date
}

/**
 * OEM服装入库明细响应 DTO
 */
export class OemClothingIncomingDetailResponseDto {
  @Expose()
  _id: string

  @Expose()
  @IsString()
  incoming_id: string

  @Expose()
  @IsString()
  oem_clothing_id: string

  @Expose()
  @IsString()
  oem_clothing_name: string

  @Expose()
  @IsString()
  @IsOptional()
  style?: string

  @Expose()
  @IsNumber()
  @IsOptional()
  price?: number

  @Expose()
  @IsNumber()
  in_pcs: number

  @Expose()
  @IsString()
  @IsOptional()
  money?: string

  @Expose()
  @IsString()
  @IsOptional()
  remark?: string

  @Expose()
  @IsDate()
  @IsOptional()
  createTime?: Date
}

/**
 * OEM服装入库列表响应 DTO
 */
export class OemClothingIncomingListResponseDto {
  @Expose()
  @IsNumber()
  total: number

  @Expose()
  @IsNumber()
  page: number

  @Expose()
  @IsNumber()
  limit: number

  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OemClothingIncomingResponseDto)
  data: OemClothingIncomingResponseDto[]
}

/**
 * OEM服装入库年份选项响应 DTO
 */
export class OemClothingIncomingYearOptionsResponseDto {
  @Expose()
  @IsArray()
  @IsString({ each: true })
  years: string[]
}

/**
 * OEM服装入库供应商选项响应 DTO
 */
export class OemClothingIncomingSupplierOptionsResponseDto {
  @Expose()
  @IsArray()
  @IsString({ each: true })
  suppliers: string[]
}
