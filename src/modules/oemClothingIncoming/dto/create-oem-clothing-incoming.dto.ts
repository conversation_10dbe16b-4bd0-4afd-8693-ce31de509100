import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsNotEmpty, IsOptional, IsNumber, IsDate } from 'class-validator'
import { Type } from 'class-transformer'

export class CreateOemClothingIncomingDto {
  @ApiProperty({ description: 'OEM服装入库编号' })
  @IsString()
  @IsNotEmpty({ message: 'OEM服装入库编号不能为空' })
  oem_clothing_incoming_id: string

  @ApiProperty({ description: 'OEM服装入库年份' })
  @IsString()
  @IsNotEmpty({ message: 'OEM服装入库年份不能为空' })
  oem_clothing_incoming_year: string

  @ApiProperty({ description: '入库日期' })
  @IsDate()
  @IsNotEmpty({ message: '入库日期不能为空' })
  @Type(() => Date)
  date_in: Date

  @ApiProperty({ description: '供应商' })
  @IsString()
  @IsNotEmpty({ message: '供应商不能为空' })
  supplier: string

  @ApiProperty({ description: '备注', required: false })
  @IsString()
  @IsOptional()
  remark?: string
}
