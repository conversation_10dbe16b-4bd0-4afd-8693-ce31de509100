import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsOptional, IsDate } from 'class-validator'
import { Type } from 'class-transformer'

export class UpdateOemClothingIncomingDto {
  @ApiProperty({ description: 'OEM服装入库编号', required: false })
  @IsString()
  @IsOptional()
  oem_clothing_incoming_id?: string

  @ApiProperty({ description: 'OEM服装入库年份', required: false })
  @IsString()
  @IsOptional()
  oem_clothing_incoming_year?: string

  @ApiProperty({ description: '入库日期', required: false })
  @IsDate()
  @IsOptional()
  @Type(() => Date)
  date_in?: Date

  @ApiProperty({ description: '供应商', required: false })
  @IsString()
  @IsOptional()
  supplier?: string

  @ApiProperty({ description: '备注', required: false })
  @IsString()
  @IsOptional()
  remark?: string
}
