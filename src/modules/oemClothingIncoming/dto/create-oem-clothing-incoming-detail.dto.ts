import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsNotEmpty, IsOptional, IsNumber } from 'class-validator'

export class CreateOemClothingIncomingDetailDto {
  @ApiProperty({ description: '入库ID', required: false })
  @IsString()
  @IsOptional()
  incoming_id?: string

  @ApiProperty({ description: 'OEM服装编号' })
  @IsString()
  @IsNotEmpty({ message: 'OEM服装编号不能为空' })
  oem_clothing_id: string

  @ApiProperty({ description: 'OEM服装名称' })
  @IsString()
  @IsNotEmpty({ message: 'OEM服装名称不能为空' })
  oem_clothing_name: string

  @ApiProperty({ description: '款式', required: false })
  @IsString()
  @IsOptional()
  style?: string

  @ApiProperty({ description: '价格', required: false })
  @IsNumber()
  @IsOptional()
  price?: number

  @ApiProperty({ description: '入库数量' })
  @IsNumber()
  @IsNotEmpty({ message: '入库数量不能为空' })
  in_pcs: number

  @ApiProperty({ description: '金额', required: false })
  @IsString()
  @IsOptional()
  money?: string

  @ApiProperty({ description: '备注', required: false })
  @IsString()
  @IsOptional()
  remark?: string
}
