import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsOptional, IsNumber } from 'class-validator'

export class UpdateOemClothingIncomingDetailDto {
  @ApiProperty({ description: '入库ID', required: false })
  @IsString()
  @IsOptional()
  incoming_id?: string

  @ApiProperty({ description: 'OEM服装编号', required: false })
  @IsString()
  @IsOptional()
  oem_clothing_id?: string

  @ApiProperty({ description: 'OEM服装名称', required: false })
  @IsString()
  @IsOptional()
  oem_clothing_name?: string

  @ApiProperty({ description: '款式', required: false })
  @IsString()
  @IsOptional()
  style?: string

  @ApiProperty({ description: '价格', required: false })
  @IsNumber()
  @IsOptional()
  price?: number

  @ApiProperty({ description: '入库数量', required: false })
  @IsNumber()
  @IsOptional()
  in_pcs?: number

  @ApiProperty({ description: '金额', required: false })
  @IsString()
  @IsOptional()
  money?: string

  @ApiProperty({ description: '备注', required: false })
  @IsString()
  @IsOptional()
  remark?: string
}
