import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { OemClothingIncoming } from '../../models/oemClothingIncoming.model'
import { OemClothingIncomingDetail } from '../../models/oemClothingIncomingDetail.model'
import { OemClothing } from '../../models/oemClothing.model'
import {
  CreateOemClothingIncomingDto,
  UpdateOemClothingIncomingDto,
  QueryOemClothingIncomingDto,
  CreateOemClothingIncomingDetailDto,
  UpdateOemClothingIncomingDetailDto,
} from './dto'

@Injectable()
export class OemClothingIncomingService {
  constructor(
    @InjectModel('OemClothingIncoming')
    private readonly oemClothingIncomingModel: Model<OemClothingIncoming>,
    @InjectModel('OemClothingIncomingDetail')
    private readonly oemClothingIncomingDetailModel: Model<OemClothingIncomingDetail>,
    @InjectModel('OemClothing')
    private readonly oemClothingModel: Model<OemClothing>
  ) {}

  // 创建OEM服装入库
  async create(
    createOemClothingIncomingDto: CreateOemClothingIncomingDto
  ): Promise<OemClothingIncoming> {
    const oemClothingIncoming = new this.oemClothingIncomingModel(createOemClothingIncomingDto)
    return oemClothingIncoming.save()
  }

  // 获取OEM服装入库列表，支持分页和筛选
  async findAll(queryParams: QueryOemClothingIncomingDto) {
    console.log('后端收到的查询参数：', JSON.stringify(queryParams))

    // 处理可能的数组参数格式问题
    const processedParams = { ...queryParams } as Record<string, any>

    // 检查并处理 oem_clothing_incoming_years[] 格式的参数
    for (const key in queryParams as Record<string, any>) {
      if (key.endsWith('[]')) {
        const baseKey = key.slice(0, -2)
        processedParams[baseKey] = (queryParams as Record<string, any>)[key]
        delete processedParams[key]
      }
    }

    const {
      oem_clothing_incoming_id,
      oem_clothing_incoming_year,
      oem_clothing_incoming_years,
      date_in_start,
      date_in_end,
      supplier,
      suppliers,
      page = 1,
      limit = 10,
    } = processedParams

    const skip = (page - 1) * limit

    // 构建查询条件
    const filter: any = {}

    // 处理入库编号条件
    if (oem_clothing_incoming_id) {
      filter.oem_clothing_incoming_id = { $regex: oem_clothing_incoming_id, $options: 'i' }
    }

    // 处理年份条件
    if (oem_clothing_incoming_year) {
      filter.oem_clothing_incoming_year = oem_clothing_incoming_year
    } else if (oem_clothing_incoming_years) {
      // 确保oem_clothing_incoming_years是数组
      const yearsArray = Array.isArray(oem_clothing_incoming_years)
        ? oem_clothing_incoming_years
        : String(oem_clothing_incoming_years).split(',').filter(Boolean)

      if (yearsArray.length > 0) {
        filter.oem_clothing_incoming_year = { $in: yearsArray }
        console.log('年份过滤条件:', filter.oem_clothing_incoming_year)
      }
    }

    // 处理入库日期条件
    if (date_in_start || date_in_end) {
      filter.date_in = {}
      if (date_in_start) {
        filter.date_in.$gte = new Date(date_in_start)
      }
      if (date_in_end) {
        filter.date_in.$lte = new Date(date_in_end)
      }
    }

    // 处理供应商条件
    if (supplier) {
      filter.supplier = { $regex: supplier, $options: 'i' }
    } else if (suppliers) {
      // 确保suppliers是数组
      const suppliersArray = Array.isArray(suppliers)
        ? suppliers
        : String(suppliers).split(',').filter(Boolean)

      if (suppliersArray.length > 0) {
        filter.supplier = { $in: suppliersArray }
        console.log('供应商过滤条件:', filter.supplier)
      }
    }

    console.log('构建的数据库查询条件：', JSON.stringify(filter))

    // 执行查询
    const [data, total] = await Promise.all([
      this.oemClothingIncomingModel
        .find(filter)
        .skip(skip)
        .limit(limit)
        .sort({ date_in: -1 }) // 按入库日期倒序排列
        .exec(),
      this.oemClothingIncomingModel.countDocuments(filter).exec(),
    ])

    console.log(`查询结果：找到 ${total} 条记录`)
    return {
      total,
      page: Number(page),
      limit: Number(limit),
      data,
    }
  }

  // 获取单个OEM服装入库
  async findOne(id: string): Promise<OemClothingIncoming> {
    const oemClothingIncoming = await this.oemClothingIncomingModel.findById(id).exec()
    if (!oemClothingIncoming) {
      throw new NotFoundException(`OEM服装入库ID ${id} 不存在`)
    }
    return oemClothingIncoming
  }

  // 更新OEM服装入库
  async update(
    id: string,
    updateOemClothingIncomingDto: UpdateOemClothingIncomingDto
  ): Promise<OemClothingIncoming> {
    const updatedOemClothingIncoming = await this.oemClothingIncomingModel
      .findByIdAndUpdate(id, updateOemClothingIncomingDto, { new: true })
      .exec()

    if (!updatedOemClothingIncoming) {
      throw new NotFoundException(`OEM服装入库ID ${id} 不存在`)
    }

    return updatedOemClothingIncoming
  }

  // 删除OEM服装入库
  async remove(id: string): Promise<OemClothingIncoming> {
    // 1. 先查找要删除的入库记录
    const oemClothingIncoming = await this.oemClothingIncomingModel.findById(id).exec()
    if (!oemClothingIncoming) {
      throw new NotFoundException(`OEM服装入库ID ${id} 不存在`)
    }

    try {
      // 2. 获取该入库下所有明细记录中涉及的OEM服装ID
      const details = await this.oemClothingIncomingDetailModel.find({ incoming_id: id }).exec()
      const oemClothingIds = [...new Set(details.map((detail) => detail.oem_clothing_id))]
      console.log(`该入库涉及 ${oemClothingIds.length} 个OEM服装需要更新入库数量`)

      // 3. 删除关联的明细记录
      console.log(`删除OEM服装入库ID ${id} 的所有关联明细记录`)
      const deleteResult = await this.oemClothingIncomingDetailModel
        .deleteMany({ incoming_id: id })
        .exec()
      console.log(`已删除 ${deleteResult.deletedCount} 条关联明细记录`)

      // 4. 删除主表记录
      const deletedOemClothingIncoming = await this.oemClothingIncomingModel
        .findByIdAndDelete(id)
        .exec()
      if (!deletedOemClothingIncoming) {
        throw new NotFoundException(`删除OEM服装入库ID ${id} 失败`)
      }
      console.log(`已删除OEM服装入库记录，ID: ${id}`)

      // 5. 更新OEM服装入库数量
      if (oemClothingIds.length > 0) {
        console.log(`开始更新 ${oemClothingIds.length} 个OEM服装的入库数量`)

        // 对每个OEM服装ID重新计算入库数量
        for (const oemClothingId of oemClothingIds) {
          try {
            // 重新计算该OEM服装的入库数量
            const result = await this.oemClothingIncomingDetailModel
              .aggregate([
                { $match: { oem_clothing_id: oemClothingId } },
                { $group: { _id: null, total_pcs: { $sum: '$in_pcs' } } },
              ])
              .exec()

            const totalPcs = result.length > 0 ? result[0].total_pcs : 0

            // 更新OEM服装表中的入库数量
            await this.oemClothingModel
              .updateOne({ oem_clothing_id: oemClothingId }, { $set: { in_pcs: totalPcs } })
              .exec()

            console.log(`已更新OEM服装 ${oemClothingId} 的入库数量为 ${totalPcs}`)
          } catch (updateError) {
            console.error(`更新OEM服装 ${oemClothingId} 的入库数量时出错:`, updateError)
            // 继续处理下一个OEM服装，不中断整个过程
          }
        }

        console.log(`OEM服装入库数量更新完成`)
      }

      return deletedOemClothingIncoming
    } catch (error) {
      console.error(`删除OEM服装入库记录时出错:`, error)
      throw error
    }
  }

  // 获取年份选项
  async getYearOptions(suppliers?: string): Promise<string[]> {
    const filter: any = {}
    console.log('getYearOptions 方法接收到的参数：', { suppliers })

    // 处理可能的数组参数格式问题
    let processedSuppliers = suppliers
    if (typeof suppliers === 'string' && suppliers.endsWith('[]')) {
      processedSuppliers = suppliers.slice(0, -2)
    }

    // 如果提供了供应商过滤条件
    if (processedSuppliers) {
      // 处理可能是数组或字符串的情况
      const supplierArray = Array.isArray(processedSuppliers)
        ? processedSuppliers
        : String(processedSuppliers).split(',').filter(Boolean)

      if (supplierArray.length > 0) {
        filter.supplier = { $in: supplierArray }
        console.log('年份选项的供应商过滤条件:', filter.supplier)
      }
    }

    // 获取不重复的年份
    const years = await this.oemClothingIncomingModel
      .distinct('oem_clothing_incoming_year', filter)
      .exec()
    console.log('获取到的年份数据：', years)

    // 按年份倒序排列
    return years.sort((a, b) => {
      const yearA = parseInt(a.match(/\d+/)?.[0] || '0')
      const yearB = parseInt(b.match(/\d+/)?.[0] || '0')
      return yearB - yearA // 倒序排列
    })
  }

  // 获取供应商选项
  async getSupplierOptions(years?: string): Promise<string[]> {
    const filter: any = {}
    console.log('getSupplierOptions 方法接收到的参数：', { years })

    // 处理可能的数组参数格式问题
    let processedYears = years
    if (typeof years === 'string' && years.endsWith('[]')) {
      processedYears = years.slice(0, -2)
    }

    // 如果提供了年份过滤条件
    if (processedYears) {
      // 处理可能是数组或字符串的情况
      const yearArray = Array.isArray(processedYears)
        ? processedYears
        : String(processedYears).split(',').filter(Boolean)

      if (yearArray.length > 0) {
        filter.oem_clothing_incoming_year = { $in: yearArray }
        console.log('供应商选项的年份过滤条件:', filter.oem_clothing_incoming_year)
      }
    }

    // 获取不重复的供应商
    const suppliers = await this.oemClothingIncomingModel.distinct('supplier', filter).exec()
    console.log('获取到的供应商数据666：', suppliers)

    // 按字母顺序排列
    return suppliers.sort()
  }

  // 创建OEM服装入库明细
  async createDetail(
    createOemClothingIncomingDetailDto: CreateOemClothingIncomingDetailDto
  ): Promise<OemClothingIncomingDetail> {
    try {
      // 如果提供了入库ID，检查入库ID是否存在
      if (createOemClothingIncomingDetailDto.incoming_id) {
        const incoming = await this.oemClothingIncomingModel
          .findById(createOemClothingIncomingDetailDto.incoming_id)
          .exec()
        if (!incoming) {
          throw new NotFoundException(
            `OEM服装入库ID ${createOemClothingIncomingDetailDto.incoming_id} 不存在`
          )
        }
      }

      // 创建明细记录
      const oemClothingIncomingDetail = new this.oemClothingIncomingDetailModel(
        createOemClothingIncomingDetailDto
      )
      const savedDetail = await oemClothingIncomingDetail.save()

      if (!savedDetail) {
        throw new Error('创建OEM服装入库明细失败')
      }

      console.log(`成功创建OEM服装入库明细，ID: ${savedDetail._id}`)

      // 更新OEM服装的入库数量
      try {
        // 计算该OEM服装的总入库数量
        const result = await this.oemClothingIncomingDetailModel
          .aggregate([
            { $match: { oem_clothing_id: savedDetail.oem_clothing_id } },
            { $group: { _id: null, total_pcs: { $sum: '$in_pcs' } } },
          ])
          .exec()

        const totalPcs = result.length > 0 ? result[0].total_pcs : 0

        // 更新OEM服装表中的入库数量
        await this.oemClothingModel
          .updateOne(
            { oem_clothing_id: savedDetail.oem_clothing_id },
            { $set: { in_pcs: totalPcs } }
          )
          .exec()

        console.log(`已更新OEM服装 ${savedDetail.oem_clothing_id} 的入库数量为 ${totalPcs}`)
      } catch (updateError) {
        console.error(`更新OEM服装 ${savedDetail.oem_clothing_id} 的入库数量时出错:`, updateError)
        // 即使更新入库数量失败，我们仍然返回已创建的明细
      }

      return savedDetail
    } catch (error) {
      console.error('创建OEM服装入库明细时出错:', error)
      throw error
    }
  }

  // 批量创建OEM服装入库明细
  async createDetailBatch(
    incomingId: string,
    details: Omit<CreateOemClothingIncomingDetailDto, 'incoming_id'>[]
  ): Promise<OemClothingIncomingDetail[]> {
    // 检查入库ID是否存在
    const incoming = await this.oemClothingIncomingModel.findById(incomingId).exec()
    if (!incoming) {
      throw new NotFoundException(`OEM服装入库ID ${incomingId} 不存在`)
    }

    // 检查明细数据是否为空
    if (!details || details.length === 0) {
      throw new Error('明细数据不能为空')
    }

    try {
      // 先获取现有明细中涉及的OEM服装ID，用于后续更新入库数量
      const existingDetails = await this.oemClothingIncomingDetailModel
        .find({ incoming_id: incomingId })
        .exec()
      const existingOemClothingIds = [...new Set(existingDetails.map((detail) => detail.oem_clothing_id))]

      // 删除现有的明细记录（避免重复保存）
      if (existingDetails.length > 0) {
        console.log(`删除现有的 ${existingDetails.length} 条明细记录`)
        await this.oemClothingIncomingDetailModel.deleteMany({ incoming_id: incomingId }).exec()
      }

      // 准备批量插入的数据
      const detailsWithIncomingId = details.map((detail) => ({
        ...detail,
        incoming_id: incomingId,
      }))

      // 批量插入明细数据
      const createdDetails =
        await this.oemClothingIncomingDetailModel.insertMany(detailsWithIncomingId)
      if (!createdDetails || createdDetails.length === 0) {
        throw new Error('创建OEM服装入库明细失败')
      }

      console.log(`成功创建 ${createdDetails.length} 条OEM服装入库明细`)

      // 合并需要更新入库数量的OEM服装ID（包括原有的和新的）
      const newOemClothingIds = [...new Set(details.map((detail) => detail.oem_clothing_id))]
      const allOemClothingIds = [...new Set([...existingOemClothingIds, ...newOemClothingIds])]
      console.log(`需要更新 ${allOemClothingIds.length} 个OEM服装的入库数量`)

      for (const oemClothingId of allOemClothingIds) {
        try {
          // 计算该OEM服装的总入库数量
          const result = await this.oemClothingIncomingDetailModel
            .aggregate([
              { $match: { oem_clothing_id: oemClothingId } },
              { $group: { _id: null, total_pcs: { $sum: '$in_pcs' } } },
            ])
            .exec()

          const totalPcs = result.length > 0 ? result[0].total_pcs : 0

          // 更新OEM服装表中的入库数量
          await this.oemClothingModel
            .updateOne({ oem_clothing_id: oemClothingId }, { $set: { in_pcs: totalPcs } })
            .exec()

          console.log(`已更新OEM服装 ${oemClothingId} 的入库数量为 ${totalPcs}`)
        } catch (updateError) {
          console.error(`更新OEM服装 ${oemClothingId} 的入库数量时出错:`, updateError)
          // 继续处理下一个OEM服装，不中断整个过程
        }
      }

      return createdDetails
    } catch (error) {
      console.error('批量创建OEM服装入库明细时出错:', error)
      throw error
    }
  }

  // 获取指定入库的所有明细
  async findAllDetailsByIncomingId(incomingId: string): Promise<OemClothingIncomingDetail[]> {
    const details = await this.oemClothingIncomingDetailModel
      .find({ incoming_id: incomingId })
      .exec()
    return details
  }

  // 获取指定OEM服装的入库明细
  async findAllDetailsByOemClothingId(oemClothingId: string): Promise<any[]> {
    // 使用聚合查询关联入库主表和明细表
    const details = await this.oemClothingIncomingDetailModel.aggregate([
      // 匹配指定的OEM服装ID
      { $match: { oem_clothing_id: oemClothingId } },
      // 关联入库主表
      {
        $lookup: {
          from: 'oem_clothing_incoming', // 入库主表集合名
          localField: 'incoming_id',
          foreignField: '_id',
          as: 'incoming'
        }
      },
      // 展开入库主表数组（因为lookup返回的是数组）
      { $unwind: '$incoming' },
      // 投影需要的字段
      {
        $project: {
          _id: 1,
          incoming_id: 1,
          oem_clothing_id: 1,
          oem_clothing_name: 1,
          style: 1,
          price: 1,
          in_pcs: 1,
          money: 1,
          remark: 1,
          createTime: 1,
          'incoming.date_in': 1,
          'incoming.supplier': 1,
          'incoming.oem_clothing_incoming_id': 1,
          'incoming.remark': 1
        }
      },
      // 按入库日期倒序排序
      { $sort: { 'incoming.date_in': -1 } }
    ]).exec()

    console.log(`找到 ${details.length} 条 OEM 服装 ${oemClothingId} 的入库明细记录`)
    return details
  }

  // 获取单个入库明细
  async findOneDetail(id: string): Promise<OemClothingIncomingDetail> {
    const detail = await this.oemClothingIncomingDetailModel.findById(id).exec()
    if (!detail) {
      throw new NotFoundException(`OEM服装入库明细ID ${id} 不存在`)
    }
    return detail
  }

  // 更新入库明细
  async updateDetail(
    id: string,
    updateOemClothingIncomingDetailDto: UpdateOemClothingIncomingDetailDto
  ): Promise<OemClothingIncomingDetail> {
    try {
      // 检查明细ID是否存在
      const existingDetail = await this.oemClothingIncomingDetailModel.findById(id).exec()
      if (!existingDetail) {
        throw new NotFoundException(`OEM服装入库明细ID ${id} 不存在`)
      }

      // 如果提供了入库ID，检查入库ID是否存在
      if (updateOemClothingIncomingDetailDto.incoming_id) {
        const incoming = await this.oemClothingIncomingModel
          .findById(updateOemClothingIncomingDetailDto.incoming_id)
          .exec()
        if (!incoming) {
          throw new NotFoundException(
            `OEM服装入库ID ${updateOemClothingIncomingDetailDto.incoming_id} 不存在`
          )
        }
      }

      // 更新明细记录
      const updatedDetail = await this.oemClothingIncomingDetailModel
        .findByIdAndUpdate(id, updateOemClothingIncomingDetailDto, { new: true })
        .exec()

      if (!updatedDetail) {
        throw new Error(`更新OEM服装入库明细ID ${id} 失败`)
      }

      console.log(`成功更新OEM服装入库明细，ID: ${updatedDetail._id}`)

      // 保存OEM服装ID，以便在发生错误时仍能更新入库数量
      const oemClothingId = updatedDetail.oem_clothing_id

      // 如果更新了入库数量或OEM服装ID，需要更新OEM服装的入库数量
      if (
        updateOemClothingIncomingDetailDto.in_pcs !== undefined ||
        updateOemClothingIncomingDetailDto.oem_clothing_id !== undefined
      ) {
        try {
          // 如果OEM服装ID发生了变化，需要更新旧的OEM服装的入库数量
          if (
            updateOemClothingIncomingDetailDto.oem_clothing_id !== undefined &&
            existingDetail.oem_clothing_id !== updateOemClothingIncomingDetailDto.oem_clothing_id
          ) {
            // 更新旧的OEM服装的入库数量
            const oldResult = await this.oemClothingIncomingDetailModel
              .aggregate([
                { $match: { oem_clothing_id: existingDetail.oem_clothing_id } },
                { $group: { _id: null, total_pcs: { $sum: '$in_pcs' } } },
              ])
              .exec()

            const oldTotalPcs = oldResult.length > 0 ? oldResult[0].total_pcs : 0

            await this.oemClothingModel
              .updateOne(
                { oem_clothing_id: existingDetail.oem_clothing_id },
                { $set: { in_pcs: oldTotalPcs } }
              )
              .exec()

            console.log(
              `已更新旧的OEM服装 ${existingDetail.oem_clothing_id} 的入库数量为 ${oldTotalPcs}`
            )
          }

          // 计算该OEM服装的总入库数量
          const result = await this.oemClothingIncomingDetailModel
            .aggregate([
              { $match: { oem_clothing_id: oemClothingId } },
              { $group: { _id: null, total_pcs: { $sum: '$in_pcs' } } },
            ])
            .exec()

          const totalPcs = result.length > 0 ? result[0].total_pcs : 0

          // 更新OEM服装表中的入库数量
          await this.oemClothingModel
            .updateOne({ oem_clothing_id: oemClothingId }, { $set: { in_pcs: totalPcs } })
            .exec()

          console.log(`已更新OEM服装 ${oemClothingId} 的入库数量为 ${totalPcs}`)
        } catch (updateError) {
          console.error(`更新OEM服装入库数量时出错:`, updateError)
          // 即使更新入库数量失败，我们仍然返回已更新的明细
        }
      }

      return updatedDetail
    } catch (error) {
      console.error('更新OEM服装入库明细时出错:', error)
      throw error
    }
  }

  // 删除入库明细
  async removeDetail(id: string): Promise<OemClothingIncomingDetail> {
    // 先查找要删除的明细记录
    const detail = await this.oemClothingIncomingDetailModel.findById(id).exec()
    if (!detail) {
      throw new NotFoundException(`OEM服装入库明细ID ${id} 不存在`)
    }

    try {
      // 删除明细记录
      const deletedDetail = await this.oemClothingIncomingDetailModel.findByIdAndDelete(id).exec()
      if (!deletedDetail) {
        throw new NotFoundException(`删除OEM服装入库明细ID ${id} 失败`)
      }

      // 保存OEM服装ID，以便在发生错误时仍能更新入库数量
      const oemClothingId = detail.oem_clothing_id

      // 更新OEM服装的入库数量
      try {
        // 计算该OEM服装的总入库数量
        const result = await this.oemClothingIncomingDetailModel
          .aggregate([
            { $match: { oem_clothing_id: oemClothingId } },
            { $group: { _id: null, total_pcs: { $sum: '$in_pcs' } } },
          ])
          .exec()

        const totalPcs = result.length > 0 ? result[0].total_pcs : 0

        // 更新OEM服装表中的入库数量
        await this.oemClothingModel
          .updateOne({ oem_clothing_id: oemClothingId }, { $set: { in_pcs: totalPcs } })
          .exec()

        console.log(`已更新OEM服装 ${oemClothingId} 的入库数量为 ${totalPcs}`)
      } catch (updateError) {
        console.error(`更新OEM服装 ${oemClothingId} 的入库数量时出错:`, updateError)
        // 即使更新入库数量失败，我们仍然返回已删除的明细
      }

      return deletedDetail
    } catch (error) {
      console.error(`删除OEM服装入库明细时出错:`, error)
      throw error
    }
  }
}
