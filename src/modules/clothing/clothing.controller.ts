import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpException,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common'
import { ClothingService } from './clothing.service'
import { Clothing } from '../../models/clothing.model'
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard'
import {
  CreateClothingDto,
  UpdateClothingDto,
  QueryClothingDto,
  ClothingResponseDto,
  ClothingListResponseDto,
  ClothingYearOptionsResponseDto,
  ImportClothingBatchResponseDto,
} from './dto'

@Controller('clothing')
@UseGuards(JwtAuthGuard)
export class ClothingController {
  constructor(private readonly clothingService: ClothingService) {}

  /**
   * 获取服装列表
   * @param query 查询参数
   * @returns 服装列表和总数
   */
  @Get()
  @UsePipes(new ValidationPipe({ transform: true }))
  async getClothingList(@Query() query: QueryClothingDto) {
    try {
      console.log('前端传入的查询参数67895955：', JSON.stringify(query))
      const result = await this.clothingService.getClothingList(query)

      return {
        code: HttpStatus.OK,
        data: result,
        message: '获取服装列表成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取服装列表失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 获取服装年份选项
   * @param supplier 供应商筛选
   * @param classification 分类筛选
   * @returns 年份选项列表
   */
  @Get('options/years')
  async getClothingYearOptions(
    @Query('supplier') supplier?: string,
    @Query('classification') classification?: string
  ) {
    try {
      const years = await this.clothingService.getClothingYearOptions(supplier, classification)
      return {
        code: HttpStatus.OK,
        data: years,
        message: '获取服装年份选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取服装年份选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 获取供应商选项
   * @param year 年份筛选
   * @param classification 分类筛选
   * @returns 供应商选项列表
   */
  @Get('options/suppliers')
  async getSupplierOptions(
    @Query('year') year?: string,
    @Query('classification') classification?: string
  ) {
    try {
      const suppliers = await this.clothingService.getSupplierOptions(year, classification)
      return {
        code: HttpStatus.OK,
        data: suppliers,
        message: '获取供应商选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取供应商选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 获取分类选项
   * @param year 年份筛选
   * @param supplier 供应商筛选
   * @returns 分类选项列表
   */
  @Get('options/classifications')
  async getClassificationOptions(
    @Query('year') year?: string,
    @Query('supplier') supplier?: string
  ) {
    try {
      const classifications = await this.clothingService.getClassificationOptions(year, supplier)
      return {
        code: HttpStatus.OK,
        data: classifications,
        message: '获取分类选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取分类选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 获取袖长、尺码、款式、口袋类型选项
   * @returns 袖长、尺码、款式、口袋类型选项列表
   */
  @Get('options/four')
  async getFourOptions() {
    try {
      const fourOptions = await this.clothingService.getFourOptions()
      return {
        code: HttpStatus.OK,
        data: fourOptions,
        message: '获取袖长、尺码、款式、口袋类型选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取袖长、尺码、款式、口袋类型选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 获取袖长选项
   * @returns 袖长选项列表
   */
  @Get('options/sleeves')
  async getSleeveOptions() {
    try {
      const fourOptions = await this.clothingService.getFourOptions()
      return {
        code: HttpStatus.OK,
        data: fourOptions.sleeves,
        message: '获取袖长选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取袖长选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 获取尺码选项
   * @returns 尺码选项列表
   */
  @Get('options/sizes')
  async getSizeOptions() {
    try {
      const fourOptions = await this.clothingService.getFourOptions()
      return {
        code: HttpStatus.OK,
        data: fourOptions.sizes,
        message: '获取尺码选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取尺码选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 获取款式选项
   * @returns 款式选项列表
   */
  @Get('options/styles')
  async getStyleOptions() {
    try {
      const fourOptions = await this.clothingService.getFourOptions()
      return {
        code: HttpStatus.OK,
        data: fourOptions.styles,
        message: '获取款式选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取款式选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 获取口袋类型选项
   * @returns 口袋类型选项列表
   */
  @Get('options/pocket-types')
  async getPocketTypeOptions() {
    try {
      const fourOptions = await this.clothingService.getFourOptions()
      return {
        code: HttpStatus.OK,
        data: fourOptions.pocketTypes,
        message: '获取口袋类型选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取口袋类型选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 根据ID获取服装详情
   * @param id 服装ID
   * @returns 服装详情
   */
  @Get(':id')
  async getClothingById(@Param('id') id: string) {
    try {
      const clothing = await this.clothingService.getClothingById(id)
      return {
        code: HttpStatus.OK,
        data: clothing,
        message: '获取服装详情成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.NOT_FOUND,
          message: error.message || '服装不存在',
        },
        HttpStatus.NOT_FOUND
      )
    }
  }

  /**
   * 创建服装
   * @param clothingData 服装数据
   * @returns 创建的服装
   */
  @Post()
  @UsePipes(new ValidationPipe({ transform: true }))
  async createClothing(@Body() clothingData: CreateClothingDto) {
    try {
      const newClothing = await this.clothingService.createClothing(clothingData)
      return {
        code: HttpStatus.CREATED,
        data: newClothing,
        message: '创建服装成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.BAD_REQUEST,
          message: '创建服装失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.BAD_REQUEST
      )
    }
  }

  /**
   * 更新服装
   * @param id 服装ID
   * @param clothingData 更新的服装数据
   * @returns 更新后的服装
   */
  @Put(':id')
  @UsePipes(new ValidationPipe({ transform: true }))
  async updateClothing(@Param('id') id: string, @Body() clothingData: UpdateClothingDto) {
    try {
      const updatedClothing = await this.clothingService.updateClothing(id, clothingData)
      return {
        code: HttpStatus.OK,
        data: updatedClothing,
        message: '更新服装成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.NOT_FOUND,
          message: error.message || '更新服装失败',
        },
        HttpStatus.NOT_FOUND
      )
    }
  }

  /**
   * 删除服装
   * @param id 服装ID
   * @returns 删除结果
   */
  @Delete(':id')
  async deleteClothing(@Param('id') id: string) {
    try {
      const result = await this.clothingService.deleteClothing(id)
      return {
        code: HttpStatus.OK,
        data: result,
        message: result.message,
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.NOT_FOUND,
          message: error.message || '删除服装失败',
        },
        HttpStatus.NOT_FOUND
      )
    }
  }

  /**
   * 批量导入服装数据
   * @param clothingDataList 服装数据列表
   * @returns 导入结果
   */
  @Post('import-batch')
  @UsePipes(new ValidationPipe({ transform: true }))
  async importClothingBatch(@Body() clothingDataList: CreateClothingDto[]) {
    try {
      const result = await this.clothingService.importClothingBatch(clothingDataList)
      return {
        code: HttpStatus.OK,
        data: result,
        message: result.message,
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.BAD_REQUEST,
          message: '导入服装数据失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.BAD_REQUEST
      )
    }
  }

  /**
   * 导入JSON数据
   * @param importJsonDto 导入的JSON数据
   * @returns 导入结果
   */
  @Post('import-json')
  async importJson(@Body() importJsonDto: any) {
    console.log('接收到服装JSON数据导入请求:', {
      sheetName: importJsonDto.sheetName,
      fileName: importJsonDto.fileName,
      totalRecords: importJsonDto.totalRecords,
      dataLength: importJsonDto.data?.length || 0,
    })

    // 检查数据是否存在
    if (
      !importJsonDto.data ||
      !Array.isArray(importJsonDto.data) ||
      importJsonDto.data.length === 0
    ) {
      return {
        code: HttpStatus.BAD_REQUEST,
        data: { success: false, count: 0 },
        message: '数据不能为空',
      }
    }

    try {
      // 打印数据示例以便于调试
      console.log('数据示例:', JSON.stringify(importJsonDto.data.slice(0, 1)))

      // 调用服务层方法处理数据
      const result = await this.clothingService.importClothingBatch(importJsonDto.data)
      return {
        code: HttpStatus.OK,
        data: result,
        message: result.message,
      }
    } catch (error: any) {
      console.error('处理导入数据时出错:', error.message)
      throw new HttpException(
        {
          code: HttpStatus.BAD_REQUEST,
          message: '导入服装数据失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.BAD_REQUEST
      )
    }
  }

  /**
   * 根据服装名称和年份查找普通服装
   * @param data 包含服装名称数组和服装年份
   * @returns 查询到的普通服装列表
   */
  @Post('find-by-names')
  async findClothingByNames(@Body() data: { clothing_names: string[]; clothing_year: string }) {
    try {
      console.log('接收到根据服装名称查找普通服装请求:', JSON.stringify(data))

      const result = await this.clothingService.findClothingByNames(data)

      return {
        code: HttpStatus.OK,
        data: result,
        message: '查找普通服装成功',
      }
    } catch (error: any) {
      console.error('根据服装名称查找普通服装失败:', error.message)
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '根据服装名称查找普通服装失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 数据校准 - 重新统计并更新指定本厂服装的入库数和出库数
   * @param calibrateData 校准数据，包含查询条件或服装ID列表
   * @returns 校准结果
   */
  @Post('calibrate-data')
  async calibrateClothingData(@Body() calibrateData: {
    clothingIds?: string[];
    queryParams?: any
  }) {
    try {
      console.log('开始本厂服装数据校准...', calibrateData)

      const result = await this.clothingService.calibrateData(calibrateData)

      console.log('本厂服装数据校准完成:', result)

      return {
        code: HttpStatus.OK,
        data: result,
        message: '本厂服装数据校准成功',
      }
    } catch (error: any) {
      console.error('本厂服装数据校准失败:', error.message)
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '本厂服装数据校准失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }
}
