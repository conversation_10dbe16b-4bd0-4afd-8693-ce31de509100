import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { Clothing } from '@/models/clothing.model'
import { FabricGroup } from '@/models/fabricGroup.model'
import { OemClothing } from '@/models/oemClothing.model'
import { FabricWarehouse } from '@/models/fabricWarehouse.model'
import { TransportationDetail } from '@/models/transportationDetail.model'
import { BillOfMaterials } from '@/models/billOfMaterials.model'
import { CreateClothingDto, UpdateClothingDto, QueryClothingDto } from './dto'

@Injectable()
export class ClothingService {
  constructor(
    @InjectModel('Clothing') private readonly clothingModel: Model<Clothing>,
    @InjectModel('FabricGroup') private readonly fabricGroupModel: Model<FabricGroup>,
    @InjectModel('OemClothing') private readonly oemClothingModel: Model<OemClothing>,
    @InjectModel('FabricWarehouse') private readonly fabricWarehouseModel: Model<FabricWarehouse>,
    @InjectModel('TransportationDetail')
    private readonly transportationDetailModel: Model<TransportationDetail>,
    @InjectModel('BillOfMaterials') private readonly bomModel: Model<BillOfMaterials>
  ) {}

  /**
   * 获取服装列表
   * @param query 查询参数
   * @returns 服装列表和总数
   */
  async getClothingList(
    query: QueryClothingDto
  ): Promise<{ clothingList: Clothing[]; total: number }> {
    const { page = 1, limit = 10, ...filters } = query
    const skip = (page - 1) * limit
    const limitNum = Number(limit)

    console.log('后端收到的查询参数：', JSON.stringify(query))

    // 构建查询条件
    const matchConditions: any = {}

    // 处理可能的 clothing_years[]、suppliers[]、classifications[] 参数
    const rawQuery = query as any // 使用 any 类型绕过 TypeScript 检查
    if (rawQuery['clothing_years[]']) {
      // 如果是字符串，转换为数组
      if (typeof rawQuery['clothing_years[]'] === 'string') {
        filters.clothing_years = [rawQuery['clothing_years[]']]
      } else if (Array.isArray(rawQuery['clothing_years[]'])) {
        filters.clothing_years = rawQuery['clothing_years[]']
      }
    }
    if (rawQuery['suppliers[]']) {
      // 如果是字符串，转换为数组
      if (typeof rawQuery['suppliers[]'] === 'string') {
        filters.suppliers = [rawQuery['suppliers[]']]
      } else if (Array.isArray(rawQuery['suppliers[]'])) {
        filters.suppliers = rawQuery['suppliers[]']
      }
    }
    if (rawQuery['classifications[]']) {
      // 如果是字符串，转换为数组
      if (typeof rawQuery['classifications[]'] === 'string') {
        filters.classifications = [rawQuery['classifications[]']]
      } else if (Array.isArray(rawQuery['classifications[]'])) {
        filters.classifications = rawQuery['classifications[]']
      }
    }

    console.log('处理后的查询参数：', JSON.stringify(filters))

    // 处理所属年份筛选
    if (filters.clothing_year) {
      matchConditions.clothing_year = filters.clothing_year
      console.log('使用单个年份筛选:', filters.clothing_year)
    } else if (
      filters.clothing_years &&
      Array.isArray(filters.clothing_years) &&
      filters.clothing_years.length > 0
    ) {
      matchConditions.clothing_year = { $in: filters.clothing_years }
      console.log('使用多个年份筛选:', filters.clothing_years)
    }

    //处理缝制年份筛选
    if (filters.make_year) {
      matchConditions.make_year = filters.make_year
      console.log('使用单个缝制年份筛选:', filters.make_year)
    } else if (
      filters.make_years &&
      Array.isArray(filters.make_years) &&
      filters.make_years.length > 0
    ) {
      matchConditions.make_year = { $in: filters.make_years }
    }

    // 处理服装名称筛选
    if (filters.clothing_name) {
      matchConditions.clothing_name = { $regex: filters.clothing_name, $options: 'i' }
    }

    // 处理服装ID筛选
    if (filters.clothing_id) {
      matchConditions.clothing_id = { $regex: filters.clothing_id, $options: 'i' }
    }

    // 处理袋长筛选
    if (filters.long_or_short_sleeve) {
      matchConditions.long_or_short_sleeve = filters.long_or_short_sleeve
    }

    // 处理状态筛选
    if (filters.state !== undefined && filters.state !== '') {
      matchConditions.state = filters.state
    }

    // 处理布料组筛选
    if (filters.fabric_group_id) {
      matchConditions.fabric_group_id = filters.fabric_group_id
    }

    // 使用聚合管道查询
    const aggregationPipeline: any[] = [
      { $match: matchConditions },
      { $sort: { clothing_id: -1 } }, // 按照服装ID倒序排列
      {
        $lookup: {
          from: 'fabric_group',
          localField: 'fabric_group_id',
          foreignField: 'fabric_group_id',
          as: 'fabricGroupInfo',
        },
      },
      {
        $lookup: {
          from: 'bill_of_materials',
          localField: 'clothing_id',
          foreignField: 'clothing_id',
          as: 'bomInfo',
        },
      },
      {
        $addFields: {
          supplier: { $arrayElemAt: ['$fabricGroupInfo.supplier', 0] },
          group_classification: { $arrayElemAt: ['$fabricGroupInfo.group_classification', 0] },
          total_material_cost: {
            $ifNull: [
              { $arrayElemAt: ['$bomInfo.total_material_cost', 0] },
              0
            ]
          },
        },
      },
      { $project: { fabricGroupInfo: 0, bomInfo: 0 } }, // 移除临时字段
    ]

    console.log('初始匹配条件:', JSON.stringify(matchConditions))

    // 处理供应商筛选
    if (filters.supplier) {
      // 如果是逗号分隔的字符串，转换为数组
      let supplierArray: string[] = []
      if (typeof filters.supplier === 'string' && filters.supplier.includes(',')) {
        supplierArray = filters.supplier.split(',').filter(Boolean)
        console.log('供应商筛选拆分为数组:', supplierArray)
      } else {
        supplierArray = [filters.supplier]
      }

      // 添加到管道中的匹配阶段
      aggregationPipeline.push({
        $match: { supplier: { $in: supplierArray } },
      })
    } else if (
      filters.suppliers &&
      Array.isArray(filters.suppliers) &&
      filters.suppliers.length > 0
    ) {
      console.log('供应商筛选数组666666:', filters.suppliers)

      // 添加到管道中的匹配阶段
      aggregationPipeline.push({
        $match: { supplier: { $in: filters.suppliers } },
      })
    }

    // 处理分类筛选
    if (filters.classification) {
      // 如果是逗号分隔的字符串，转换为数组
      let classificationArray: string[] = []
      if (typeof filters.classification === 'string' && filters.classification.includes(',')) {
        classificationArray = filters.classification.split(',').filter(Boolean)
        console.log('分类筛选拆分为数组:', classificationArray)
      } else {
        classificationArray = [filters.classification]
      }

      // 添加到管道中的匹配阶段
      aggregationPipeline.push({
        $match: { group_classification: { $in: classificationArray } },
      })
    } else if (
      filters.classifications &&
      Array.isArray(filters.classifications) &&
      filters.classifications.length > 0
    ) {
      // 添加到管道中的匹配阶段
      aggregationPipeline.push({
        $match: { group_classification: { $in: filters.classifications } },
      })
    }

    // 获取总数的聚合管道
    const countPipeline: any[] = [...aggregationPipeline]
    countPipeline.push({ $count: 'total' })

    // 添加分页到查询管道
    aggregationPipeline.push({ $skip: skip }, { $limit: limitNum })

    console.log('最终聚合管道：', JSON.stringify(aggregationPipeline))

    // 打印每个阶段的匹配条件
    aggregationPipeline.forEach((stage, index) => {
      if (stage.$match) {
        console.log(`管道阶段 ${index} 的匹配条件:`, JSON.stringify(stage.$match))
      }
    })

    // 执行聚合查询
    const clothingList = await this.clothingModel.aggregate(aggregationPipeline as any).exec()

    // 获取总数
    const countResult = await this.clothingModel.aggregate(countPipeline as any).exec()
    const total = countResult.length > 0 ? countResult[0].total : 0

    console.log(`查询结果：找到 ${clothingList.length} 条记录，总数：${total}`)

    return { clothingList, total }
  }

  /**
   * 根据ID获取服装详情
   * @param id 服装ID
   * @returns 服装详情
   */
  async getClothingById(id: string): Promise<Clothing> {
    try {
      // 构建匹配条件
      let matchConditions: any = {}

      // 尝试使用 ObjectId 或 clothing_id 查询
      if (Types.ObjectId.isValid(id)) {
        matchConditions = { $or: [{ _id: new Types.ObjectId(id) }, { clothing_id: id }] }
      } else {
        matchConditions = { clothing_id: id }
      }

      // 使用聚合管道查询
      const aggregationPipeline: any[] = [
        { $match: matchConditions },
        {
          $lookup: {
            from: 'fabric_group',
            localField: 'fabric_group_id',
            foreignField: 'fabric_group_id',
            as: 'fabricGroupInfo',
          },
        },
        {
          $addFields: {
            supplier: { $arrayElemAt: ['$fabricGroupInfo.supplier', 0] },
            group_classification: { $arrayElemAt: ['$fabricGroupInfo.group_classification', 0] },
          },
        },
        { $project: { fabricGroupInfo: 0 } }, // 移除临时字段
      ]

      // 执行聚合查询
      const result = await this.clothingModel.aggregate(aggregationPipeline).exec()

      // 检查是否找到服装
      if (!result || result.length === 0) {
        throw new NotFoundException(`服装不存在，ID: ${id}`)
      }

      return result[0] as Clothing
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error
      }
      throw new NotFoundException(`查询服装失败，ID: ${id}`)
    }
  }

  /**
   * 创建服装
   * @param clothingData 服装数据
   * @returns 创建的服装
   */
  async createClothing(clothingData: CreateClothingDto): Promise<Clothing> {
    // 设置创建时间和最后修改时间
    const now = new Date()
    const newClothing = new this.clothingModel({
      ...clothingData,
      createTime: now,
      lastChangeTime: now,
    })

    return await newClothing.save()
  }

  /**
   * 更新服装
   * @param id 服装ID
   * @param clothingData 更新的服装数据
   * @returns 更新后的服装
   */
  async updateClothing(id: string, clothingData: UpdateClothingDto): Promise<Clothing> {
    // 设置最后修改时间
    const updateData = { ...clothingData, lastChangeTime: new Date() }

    let updatedClothing: Clothing | null = null

    try {
      // 尝试使用 ObjectId 更新
      if (Types.ObjectId.isValid(id)) {
        updatedClothing = await this.clothingModel
          .findByIdAndUpdate(id, updateData, { new: true })
          .exec()
      }

      // 如果没有找到，尝试使用 clothing_id 更新
      if (!updatedClothing) {
        updatedClothing = await this.clothingModel
          .findOneAndUpdate({ clothing_id: id }, updateData, { new: true })
          .exec()
      }

      if (!updatedClothing) {
        throw new NotFoundException(`服装不存在，ID: ${id}`)
      }

      return updatedClothing
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error
      }
      throw new NotFoundException(`更新服装失败，ID: ${id}`)
    }
  }

  /**
   * 删除服装
   * @param id 服装ID
   * @returns 删除结果
   */
  async deleteClothing(id: string): Promise<{ success: boolean; message: string }> {
    try {
      let result

      // 尝试使用 ObjectId 删除
      if (Types.ObjectId.isValid(id)) {
        result = await this.clothingModel.findByIdAndDelete(id).exec()
      }

      // 如果没有找到，尝试使用 clothing_id 删除
      if (!result) {
        result = await this.clothingModel.findOneAndDelete({ clothing_id: id }).exec()
      }

      if (!result) {
        throw new NotFoundException(`服装不存在，ID: ${id}`)
      }

      return { success: true, message: '服装删除成功' }
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error
      }
      throw new NotFoundException(`删除服装失败，ID: ${id}`)
    }
  }

  /**
   * 获取服装年份选项
   * @param supplier 供应商筛选
   * @param classification 分类筛选
   * @returns 年份选项列表
   */
  async getClothingYearOptions(supplier?: string, classification?: string): Promise<string[]> {
    console.log('getClothingYearOptions 方法接收到的参数：', { supplier, classification })

    // 构建聚合管道
    const pipeline: any[] = []

    // 如果有供应商或分类筛选，需要先关联布料组信息
    if (supplier || classification) {
      // 关联布料组信息
      pipeline.push({
        $lookup: {
          from: 'fabric_group',
          localField: 'fabric_group_id',
          foreignField: 'fabric_group_id',
          as: 'fabricGroupInfo',
        },
      })

      // 添加供应商和分类字段
      pipeline.push({
        $addFields: {
          supplier: { $arrayElemAt: ['$fabricGroupInfo.supplier', 0] },
          group_classification: { $arrayElemAt: ['$fabricGroupInfo.group_classification', 0] },
        },
      })

      // 移除临时字段
      pipeline.push({ $project: { fabricGroupInfo: 0 } })

      // 添加筛选条件
      const matchConditions: any = {}

      // 处理供应商筛选
      if (supplier) {
        // 如果是逗号分隔的字符串，转换为数组
        if (supplier.includes(',')) {
          const supplierArray = supplier.split(',').filter(Boolean)
          matchConditions.supplier = { $in: supplierArray }
        } else {
          matchConditions.supplier = supplier
        }
      }

      // 处理分类筛选
      if (classification) {
        // 如果是逗号分隔的字符串，转换为数组
        if (classification.includes(',')) {
          const classificationArray = classification.split(',').filter(Boolean)
          matchConditions['group_classification'] = { $in: classificationArray }
        } else {
          matchConditions['group_classification'] = classification
        }
      }

      // 添加匹配阶段
      if (Object.keys(matchConditions).length > 0) {
        pipeline.push({ $match: matchConditions })
      }
    }

    // 分组获取不同的年份
    pipeline.push({ $group: { _id: '$clothing_year' } })

    // 投影年份字段
    pipeline.push({ $project: { _id: 0, year: '$_id' } })

    console.log('getClothingYearOptions 聚合管道：', JSON.stringify(pipeline))

    // 执行聚合查询
    let years: string[] = []

    if (pipeline.length > 0) {
      const result = await this.clothingModel.aggregate(pipeline).exec()
      years = result.map((item) => item.year)
    } else {
      // 如果没有筛选条件，直接使用 distinct 查询
      years = await this.clothingModel.distinct('clothing_year').exec()
    }

    return years.sort((a, b) => {
      // 提取年份数字进行比较
      const yearA = parseInt(a.match(/\d+/)?.[0] || '0')
      const yearB = parseInt(b.match(/\d+/)?.[0] || '0')
      return yearB - yearA // 倒序排列
    })
  }

  /**
   * 获取供应商选项
   * @param year 年份筛选
   * @param classification 分类筛选
   * @returns 供应商选项列表
   */
  async getSupplierOptions(year?: string, classification?: string): Promise<string[]> {
    console.log('getSupplierOptions 方法接收到的参数：', { year, classification })

    const filter: any = {}

    if (year) {
      // 如果是逗号分隔的字符串，转换为数组
      if (year.includes(',')) {
        const yearArray = year.split(',').filter(Boolean)
        filter.fabric_group_year = { $in: yearArray }
      } else {
        filter.fabric_group_year = year
      }
    }

    if (classification) {
      // 如果是逗号分隔的字符串，转换为数组
      if (classification.includes(',')) {
        const classificationArray = classification.split(',').filter(Boolean)
        filter.group_classification = { $in: classificationArray }
      } else {
        filter.group_classification = classification
      }
    }

    console.log('getSupplierOptions 构建的查询条件：', JSON.stringify(filter))

    return this.fabricGroupModel.find(filter).distinct('supplier').exec()
  }

  /**
   * 获取分类选项
   * @param year 年份筛选
   * @param supplier 供应商筛选
   * @returns 分类选项列表
   */
  async getClassificationOptions(year?: string, supplier?: string): Promise<string[]> {
    console.log('getClassificationOptions 方法接收到的参数：', { year, supplier })

    const filter: any = {}

    if (year) {
      // 如果是逗号分隔的字符串，转换为数组
      if (year.includes(',')) {
        const yearArray = year.split(',').filter(Boolean)
        filter.fabric_group_year = { $in: yearArray }
      } else {
        filter.fabric_group_year = year
      }
    }

    if (supplier) {
      // 如果是逗号分隔的字符串，转换为数组
      if (supplier.includes(',')) {
        const supplierArray = supplier.split(',').filter(Boolean)
        filter.supplier = { $in: supplierArray }
      } else {
        filter.supplier = supplier
      }
    }

    console.log('getClassificationOptions 构建的查询条件：', JSON.stringify(filter))

    // 由于group_classification是数组，需要使用聚合查询
    const result = await this.fabricGroupModel
      .aggregate([
        { $match: filter },
        { $unwind: '$group_classification' },
        { $group: { _id: '$group_classification' } },
        { $project: { _id: 0, classification: '$_id' } },
      ] as any)
      .exec()

    return result.map((item) => item.classification)
  }

  /**
   * 获取袖长、尺码、款式、口袋类型选项
   * @returns 袖长、尺码、款式、口袋类型选项的对象
   */
  async getFourOptions(): Promise<{
    sleeves: string[]
    sizes: string[]
    styles: string[]
    pocketTypes: string[]
  }> {
    // 获取当前年份
    const currentYear = new Date().getFullYear()

    // 构建最近两年的年份字符串数组，例如 ["2023年", "2024年"]
    const recentYears = [`${currentYear}年`, `${currentYear - 1}年`]

    console.log('获取最近两年的选项数据，年份范围：', recentYears)

    // 构建查询条件，只查询最近两年的数据
    const query = { clothing_year: { $in: recentYears } }

    // 使用聚合管道获取四个字段的不重复值
    const [sleeves, sizes, styles, pocketTypes] = await Promise.all([
      this.clothingModel.distinct('long_or_short_sleeve', query).exec(),
      this.clothingModel.distinct('size', query).exec(),
      this.clothingModel.distinct('style', query).exec(),
      this.clothingModel.distinct('pocket_type', query).exec(),
    ])

    // 过滤掉 null 和空字符串值
    const filterValues = (arr: any[]): string[] => {
      return arr
        .filter((item) => item !== null && item !== undefined && item !== '')
        .map((item) => String(item))
    }

    const result = {
      sleeves: filterValues(sleeves),
      sizes: filterValues(sizes),
      styles: filterValues(styles),
      pocketTypes: filterValues(pocketTypes),
    }

    console.log('获取到的选项数据：', {
      sleeves: result.sleeves.length,
      sizes: result.sizes.length,
      styles: result.styles.length,
      pocketTypes: result.pocketTypes.length,
    })

    return result
  }

  /**
   * 批量导入服装数据
   * @param clothingDataList 服装数据列表
   * @returns 导入结果
   */
  async importClothingBatch(
    clothingDataList: CreateClothingDto[]
  ): Promise<{ success: boolean; message: string; count: number }> {
    if (!clothingDataList || clothingDataList.length === 0) {
      return { success: false, message: '没有数据可导入', count: 0 }
    }

    try {
      const now = new Date()
      const preparedData = clothingDataList.map((item) => ({
        ...item,
        createTime: now,
        lastChangeTime: now,
      }))

      const result = await this.clothingModel.insertMany(preparedData, { ordered: false })
      return { success: true, message: '数据导入成功', count: result.length }
    } catch (error: any) {
      // 处理部分导入成功的情况
      if (error.writeErrors && error.insertedDocs && error.insertedDocs.length > 0) {
        return {
          success: true,
          message: `部分数据导入成功，${error.insertedDocs.length}/${clothingDataList.length} 条记录已导入`,
          count: error.insertedDocs.length,
        }
      }
      throw error
    }
  }

  /**
   * 根据服装名称和年份查找普通服装
   * @param data 包含服装名称数组和服装年份
   * @returns 查询到的普通服装列表
   */
  async findClothingByNames(data: { clothing_names: string[]; clothing_year: string }): Promise<{
    success: boolean
    clothingList: Clothing[]
  }> {
    console.log('服务层接收到的普通服装查询参数:', JSON.stringify(data))

    try {
      // 查询普通服装
      let clothingList: Clothing[] = []
      if (data.clothing_names && data.clothing_names.length > 0) {
        clothingList = await this.clothingModel
          .find({
            clothing_name: { $in: data.clothing_names },
            clothing_year: data.clothing_year + '年',
          })
          .exec()

        console.log(`找到 ${clothingList.length} 条普通服装记录`)
      }

      return {
        success: true,
        clothingList,
      }
    } catch (error) {
      console.error('查询普通服装失败:', error)
      throw new Error('查询普通服装失败: ' + (error || '未知错误'))
    }
  }

  /**
   * 数据校准 - 重新统计并更新指定本厂服装的入库数和出库数
   * @param calibrateData 校准数据，包含查询条件或服装ID列表
   * @returns 校准结果
   */
  async calibrateData(calibrateData?: {
    clothingIds?: string[];
    queryParams?: any
  }): Promise<{ message: string; updated: number }> {
    try {
      console.log('开始本厂服装数据校准...', calibrateData)

      let targetClothing: any[] = []

      if (calibrateData?.clothingIds && calibrateData.clothingIds.length > 0) {
        // 如果提供了服装ID列表，只校准这些服装
        targetClothing = await this.clothingModel
          .find({ clothing_id: { $in: calibrateData.clothingIds } })
          .exec()
        console.log(`根据服装ID列表找到 ${targetClothing.length} 条本厂服装记录需要校准`)
      } else if (calibrateData?.queryParams) {
        // 如果提供了查询参数，根据查询条件获取服装列表
        const queryResult = await this.getClothingList({
          ...calibrateData.queryParams,
          page: 1,
          limit: 10000 // 设置一个较大的限制以获取所有匹配的记录
        })
        targetClothing = queryResult.clothingList
        console.log(`根据查询条件找到 ${targetClothing.length} 条本厂服装记录需要校准`)
      } else {
        // 如果没有提供任何条件，校准所有本厂服装（保持向后兼容）
        targetClothing = await this.clothingModel.find({}).exec()
        console.log(`找到 ${targetClothing.length} 条本厂服装记录需要校准`)
      }

      let updatedCount = 0

      // 逐个处理每个本厂服装
      for (const clothing of targetClothing) {
        try {
          // 重新计算出库数 (shipments) - 基于transportation_detail集合
          const outgoingResult = await this.transportationDetailModel
            .aggregate([
              {
                $match: {
                  clothing_id: clothing.clothing_id,
                  $or: [
                    { oem: { $ne: '是' } }, // 不是OEM服装
                    { oem: null }, // oem字段为空
                    { oem: { $exists: false } }, // oem字段不存在
                  ],
                },
              },
              { $group: { _id: null, total_pcs: { $sum: '$out_pcs' } } },
            ])
            .exec()

          const totalShipments = outgoingResult.length > 0 ? outgoingResult[0].total_pcs : 0

          // 更新本厂服装记录
          await this.clothingModel
            .updateOne(
              { clothing_id: clothing.clothing_id },
              {
                $set: {
                  shipments: totalShipments,
                },
              }
            )
            .exec()

          console.log(`已校准本厂服装 ${clothing.clothing_id}:  发库数=${totalShipments}, `)
          updatedCount++
        } catch (itemError) {
          console.error(`校准本厂服装 ${clothing.clothing_id} 时出错:`, itemError)
          // 继续处理下一个，不中断整个过程
        }
      }

      console.log(`本厂服装数据校准完成，共更新 ${updatedCount} 条记录`)

      return {
        message: `数据校准完成，共更新 ${updatedCount} 条本厂服装记录（出库数、库存数）`,
        updated: updatedCount,
      }
    } catch (error) {
      console.error('本厂服装数据校准失败:', error)
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      throw new Error('本厂服装数据校准失败: ' + errorMessage)
    }
  }
}
