import { Exclude, Expose, Type } from 'class-transformer'
import { ClothingImageDto, ColorPcsDto } from './create-clothing.dto'
import { IsArray, IsDate, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator'

/**
 * 服装响应 DTO
 */
export class ClothingResponseDto {
  @Expose()
  _id: string

  @Expose()
  @IsString()
  clothing_year: string

  @Expose()
  @IsString()
  @IsOptional()
  clipping_year?: string

  @Expose()
  @IsString()
  @IsOptional()
  make_year?: string

  @Expose()
  @IsString()
  clothing_id: string

  @Expose()
  @IsString()
  clothing_name: string

  @Expose()
  @IsString()
  @IsOptional()
  long_or_short_sleeve?: string

  @Expose()
  @IsString()
  @IsOptional()
  size?: string

  @Expose()
  @IsString()
  @IsOptional()
  style?: string

  @Expose()
  @IsString()
  @IsOptional()
  pocket_type?: string

  @Expose()
  @IsString()
  @IsOptional()
  fabric_group_id?: string

  @Expose()
  @IsString()
  @IsOptional()
  supplier?: string

  @Expose()
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  group_classification?: string[]

  @Expose()
  @IsNumber()
  @IsOptional()
  order_quantity?: number

  @Expose()
  @IsNumber()
  @IsOptional()
  clipping_pcs?: number

  @Expose()
  @IsNumber()
  @IsOptional()
  fabric_usage_per_clothing?: number

  @Expose()
  @IsString()
  @IsOptional()
  memo?: string

  @Expose()
  @IsString()
  @IsOptional()
  printed?: string

  @Expose()
  @IsNumber()
  @IsOptional()
  shipments?: number

  @Expose()
  @IsString()
  @IsOptional()
  state?: string

  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ClothingImageDto)
  @IsOptional()
  img?: ClothingImageDto[]

  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ColorPcsDto)
  @IsOptional()
  colorPcs?: ColorPcsDto[]

  @Expose()
  @IsDate()
  @IsOptional()
  createTime?: Date

  @Expose()
  @IsDate()
  @IsOptional()
  lastChangeTime?: Date
}

/**
 * 服装列表响应 DTO
 */
export class ClothingListResponseDto {
  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ClothingResponseDto)
  clothingList: ClothingResponseDto[]

  @Expose()
  @IsNumber()
  total: number
}

/**
 * 服装年份选项响应 DTO
 */
export class ClothingYearOptionsResponseDto {
  @Expose()
  @IsArray()
  @IsString({ each: true })
  years: string[]
}

/**
 * 导入服装批量响应 DTO
 */
export class ImportClothingBatchResponseDto {
  @Expose()
  success: boolean

  @Expose()
  @IsString()
  message: string

  @Expose()
  @IsNumber()
  count: number
}
