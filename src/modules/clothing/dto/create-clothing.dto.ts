import { IsString, IsOptional, IsNumber, IsArray, ValidateNested, IsDate, IsEnum } from 'class-validator'
import { Type } from 'class-transformer'

/**
 * 服装图片 DTO
 */
export class ClothingImageDto {
  @IsString()
  @IsOptional()
  url?: string

  @IsString()
  @IsOptional()
  Key?: string
}

/**
 * 颜色件数 DTO
 */
export class ColorPcsDto {
  @IsString()
  @IsOptional()
  fabric_id?: string

  @IsString()
  @IsOptional()
  pcs?: string
}

/**
 * 创建服装 DTO
 */
export class CreateClothingDto {
  @IsString({ message: '所属年份必须是字符串' })
  clothing_year: string

  @IsString({ message: '裁剪年份必须是字符串' })
  @IsOptional()
  clipping_year?: string

  @IsString({ message: '制作年份必须是字符串' })
  @IsOptional()
  make_year?: string

  @IsString({ message: '服装编号必须是字符串' })
  clothing_id: string

  @IsString({ message: '服装名称必须是字符串' })
  clothing_name: string

  @IsString({ message: '袖长必须是字符串' })
  @IsOptional()
  long_or_short_sleeve?: string

  @IsString({ message: '尺码必须是字符串' })
  @IsOptional()
  size?: string

  @IsString({ message: '款式必须是字符串' })
  @IsOptional()
  style?: string

  @IsString({ message: '口袋类型必须是字符串' })
  @IsOptional()
  pocket_type?: string

  @IsString({ message: '面料组必须是字符串' })
  @IsOptional()
  fabric_group_id?: string

  @IsNumber({}, { message: '订单数量必须是数字' })
  @IsOptional()
  order_quantity?: number

  @IsNumber({}, { message: '裁剪件数必须是数字' })
  @IsOptional()
  clipping_pcs?: number

  @IsNumber({}, { message: '单件用料必须是数字' })
  @IsOptional()
  fabric_usage_per_clothing?: number

  @IsString({ message: '备注必须是字符串' })
  @IsOptional()
  memo?: string

  @IsString({ message: '印花必须是字符串' })
  @IsOptional()
  printed?: string

  @IsNumber({}, { message: '出货数必须是数字' })
  @IsOptional()
  shipments?: number

  @IsString({ message: '状态必须是字符串' })
  @IsOptional()
  state?: string

  @IsString({ message: '工艺细节必须是字符串' })
  @IsOptional()
  craft_details?: string

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ClothingImageDto)
  @IsOptional()
  img?: ClothingImageDto[]

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ColorPcsDto)
  @IsOptional()
  colorPcs?: ColorPcsDto[]
}
