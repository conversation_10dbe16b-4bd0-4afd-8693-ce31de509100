import { IsString, IsO<PERSON>al, IsNumber, IsArray, IsEnum } from 'class-validator'
import { Type, Transform } from 'class-transformer'

/**
 * 查询服装列表 DTO
 */
export class QueryClothingDto {
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  page?: number = 1

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  limit?: number = 10

  @IsString()
  @IsOptional()
  clothing_year?: string

  @IsArray()
  @IsOptional()
  @Transform(({ value }) => {
    // 处理 clothing_years[] 的情况
    if (typeof value === 'string') {
      return [value]
    }
    return value
  })
  clothing_years?: string[]

  @IsString()
  @IsOptional()
  make_year?: string

  @IsArray()
  @IsOptional()
  @Transform(({ value }) => {
    // 处理 make_years[] 的情况
    if (typeof value === 'string') {
      return [value]
    }
    return value
  })
  make_years?: string[]

  @IsString()
  @IsOptional()
  clothing_id?: string

  @IsString()
  @IsOptional()
  clothing_name?: string

  @IsString()
  @IsOptional()
  long_or_short_sleeve?: string

  @IsString()
  @IsOptional()
  supplier?: string

  @IsArray()
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').filter(Boolean)
    }
    return value
  })
  suppliers?: string[]

  @IsString()
  @IsOptional()
  classification?: string

  @IsArray()
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').filter(Boolean)
    }
    return value
  })
  classifications?: string[]

  @IsString()
  @IsOptional()
  state?: string

  @IsString()
  @IsOptional()
  fabric_group_id?: string
}
