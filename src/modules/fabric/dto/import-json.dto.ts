import { <PERSON><PERSON>tring, <PERSON>Not<PERSON>mpty, Is<PERSON>rray, IsO<PERSON>al, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { CreateFabricDto } from './create-fabric.dto'

export class ImportJsonDto {
  @ApiProperty({ description: '工作表名称' })
  @IsString()
  @IsNotEmpty({ message: '工作表名称不能为空' })
  sheetName: string

  @ApiProperty({ description: '文件名称' })
  @IsString()
  @IsNotEmpty({ message: '文件名称不能为空' })
  fileName: string

  @ApiProperty({ description: '总记录数' })
  @IsOptional()
  totalRecords?: number

  @ApiProperty({ description: '布料数据数组', type: [Object] })
  @IsArray()
  @IsNotEmpty({ message: '数据不能为空' })
  @ValidateNested({ each: true })
  @Type(() => Object)
  data: any[]
}
