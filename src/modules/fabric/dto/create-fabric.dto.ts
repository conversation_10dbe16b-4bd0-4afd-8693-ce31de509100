import { IsString, <PERSON><PERSON>otEmpty, <PERSON><PERSON><PERSON>ber, IsOptional } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'

export class CreateFabricDto {
  @ApiProperty({ description: '年份' })
  @IsString()
  @IsNotEmpty({ message: '年份不能为空' })
  fabric_year: string

  @ApiProperty({ description: '布料编码' })
  @IsString()
  @IsNotEmpty({ message: '布料编码不能为空' })
  fabric_id: string

  @ApiProperty({ description: '布料名称' })
  @IsString()
  @IsNotEmpty({ message: '布料名称不能为空' })
  fabric_name: string

  @ApiProperty({ description: '供应商' })
  @IsString()
  @IsNotEmpty({ message: '供应商不能为空' })
  supplier: string

  @ApiProperty({ description: '分类', required: false })
  @IsString()
  @IsOptional()
  classification?: string

  @ApiProperty({ description: '订购数量', required: false })
  @IsNumber()
  @IsOptional()
  order_quantity?: number

  @ApiProperty({ description: '备注', required: false })
  @IsString()
  @IsOptional()
  remark?: string

  @ApiProperty({ description: '入库数量', required: false })
  @IsNumber()
  @IsOptional()
  in_quantity?: number
}