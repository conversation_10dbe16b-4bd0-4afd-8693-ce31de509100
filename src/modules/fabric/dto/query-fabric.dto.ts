import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsArray } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type, Transform } from 'class-transformer'

export class QueryFabricDto {
  @ApiProperty({ description: '年份', required: false })
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  fabric_year?: string

  @ApiProperty({ description: '年份列表', required: false, type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').filter(Boolean)
    }
    return value
  })
  fabric_years?: string[]

  @ApiProperty({ description: '布料编码', required: false })
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  fabric_id?: string

  @ApiProperty({ description: '布料名称', required: false })
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  fabric_name?: string

  @ApiProperty({ description: '供应商', required: false })
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  supplier?: string

  @ApiProperty({ description: '供应商列表', required: false, type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').filter(Boolean)
    }
    return value
  })
  suppliers?: string[]

  @ApiProperty({ description: '分类', required: false })
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  classification?: string

  @ApiProperty({ description: '分类列表', required: false, type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').filter(Boolean)
    }
    return value
  })
  classifications?: string[]

  @ApiProperty({ description: '页码', default: 1, required: false })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  page?: number = 1

  @ApiProperty({ description: '每页数量', default: 10, required: false })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  limit?: number = 10
} 