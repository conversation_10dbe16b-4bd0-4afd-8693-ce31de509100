import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { Fabric } from '../../models/fabric.model'
import { FabricWarehouseDetail } from '../../models/fabricWarehouseDetail.model'
import { CreateFabricDto, UpdateFabricDto, QueryFabricDto } from './dto'

// 定义返回类型接口
interface FabricListResponse {
  total: number
  page: number
  limit: number
  data: Fabric[]
}

@Injectable()
export class FabricsService {
  constructor(
    @InjectModel('Fabric') private readonly fabricModel: Model<Fabric>,
    @InjectModel('FabricWarehouseDetail')
    private readonly fabricWarehouseDetailModel: Model<FabricWarehouseDetail>
  ) {}

  // 创建布料
  async create(createFabricDto: CreateFabricDto): Promise<Fabric> {
    const fabric = new this.fabricModel(createFabricDto)
    return fabric.save()
  }

  // 获取布料列表，支持分页和筛选
  async findAll(queryParams: QueryFabricDto) {
    console.log('后端收到的查询参数：', JSON.stringify(queryParams))

    const {
      fabric_year,
      fabric_years,
      fabric_id,
      fabric_name,
      supplier,
      suppliers,
      classification,
      classifications,
      page = 1,
      limit = 10,
    } = queryParams

    const skip = (page - 1) * limit

    // 构建查询条件
    const filter: any = {}

    // 处理年份条件
    if (fabric_year) {
      filter.fabric_year = fabric_year
    } else if (fabric_years?.length) {
      filter.fabric_year = { $in: fabric_years }
    }

    // 处理编码和名称条件
    if (fabric_id) {
      filter.fabric_id = { $regex: fabric_id, $options: 'i' }
    }

    if (fabric_name) {
      filter.fabric_name = { $regex: fabric_name, $options: 'i' }
    }

    // 处理供应商条件
    if (supplier) {
      filter.supplier = supplier
    } else if (suppliers?.length) {
      filter.supplier = { $in: suppliers }
    }

    // 处理分类条件
    if (classification) {
      filter.classification = classification
    } else if (classifications?.length) {
      filter.classification = { $in: classifications }
    }

    console.log('构建的数据库查询条件：', JSON.stringify(filter))

    // 如果没有条件，尝试获取所有数据
    if (Object.keys(filter).length === 0) {
      console.log('没有查询条件，将返回所有数据')
    }

    // 执行查询
    const [data, total] = await Promise.all([
      this.fabricModel.find(filter).skip(skip).limit(limit).sort({ fabric_id: -1 }).exec(), // 按照布料编码倒序排列
      this.fabricModel.countDocuments(filter).exec(),
    ])

    console.log(`查询结果：找到 ${total} 条记录`)
    const response = {
      data: {
        total,
        page: Number(page),
        limit: Number(limit),
        fabricList: data as Fabric[],
      },
    }
    return response
  }

  // 获取单个布料
  async findOne(id: string): Promise<Fabric> {
    // 如果 Id 的开头是 BL，说明是布料编码
    if (id.startsWith('BL')) {
      const fabric = await this.fabricModel.findOne({ fabric_id: id }).exec()
      if (!fabric) {
        throw new NotFoundException(`布料编码 ${id} 不存在`)
      }
      return fabric
    }
    // 如果 Id 的开头不是 BL，说明是布料 ID
    const fabric = await this.fabricModel.findById(id).exec()
    if (!fabric) {
      throw new NotFoundException(`布料ID ${id} 不存在`)
    }
    return fabric
  }

  // 更新布料
  async update(id: string, updateFabricDto: UpdateFabricDto): Promise<Fabric> {
    const updatedFabric = await this.fabricModel
      .findByIdAndUpdate(id, updateFabricDto, { new: true })
      .exec()

    if (!updatedFabric) {
      throw new NotFoundException(`布料ID ${id} 不存在`)
    }

    return updatedFabric
  }

  // 删除布料
  async remove(id: string): Promise<Fabric> {
    const deletedFabric = await this.fabricModel.findByIdAndDelete(id).exec()

    if (!deletedFabric) {
      throw new NotFoundException(`布料ID ${id} 不存在`)
    }

    return deletedFabric
  }

  // 获取分类选项
  async getCategoryOptions(
    years: string = '',
    suppliers: string = ''
  ): Promise<{ data: string[]; total: number }> {
    const filter: any = {}

    if (years) {
      const yearArray = years.split(',')
      filter.fabric_year = { $in: yearArray }
    }

    if (suppliers) {
      const supplierArray = suppliers.split(',')
      filter.supplier = { $in: supplierArray }
    }

    const result = await this.fabricModel.distinct('classification', filter).exec()
    return {
      data: result as string[],
      total: result.length,
    }
  }

  // 获取年份选项
  async getYearOptions(
    suppliers: string = '',
    classifications: string = ''
  ): Promise<{ data: string[]; total: number }> {
    const filter: any = {}

    if (suppliers) {
      const supplierArray = suppliers.split(',')
      filter.supplier = { $in: supplierArray }
    }

    if (classifications) {
      const classArray = classifications.split(',')
      filter.classification = { $in: classArray }
    }

    const result = await this.fabricModel.distinct('fabric_year', filter).exec()
    //result 排序一下，倒序
    result.sort((a, b) => b.localeCompare(a))

    //返回是是一个 yearResponse
    return {
      data: result as string[],
      total: result.length,
    }
  }

  // 获取供应商选项
  async getSupplierOptions(
    years: string = '',
    classifications: string = ''
  ): Promise<{ data: string[]; total: number }> {
    const filter: any = {}

    if (years) {
      const yearArray = years.split(',')
      filter.fabric_year = { $in: yearArray }
    }

    if (classifications) {
      const classArray = classifications.split(',')
      filter.classification = { $in: classArray }
    }

    const result = await this.fabricModel.distinct('supplier', filter).exec()
    return {
      data: result as string[],
      total: result.length,
    }
  }

  // 更新布料的入库数量
  async updateInQuantity(fabricId: string): Promise<any> {
    console.log(`开始更新布料 ${fabricId} 的入库数量`)

    try {
      // 1. 查询布料是否存在
      const fabric = await this.fabricModel.findOne({ fabric_id: fabricId }).exec()

      if (!fabric) {
        console.warn(`布料ID ${fabricId} 不存在`)
        return {
          success: false,
          fabricId,
          error: `布料ID ${fabricId} 不存在`,
        }
      }

      // 2. 统计该布料在所有入库明细中的总数量
      const result = await this.fabricWarehouseDetailModel
        .aggregate([
          { $match: { fabric_id: fabricId } },
          { $group: { _id: null, total_meter: { $sum: '$meter' } } },
        ])
        .exec()

      const totalMeter = result.length > 0 ? result[0].total_meter : 0

      console.log(`布料 ${fabricId} 的入库总数量为: ${totalMeter}`)

      // 3. 更新布料表中的 in_quantity 字段
      const updatedFabric = await this.fabricModel
        .findByIdAndUpdate(fabric._id, { in_quantity: totalMeter }, { new: true })
        .exec()

      return {
        success: true,
        fabricId,
        in_quantity: totalMeter,
        fabric: updatedFabric,
      }
    } catch (error: any) {
      console.error(`更新布料入库数量失败: ${error.message}`)
      return {
        success: false,
        fabricId,
        error: error.message,
      }
    }
  }

  // 批量更新多个布料的入库数量
  async updateInQuantityBatch(fabricIds: string[]): Promise<any> {
    console.log(`开始批量更新布料入库数量，共 ${fabricIds.length} 个布料`)

    // 验证输入
    if (!fabricIds || !Array.isArray(fabricIds) || fabricIds.length === 0) {
      return {
        success: false,
        message: '没有提供有效的布料ID列表',
        totalUpdated: 0,
        totalFailed: 0,
      }
    }

    const results = []
    const errors = []

    // 去除重复的布料ID
    const uniqueFabricIds = [...new Set(fabricIds)]
    console.log(`去除重复后的布料ID数量: ${uniqueFabricIds.length}`)

    for (const fabricId of uniqueFabricIds) {
      // 跳过空值
      if (!fabricId) {
        errors.push({ fabricId: '空值', error: '布料ID不能为空' })
        continue
      }

      try {
        const result = await this.updateInQuantity(fabricId)
        if (result.success) {
          results.push(result)
        } else {
          errors.push(result) // 现在updateInQuantity会返回错误对象而不是抛出异常
        }
      } catch (error: any) {
        // 以防万一还有未捕获的异常
        errors.push({ fabricId, error: error.message || '未知错误' })
      }
    }

    return {
      success: errors.length === 0,
      results,
      errors,
      totalUpdated: results.length,
      totalFailed: errors.length,
    }
  }

  // 批量导入布料数据
  async importJsonData(jsonData: any[]): Promise<any> {
    console.log(`开始批量导入布料数据，共 ${jsonData.length} 条记录`)
    console.log('导入数据示例:', JSON.stringify(jsonData.slice(0, 1)))

    const results = []
    const errors = []
    const ids = []

    try {
      for (const item of jsonData) {
        try {
          // 打印当前处理的数据项
          console.log('处理数据项:', JSON.stringify(item))

          // 检查并转换字段名称（处理Excel导入时可能的列名不一致问题）
          const processedItem = {
            fabric_id: item.fabric_id || item.fabricId || item['布料编码'] || '',
            fabric_name: item.fabric_name || item.fabricName || item['布料名称'] || '',
            fabric_year: item.fabric_year || item.fabricYear || item['年份'] || '',
            supplier: item.supplier || item['供应商'] || '',
            classification: item.classification || item['分类'] || '',
            order_quantity: Number(
              item.order_quantity || item.orderQuantity || item['订购数量'] || 0
            ),
            in_quantity: Number(item.in_quantity || item.inQuantity || item['入库数量'] || 0),
            remark: item.remark || item['备注'] || '',
          }

          console.log('处理后的数据项:', JSON.stringify(processedItem))

          // 检查必填字段
          if (
            !processedItem.fabric_id ||
            !processedItem.fabric_name ||
            !processedItem.fabric_year ||
            !processedItem.supplier
          ) {
            throw new Error(
              `缺少必填字段: fabric_id=${processedItem.fabric_id}, fabric_name=${processedItem.fabric_name}, fabric_year=${processedItem.fabric_year}, supplier=${processedItem.supplier}`
            )
          }

          // 检查布料编码是否已存在
          const existingFabric = await this.fabricModel
            .findOne({ fabric_id: processedItem.fabric_id })
            .exec()

          let fabric
          if (existingFabric) {
            // 如果存在，则更新
            console.log(`布料编码 ${processedItem.fabric_id} 已存在，进行更新操作`)
            fabric = await this.fabricModel
              .findByIdAndUpdate(
                existingFabric._id,
                {
                  fabric_year: processedItem.fabric_year,
                  fabric_name: processedItem.fabric_name,
                  supplier: processedItem.supplier,
                  classification: processedItem.classification || '',
                  order_quantity: processedItem.order_quantity || 0,
                  in_quantity: processedItem.in_quantity || 0,
                  remark: processedItem.remark || '',
                },
                { new: true }
              )
              .exec()

            results.push({ status: 'updated', fabric })
          } else {
            // 如果不存在，则创建
            console.log(`布料编码 ${processedItem.fabric_id} 不存在，进行创建操作`)
            const newFabric = new this.fabricModel({
              fabric_id: processedItem.fabric_id,
              fabric_year: processedItem.fabric_year,
              fabric_name: processedItem.fabric_name,
              supplier: processedItem.supplier,
              classification: processedItem.classification || '',
              order_quantity: processedItem.order_quantity || 0,
              in_quantity: processedItem.in_quantity || 0,
              remark: processedItem.remark || '',
            })

            fabric = await newFabric.save()
            results.push({ status: 'created', fabric })
          }
          if (fabric) {
            ids.push(fabric._id)
          }
        } catch (error: any) {
          console.error('处理数据项时出错:', error.message)
          errors.push({ item, error: error.message })
        }
      }

      if (errors.length > 0) {
        console.log('导入过程中发生错误')
        console.log('错误详情:', JSON.stringify(errors))
      } else {
        console.log('导入成功')
      }
    } catch (error: any) {
      console.error('导入过程中发生未捕获的错误:', error.message, error.stack)
      throw error
    }

    return {
      success: errors.length === 0,
      count: results.length,
      ids,
      results,
      errors,
      totalCreated: results.filter((r) => r.status === 'created').length,
      totalUpdated: results.filter((r) => r.status === 'updated').length,
      totalFailed: errors.length,
    }
  }
}
