import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Res,
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger'
import { FabricsService } from './fabric.service'
import { CreateFabricDto, UpdateFabricDto, QueryFabricDto } from './dto'
import { Response } from 'express'

@ApiTags('布料管理')
@Controller('/fabric')
export class FabricsController {
  constructor(private readonly fabricsService: FabricsService) {}

  @Post()
  @ApiOperation({ summary: '创建布料' })
  create(@Body() createFabricDto: CreateFabricDto) {
    return this.fabricsService.create(createFabricDto)
  }

  @Get()
  @ApiOperation({ summary: '获取布料列表' })
  findAll(@Query() queryFabricDto: QueryFabricDto) {
    return this.fabricsService.findAll(queryFabricDto)
  }

  // 已删除旧的import方法，改用import-json方法

  @Post('import-json')
  @ApiOperation({ summary: '导入JSON数据', description: '接收前端传来的JSON数据，批量写入数据库' })
  importJson(@Body() importJsonDto: any) {
    console.log('接收到 JSON 数据导入请求:', {
      sheetName: importJsonDto.sheetName,
      fileName: importJsonDto.fileName,
      totalRecords: importJsonDto.totalRecords,
      dataLength: importJsonDto.data?.length || 0,
    })

    // 检查数据是否存在
    if (
      !importJsonDto.data ||
      !Array.isArray(importJsonDto.data) ||
      importJsonDto.data.length === 0
    ) {
      return { success: false, message: '数据不能为空' }
    }

    try {
      // 打印数据示例以便于调试
      console.log('数据示例:', JSON.stringify(importJsonDto.data.slice(0, 1)))

      // 调用服务层方法处理数据
      return this.fabricsService.importJsonData(importJsonDto.data)
    } catch (error: any) {
      console.error('处理导入数据时出错:', error.message)
      return { success: false, message: `处理导入数据时出错: ${error.message}` }
    }
  }

  @Get('category-options')
  @ApiOperation({ summary: '获取分类选项列表' })
  @ApiQuery({ name: 'year', required: false, description: '年份过滤，多个用逗号分隔' })
  @ApiQuery({ name: 'supplier', required: false, description: '供应商过滤，多个用逗号分隔' })
  getCategoryOptions(@Query('year') years: string, @Query('supplier') suppliers: string) {
    return this.fabricsService.getCategoryOptions(years, suppliers)
  }

  @Get('year-options')
  @ApiOperation({ summary: '获取年份选项列表' })
  @ApiQuery({ name: 'supplier', required: false, description: '供应商过滤，多个用逗号分隔' })
  @ApiQuery({ name: 'classification', required: false, description: '分类过滤，多个用逗号分隔' })
  getYearOptions(
    @Query('supplier') suppliers: string,
    @Query('classification') classifications: string
  ) {
    return this.fabricsService.getYearOptions(suppliers, classifications)
  }

  @Get('supplier-options')
  @ApiOperation({ summary: '获取供应商选项列表' })
  @ApiQuery({ name: 'year', required: false, description: '年份过滤，多个用逗号分隔' })
  @ApiQuery({ name: 'classification', required: false, description: '分类过滤，多个用逗号分隔' })
  getSupplierOptions(
    @Query('year') years: string,
    @Query('classification') classifications: string
  ) {
    return this.fabricsService.getSupplierOptions(years, classifications)
  }

  @Get('export')
  @ApiOperation({ summary: '导出Excel' })
  exportExcel(@Query() queryParams: QueryFabricDto, @Res() res: Response) {
    // 需要在实际项目中实现Excel导出逻辑
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    res.setHeader('Content-Disposition', 'attachment; filename="fabrics.xlsx"')
    res.send('Excel导出功能需要实现')
  }

  @Get(':id')
  @ApiOperation({ summary: '获取布料详情' })
  @ApiParam({ name: 'id', description: '布料ID' })
  findOne(@Param('id') id: string) {
    return this.fabricsService.findOne(id)
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新布料' })
  @ApiParam({ name: 'id', description: '布料ID' })
  update(@Param('id') id: string, @Body() updateFabricDto: UpdateFabricDto) {
    return this.fabricsService.update(id, updateFabricDto)
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除布料' })
  @ApiParam({ name: 'id', description: '布料ID' })
  remove(@Param('id') id: string) {
    return this.fabricsService.remove(id)
  }

  @Post('update-in-quantity/:fabricId')
  @ApiOperation({ summary: '更新布料入库数量' })
  @ApiParam({ name: 'fabricId', description: '布料ID' })
  updateInQuantity(@Param('fabricId') fabricId: string) {
    return this.fabricsService.updateInQuantity(fabricId)
  }

  @Post('update-in-quantity-batch')
  @ApiOperation({ summary: '批量更新布料入库数量' })
  updateInQuantityBatch(@Body() body: { fabricIds: string[] }) {
    return this.fabricsService.updateInQuantityBatch(body.fabricIds)
  }
}
