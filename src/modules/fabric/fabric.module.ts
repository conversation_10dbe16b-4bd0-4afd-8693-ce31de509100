import { Module } from '@nestjs/common'
import { FabricsController } from './fabric.controller'
import { FabricsService } from './fabric.service'
import { ModelsModule } from '../../models/models.module'
import { MulterModule } from '@nestjs/platform-express'
import { diskStorage } from 'multer'
import { extname } from 'path'
import { Request } from 'express'

@Module({
  imports: [
    ModelsModule,
    MulterModule.register({
      storage: diskStorage({
        destination: './uploads/excel',
        filename: (req: Request, file: Express.Multer.File, cb: Function) => {
          const randomName = Array(32)
            .fill(null)
            .map(() => Math.round(Math.random() * 16).toString(16))
            .join('')
          return cb(null, `${randomName}${extname(file.originalname)}`)
        },
      }),
    }),
  ],
  controllers: [FabricsController],
  providers: [FabricsService],
  exports: [FabricsService],
})
export class FabricsModule {}
