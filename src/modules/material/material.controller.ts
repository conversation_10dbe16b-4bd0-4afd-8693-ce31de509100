import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Put,
  HttpStatus,
  HttpException,
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger'
import { MaterialService } from './material.service'
import { CreateMaterialDto } from './dto/create-material.dto'
import { UpdateMaterialDto } from './dto/update-material.dto'
import { QueryMaterialDto } from './dto/query-material.dto'

@ApiTags('物料管理')
@Controller('material')
export class MaterialController {
  constructor(private readonly materialService: MaterialService) {}

  @Post()
  @ApiOperation({ summary: '创建物料' })
  @ApiResponse({ status: 201, description: '物料创建成功' })
  async create(@Body() createMaterialDto: CreateMaterialDto) {
    try {
      const result = await this.materialService.create(createMaterialDto)
      return {
        code: HttpStatus.CREATED,
        data: result,
        message: '创建物料成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '创建物料失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Get()
  @ApiOperation({ summary: '获取物料列表' })
  @ApiResponse({ status: 200, description: '获取物料列表成功' })
  async findAll(@Query() queryParams: QueryMaterialDto) {
    try {
      const result = await this.materialService.findAll(queryParams)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '获取物料列表成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取物料列表失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Get('options/years')
  @ApiOperation({ summary: '获取年份选项' })
  @ApiResponse({ status: 200, description: '获取年份选项成功' })
  getYearOptions() {
    return this.materialService.getYearOptions()
  }

  @Get('options/units')
  @ApiOperation({ summary: '获取单位选项' })
  @ApiResponse({ status: 200, description: '获取单位选项成功' })
  getUnitOptions() {
    return this.materialService.getUnitOptions()
  }

  @Get('generate-id')
  @ApiOperation({ summary: '生成物料编号' })
  @ApiResponse({ status: 200, description: '生成物料编号成功' })
  async generateMaterialId(@Query('categoryId') categoryId: string) {
    try {
      const materialId = await this.materialService.generateMaterialId(categoryId)
      return {
        code: HttpStatus.OK,
        data: { material_id: materialId },
        message: '生成物料编号成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '生成物料编号失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Post('import-batch')
  @ApiOperation({ summary: '批量导入物料数据' })
  @ApiResponse({ status: 201, description: '批量导入成功' })
  importBatch(@Body() data: any[]) {
    return this.materialService.importBatch(data)
  }

  @Post('import-json')
  @ApiOperation({ summary: '导入JSON数据' })
  @ApiResponse({ status: 201, description: '导入JSON数据成功' })
  importJsonData(@Body() importData: any) {
    return this.materialService.importBatch(importData.data || importData)
  }

  @Get(':id')
  @ApiOperation({ summary: '获取物料详情' })
  @ApiResponse({ status: 200, description: '获取物料详情成功' })
  findOne(@Param('id') id: string) {
    return this.materialService.findOne(id)
  }

  @Put(':id')
  @ApiOperation({ summary: '更新物料' })
  @ApiResponse({ status: 200, description: '物料更新成功' })
  async update(@Param('id') id: string, @Body() updateMaterialDto: UpdateMaterialDto) {
    try {
      const result = await this.materialService.update(id, updateMaterialDto)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '更新物料成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '更新物料失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除物料' })
  @ApiResponse({ status: 200, description: '物料删除成功' })
  remove(@Param('id') id: string) {
    return this.materialService.remove(id)
  }
}
