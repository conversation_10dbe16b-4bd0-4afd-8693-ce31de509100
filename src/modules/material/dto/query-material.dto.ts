import { ApiPropertyOptional } from '@nestjs/swagger'
import { IsOptional, IsString, IsArray, IsNumber } from 'class-validator'
import { Transform } from 'class-transformer'

export class QueryMaterialDto {
  @ApiPropertyOptional({ description: '物料编号' })
  @IsOptional()
  @IsString()
  material_id?: string

  @ApiPropertyOptional({ description: '物料名称' })
  @IsOptional()
  @IsString()
  material_name?: string

  @ApiPropertyOptional({ description: '分类ID' })
  @IsOptional()
  @IsString()
  category_id?: string

  @ApiPropertyOptional({ description: '年份列表' })
  @IsOptional()
  @IsArray()
  @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
  years?: string[]

  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  page?: number = 1

  @ApiPropertyOptional({ description: '每页数量', default: 10 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  limit?: number = 10
}
