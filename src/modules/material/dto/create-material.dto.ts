import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsString, IsNumber, IsOptional, IsArray } from 'class-validator'
import { MaterialYearlyPrice } from '../../../models/material.model'

export class CreateMaterialDto {
  @ApiProperty({ description: '物料编号' })
  @IsString()
  material_id: string

  @ApiProperty({ description: '物料名称' })
  @IsString()
  material_name: string

  @ApiProperty({ description: '分类ID' })
  @IsString()
  category_id: string

  @ApiPropertyOptional({ description: '规格型号' })
  @IsOptional()
  @IsString()
  specification?: string

  @ApiProperty({ description: '计量单位' })
  @IsString()
  unit: string

  @ApiPropertyOptional({ description: '当前价格' })
  @IsOptional()
  @IsNumber()
  current_price?: number

  @ApiPropertyOptional({ description: '年度价格配置' })
  @IsOptional()
  @IsArray()
  yearly_prices?: MaterialYearlyPrice[]

  @ApiPropertyOptional({ description: '描述' })
  @IsOptional()
  @IsString()
  description?: string

  @ApiPropertyOptional({ description: '状态' })
  @IsOptional()
  @IsString()
  state?: string
}
