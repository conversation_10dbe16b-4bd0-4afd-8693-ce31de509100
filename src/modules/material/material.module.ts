import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { MaterialService } from './material.service'
import { MaterialController } from './material.controller'
import { MaterialSchema } from '../../models/material.model'
import { MaterialCategorySchema } from '../../models/materialCategory.model'

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Material', schema: MaterialSchema },
      { name: 'MaterialCategory', schema: MaterialCategorySchema }
    ]),
  ],
  controllers: [MaterialController],
  providers: [MaterialService],
  exports: [MaterialService],
})
export class MaterialModule {}
