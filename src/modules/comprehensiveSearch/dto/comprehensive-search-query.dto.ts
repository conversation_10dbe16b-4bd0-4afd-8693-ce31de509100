import { IsString, IsOptional, IsArray } from 'class-validator'
import { Transform } from 'class-transformer'
import { ApiProperty } from '@nestjs/swagger'

export class ComprehensiveSearchQueryDto {
  @ApiProperty({ description: '工厂类型', example: 'own', required: false })
  @IsOptional()
  @IsString()
  factoryType?: string

  @ApiProperty({ description: '年份', example: '2024', required: false })
  @IsOptional()
  @IsString()
  year?: string

  @ApiProperty({ description: '袖型数组', example: ['长袖', '短袖'], required: false })
  @IsOptional()
  @IsArray()
  @Transform(({ value }) => {
    console.log('袖型Transform - 原始值:', value, '类型:', typeof value)

    if (typeof value === 'string') {
      try {
        // 尝试解析JSON字符串
        const parsed = JSON.parse(value)
        console.log('袖型Transform - JSON解析结果:', parsed)
        return Array.isArray(parsed) ? parsed : [value]
      } catch {
        // 如果不是JSON，按逗号分割
        const result = value.split(',').map(item => item.trim()).filter(item => item)
        console.log('袖型Transform - 逗号分割结果:', result)
        return result
      }
    }

    if (Array.isArray(value)) {
      console.log('袖型Transform - 数组值:', value)
      return value
    }

    console.log('袖型Transform - 默认返回空数组')
    return []
  })
  sleeveTypes?: string[]

  @ApiProperty({ description: '供应商数组', required: false })
  @IsOptional()
  @IsArray()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        // 尝试解析JSON字符串
        const parsed = JSON.parse(value)
        return Array.isArray(parsed) ? parsed : [value]
      } catch {
        // 如果不是JSON，按逗号分割
        return value.split(',').map(item => item.trim()).filter(item => item)
      }
    }
    return Array.isArray(value) ? value : []
  })
  suppliers?: string[]

  @ApiProperty({ description: '布料分类数组', required: false })
  @IsOptional()
  @IsArray()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        // 尝试解析JSON字符串
        const parsed = JSON.parse(value)
        return Array.isArray(parsed) ? parsed : [value]
      } catch {
        // 如果不是JSON，按逗号分割
        return value.split(',').map(item => item.trim()).filter(item => item)
      }
    }
    return Array.isArray(value) ? value : []
  })
  classifications?: string[]

  @ApiProperty({ description: '款式数组', required: false })
  @IsOptional()
  @IsArray()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        // 尝试解析JSON字符串
        const parsed = JSON.parse(value)
        return Array.isArray(parsed) ? parsed : [value]
      } catch {
        // 如果不是JSON，按逗号分割
        return value.split(',').map(item => item.trim()).filter(item => item)
      }
    }
    return Array.isArray(value) ? value : []
  })
  styles?: string[]
}
