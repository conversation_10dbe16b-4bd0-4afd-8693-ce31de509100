import { Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { Clothing } from '../../models/clothing.model'
import { OemClothing } from '../../models/oemClothing.model'
import { FabricGroup } from '../../models/fabricGroup.model'
import { ComprehensiveSearchQueryDto } from './dto'

export interface ComprehensiveSearchOptions {
  sleeveTypes: string[]
  suppliers: string[]
  classifications: string[]
  styles: string[]
}

@Injectable()
export class ComprehensiveSearchService {
  constructor(
    @InjectModel('Clothing') private readonly clothingModel: Model<Clothing>,
    @InjectModel('OemClothing') private readonly oemClothingModel: Model<OemClothing>,
    @InjectModel('FabricGroup') private readonly fabricGroupModel: Model<FabricGroup>
  ) {}

  /**
   * 获取年份选项
   * @returns 年份选项数组
   */
  async getYears(): Promise<string[]> {
    try {
      // 从服装集合和OEM服装集合获取年份
      const [clothingYears, oemClothingYears] = await Promise.all([
        this.clothingModel.distinct('clothing_year').exec(),
        this.oemClothingModel.distinct('oem_clothing_year').exec(),
      ])

      // 合并并去重年份
      const allYears = [...new Set([...clothingYears, ...oemClothingYears])]
      
      // 过滤掉空值并排序
      const validYears = allYears
        .filter((year) => year && year.trim() !== '')
        .sort((a, b) => parseInt(b) - parseInt(a)) // 按年份倒序排列

      console.log('获取到的年份选项:', validYears)
      return validYears
    } catch (error) {
      console.error('获取年份选项失败:', error)
      throw new Error('获取年份选项失败: ' + (error || '未知错误'))
    }
  }

  /**
   * 根据查询条件获取筛选选项（支持级联查询）
   * @param query 查询参数
   * @returns 筛选选项
   */
  async getOptions(query: ComprehensiveSearchQueryDto): Promise<ComprehensiveSearchOptions> {
    try {
      const { factoryType, year, sleeveTypes, suppliers, classifications } = query

      if (!factoryType || !year) {
        throw new Error('工厂类型和年份参数不能为空')
      }

      console.log(`获取筛选选项 - 参数:`, JSON.stringify(query))

      if (factoryType === 'own') {
        // 本厂服装：从clothing集合和fabric_group集合获取数据
        return await this.getOwnClothingCascadeOptions(year, sleeveTypes, suppliers, classifications)
      } else if (factoryType === 'oem') {
        // 外加工服装：从oem_clothing集合获取数据
        return await this.getOemClothingCascadeOptions(year, suppliers, classifications)
      } else {
        throw new Error('无效的工厂类型参数')
      }
    } catch (error) {
      console.error('获取筛选选项失败:', error)
      throw new Error('获取筛选选项失败: ' + (error || '未知错误'))
    }
  }

  /**
   * 获取本厂服装的级联筛选选项
   * @param year 年份
   * @param sleeveTypes 袖型数组（可选）
   * @param suppliers 供应商数组（可选）
   * @param classifications 布料分类数组（可选）
   * @returns 本厂服装筛选选项
   */
  private async getOwnClothingCascadeOptions(
    year: string,
    sleeveTypes?: string[],
    suppliers?: string[],
    classifications?: string[]
  ): Promise<ComprehensiveSearchOptions> {
    try {
      console.log('getOwnClothingCascadeOptions 方法接收到的参数：', { year, sleeveTypes, suppliers, classifications })

      // 构建基础查询条件
      const baseMatchConditions: any = {
        clothing_year: year
      }

      // 如果有袖型条件，添加到查询中
      if (sleeveTypes && sleeveTypes.length > 0) {
        console.log('原始袖型数据:', sleeveTypes)
        console.log('袖型数据类型:', typeof sleeveTypes, '是否为数组:', Array.isArray(sleeveTypes))

        // 将前端的 'long', 'short' 转换为数据库的 '长袖', '短袖'
        const dbSleeveTypes = sleeveTypes.map(type => {
          console.log('转换袖型:', type, '类型:', typeof type)
          return type === 'long' ? '长袖' : '短袖'
        })
        console.log('转换后的袖型:', dbSleeveTypes)
        baseMatchConditions.long_or_short_sleeve = { $in: dbSleeveTypes }
      }

      console.log('本厂服装基础查询条件:', baseMatchConditions)

      // 如果没有供应商和布料分类条件，返回所有可用选项
      if ((!suppliers || suppliers.length === 0) && (!classifications || classifications.length === 0)) {
        return await this.getOwnClothingAllOptions(baseMatchConditions)
      }

      // 如果有供应商条件但没有布料分类条件，返回该供应商下的布料分类和款式
      if (suppliers && suppliers.length > 0 && (!classifications || classifications.length === 0)) {
        return await this.getOwnClothingOptionsBySuppliers(baseMatchConditions, suppliers)
      }

      // 如果有供应商和布料分类条件，返回款式选项
      if (suppliers && suppliers.length > 0 && classifications && classifications.length > 0) {
        return await this.getOwnClothingOptionsBySupplierAndClassification(baseMatchConditions, suppliers, classifications)
      }

      // 默认返回所有选项
      return await this.getOwnClothingAllOptions(baseMatchConditions)
    } catch (error) {
      console.error('获取本厂服装级联筛选选项失败:', error)
      throw error
    }
  }

  /**
   * 获取本厂服装的所有选项（无级联筛选）
   */
  private async getOwnClothingAllOptions(matchConditions: any): Promise<ComprehensiveSearchOptions> {
    try {
      // 从服装集合获取袖型和款式
      const [sleeveTypes, styles] = await Promise.all([
        this.clothingModel.distinct('long_or_short_sleeve', matchConditions).exec(),
        this.clothingModel.distinct('style', matchConditions).exec(),
      ])

      // 获取该年份的所有fabric_group_id
      const fabricGroupConditions = {
        ...matchConditions,
        fabric_group_id: { $exists: true, $nin: [null, ''] }
      }
      const fabricGroupIds = await this.clothingModel
        .distinct('fabric_group_id', fabricGroupConditions)
        .exec()

      // 从fabric_group集合获取供应商和布料分类
      let suppliers: string[] = []
      let classifications: string[] = []

      if (fabricGroupIds.length > 0) {
        const [suppliersResult, classificationsResult] = await Promise.all([
          this.fabricGroupModel.distinct('supplier', {
            fabric_group_id: { $in: fabricGroupIds }
          }).exec(),
          this.fabricGroupModel.distinct('group_classification', {
            fabric_group_id: { $in: fabricGroupIds }
          }).exec(),
        ])

        suppliers = suppliersResult
        // 展开布料分类数组
        classifications = [...new Set(classificationsResult.flat())]
      }

      // 过滤空值
      const filterEmpty = (arr: any[]): string[] => 
        arr.filter(item => item && item.trim() !== '').map(item => String(item))

      const result = {
        sleeveTypes: filterEmpty(sleeveTypes),
        suppliers: filterEmpty(suppliers),
        classifications: filterEmpty(classifications),
        styles: filterEmpty(styles),
      }

      console.log('本厂服装筛选选项:', result)
      return result
    } catch (error) {
      console.error('获取本厂服装筛选选项失败:', error)
      throw error
    }
  }

  /**
   * 根据供应商获取本厂服装的筛选选项
   */
  private async getOwnClothingOptionsBySuppliers(
    matchConditions: any,
    suppliers: string[]
  ): Promise<ComprehensiveSearchOptions> {
    try {
      // 先获取符合供应商条件的fabric_group_id
      const supplierFabricGroupIds = await this.fabricGroupModel
        .distinct('fabric_group_id', {
          supplier: { $in: suppliers }
        })
        .exec()

      if (supplierFabricGroupIds.length === 0) {
        return {
          sleeveTypes: [],
          suppliers: [],
          classifications: [],
          styles: []
        }
      }

      // 构建包含fabric_group_id条件的查询（同时满足袖型和供应商条件）
      const extendedMatchConditions = {
        ...matchConditions,
        fabric_group_id: { $in: supplierFabricGroupIds }
      }

      console.log('根据供应商筛选的查询条件:', extendedMatchConditions)

      // 获取同时满足袖型和供应商条件的服装的fabric_group_id
      const validFabricGroupIds = await this.clothingModel
        .distinct('fabric_group_id', extendedMatchConditions)
        .exec()

      console.log('同时满足袖型和供应商条件的fabric_group_id:', validFabricGroupIds)

      if (validFabricGroupIds.length === 0) {
        return {
          sleeveTypes: [],
          suppliers: [],
          classifications: [],
          styles: []
        }
      }

      // 获取袖型和款式（基于有效的fabric_group_id）
      const finalMatchConditions = {
        ...matchConditions,
        fabric_group_id: { $in: validFabricGroupIds }
      }

      const [sleeveTypes, styles] = await Promise.all([
        this.clothingModel.distinct('long_or_short_sleeve', finalMatchConditions).exec(),
        this.clothingModel.distinct('style', finalMatchConditions).exec(),
      ])

      // 获取布料分类（基于有效的fabric_group_id）
      const classificationsResult = await this.fabricGroupModel.distinct('group_classification', {
        fabric_group_id: { $in: validFabricGroupIds }
      }).exec()

      // 过滤空值
      const filterEmpty = (arr: any[]): string[] =>
        arr.filter(item => item && item.trim() !== '').map(item => String(item))

      const result = {
        sleeveTypes: filterEmpty(sleeveTypes),
        suppliers: suppliers, // 返回传入的供应商
        classifications: filterEmpty([...new Set(classificationsResult.flat())]),
        styles: filterEmpty(styles),
      }

      console.log('根据供应商筛选的本厂服装选项:', result)
      return result
    } catch (error) {
      console.error('根据供应商获取本厂服装筛选选项失败:', error)
      throw error
    }
  }

  /**
   * 根据供应商和布料分类获取本厂服装的筛选选项
   */
  private async getOwnClothingOptionsBySupplierAndClassification(
    matchConditions: any,
    suppliers: string[],
    classifications: string[]
  ): Promise<ComprehensiveSearchOptions> {
    try {
      // 先获取符合供应商和布料分类条件的fabric_group_id
      const fabricGroupIds = await this.fabricGroupModel
        .distinct('fabric_group_id', {
          supplier: { $in: suppliers },
          group_classification: { $in: classifications }
        })
        .exec()

      if (fabricGroupIds.length === 0) {
        return {
          sleeveTypes: [],
          suppliers: [],
          classifications: [],
          styles: []
        }
      }

      // 构建包含fabric_group_id条件的查询
      const extendedMatchConditions = {
        ...matchConditions,
        fabric_group_id: { $in: fabricGroupIds }
      }

      console.log('根据供应商和布料分类筛选的查询条件:', extendedMatchConditions)
      console.log('符合供应商和布料分类条件的fabric_group_id:', fabricGroupIds)

      // 获取袖型和款式
      const [sleeveTypes, styles] = await Promise.all([
        this.clothingModel.distinct('long_or_short_sleeve', extendedMatchConditions).exec(),
        this.clothingModel.distinct('style', extendedMatchConditions).exec(),
      ])

      // 过滤空值
      const filterEmpty = (arr: any[]): string[] =>
        arr.filter(item => item && item.trim() !== '').map(item => String(item))

      const result = {
        sleeveTypes: filterEmpty(sleeveTypes),
        suppliers: suppliers, // 返回传入的供应商
        classifications: classifications, // 返回传入的布料分类
        styles: filterEmpty(styles),
      }

      console.log('根据供应商和布料分类筛选的本厂服装选项:', result)
      return result
    } catch (error) {
      console.error('根据供应商和布料分类获取本厂服装筛选选项失败:', error)
      throw error
    }
  }

  /**
   * 获取外加工服装的级联筛选选项
   * @param year 年份
   * @param suppliers 供应商数组（可选）
   * @param classifications 布料分类数组（可选）
   * @returns 外加工服装筛选选项
   */
  private async getOemClothingCascadeOptions(
    year: string,
    suppliers?: string[],
    classifications?: string[]
  ): Promise<ComprehensiveSearchOptions> {
    try {
      const yearWithSuffix = year

      // 构建基础查询条件
      const baseMatchConditions: any = {
        oem_clothing_year: yearWithSuffix
      }

      console.log('外加工服装基础查询条件:', baseMatchConditions)

      // 如果没有供应商和布料分类条件，返回所有可用选项
      if ((!suppliers || suppliers.length === 0) && (!classifications || classifications.length === 0)) {
        return await this.getOemClothingAllOptions(baseMatchConditions)
      }

      // 如果有供应商条件，添加到查询中
      if (suppliers && suppliers.length > 0) {
        baseMatchConditions.oem_supplier = { $in: suppliers }
      }

      // 如果有布料分类条件，添加到查询中
      if (classifications && classifications.length > 0) {
        baseMatchConditions.classification = { $in: classifications }
      }

      // 根据条件获取选项
      return await this.getOemClothingFilteredOptions(baseMatchConditions, suppliers, classifications)
    } catch (error) {
      console.error('获取外加工服装级联筛选选项失败:', error)
      throw error
    }
  }

  /**
   * 获取外加工服装的所有选项（无级联筛选）
   */
  private async getOemClothingAllOptions(matchConditions: any): Promise<ComprehensiveSearchOptions> {
    try {
      // 从OEM服装集合获取所有选项
      const [suppliers, classifications, styles] = await Promise.all([
        this.oemClothingModel.distinct('oem_supplier', matchConditions).exec(),
        this.oemClothingModel.distinct('classification', matchConditions).exec(),
        this.oemClothingModel.distinct('style', matchConditions).exec(),
      ])

      // 过滤空值
      const filterEmpty = (arr: any[]): string[] =>
        arr.filter(item => item && item.trim() !== '').map(item => String(item))

      // OEM服装没有袖型字段，返回空数组
      const result = {
        sleeveTypes: [], // 外加工不返回袖型选项
        suppliers: filterEmpty(suppliers),
        classifications: filterEmpty(classifications),
        styles: filterEmpty(styles),
      }

      console.log('外加工服装筛选选项:', result)
      return result
    } catch (error) {
      console.error('获取外加工服装筛选选项失败:', error)
      throw error
    }
  }

  /**
   * 根据条件获取外加工服装的筛选选项
   */
  private async getOemClothingFilteredOptions(
    matchConditions: any,
    suppliers?: string[],
    classifications?: string[]
  ): Promise<ComprehensiveSearchOptions> {
    try {
      // 从OEM服装集合获取选项
      const [suppliersResult, classificationsResult, styles] = await Promise.all([
        this.oemClothingModel.distinct('oem_supplier', matchConditions).exec(),
        this.oemClothingModel.distinct('classification', matchConditions).exec(),
        this.oemClothingModel.distinct('style', matchConditions).exec(),
      ])

      // 过滤空值
      const filterEmpty = (arr: any[]): string[] =>
        arr.filter(item => item && item.trim() !== '').map(item => String(item))

      // 根据传入的参数决定返回哪些选项
      const result = {
        sleeveTypes: [], // 外加工不返回袖型选项
        suppliers: suppliers || filterEmpty(suppliersResult),
        classifications: classifications || filterEmpty(classificationsResult),
        styles: filterEmpty(styles),
      }

      console.log('根据条件筛选的外加工服装选项:', result)
      return result
    } catch (error) {
      console.error('根据条件获取外加工服装筛选选项失败:', error)
      throw error
    }
  }

  /**
   * 获取汇总查询结果
   * @param query 查询参数
   * @returns 服装汇总数据列表
   */
  async getSummaryResults(query: ComprehensiveSearchQueryDto): Promise<any[]> {
    try {
      const { factoryType, year, sleeveTypes, suppliers, classifications, styles } = query

      if (!factoryType || !year) {
        throw new Error('工厂类型和年份参数不能为空')
      }

      console.log(`获取汇总查询结果 - 参数:`, JSON.stringify(query))

      if (factoryType === 'own') {
        // 本厂服装汇总查询
        return await this.getOwnClothingSummaryResults(year, sleeveTypes, suppliers, classifications, styles)
      } else if (factoryType === 'oem') {
        // 外加工服装汇总查询
        return await this.getOemClothingSummaryResults(year, suppliers, classifications, styles)
      } else {
        throw new Error('无效的工厂类型参数')
      }
    } catch (error) {
      console.error('获取汇总查询结果失败:', error)
      throw error
    }
  }

  /**
   * 获取本厂服装汇总查询结果
   */
  private async getOwnClothingSummaryResults(
    year: string,
    sleeveTypes?: string[],
    suppliers?: string[],
    classifications?: string[],
    styles?: string[]
  ): Promise<any[]> {
    try {
      // 构建基础查询条件
      const baseMatchConditions: any = {
        clothing_year: year,
        state: { $ne: '1' } // 排除已删除的记录
      }

      // 如果有袖型条件，添加到查询中
      if (sleeveTypes && sleeveTypes.length > 0) {
        const dbSleeveTypes = sleeveTypes.map(type => {
          return type === 'long' ? '长袖' : '短袖'
        })
        baseMatchConditions.long_or_short_sleeve = { $in: dbSleeveTypes }
      }

      // 如果有款式条件，添加到查询中
      if (styles && styles.length > 0) {
        baseMatchConditions.style = { $in: styles }
      }

      console.log('本厂服装汇总查询基础条件:', baseMatchConditions)

      // 如果有供应商或布料分类条件，需要通过fabric_group关联查询
      if ((suppliers && suppliers.length > 0) || (classifications && classifications.length > 0)) {
        const fabricGroupConditions: any = {}

        if (suppliers && suppliers.length > 0) {
          fabricGroupConditions.supplier = { $in: suppliers }
        }

        if (classifications && classifications.length > 0) {
          fabricGroupConditions.group_classification = { $in: classifications }
        }

        // 获取符合条件的fabric_group_id
        const validFabricGroupIds = await this.fabricGroupModel
          .distinct('fabric_group_id', fabricGroupConditions)
          .exec()

        if (validFabricGroupIds.length === 0) {
          return []
        }

        baseMatchConditions.fabric_group_id = { $in: validFabricGroupIds }
      }

      console.log('本厂服装汇总查询最终条件:', baseMatchConditions)

      // 执行聚合查询，关联fabric_group获取供应商和布料分类信息
      const results = await this.clothingModel.aggregate([
        { $match: baseMatchConditions },
        {
          $lookup: {
            from: 'fabric_group',
            localField: 'fabric_group_id',
            foreignField: 'fabric_group_id',
            as: 'fabricGroup'
          }
        },
        {
          $addFields: {
            supplier: { $arrayElemAt: ['$fabricGroup.supplier', 0] },
            group_classification: { $arrayElemAt: ['$fabricGroup.group_classification', 0] }
          }
        },
        {
          $addFields: {
            cutting_quantity: '$clipping_pcs', // 本厂使用裁剪数
            inbound_quantity: 0, // 本厂没有入库数
            shipped_quantity: '$shipments',
            arrived_quantity: '$arrival_quantity',
            inventory_quantity: '$stock_quantity'
          }
        },
        {
          $project: {
            clothing_id: 1,
            clothing_name: 1,
            long_or_short_sleeve: 1,
            size: 1,
            style: 1,
            supplier: 1,
            group_classification: 1,
            cutting_quantity: 1,
            inbound_quantity: 1,
            shipped_quantity: 1,
            arrived_quantity: 1,
            inventory_quantity: 1
          }
        },
        { $sort: { clothing_name: 1 } },
        { $limit: 1000 }
      ]).exec()

      console.log(`本厂服装汇总查询结果数量: ${results.length}`)
      return results
    } catch (error) {
      console.error('获取本厂服装汇总查询结果失败:', error)
      throw error
    }
  }

  /**
   * 获取外加工服装汇总查询结果
   */
  private async getOemClothingSummaryResults(
    year: string,
    suppliers?: string[],
    classifications?: string[],
    styles?: string[]
  ): Promise<any[]> {
    try {
      // 构建查询条件
      const matchConditions: any = {
        oem_clothing_year: year,
        state: { $ne: '1' } // 排除已删除的记录
      }

      if (suppliers && suppliers.length > 0) {
        matchConditions.oem_supplier = { $in: suppliers }
      }

      if (classifications && classifications.length > 0) {
        matchConditions.classification = { $in: classifications }
      }

      if (styles && styles.length > 0) {
        matchConditions.style = { $in: styles }
      }

      console.log('外加工服装汇总查询条件:', matchConditions)

      // 执行查询
      const results = await this.oemClothingModel.aggregate([
        { $match: matchConditions },
        {
          $addFields: {
            clothing_name: '$oem_clothing_name',
            supplier: '$oem_supplier',
            cutting_quantity: 0, // 外加工没有裁剪数
            inbound_quantity: '$in_pcs', // 外加工使用入库数
            shipped_quantity: '$shipments',
            arrived_quantity: '$arrival_quantity',
            inventory_quantity: '$stock_quantity'
          }
        },
        {
          $project: {
            oem_clothing_id: 1,
            clothing_name: 1,
            size: 1,
            style: 1,
            supplier: 1,
            classification: 1,
            price: 1, // 添加单价字段
            cutting_quantity: 1,
            inbound_quantity: 1,
            shipped_quantity: 1,
            arrived_quantity: 1,
            inventory_quantity: 1
          }
        },
        { $sort: { clothing_name: 1 } },
        { $limit: 1000 }
      ]).exec()

      console.log(`外加工服装汇总查询结果数量: ${results.length}`)
      return results
    } catch (error) {
      console.error('获取外加工服装汇总查询结果失败:', error)
      throw error
    }
  }
}
