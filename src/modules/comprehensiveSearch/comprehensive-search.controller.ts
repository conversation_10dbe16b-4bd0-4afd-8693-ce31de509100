import {
  Controller,
  Get,
  Query,
  HttpStatus,
  HttpException,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common'
import { ComprehensiveSearchService } from './comprehensive-search.service'
import { ComprehensiveSearchQueryDto } from './dto'

@Controller('comprehensive-search')
export class ComprehensiveSearchController {
  constructor(private readonly comprehensiveSearchService: ComprehensiveSearchService) {}

  /**
   * 获取服装年份选项
   * @returns 年份选项列表
   */
  @Get('years')
  async getYears() {
    try {
      const years = await this.comprehensiveSearchService.getYears()
      return {
        code: HttpStatus.OK,
        data: { years },
        message: '获取年份选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取年份选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 根据工厂类型和年份获取所有筛选选项
   * @param query 查询参数
   * @returns 所有筛选选项
   */
  @Get('options')
  @UsePipes(new ValidationPipe({ transform: true }))
  async getOptions(@Query() query: ComprehensiveSearchQueryDto) {
    try {
      console.log('综合搜索获取选项 - 原始query对象:', query)
      console.log('综合搜索获取选项 - JSON字符串:', JSON.stringify(query))
      console.log('综合搜索获取选项 - sleeveTypes详情:', {
        value: query.sleeveTypes,
        type: typeof query.sleeveTypes,
        isArray: Array.isArray(query.sleeveTypes),
        length: query.sleeveTypes?.length
      })

      const options = await this.comprehensiveSearchService.getOptions(query)
      
      return {
        code: HttpStatus.OK,
        data: options,
        message: '获取筛选选项成功',
      }
    } catch (error: any) {
      console.error('获取筛选选项失败:', error.message)
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取筛选选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 兼容旧的API接口 - 获取供应商选项
   */
  @Get('suppliers')
  @UsePipes(new ValidationPipe({ transform: true }))
  async getSuppliers(@Query() query: ComprehensiveSearchQueryDto) {
    try {
      const options = await this.comprehensiveSearchService.getOptions(query)
      return {
        code: HttpStatus.OK,
        data: { suppliers: options.suppliers },
        message: '获取供应商选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取供应商选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 兼容旧的API接口 - 获取布料分类选项
   */
  @Get('classifications')
  @UsePipes(new ValidationPipe({ transform: true }))
  async getClassifications(@Query() query: ComprehensiveSearchQueryDto) {
    try {
      const options = await this.comprehensiveSearchService.getOptions(query)
      return {
        code: HttpStatus.OK,
        data: { classifications: options.classifications },
        message: '获取布料分类选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取布料分类选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 兼容旧的API接口 - 获取款式选项
   */
  @Get('styles')
  @UsePipes(new ValidationPipe({ transform: true }))
  async getStyles(@Query() query: ComprehensiveSearchQueryDto) {
    try {
      const options = await this.comprehensiveSearchService.getOptions(query)
      return {
        code: HttpStatus.OK,
        data: { styles: options.styles },
        message: '获取款式选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取款式选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 获取汇总查询结果
   * @param query 查询参数
   * @returns 汇总查询结果列表
   */
  @Get('summary-results')
  @UsePipes(new ValidationPipe({ transform: true }))
  async getSummaryResults(@Query() query: ComprehensiveSearchQueryDto) {
    try {
      console.log('获取汇总查询结果 - 参数:', JSON.stringify(query))

      const results = await this.comprehensiveSearchService.getSummaryResults(query)

      return {
        code: HttpStatus.OK,
        data: results,
        message: '获取汇总查询结果成功',
      }
    } catch (error: any) {
      console.error('获取汇总查询结果失败:', error.message)
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取汇总查询结果失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }
}
