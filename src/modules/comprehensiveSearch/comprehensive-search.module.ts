import { Module } from '@nestjs/common'
import { ComprehensiveSearchController } from './comprehensive-search.controller'
import { ComprehensiveSearchService } from './comprehensive-search.service'
import { ModelsModule } from '../../models/models.module'

@Module({
  imports: [ModelsModule],
  controllers: [ComprehensiveSearchController],
  providers: [ComprehensiveSearchService],
  exports: [ComprehensiveSearchService],
})
export class ComprehensiveSearchModule {}
