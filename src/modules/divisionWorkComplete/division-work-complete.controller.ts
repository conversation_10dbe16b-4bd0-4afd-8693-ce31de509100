import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpException,
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiParam, ApiQuery, ApiResponse } from '@nestjs/swagger'
import { DivisionWorkCompleteService } from './division-work-complete.service'
import {
  CreateDivisionWorkCompleteDto,
  UpdateDivisionWorkCompleteDto,
  QueryDivisionWorkCompleteDto,
  BatchUpdateDivisionWorkCompleteDto,
} from './dto'
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard'

@ApiTags('分工完成管理')
@Controller('/division-work-complete')
@UseGuards(JwtAuthGuard)
export class DivisionWorkCompleteController {
  constructor(private readonly divisionWorkCompleteService: DivisionWorkCompleteService) {}

  @Post()
  @ApiOperation({ summary: '创建分工完成' })
  async create(@Body() createDivisionWorkCompleteDto: CreateDivisionWorkCompleteDto) {
    try {
      const result = await this.divisionWorkCompleteService.create(createDivisionWorkCompleteDto)
      return {
        code: HttpStatus.CREATED,
        data: result,
        message: '创建分工完成成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '创建分工完成失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Get()
  @ApiOperation({ summary: '获取分工完成列表' })
  async findAll(@Query() queryDivisionWorkCompleteDto: QueryDivisionWorkCompleteDto) {
    try {
      const result = await this.divisionWorkCompleteService.findAll(queryDivisionWorkCompleteDto)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '获取分工完成列表成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取分工完成列表失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Get(':id')
  @ApiOperation({ summary: '获取分工完成详情' })
  @ApiParam({ name: 'id', description: '分工完成ID' })
  async findOne(@Param('id') id: string) {
    try {
      const result = await this.divisionWorkCompleteService.findOne(id)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '获取分工完成详情成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取分工完成详情失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Get('division-work/:divisionWorkId')
  @ApiOperation({ summary: '根据分工ID获取分工完成列表' })
  @ApiParam({ name: 'divisionWorkId', description: '分工ID' })
  async findByDivisionWorkId(@Param('divisionWorkId') divisionWorkId: string) {
    try {
      const result = await this.divisionWorkCompleteService.findByDivisionWorkId(divisionWorkId)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '获取分工完成列表成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取分工完成列表失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新分工完成' })
  @ApiParam({ name: 'id', description: '分工完成ID' })
  async update(
    @Param('id') id: string,
    @Body() updateDivisionWorkCompleteDto: UpdateDivisionWorkCompleteDto
  ) {
    try {
      const result = await this.divisionWorkCompleteService.update(id, updateDivisionWorkCompleteDto)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '更新分工完成成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '更新分工完成失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除分工完成' })
  @ApiParam({ name: 'id', description: '分工完成ID' })
  async remove(@Param('id') id: string) {
    try {
      const result = await this.divisionWorkCompleteService.remove(id)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '删除分工完成成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '删除分工完成失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Post('batch-update')
  @ApiOperation({ summary: '批量更新分工完成' })
  @ApiResponse({ status: 200, description: '批量更新成功' })
  async batchUpdate(@Body() batchUpdateDto: BatchUpdateDivisionWorkCompleteDto) {
    try {
      const result = await this.divisionWorkCompleteService.batchUpdate(batchUpdateDto.assigns)
      return {
        data: {
          code: HttpStatus.OK,
          data: result,
          message: '批量更新分工完成成功',
        },
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '批量更新分工完成失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Post('batch-create')
  @ApiOperation({ summary: '批量创建分工完成' })
  @ApiResponse({ status: 200, description: '批量创建成功' })
  async batchCreate(@Body() batchCreateDto: BatchUpdateDivisionWorkCompleteDto) {
    try {
      const result = await this.divisionWorkCompleteService.batchCreate(batchCreateDto.assigns)
      return {
        data: {
          code: HttpStatus.OK,
          data: result,
          message: '批量创建分工完成成功',
        },
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '批量创建分工完成失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }
}
