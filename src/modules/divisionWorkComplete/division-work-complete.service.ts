import { Injectable, NotFoundException, InternalServerErrorException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, PipelineStage } from 'mongoose'
import { DivisionWorkComplete } from '../../models/divisionWorkComplete.model'
import { DivisionWork } from '../../models/divisionWork.model'
import { Staff } from '../../models/staff.model'
import { Work } from '../../models/work.model'
import {
  CreateDivisionWorkCompleteDto,
  UpdateDivisionWorkCompleteDto,
  QueryDivisionWorkCompleteDto,
} from './dto'

@Injectable()
export class DivisionWorkCompleteService {
  constructor(
    @InjectModel('DivisionWorkComplete')
    private readonly divisionWorkCompleteModel: Model<DivisionWorkComplete>,
    @InjectModel('DivisionWork')
    private readonly divisionWorkModel: Model<DivisionWork>,
    @InjectModel('Staff')
    private readonly staffModel: Model<Staff>,
    @InjectModel('Work')
    private readonly workModel: Model<Work>
  ) {}

  // 创建分工完成
  async create(
    createDivisionWorkCompleteDto: CreateDivisionWorkCompleteDto
  ): Promise<DivisionWorkComplete> {
    // 验证分工ID是否存在
    const divisionWork = await this.divisionWorkModel
      .findOne({ division_work_id: createDivisionWorkCompleteDto.division_work_id })
      .exec()
    if (!divisionWork) {
      throw new NotFoundException(`分工ID ${createDivisionWorkCompleteDto.division_work_id} 不存在`)
    }

    // 验证员工ID是否存在
    const staff = await this.staffModel
      .findOne({ staff_id: createDivisionWorkCompleteDto.staff_id })
      .exec()
    if (!staff) {
      throw new NotFoundException(`员工ID ${createDivisionWorkCompleteDto.staff_id} 不存在`)
    }

    // 创建并保存分工完成
    const divisionWorkComplete = new this.divisionWorkCompleteModel(createDivisionWorkCompleteDto)
    return divisionWorkComplete.save()
  }

  // 获取分工完成列表
  async findAll(queryDivisionWorkCompleteDto: QueryDivisionWorkCompleteDto): Promise<{
    total: number
    page: number
    limit: number
    data: any[]
  }> {
    const {
      division_work_id,
      division_work_year,
      staff_id,
      staff_name,
      page = 1,
      limit = 10,
    } = queryDivisionWorkCompleteDto

    // 构建查询条件
    const matchStage: any = {}

    // 添加分工ID条件
    if (division_work_id) {
      matchStage.division_work_id = division_work_id
    }

    // 添加所属年份条件
    if (division_work_year) {
      matchStage.division_work_year = division_work_year
    }

    // 添加员工ID条件
    if (staff_id) {
      matchStage.staff_id = { $regex: staff_id, $options: 'i' }
    }

    // 添加员工姓名条件
    if (staff_name) {
      matchStage.staff_name = { $regex: staff_name, $options: 'i' }
    }

    // 计算分页
    const skip = (page - 1) * limit

    // 构建聚合管道
    const pipeline: PipelineStage[] = [
      { $match: matchStage },
      { $skip: skip },
      { $limit: Number(limit) },
      // 关联分工信息
      {
        $lookup: {
          from: 'division_work',
          let: { division_work_id: '$division_work_id' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$division_work_id', '$$division_work_id'] },
              },
            },
            {
              $project: {
                _id: 1,
                division_work_id: 1,
                clothing_id: 1,
                group_name: 1,
                pcs: 1,
                is_complete: 1,
              },
            },
          ],
          as: 'division_work_info',
        },
      },
      { $unwind: { path: '$division_work_info', preserveNullAndEmptyArrays: true } },
      // 添加 division_work 集合中的字段
      {
        $project: {
          _id: 1,
          division_work_id: 1,
          division_work_year: 1,
          staff_id: 1,
          staff_name: 1,
          totalPrice: 1,
          work_detail: 1,
          createTime: 1,
          lastChangeTime: 1,
          clothing_id: '$division_work_info.clothing_id',
          group_name: '$division_work_info.group_name',
          pcs: '$division_work_info.pcs',
          is_complete: '$division_work_info.is_complete',
        },
      },
    ]

    // 执行聚合查询
    const data = await this.divisionWorkCompleteModel.aggregate(pipeline).exec()

    // 计算总数
    const countPipeline: PipelineStage[] = [{ $match: matchStage }, { $count: 'total' }]
    const totalResult = await this.divisionWorkCompleteModel.aggregate(countPipeline).exec()
    const total = totalResult.length > 0 ? totalResult[0].total : 0

    return {
      total,
      page: Number(page),
      limit: Number(limit),
      data,
    }
  }

  // 获取分工完成详情
  async findOne(id: string): Promise<DivisionWorkComplete> {
    const divisionWorkComplete = await this.divisionWorkCompleteModel.findById(id).exec()
    if (!divisionWorkComplete) {
      throw new NotFoundException(`分工完成ID ${id} 不存在`)
    }
    return divisionWorkComplete
  }

  // 根据分工ID获取分工完成列表
  async findByDivisionWorkId(divisionWorkId: string): Promise<DivisionWorkComplete[]> {
    return this.divisionWorkCompleteModel.find({ division_work_id: divisionWorkId }).exec()
  }

  // 更新分工完成
  async update(
    id: string,
    updateDivisionWorkCompleteDto: UpdateDivisionWorkCompleteDto
  ): Promise<DivisionWorkComplete> {
    // 如果更新了分工ID，验证其是否存在
    if (updateDivisionWorkCompleteDto.division_work_id) {
      const divisionWork = await this.divisionWorkModel
        .findOne({ division_work_id: updateDivisionWorkCompleteDto.division_work_id })
        .exec()
      if (!divisionWork) {
        throw new NotFoundException(
          `分工ID ${updateDivisionWorkCompleteDto.division_work_id} 不存在`
        )
      }
    }

    // 如果更新了员工ID，验证其是否存在
    if (updateDivisionWorkCompleteDto.staff_id) {
      const staff = await this.staffModel
        .findOne({ staff_id: updateDivisionWorkCompleteDto.staff_id })
        .exec()
      if (!staff) {
        throw new NotFoundException(`员工ID ${updateDivisionWorkCompleteDto.staff_id} 不存在`)
      }
    }

    const updatedDivisionWorkComplete = await this.divisionWorkCompleteModel
      .findByIdAndUpdate(
        id,
        { ...updateDivisionWorkCompleteDto, lastChangeTime: new Date() },
        { new: true }
      )
      .exec()
    if (!updatedDivisionWorkComplete) {
      throw new NotFoundException(`分工完成ID ${id} 不存在`)
    }
    return updatedDivisionWorkComplete
  }

  // 删除分工完成
  async remove(id: string): Promise<{ success: boolean; message: string }> {
    const result = await this.divisionWorkCompleteModel.findByIdAndDelete(id).exec()
    if (!result) {
      throw new NotFoundException(`分工完成ID ${id} 不存在`)
    }
    return { success: true, message: '删除成功' }
  }

  // 批量更新分工完成
  async batchUpdate(assigns: CreateDivisionWorkCompleteDto[]) {
    try {
      const operations = assigns.map((assign) => {
        return {
          updateOne: {
            filter: {
              division_work_id: assign.division_work_id,
              staff_id: assign.staff_id,
            },
            update: {
              $set: {
                division_work_year: assign.division_work_year,
                staff_name: assign.staff_name,
                totalPrice: assign.totalPrice,
                work_detail: assign.work_detail,
                lastChangeTime: new Date(),
              },
            },
            upsert: true, // 如果不存在则创建
          },
        }
      })

      const result = await this.divisionWorkCompleteModel.bulkWrite(operations)

      return {
        matched: result.matchedCount,
        modified: result.modifiedCount,
        upserted: result.upsertedCount,
      }
    } catch (error: any) {
      throw new InternalServerErrorException(`批量更新分工完成失败: ${error.message}`)
    }
  }

  // 批量创建分工完成
  async batchCreate(assigns: CreateDivisionWorkCompleteDto[]) {
    try {
      const result = await this.divisionWorkCompleteModel.insertMany(assigns)
      return result
    } catch (error: any) {
      throw new InternalServerErrorException(`批量创建分工完成失败: ${error.message}`)
    }
  }
}
