import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsArray, IsOptional, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { WorkDetailDto } from './create-division-work-complete.dto'

export class UpdateDivisionWorkCompleteDto {
  @ApiProperty({ description: '分工ID' })
  @IsString()
  @IsOptional()
  division_work_id?: string

  @ApiProperty({ description: '分工年份' })
  @IsString()
  @IsOptional()
  division_work_year?: string

  @ApiProperty({ description: '员工ID' })
  @IsString()
  @IsOptional()
  staff_id?: string

  @ApiProperty({ description: '员工姓名' })
  @IsString()
  @IsOptional()
  staff_name?: string

  @ApiProperty({ description: '总价格' })
  @IsString()
  @IsOptional()
  totalPrice?: string

  @ApiProperty({ description: '工序明细', type: [WorkDetailDto] })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => WorkDetailDto)
  work_detail?: WorkDetailDto[]
}
