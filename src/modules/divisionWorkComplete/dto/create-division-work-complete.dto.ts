import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsArray, IsOptional, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'

export class WorkDetailDto {
  @ApiProperty({ description: '工序ID' })
  @IsString()
  work_id: string

  @ApiProperty({ description: '工序名称' })
  @IsString()
  @IsOptional()
  work_name?: string

  @ApiProperty({ description: '数量' })
  pcs: number
}

export class CreateDivisionWorkCompleteDto {
  @ApiProperty({ description: '分工ID' })
  @IsString()
  division_work_id: string

  @ApiProperty({ description: '分工年份' })
  @IsString()
  division_work_year: string

  @ApiProperty({ description: '员工ID' })
  @IsString()
  staff_id: string

  @ApiProperty({ description: '员工姓名' })
  @IsString()
  staff_name: string

  @ApiProperty({ description: '总价格' })
  @IsString()
  @IsOptional()
  totalPrice?: string

  @ApiProperty({ description: '工序明细', type: [WorkDetailDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkDetailDto)
  work_detail: WorkDetailDto[]
}
