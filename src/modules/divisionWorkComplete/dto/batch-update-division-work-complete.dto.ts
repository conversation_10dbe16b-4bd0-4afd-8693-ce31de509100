import { ApiProperty } from '@nestjs/swagger'
import { IsArray, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { CreateDivisionWorkCompleteDto } from './create-division-work-complete.dto'

export class BatchUpdateDivisionWorkCompleteDto {
  @ApiProperty({ description: '分工完成数组', type: [CreateDivisionWorkCompleteDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateDivisionWorkCompleteDto)
  assigns: CreateDivisionWorkCompleteDto[]
}
