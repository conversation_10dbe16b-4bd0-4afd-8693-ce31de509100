import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsOptional, IsNumber } from 'class-validator'
import { Type } from 'class-transformer'

export class QueryDivisionWorkCompleteDto {
  @ApiProperty({ description: '分工ID', required: false })
  @IsString()
  @IsOptional()
  division_work_id?: string

  @ApiProperty({ description: '分工年份', required: false })
  @IsString()
  @IsOptional()
  division_work_year?: string

  @ApiProperty({ description: '员工ID', required: false })
  @IsString()
  @IsOptional()
  staff_id?: string

  @ApiProperty({ description: '员工姓名', required: false })
  @IsString()
  @IsOptional()
  staff_name?: string

  @ApiProperty({ description: '页码', required: false, default: 1 })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  page?: number

  @ApiProperty({ description: '每页数量', required: false, default: 10 })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  limit?: number
}
