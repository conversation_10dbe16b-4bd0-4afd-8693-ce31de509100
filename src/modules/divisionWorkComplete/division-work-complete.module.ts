import { Module } from '@nestjs/common'
import { ModelsModule } from '../../models/models.module'
import { DivisionWorkCompleteController } from './division-work-complete.controller'
import { DivisionWorkCompleteService } from './division-work-complete.service'

@Module({
  imports: [ModelsModule],
  controllers: [DivisionWorkCompleteController],
  providers: [DivisionWorkCompleteService],
  exports: [DivisionWorkCompleteService],
})
export class DivisionWorkCompleteModule {}
