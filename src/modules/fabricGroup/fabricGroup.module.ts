import { Module } from '@nestjs/common'
import { ModelsModule } from '../../models/models.module'
import { FabricGroupController } from './fabricGroup.controller'
import { FabricGroupService } from './fabricGroup.service'

@Module({
  imports: [ModelsModule],
  controllers: [FabricGroupController],
  providers: [FabricGroupService],
  exports: [FabricGroupService],
})
export class FabricGroupModule {}
