import { ApiProperty } from '@nestjs/swagger'
import { Expose, Type } from 'class-transformer'
import { IsArray, IsString, ValidateNested } from 'class-validator'

/**
 * 服装颜色件数 DTO
 */
export class ColorPcsDto {
  @ApiProperty({ description: '布料ID' })
  @Expose()
  @IsString()
  fabric_id: string

  @ApiProperty({ description: '件数' })
  @Expose()
  @IsString()
  pcs: string
}

/**
 * 服装信息 DTO
 */
export class ClothingInfoDto {
  @ApiProperty({ description: '服装ID' })
  @Expose()
  @IsString()
  clothing_id: string

  @ApiProperty({ description: '服装名称' })
  @Expose()
  @IsString()
  clothing_name: string

  @ApiProperty({ description: '颜色件数', type: [ColorPcsDto] })
  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ColorPcsDto)
  colorPcs: ColorPcsDto[]
}

/**
 * 裁剪完成的布料组信息 DTO
 */
export class FinishedCuttingGroupDto {
  @ApiProperty({ description: '布料组ID' })
  @Expose()
  @IsString()
  fabric_group_id: string

  @ApiProperty({ description: '布料组年份' })
  @Expose()
  @IsString()
  fabric_group_year: string

  @ApiProperty({ description: '供应商' })
  @Expose()
  @IsString()
  supplier: string

  @ApiProperty({ description: '分类', type: [String] })
  @Expose()
  @IsArray()
  @IsString({ each: true })
  group_classification: string[]

  @ApiProperty({ description: '服装列表', type: [ClothingInfoDto] })
  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ClothingInfoDto)
  clothingList: ClothingInfoDto[]
}

/**
 * 裁剪完成的布料组列表响应 DTO
 */
export class FinishedCuttingGroupsResponseDto {
  @ApiProperty({ description: '布料组列表', type: [FinishedCuttingGroupDto] })
  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FinishedCuttingGroupDto)
  data: FinishedCuttingGroupDto[]
}
