import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsNotEmpty, IsOptional, IsArray } from 'class-validator'

export class CreateFabricGroupDto {
  @ApiProperty({ description: '布料组编码' })
  @IsString()
  @IsNotEmpty({ message: '布料组编码不能为空' })
  fabric_group_id: string

  @ApiProperty({ description: '布料组年份' })
  @IsString()
  @IsNotEmpty({ message: '布料组年份不能为空' })
  fabric_group_year: string

  @ApiProperty({ description: '供应商' })
  @IsString()
  @IsNotEmpty({ message: '供应商不能为空' })
  supplier: string

  @ApiProperty({ description: '分类', required: false, type: [String] })
  @IsArray()
  @IsOptional()
  group_classification?: string[]

  @ApiProperty({ description: '布料列表', required: false, type: [String] })
  @IsArray()
  @IsOptional()
  fabrics?: string[]

  @ApiProperty({ description: '结算状态', required: false })
  @IsString()
  @IsOptional()
  settlement_state?: string

  @ApiProperty({ description: '状态', required: false })
  @IsString()
  @IsOptional()
  state?: string

  @ApiProperty({ description: '图片URL', required: false })
  @IsString()
  @IsOptional()
  img_url?: string

  @ApiProperty({ description: '调度信息', required: false })
  @IsString()
  @IsOptional()
  scheduling?: string
}
