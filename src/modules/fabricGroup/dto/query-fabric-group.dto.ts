import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsOptional, IsArray, IsNumber } from 'class-validator'
import { Type, Transform } from 'class-transformer'

export class QueryFabricGroupDto {
  @ApiProperty({ description: '布料组年份', required: false })
  @IsString()
  @IsOptional()
  @Transform(({ value }: { value: any }) => (value === '' ? undefined : value))
  fabric_group_year?: string

  @ApiProperty({ description: '布料组年份列表', required: false, type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform(({ value }: { value: any }) => {
    if (typeof value === 'string') {
      return value.split(',').filter(Boolean)
    }
    return value
  })
  fabric_group_years?: string[]

  @ApiProperty({ description: '布料组编码', required: false })
  @IsString()
  @IsOptional()
  @Transform(({ value }: { value: any }) => (value === '' ? undefined : value))
  fabric_group_id?: string

  @ApiProperty({ description: '供应商', required: false })
  @IsString()
  @IsOptional()
  @Transform(({ value }: { value: any }) => (value === '' ? undefined : value))
  supplier?: string

  @ApiProperty({ description: '供应商列表', required: false, type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform(({ value }: { value: any }) => {
    if (typeof value === 'string') {
      return value.split(',').filter(Boolean)
    }
    return value
  })
  suppliers?: string[]

  @ApiProperty({ description: '分类', required: false })
  @IsString()
  @IsOptional()
  @Transform(({ value }: { value: any }) => (value === '' ? undefined : value))
  classification?: string

  @ApiProperty({ description: '分类列表', required: false, type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform(({ value }: { value: any }) => {
    if (typeof value === 'string') {
      return value.split(',').filter(Boolean)
    }
    return value
  })
  classifications?: string[]

  @ApiProperty({ description: '状态', required: false })
  @IsString()
  @IsOptional()
  @Transform(({ value }: { value: any }) => (value === '' ? undefined : value))
  state?: string

  @ApiProperty({ description: '页码', default: 1, required: false })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  page?: number = 1

  @ApiProperty({ description: '每页数量', default: 10, required: false })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  limit?: number = 10
}
