import { Controller, Get, Post, Body, Patch, Param, Delete, Query, HttpStatus, HttpException } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiParam, ApiQuery, ApiResponse } from '@nestjs/swagger'
import { FabricGroupService } from './fabricGroup.service'
import {
  CreateFabricGroupDto,
  UpdateFabricGroupDto,
  QueryFabricGroupDto,
  FinishedCuttingGroupsResponseDto,
  FinishedCuttingGroupDto
} from './dto'

@ApiTags('布料组管理')
@Controller('/fabric-group')
export class FabricGroupController {
  constructor(private readonly fabricGroupService: FabricGroupService) {}

  @Post()
  @ApiOperation({ summary: '创建布料组' })
  create(@Body() createFabricGroupDto: CreateFabricGroupDto) {
    return this.fabricGroupService.create(createFabricGroupDto)
  }

  @Get()
  @ApiOperation({ summary: '获取布料组列表' })
  findAll(@Query() queryFabricGroupDto: QueryFabricGroupDto) {
    console.log('获取布料组列表，查询参数:', JSON.stringify(queryFabricGroupDto))
    return this.fabricGroupService.findAll(queryFabricGroupDto)
  }

  @Get('year-options')
  @ApiOperation({ summary: '获取年份选项列表' })
  @ApiQuery({ name: 'supplier', required: false, description: '供应商过滤' })
  @ApiQuery({ name: 'classification', required: false, description: '分类过滤' })
  getYearOptions(
    @Query('supplier') supplier: string,
    @Query('classification') classification: string
  ) {
    console.log('获取年份选项，供应商:', supplier)
    console.log('获取年份选项，分类:', classification)
    return this.fabricGroupService.getYearOptions(supplier, classification)
  }

  @Get('supplier-options')
  @ApiOperation({ summary: '获取供应商选项列表' })
  @ApiQuery({ name: 'year', required: false, description: '年份过滤' })
  @ApiQuery({ name: 'classification', required: false, description: '分类过滤' })
  getSupplierOptions(@Query('year') year: string, @Query('classification') classification: string) {
    console.log('获取供应商选项，年份:', year)
    console.log('获取供应商选项，分类:', classification)
    return this.fabricGroupService.getSupplierOptions(year, classification)
  }

  @Get('classification-options')
  @ApiOperation({ summary: '获取分类选项列表' })
  @ApiQuery({ name: 'year', required: false, description: '年份过滤' })
  @ApiQuery({ name: 'supplier', required: false, description: '供应商过滤' })
  getClassificationOptions(@Query('year') year: string, @Query('supplier') supplier: string) {
    console.log('获取分类选项，年份454544564564654:', year)
    console.log('获取分类选项，供应商:', supplier)
    return this.fabricGroupService.getClassificationOptions(year, supplier)
  }

  @Get(':id')
  @ApiOperation({ summary: '获取布料组详情' })
  @ApiParam({ name: 'id', description: '布料组ID' })
  findOne(@Param('id') id: string) {
    return this.fabricGroupService.findOne(id)
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新布料组' })
  @ApiParam({ name: 'id', description: '布料组ID' })
  update(@Param('id') id: string, @Body() updateFabricGroupDto: UpdateFabricGroupDto) {
    return this.fabricGroupService.update(id, updateFabricGroupDto)
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除布料组' })
  @ApiParam({ name: 'id', description: '布料组ID' })
  remove(@Param('id') id: string) {
    return this.fabricGroupService.remove(id)
  }

  @Get('finished-cutting/clothing')
  @ApiOperation({ summary: '获取状态为"裁剪完成"的布料组及其相关服装信息' })
  @ApiResponse({
    status: 200,
    description: '成功获取数据',
    type: FinishedCuttingGroupsResponseDto
  })
  async getFinishedCuttingGroupsWithClothing() {
    try {
      const result = await this.fabricGroupService.getFinishedCuttingGroupsWithClothing()
      return {
        code: HttpStatus.OK,
        data: result,
        message: '获取裁剪完成的布料组及服装信息成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取裁剪完成的布料组及服装信息失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }
}
