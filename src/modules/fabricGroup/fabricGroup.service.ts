import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { FabricGroup } from '@/models/fabricGroup.model'
import { Fabric } from '@/models/fabric.model'
import { CreateFabricGroupDto, UpdateFabricGroupDto, QueryFabricGroupDto } from './dto'

// 定义返回类型接口
interface FabricGroupListResponse {
  total: number
  page: number
  limit: number
  data: FabricGroup[]
}

@Injectable()
export class FabricGroupService {
  constructor(
    @InjectModel('FabricGroup') private readonly fabricGroupModel: Model<FabricGroup>,
    @InjectModel('Fabric') private readonly fabricModel: Model<Fabric>
  ) {}

  // 创建布料组
  async create(createFabricGroupDto: CreateFabricGroupDto): Promise<FabricGroup> {
    const fabricGroup = new this.fabricGroupModel({
      ...createFabricGroupDto,
      lastChangeTime: new Date(),
    })
    return fabricGroup.save()
  }

  // 获取布料组列表，支持分页和筛选
  async findAll(queryParams: QueryFabricGroupDto): Promise<FabricGroupListResponse> {
    console.log('后端收到的查询参数：', JSON.stringify(queryParams))

    const {
      fabric_group_year,
      fabric_group_years,
      fabric_group_id,
      supplier,
      suppliers,
      classification,
      classifications,
      state,
      page = 1,
      limit = 10,
    } = queryParams

    // 确保 page 和 limit 是数字类型
    const pageNum = Number(page)
    const limitNum = Number(limit)

    // 计算跳过的文档数量
    const skip = (pageNum - 1) * limitNum

    // 构建查询条件
    const filter: any = {}

    // 处理年份条件
    if (fabric_group_year) {
      filter.fabric_group_year = fabric_group_year
    } else if (fabric_group_years) {
      // 将 fabric_group_years 转换为字符串，然后检查是否包含逗号
      const yearsStr = String(fabric_group_years)

      if (yearsStr.includes(',')) {
        const yearsArray = yearsStr.split(',').filter(Boolean)

        if (yearsArray.length > 0) {
          filter.fabric_group_year = { $in: yearsArray }
        }
      }
      // 如果是数组
      else if (Array.isArray(fabric_group_years) && fabric_group_years.length > 0) {
        filter.fabric_group_year = { $in: fabric_group_years }
      }
      // 如果是单个值
      else if (yearsStr.trim() !== '') {
        filter.fabric_group_year = yearsStr.trim()
      }
    }

    // 处理编码条件
    if (fabric_group_id) {
      filter.fabric_group_id = { $regex: fabric_group_id, $options: 'i' }
    }

    // 处理供应商条件
    if (supplier) {
      filter.supplier = { $regex: supplier, $options: 'i' }
    } else if (suppliers) {
      // 将 suppliers 转换为字符串，然后检查是否包含逗号
      const suppliersStr = String(suppliers)

      if (suppliersStr.includes(',')) {
        const suppliersArray = suppliersStr.split(',').filter(Boolean)

        if (suppliersArray.length > 0) {
          filter.supplier = { $in: suppliersArray }
        }
      }
      // 如果是数组
      else if (Array.isArray(suppliers) && suppliers.length > 0) {
        filter.supplier = { $in: suppliers }
      }
      // 如果是单个值
      else if (suppliersStr.trim() !== '') {
        filter.supplier = suppliersStr.trim()
      }
    }

    // 处理分类条件
    if (classification) {
      filter.group_classification = classification
    } else if (classifications) {
      // 将 classifications 转换为字符串，然后检查是否包含逗号
      const classificationsStr = String(classifications)

      if (classificationsStr.includes(',')) {
        const classificationsArray = classificationsStr.split(',').filter(Boolean)

        if (classificationsArray.length > 0) {
          filter.group_classification = { $in: classificationsArray }
        }
      }
      // 如果是数组
      else if (Array.isArray(classifications) && classifications.length > 0) {
        filter.group_classification = { $in: classifications }
      }
      // 如果是单个值
      else if (classificationsStr.trim() !== '') {
        filter.group_classification = classificationsStr.trim()
      }
    }

    // 处理状态条件
    if (state) {
      filter.state = state
    }

    console.log('构建的查询条件：', JSON.stringify(filter))
    try {
      // 查询总数
      const total = await this.fabricGroupModel.countDocuments(filter).exec()

      // 使用聚合管道查询数据，并关联 fabric 集合
      const aggregationPipeline = [
        { $match: filter }, // 应用过滤条件
        { $sort: { fabric_group_id: -1 } }, // 按布料组编码倒序排序
        { $skip: skip }, // 分页跳过
        { $limit: limitNum }, // 分页限制
        {
          $addFields: {
            // 创建一个临时字段，用于存储 fabrics 数组中的每个元素
            fabricsForLookup: {
              $map: {
                input: '$fabrics',
                as: 'fabricId',
                in: { fabric_id: '$$fabricId' },
              },
            },
          },
        },
        {
          $lookup: {
            // 关联 fabric 集合
            from: 'fabric',
            let: { fabricIds: '$fabrics' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $in: ['$fabric_id', '$$fabricIds'],
                  },
                },
              },
              {
                $project: {
                  _id: 1,
                  fabric_id: 1,
                  fabric_name: 1,
                  in_quantity: 1,
                  remark: 1,
                },
              },
            ],
            as: 'fabricDetails',
          },
        },
        {
          $addFields: {
            // 将原始的 fabrics 字段替换为关联后的详细信息
            fabricsWithDetails: '$fabricDetails',
          },
        },
      ]

      const data = await this.fabricGroupModel.aggregate(aggregationPipeline as any).exec()

      console.log(`查询结果：找到 ${total} 条记录`)
      return {
        total,
        page: pageNum,
        limit: limitNum,
        data,
      }
    } catch (error) {
      console.error('查询布料组列表失败：', error)
      throw error
    }
  }

  // 获取单个布料组
  async findOne(id: string): Promise<any> {
    // 使用聚合管道查询单个布料组，并关联 fabric 集合
    const aggregationPipeline = [
      { $match: { _id: new Types.ObjectId(id) } }, // 匹配指定ID
      {
        $lookup: {
          // 关联 fabric 集合
          from: 'fabric',
          let: { fabricIds: '$fabrics' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: ['$fabric_id', '$$fabricIds'],
                },
              },
            },
            {
              $project: {
                _id: 1,
                fabric_id: 1,
                fabric_name: 1,
                in_quantity: 1,
              },
            },
          ],
          as: 'fabricDetails',
        },
      },
      {
        $addFields: {
          // 将原始的 fabrics 字段替换为关联后的详细信息
          fabricsWithDetails: '$fabricDetails',
        },
      },
    ]

    const result = await this.fabricGroupModel.aggregate(aggregationPipeline as any).exec()

    if (!result || result.length === 0) {
      throw new NotFoundException(`布料组ID ${id} 不存在`)
    }

    return result[0]
  }

  // 更新布料组
  async update(id: string, updateFabricGroupDto: UpdateFabricGroupDto): Promise<FabricGroup> {
    const updatedFabricGroup = await this.fabricGroupModel
      .findByIdAndUpdate(id, { ...updateFabricGroupDto, lastChangeTime: new Date() }, { new: true })
      .exec()

    if (!updatedFabricGroup) {
      throw new NotFoundException(`布料组ID ${id} 不存在`)
    }

    return updatedFabricGroup
  }

  // 删除布料组
  async remove(id: string): Promise<{ success: boolean; message: string }> {
    const result = await this.fabricGroupModel.findByIdAndDelete(id).exec()
    if (!result) {
      throw new NotFoundException(`布料组ID ${id} 不存在`)
    }
    return { success: true, message: '布料组删除成功' }
  }

  // 获取年份选项
  async getYearOptions(supplier?: string, classification?: string): Promise<string[]> {
    console.log('getYearOptions 方法接收到的参数：', { supplier, classification })

    const filter: any = {}

    if (supplier) {
      // 如果是逗号分隔的字符串，转换为数组
      if (supplier.includes(',')) {
        const supplierArray = supplier.split(',').filter(Boolean)
        filter.supplier = { $in: supplierArray }
      } else {
        filter.supplier = supplier
      }
    }

    if (classification) {
      // 如果是逗号分隔的字符串，转换为数组
      if (classification.includes(',')) {
        const classificationArray = classification.split(',').filter(Boolean)
        filter.group_classification = { $in: classificationArray }
      } else {
        filter.group_classification = classification
      }
    }

    console.log('getYearOptions 构建的查询条件：', JSON.stringify(filter))

    const years = await this.fabricGroupModel.find(filter).distinct('fabric_group_year').exec()

    // 按年份倒序排序
    return years.sort((a, b) => b.localeCompare(a))
  }

  // 获取供应商选项
  async getSupplierOptions(year?: string, classification?: string): Promise<string[]> {
    console.log('getSupplierOptions 方法接收到的参数：', { year, classification })

    const filter: any = {}

    if (year) {
      // 如果是逗号分隔的字符串，转换为数组
      if (year.includes(',')) {
        const yearArray = year.split(',').filter(Boolean)
        filter.fabric_group_year = { $in: yearArray }
      } else {
        filter.fabric_group_year = year
      }
    }

    if (classification) {
      // 如果是逗号分隔的字符串，转换为数组
      if (classification.includes(',')) {
        const classificationArray = classification.split(',').filter(Boolean)
        filter.group_classification = { $in: classificationArray }
      } else {
        filter.group_classification = classification
      }
    }

    console.log('getSupplierOptions 构建的查询条件：', JSON.stringify(filter))

    return this.fabricGroupModel.find(filter).distinct('supplier').exec()
  }

  // 获取分类选项
  async getClassificationOptions(year?: string, supplier?: string): Promise<string[]> {
    console.log('getClassificationOptions 方法接收到的参数：', { year, supplier })

    const filter: any = {}

    if (year) {
      // 如果是逗号分隔的字符串，转换为数组
      if (year.includes(',')) {
        const yearArray = year.split(',').filter(Boolean)
        filter.fabric_group_year = { $in: yearArray }
      } else {
        filter.fabric_group_year = year
      }
    }

    if (supplier) {
      // 如果是逗号分隔的字符串，转换为数组
      if (supplier.includes(',')) {
        const supplierArray = supplier.split(',').filter(Boolean)
        filter.supplier = { $in: supplierArray }
      } else {
        filter.supplier = supplier
      }
    }

    console.log('getClassificationOptions 构建的查询条件：', JSON.stringify(filter))

    // 由于group_classification是数组，需要使用聚合查询
    const result = await this.fabricGroupModel.aggregate([
      { $match: filter },
      { $unwind: '$group_classification' },
      { $group: { _id: '$group_classification' } },
      { $project: { _id: 0, classification: '$_id' } },
    ])

    return result.map((item) => item.classification)
  }

  /**
   * 获取状态为"裁剪完成"的布料组及其相关服装信息
   * @returns 布料组和服装信息列表
   */
  async getFinishedCuttingGroupsWithClothing(): Promise<any[]> {
    try {
      // 查询状态为"裁剪完成"的布料组
      const finishedGroups = await this.fabricGroupModel.find({ state: '裁剪完成' }).exec()

      if (!finishedGroups || finishedGroups.length === 0) {
        return []
      }

      // 使用聚合管道查询服装信息
      const result = await this.fabricGroupModel
        .aggregate([
          // 匹配状态为"裁剪完成"的布料组
          { $match: { state: '裁剪完成' } },
          // 关联服装集合
          {
            $lookup: {
              from: 'clothing',
              let: { fabricGroupId: '$fabric_group_id' },
              pipeline: [
                {
                  $match: {
                    $expr: { $eq: ['$fabric_group_id', '$$fabricGroupId'] },
                  },
                },
                {
                  $project: {
                    _id: 1,
                    clothing_id: 1,
                    clothing_name: 1,
                    colorPcs: 1,
                  },
                },
              ],
              as: 'clothingList',
            },
          },
          // 只返回有关联服装的布料组
          { $match: { 'clothingList.0': { $exists: true } } },
          // 投影需要的字段
          {
            $project: {
              clothingList: 1,
            },
          },
        ])
        .exec()

      return result
    } catch (error) {
      console.error('获取裁剪完成的布料组及服装信息失败：', error)
      throw error
    }
  }
}
