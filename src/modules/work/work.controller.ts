import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger'
import { WorkService } from './work.service'
import { CreateWorkDto, UpdateWorkDto, QueryWorkDto } from './dto'

@ApiTags('工序管理')
@Controller('/work')
export class WorkController {
  constructor(private readonly workService: WorkService) {}

  @Post()
  @ApiOperation({ summary: '创建工序' })
  create(@Body() createWorkDto: CreateWorkDto) {
    return this.workService.create(createWorkDto)
  }

  @Get('year-options')
  @ApiOperation({ summary: '获取年份选项列表' })
  getYearOptions() {
    return this.workService.getYearOptions()
  }

  @Get()
  @ApiOperation({ summary: '获取工序列表' })
  findAll(@Query() queryWorkDto: QueryWorkDto) {
    return this.workService.findAll(queryWorkDto)
  }

  @Get(':id')
  @ApiOperation({ summary: '获取工序详情' })
  @ApiParam({ name: 'id', description: '工序ID' })
  findOne(@Param('id') id: string) {
    return this.workService.findOne(id)
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新工序' })
  @ApiParam({ name: 'id', description: '工序ID' })
  update(@Param('id') id: string, @Body() updateWorkDto: UpdateWorkDto) {
    return this.workService.update(id, updateWorkDto)
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除工序' })
  @ApiParam({ name: 'id', description: '工序ID' })
  remove(@Param('id') id: string) {
    return this.workService.remove(id)
  }

  @Post('import-json')
  @ApiOperation({ summary: '导入JSON数据', description: '接收前端传来的JSON数据，批量写入数据库' })
  importJson(@Body() importJsonDto: any) {
    console.log('接收到 JSON 数据导入请求:', {
      sheetName: importJsonDto.sheetName,
      fileName: importJsonDto.fileName,
      totalRecords: importJsonDto.totalRecords,
      dataLength: importJsonDto.data?.length || 0,
    })

    // 检查数据是否存在
    if (
      !importJsonDto.data ||
      !Array.isArray(importJsonDto.data) ||
      importJsonDto.data.length === 0
    ) {
      return { success: false, message: '数据不能为空' }
    }

    try {
      // 打印数据示例以便于调试
      console.log('数据示例:', JSON.stringify(importJsonDto.data.slice(0, 1)))

      // 调用服务层方法处理数据
      return this.workService.importJsonData(importJsonDto.data)
    } catch (error: any) {
      console.error('处理导入数据时出错:', error.message)
      return { success: false, message: `处理导入数据时出错: ${error.message}` }
    }
  }
}
