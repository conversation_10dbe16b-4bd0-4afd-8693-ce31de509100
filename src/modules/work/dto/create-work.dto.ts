import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsString, IsNumber, IsOptional, IsArray } from 'class-validator'
import { YearlyPrice } from '../../../models/work.model'

export class CreateWorkDto {
  @ApiProperty({ description: '工序编号' })
  @IsString()
  work_id: string

  @ApiProperty({ description: '工序名称' })
  @IsString()
  work_name: string

  @ApiProperty({ description: '工序单价' })
  @IsNumber()
  work_price: number

  @ApiPropertyOptional({ description: '小时产量' })
  @IsNumber()
  @IsOptional()
  hourly_output?: number

  @ApiPropertyOptional({ description: '备注' })
  @IsString()
  @IsOptional()
  remark?: string

  @ApiPropertyOptional({ description: '年度工价配置', type: [Object] })
  @IsArray()
  @IsOptional()
  yearly_prices?: YearlyPrice[]
}
