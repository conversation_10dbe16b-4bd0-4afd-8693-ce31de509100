import { ApiPropertyOptional } from '@nestjs/swagger'
import { IsString, IsNumber, IsOptional, IsArray } from 'class-validator'
import { Transform, Type } from 'class-transformer'

export class QueryWorkDto {
  @ApiPropertyOptional({ description: '工序编号' })
  @IsString()
  @IsOptional()
  work_id?: string

  @ApiPropertyOptional({ description: '工序名称' })
  @IsString()
  @IsOptional()
  work_name?: string

  @ApiPropertyOptional({ description: '年份', type: [String] })
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => (typeof value === 'string' ? [value] : value))
  years?: string[]

  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  page?: number = 1

  @ApiPropertyOptional({ description: '每页数量', default: 10 })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  limit?: number = 10
}
