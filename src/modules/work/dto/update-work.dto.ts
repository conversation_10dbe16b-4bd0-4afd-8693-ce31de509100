import { ApiPropertyOptional } from '@nestjs/swagger'
import { IsString, IsNumber, IsOptional, IsArray } from 'class-validator'
import { YearlyPrice } from '../../../models/work.model'

export class UpdateWorkDto {
  @ApiPropertyOptional({ description: '工序编号' })
  @IsString()
  @IsOptional()
  work_id?: string

  @ApiPropertyOptional({ description: '工序名称' })
  @IsString()
  @IsOptional()
  work_name?: string

  @ApiPropertyOptional({ description: '工序单价' })
  @IsNumber()
  @IsOptional()
  work_price?: number

  @ApiPropertyOptional({ description: '小时产量' })
  @IsNumber()
  @IsOptional()
  hourly_output?: number

  @ApiPropertyOptional({ description: '备注' })
  @IsString()
  @IsOptional()
  remark?: string

  @ApiPropertyOptional({ description: '年度工价配置', type: [Object] })
  @IsArray()
  @IsOptional()
  yearly_prices?: YearlyPrice[]

  @ApiPropertyOptional({ description: '最后修改时间' })
  @IsOptional()
  lastChangeTime?: Date
}
