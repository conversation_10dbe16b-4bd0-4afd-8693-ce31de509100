import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { Work } from '../../models/work.model'
import { CreateWorkDto, UpdateWorkDto, QueryWorkDto } from './dto'

@Injectable()
export class WorkService {
  constructor(@InjectModel('Work') private readonly workModel: Model<Work>) {}

  // 创建工序
  async create(createWorkDto: CreateWorkDto): Promise<Work> {
    const work = new this.workModel(createWorkDto)
    return work.save()
  }

  // 获取工序列表，支持分页和筛选
  async findAll(queryParams: QueryWorkDto) {
    console.log('后端收到的查询参数：', JSON.stringify(queryParams))

    const { work_id, work_name, years, page = 1, limit = 10 } = queryParams

    const skip = (page - 1) * limit

    // 构建查询条件
    const filter: any = {}

    // 处理工序编号和名称条件
    if (work_id) {
      filter.work_id = { $regex: work_id, $options: 'i' }
    }

    if (work_name) {
      filter.work_name = { $regex: work_name, $options: 'i' }
    }

    // 处理年份条件（从年度工价配置中查询）
    if (years && years.length > 0) {
      filter['yearly_prices.year'] = { $in: years }
    }

    console.log('构建的查询条件：', JSON.stringify(filter))

    // 执行查询
    const total = await this.workModel.countDocuments(filter).exec()
    const data = await this.workModel
      .find(filter)
      .sort({ work_id: 1 }) // 按工序编号升序排序
      .skip(skip)
      .limit(limit)
      .exec()

    console.log(`查询结果：找到 ${total} 条记录`)
    return {
      data: {
        total,
        page: Number(page),
        limit: Number(limit),
        workList: data as Work[],
      },
    }
  }

  // 获取单个工序
  async findOne(id: string): Promise<Work> {
    // 如果 Id 的开头是 GX，说明是工序编码
    if (id.startsWith('GX')) {
      const work = await this.workModel.findOne({ work_id: id }).exec()
      if (!work) {
        throw new NotFoundException(`工序编码 ${id} 不存在`)
      }
      return work
    }
    // 如果 Id 的开头不是 GX，说明是工序 ID
    const work = await this.workModel.findById(id).exec()
    if (!work) {
      throw new NotFoundException(`工序ID ${id} 不存在`)
    }
    return work
  }

  // 更新工序
  async update(id: string, updateWorkDto: UpdateWorkDto): Promise<Work> {
    const updatedWork = await this.workModel
      .findByIdAndUpdate(id, updateWorkDto, { new: true })
      .exec()

    if (!updatedWork) {
      throw new NotFoundException(`工序ID ${id} 不存在`)
    }

    return updatedWork
  }

  // 删除工序
  async remove(id: string): Promise<Work> {
    const deletedWork = await this.workModel.findByIdAndDelete(id).exec()

    if (!deletedWork) {
      throw new NotFoundException(`工序ID ${id} 不存在`)
    }

    return deletedWork
  }

  // 获取年份选项
  async getYearOptions() {
    const result = await this.workModel.distinct('yearly_prices.year').exec()
    // 结果排序，倒序
    const stringResult = result as string[]
    stringResult.sort((a, b) => b.localeCompare(a))

    return {
      data: {
        data: stringResult,
        total: stringResult.length,
      },
    }
  }

  // 导入JSON数据
  async importJsonData(
    data: any[]
  ): Promise<{ success: boolean; message: string; count?: number }> {
    console.log(`开始处理 ${data.length} 条工序数据导入`)

    const results = []
    const errors = []
    const ids = []

    // 处理每一条数据
    for (const item of data) {
      try {
        // 处理数据，确保字段名称正确
        const processedItem = {
          work_id: item.work_id || item['工序编号'],
          work_name: item.work_name || item['工序名称'],
          work_price: parseFloat(item.work_price || item['工序单价'] || 0),
          hourly_output: parseFloat(item.hourly_output || item['小时产量'] || 0),
          remark: item.remark || item['备注'] || '',
          yearly_prices: item.yearly_prices || [],
        }

        // 验证必要字段
        if (!processedItem.work_id || !processedItem.work_name) {
          throw new Error('工序编号和工序名称不能为空')
        }

        // 检查是否已存在相同编号的工序
        const existingWork = await this.workModel
          .findOne({
            work_id: processedItem.work_id,
          })
          .exec()

        let work
        if (existingWork) {
          // 如果存在，则更新
          console.log(`工序编码 ${processedItem.work_id} 已存在，进行更新操作`)
          work = await this.workModel
            .findByIdAndUpdate(
              existingWork._id,
              {
                ...processedItem,
                lastChangeTime: new Date(),
              },
              { new: true }
            )
            .exec()
          results.push({ status: 'updated', work })
        } else {
          // 如果不存在，则创建
          console.log(`工序编码 ${processedItem.work_id} 不存在，进行创建操作`)
          const newWork = new this.workModel({
            ...processedItem,
          })

          work = await newWork.save()
          results.push({ status: 'created', work })
        }
        if (work) {
          ids.push(work._id)
        }
      } catch (error: any) {
        console.error('处理数据项时出错:', error.message)
        errors.push({ item, error: error.message })
      }
    }

    // 返回处理结果
    if (errors.length > 0) {
      console.error(`导入过程中有 ${errors.length} 条记录出错:`, errors)
      return {
        success: false,
        message: `导入部分成功，有 ${errors.length} 条记录出错`,
        count: results.length,
      }
    }

    return {
      success: true,
      message: `成功导入 ${results.length} 条工序数据`,
      count: results.length,
    }
  }
}
