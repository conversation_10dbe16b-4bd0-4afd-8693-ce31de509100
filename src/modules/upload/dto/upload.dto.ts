import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 图片信息DTO
 */
export class ImageInfoDto {
  @ApiProperty({ description: '图片URL' })
  url: string;

  @ApiProperty({ description: '图片在COS中的Key' })
  Key: string;
}

/**
 * 上传响应DTO
 */
export class UploadResponseDto {
  @ApiProperty({ description: '状态码', example: 200 })
  code: number;

  @ApiProperty({ description: '响应数据', type: ImageInfoDto })
  data: ImageInfoDto;

  @ApiProperty({ description: '响应消息', example: '上传成功' })
  message: string;
}

/**
 * 删除图片请求DTO
 */
export class DeleteImagesRequestDto {
  @ApiProperty({ description: '要删除的图片Key列表', type: [ImageInfoDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DeleteImageItemDto)
  @IsNotEmpty({ message: '未提供要删除的文件列表' })
  keys: DeleteImageItemDto[];
}

/**
 * 删除图片项DTO
 */
export class DeleteImageItemDto {
  @ApiProperty({ description: '图片在COS中的Key' })
  @IsNotEmpty({ message: 'Key不能为空' })
  Key: string;
}

/**
 * 删除响应DTO
 */
export class DeleteResponseDto {
  @ApiProperty({ description: '状态码', example: 200 })
  code: number;

  @ApiProperty({ description: '响应数据', type: Object })
  data: any;

  @ApiProperty({ description: '响应消息', example: '删除成功' })
  message: string;
}
