import { memoryStorage } from 'multer';
import { extname } from 'path';
import { Request } from 'express';

// Multer配置
export const multerConfig = {
  storage: memoryStorage(), // 使用内存存储而不是磁盘存储
  limits: {
    fileSize: 10 * 1024 * 1024, // 限制文件大小为10MB
  },
  fileFilter: (req: Request, file: Express.Multer.File, cb: Function) => {
    // 处理中文文件名编码问题
    try {
      if (file.originalname && !/^[\u4e00-\u9fa5a-zA-Z0-9._\-\s]+$/.test(file.originalname)) {
        // 尝试从 latin1 转换为 utf8
        const decodedName = Buffer.from(file.originalname, 'latin1').toString('utf8');
        if (decodedName && decodedName.length > 0) {
          file.originalname = decodedName;
        }
      }
    } catch (error) {
      // 编码转换失败，保持原始文件名
      console.warn('文件名编码转换失败:', error);
    }

    // 只允许上传图片
    const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传图片文件 (JPEG, PNG, GIF, WEBP)'), false);
    }
  },
};
