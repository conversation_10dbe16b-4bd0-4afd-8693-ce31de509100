import { Controller, Post, UseInterceptors, UploadedFile, Body, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiConsumes, ApiBody, ApiResponse } from '@nestjs/swagger';
import { UploadService } from './upload.service';
import {
  UploadResponseDto,
  DeleteImagesRequestDto,
  DeleteResponseDto
} from './dto/upload.dto';

@ApiTags('上传')
@Controller('upload')
export class UploadController {
  private readonly logger = new Logger(UploadController.name);

  constructor(private readonly uploadService: UploadService) {}

  @Post('clothing')
  @ApiOperation({ summary: '上传服装图片' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({ status: 200, description: '上传成功', type: UploadResponseDto })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadClothingImage(@UploadedFile() file: Express.Multer.File): Promise<UploadResponseDto> {
    try {
      if (!file) {
        throw new HttpException('未找到上传文件', HttpStatus.BAD_REQUEST);
      }

      const result = await this.uploadService.uploadFile(file, 'clothing');

      return {
        code: 200,
        data: result,
        message: '上传成功',
      };
    } catch (error) {
      const err = error as Error;
      this.logger.error(`上传服装图片失败: ${err.message}`, err.stack);
      throw new HttpException(err.message || '上传失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('oemClothing')
  @ApiOperation({ summary: '上传OEM服装图片' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({ status: 200, description: '上传成功', type: UploadResponseDto })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadOemClothingImage(@UploadedFile() file: Express.Multer.File): Promise<UploadResponseDto> {
    try {
      if (!file) {
        throw new HttpException('未找到上传文件', HttpStatus.BAD_REQUEST);
      }

      const result = await this.uploadService.uploadFile(file, 'oemClothing');

      return {
        code: 200,
        data: result,
        message: '上传成功',
      };
    } catch (error) {
      const err = error as Error;
      this.logger.error(`上传OEM服装图片失败: ${err.message}`, err.stack);
      throw new HttpException(err.message || '上传失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('transportation')
  @ApiOperation({ summary: '上传发货图片' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({ status: 200, description: '上传成功', type: UploadResponseDto })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadTransportationImage(@UploadedFile() file: Express.Multer.File): Promise<UploadResponseDto> {
    try {
      if (!file) {
        throw new HttpException('未找到上传文件', HttpStatus.BAD_REQUEST);
      }

      const result = await this.uploadService.uploadFile(file, 'transportation');

      return {
        code: 200,
        data: result,
        message: '上传成功',
      };
    } catch (error) {
      const err = error as Error;
      this.logger.error(`上传发货图片失败: ${err.message}`, err.stack);
      throw new HttpException(err.message || '上传失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('delete')
  @ApiOperation({ summary: '删除图片' })
  @ApiResponse({ status: 200, description: '删除成功', type: DeleteResponseDto })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async deleteImages(@Body() body: DeleteImagesRequestDto): Promise<DeleteResponseDto> {
    try {
      if (!body.keys || !Array.isArray(body.keys) || body.keys.length === 0) {
        throw new HttpException('未提供要删除的文件列表', HttpStatus.BAD_REQUEST);
      }

      const result = await this.uploadService.deleteFiles(body.keys);

      if (result.statusCode === 200) {
        return {
          code: 200,
          data: result,
          message: '删除成功',
        };
      } else {
        throw new HttpException('删除失败', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    } catch (error) {
      const err = error as Error;
      this.logger.error(`删除图片失败: ${err.message}`, err.stack);
      throw new HttpException(err.message || '删除失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
