import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsNotEmpty, IsOptional, IsNumber, IsDate } from 'class-validator'
import { Type } from 'class-transformer'

export class CreateFabricWarehouseDto {
  @ApiProperty({ description: '布料仓库编号' })
  @IsString()
  @IsNotEmpty({ message: '布料仓库编号不能为空' })
  fabric_warehouse_id: string

  @ApiProperty({ description: '布料仓库年份' })
  @IsString()
  @IsNotEmpty({ message: '布料仓库年份不能为空' })
  fabric_warehouse_year: string

  @ApiProperty({ description: '入库日期' })
  @IsDate()
  @IsNotEmpty({ message: '入库日期不能为空' })
  @Type(() => Date)
  date_in: Date

  @ApiProperty({ description: '供应商' })
  @IsString()
  @IsNotEmpty({ message: '供应商不能为空' })
  supplier: string

  @ApiProperty({ description: '备注', required: false })
  @IsString()
  @IsOptional()
  remark?: string

  @ApiProperty({ description: '总米数', required: false })
  @IsNumber()
  @IsOptional()
  total_meter?: number
}
