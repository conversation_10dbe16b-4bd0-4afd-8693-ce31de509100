import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsOptional, IsNumber, IsArray, IsDateString } from 'class-validator'
import { Type } from 'class-transformer'

export class QueryFabricWarehouseDto {
  @ApiProperty({ description: '布料仓库编号', required: false })
  @IsString()
  @IsOptional()
  fabric_warehouse_id?: string

  @ApiProperty({ description: '布料仓库年份', required: false })
  @IsString()
  @IsOptional()
  fabric_warehouse_year?: string

  @ApiProperty({ description: '布料仓库年份列表', required: false, type: [String] })
  @IsArray()
  @IsOptional()
  fabric_warehouse_years?: string[]

  @ApiProperty({ description: '入库日期开始', required: false })
  @IsDateString()
  @IsOptional()
  date_in_start?: string

  @ApiProperty({ description: '入库日期结束', required: false })
  @IsDateString()
  @IsOptional()
  date_in_end?: string

  @ApiProperty({ description: '供应商', required: false })
  @IsString()
  @IsOptional()
  supplier?: string

  @ApiProperty({ description: '供应商列表', required: false, type: [String] })
  @IsArray()
  @IsOptional()
  suppliers?: string[]



  @ApiProperty({ description: '页码', required: false, default: 1 })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  page?: number = 1

  @ApiProperty({ description: '每页数量', required: false, default: 10 })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  limit?: number = 10
}
