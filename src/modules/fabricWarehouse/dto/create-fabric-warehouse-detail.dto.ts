import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsNotEmpty, IsOptional, IsNumber } from 'class-validator'

export class CreateFabricWarehouseDetailDto {
  @ApiProperty({ description: '关联的仓库ID' })
  @IsString()
  @IsNotEmpty({ message: '仓库ID不能为空' })
  warehouse_id: string

  @ApiProperty({ description: '布料编码' })
  @IsString()
  @IsNotEmpty({ message: '布料编码不能为空' })
  fabric_id: string

  @ApiProperty({ description: '布料名称' })
  @IsString()
  @IsNotEmpty({ message: '布料名称不能为空' })
  fabric_name: string

  @ApiProperty({ description: '米数' })
  @IsNumber()
  @IsNotEmpty({ message: '米数不能为空' })
  meter: number

  @ApiProperty({ description: '分类', required: false })
  @IsString()
  @IsOptional()
  classification?: string

  @ApiProperty({ description: '备注', required: false })
  @IsString()
  @IsOptional()
  remark?: string
}
