import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { FabricWarehouse } from '../../models/fabricWarehouse.model'
import { FabricWarehouseDetail } from '../../models/fabricWarehouseDetail.model'
import { Fabric } from '../../models/fabric.model'
import { CreateFabricWarehouseDto, UpdateFabricWarehouseDto, QueryFabricWarehouseDto } from './dto'

@Injectable()
export class FabricWarehouseService {
  constructor(
    @InjectModel('FabricWarehouse') private readonly fabricWarehouseModel: Model<FabricWarehouse>,
    @InjectModel('FabricWarehouseDetail')
    private readonly fabricWarehouseDetailModel: Model<FabricWarehouseDetail>,
    @InjectModel('Fabric') private readonly fabricModel: Model<Fabric>
  ) {}

  // 创建布料仓库
  async create(createFabricWarehouseDto: CreateFabricWarehouseDto): Promise<FabricWarehouse> {
    const fabricWarehouse = new this.fabricWarehouseModel(createFabricWarehouseDto)
    return fabricWarehouse.save()
  }

  // 获取布料仓库列表，支持分页和筛选
  async findAll(queryParams: QueryFabricWarehouseDto) {
    console.log('后端收到的查询参数：', JSON.stringify(queryParams))

    const {
      fabric_warehouse_id,
      fabric_warehouse_year,
      fabric_warehouse_years,
      date_in_start,
      date_in_end,
      supplier,
      suppliers,
      page = 1,
      limit = 10,
    } = queryParams

    const skip = (page - 1) * limit

    // 构建查询条件
    const filter: any = {}

    // 处理仓库编号条件
    if (fabric_warehouse_id) {
      filter.fabric_warehouse_id = { $regex: fabric_warehouse_id, $options: 'i' }
    }

    // 处理年份条件
    if (fabric_warehouse_year) {
      filter.fabric_warehouse_year = fabric_warehouse_year
    } else if (fabric_warehouse_years?.length) {
      filter.fabric_warehouse_year = { $in: fabric_warehouse_years }
    }

    // 处理入库日期条件
    if (date_in_start || date_in_end) {
      filter.date_in = {}
      if (date_in_start) {
        filter.date_in.$gte = new Date(date_in_start)
      }
      if (date_in_end) {
        filter.date_in.$lte = new Date(date_in_end)
      }
    }

    // 处理供应商条件
    if (supplier) {
      filter.supplier = { $regex: supplier, $options: 'i' }
    } else if (suppliers?.length) {
      filter.supplier = { $in: suppliers }
    }

    // 布料详情条件将在入库明细服务中处理

    console.log('构建的数据库查询条件：', JSON.stringify(filter))

    // 执行查询
    const [data, total] = await Promise.all([
      this.fabricWarehouseModel
        .find(filter)
        .skip(skip)
        .limit(limit)
        .sort({ date_in: -1 }) // 按入库日期倒序排列
        .exec(),
      this.fabricWarehouseModel.countDocuments(filter).exec(),
    ])

    console.log(`查询结果：找到 ${total} 条记录`)
    const response = {
      data: {
        total,
        page: Number(page),
        limit: Number(limit),
        data: data as FabricWarehouse[],
      },
    }
    return response
  }

  // 获取单个布料仓库
  async findOne(id: string): Promise<FabricWarehouse> {
    const fabricWarehouse = await this.fabricWarehouseModel.findById(id).exec()
    if (!fabricWarehouse) {
      throw new NotFoundException(`布料仓库ID ${id} 不存在`)
    }
    return fabricWarehouse
  }

  // 更新布料仓库
  async update(
    id: string,
    updateFabricWarehouseDto: UpdateFabricWarehouseDto
  ): Promise<FabricWarehouse> {
    const updatedFabricWarehouse = await this.fabricWarehouseModel
      .findByIdAndUpdate(id, updateFabricWarehouseDto, { new: true })
      .exec()

    if (!updatedFabricWarehouse) {
      throw new NotFoundException(`布料仓库ID ${id} 不存在`)
    }

    return updatedFabricWarehouse
  }

  // 删除布料仓库
  async remove(id: string): Promise<FabricWarehouse> {
    // 1. 先查找要删除的仓库记录
    const fabricWarehouse = await this.fabricWarehouseModel.findById(id).exec()
    if (!fabricWarehouse) {
      throw new NotFoundException(`布料仓库ID ${id} 不存在`)
    }

    try {
      // 2. 获取该仓库下所有明细记录中涉及的布料ID
      const details = await this.fabricWarehouseDetailModel.find({ warehouse_id: id }).exec()
      const fabricIds = [...new Set(details.map((detail) => detail.fabric_id))]
      console.log(`该仓库涉及 ${fabricIds.length} 个布料需要更新入库数量`)

      // 3. 删除关联的明细记录
      console.log(`删除布料仓库ID ${id} 的所有关联明细记录`)
      const deleteResult = await this.fabricWarehouseDetailModel
        .deleteMany({ warehouse_id: id })
        .exec()
      console.log(`已删除 ${deleteResult.deletedCount} 条关联明细记录`)

      // 4. 删除主表记录
      const deletedFabricWarehouse = await this.fabricWarehouseModel.findByIdAndDelete(id).exec()
      console.log(`已删除布料仓库记录，ID: ${id}`)

      // 5. 更新布料入库数量
      if (fabricIds.length > 0) {
        console.log(`开始更新 ${fabricIds.length} 个布料的入库数量`)

        // 对每个布料ID重新计算入库数量
        for (const fabricId of fabricIds) {
          try {
            // 重新计算该布料的入库数量
            const result = await this.fabricWarehouseDetailModel
              .aggregate([
                { $match: { fabric_id: fabricId } },
                { $group: { _id: null, total_meter: { $sum: '$meter' } } },
              ])
              .exec()

            const totalMeter = result.length > 0 ? result[0].total_meter : 0

            // 更新布料表中的入库数量
            await this.fabricModel
              .updateOne({ fabric_id: fabricId }, { $set: { in_quantity: totalMeter } })
              .exec()

            console.log(`已更新布料 ${fabricId} 的入库数量为 ${totalMeter}`)
          } catch (updateError) {
            console.error(`更新布料 ${fabricId} 的入库数量时出错:`, updateError)
            // 继续处理下一个布料，不中断整个过程
          }
        }

        console.log(`布料入库数量更新完成`)
      }

      // 由于我们已经在开始检查了记录是否存在，这里可以安全地断言返回值不为null
      return deletedFabricWarehouse!
    } catch (error) {
      console.error(`删除布料仓库及关联明细时出错:`, error)
      throw error
    }
  }

  // 获取年份选项列表
  async getYearOptions(suppliers: string = '') {
    const filter: any = {}

    // 如果有供应商过滤条件
    if (suppliers) {
      const supplierArray = suppliers.split(',')
      if (supplierArray.length > 0) {
        filter.supplier = { $in: supplierArray }
      }
    }

    const years = await this.fabricWarehouseModel.distinct('fabric_warehouse_year', filter).exec()
    return { data: years }
  }

  // 获取供应商选项列表
  async getSupplierOptions(years: string = '') {
    const filter: any = {}

    // 如果有年份过滤条件
    if (years) {
      const yearArray = years.split(',')
      if (yearArray.length > 0) {
        filter.fabric_warehouse_year = { $in: yearArray }
      }
    }

    const suppliers = await this.fabricWarehouseModel.distinct('supplier', filter).exec()
    return { data: suppliers }
  }

  // 获取布料分类选项列表
  async getClassificationOptions(years: string = '', suppliers: string = '') {
    // 构建查询条件
    const filter: any = {}

    // 如果有年份过滤条件
    if (years) {
      const yearArray = years.split(',')
      if (yearArray.length > 0) {
        filter.fabric_warehouse_year = { $in: yearArray }
      }
    }

    // 如果有供应商过滤条件
    if (suppliers) {
      const supplierArray = suppliers.split(',')
      if (supplierArray.length > 0) {
        filter.supplier = { $in: supplierArray }
      }
    }

    // 使用聚合管道来获取布料详情中的分类
    const classifications = await this.fabricWarehouseModel
      .aggregate([
        { $match: filter },
        { $unwind: '$detail' },
        { $group: { _id: '$detail.classification' } },
        { $match: { _id: { $ne: null } } },
        { $project: { _id: 0, classification: '$_id' } },
      ])
      .exec()

    return { data: classifications.map((item) => item.classification) }
  }
}
