import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger'
import { FabricWarehouseService } from './fabricWarehouse.service'
import { CreateFabricWarehouseDto, UpdateFabricWarehouseDto, QueryFabricWarehouseDto } from './dto'

@ApiTags('布料仓库管理')
@Controller('/fabric-warehouse')
export class FabricWarehouseController {
  constructor(private readonly fabricWarehouseService: FabricWarehouseService) {}

  @Post()
  @ApiOperation({ summary: '创建布料仓库' })
  create(@Body() createFabricWarehouseDto: CreateFabricWarehouseDto) {
    return this.fabricWarehouseService.create(createFabricWarehouseDto)
  }

  @Get()
  @ApiOperation({ summary: '获取布料仓库列表' })
  findAll(@Query() queryFabricWarehouseDto: QueryFabricWarehouseDto) {
    return this.fabricWarehouseService.findAll(queryFabricWarehouseDto)
  }

  @Get('year-options')
  @ApiOperation({ summary: '获取年份选项列表' })
  @ApiQuery({ name: 'supplier', required: false, description: '供应商过滤，多个用逗号分隔' })
  getYearOptions(@Query('supplier') suppliers: string) {
    return this.fabricWarehouseService.getYearOptions(suppliers)
  }

  @Get('supplier-options')
  @ApiOperation({ summary: '获取供应商选项列表' })
  @ApiQuery({ name: 'year', required: false, description: '年份过滤，多个用逗号分隔' })
  getSupplierOptions(@Query('year') years: string) {
    return this.fabricWarehouseService.getSupplierOptions(years)
  }

  @Get('classification-options')
  @ApiOperation({ summary: '获取布料分类选项列表' })
  @ApiQuery({ name: 'year', required: false, description: '年份过滤，多个用逗号分隔' })
  @ApiQuery({ name: 'supplier', required: false, description: '供应商过滤，多个用逗号分隔' })
  getClassificationOptions(
    @Query('year') years: string,
    @Query('supplier') suppliers: string
  ) {
    return this.fabricWarehouseService.getClassificationOptions(years, suppliers)
  }

  @Get(':id')
  @ApiOperation({ summary: '获取布料仓库详情' })
  @ApiParam({ name: 'id', description: '布料仓库ID' })
  findOne(@Param('id') id: string) {
    return this.fabricWarehouseService.findOne(id)
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新布料仓库' })
  @ApiParam({ name: 'id', description: '布料仓库ID' })
  update(@Param('id') id: string, @Body() updateFabricWarehouseDto: UpdateFabricWarehouseDto) {
    return this.fabricWarehouseService.update(id, updateFabricWarehouseDto)
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除布料仓库' })
  @ApiParam({ name: 'id', description: '布料仓库ID' })
  remove(@Param('id') id: string) {
    return this.fabricWarehouseService.remove(id)
  }


}
