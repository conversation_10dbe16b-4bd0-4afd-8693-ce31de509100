import { Module } from '@nestjs/common'
import { ModelsModule } from '../../models/models.module'
import { FabricWarehouseController } from './fabricWarehouse.controller'
import { FabricWarehouseService } from './fabricWarehouse.service'
import { FabricWarehouseDetailController } from './fabricWarehouseDetail.controller'
import { FabricWarehouseDetailService } from './fabricWarehouseDetail.service'

@Module({
  imports: [ModelsModule],
  controllers: [FabricWarehouseController, FabricWarehouseDetailController],
  providers: [FabricWarehouseService, FabricWarehouseDetailService],
  exports: [FabricWarehouseService, FabricWarehouseDetailService],
})
export class FabricWarehouseModule {}
