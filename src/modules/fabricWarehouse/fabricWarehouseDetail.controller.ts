import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiParam, ApiBody } from '@nestjs/swagger'
import { FabricWarehouseDetailService } from './fabricWarehouseDetail.service'
import { CreateFabricWarehouseDetailDto, UpdateFabricWarehouseDetailDto } from './dto'

@ApiTags('布料入库明细管理')
@Controller('/fabric-warehouse-detail')
export class FabricWarehouseDetailController {
  constructor(private readonly fabricWarehouseDetailService: FabricWarehouseDetailService) {}

  @Post()
  @ApiOperation({ summary: '创建布料入库明细' })
  create(@Body() createFabricWarehouseDetailDto: CreateFabricWarehouseDetailDto) {
    return this.fabricWarehouseDetailService.create(createFabricWarehouseDetailDto)
  }

  @Post('batch/:warehouseId')
  @ApiOperation({ summary: '批量创建布料入库明细' })
  @ApiParam({ name: 'warehouseId', description: '仓库ID' })
  @ApiBody({ type: [CreateFabricWarehouseDetailDto] })
  createBatch(
    @Param('warehouseId') warehouseId: string,
    @Body() details: Omit<CreateFabricWarehouseDetailDto, 'warehouse_id'>[]
  ) {
    return this.fabricWarehouseDetailService.createBatch(warehouseId, details)
  }

  @Get('warehouse/:warehouseId')
  @ApiOperation({ summary: '获取指定仓库的所有入库明细' })
  @ApiParam({ name: 'warehouseId', description: '仓库ID' })
  findAllByWarehouseId(@Param('warehouseId') warehouseId: string) {
    return this.fabricWarehouseDetailService.findAllByWarehouseId(warehouseId)
  }

  @Get('fabric/:fabricId')
  @ApiOperation({ summary: '获取指定布料的入库明细' })
  @ApiParam({ name: 'fabricId', description: '布料ID' })
  findAllByFabricId(@Param('fabricId') fabricId: string) {
    return this.fabricWarehouseDetailService.findAllByFabricId(fabricId)
  }

  @Get(':id')
  @ApiOperation({ summary: '获取入库明细详情' })
  @ApiParam({ name: 'id', description: '入库明细ID' })
  findOne(@Param('id') id: string) {
    return this.fabricWarehouseDetailService.findOne(id)
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新入库明细' })
  @ApiParam({ name: 'id', description: '入库明细ID' })
  update(
    @Param('id') id: string,
    @Body() updateFabricWarehouseDetailDto: UpdateFabricWarehouseDetailDto
  ) {
    return this.fabricWarehouseDetailService.update(id, updateFabricWarehouseDetailDto)
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除入库明细' })
  @ApiParam({ name: 'id', description: '入库明细ID' })
  remove(@Param('id') id: string) {
    return this.fabricWarehouseDetailService.remove(id)
  }
}
