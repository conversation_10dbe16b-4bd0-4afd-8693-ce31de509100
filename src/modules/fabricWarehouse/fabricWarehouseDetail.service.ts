import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { FabricWarehouseDetail } from '../../models/fabricWarehouseDetail.model'
import { FabricWarehouse } from '../../models/fabricWarehouse.model'
import { CreateFabricWarehouseDetailDto, UpdateFabricWarehouseDetailDto } from './dto'

@Injectable()
export class FabricWarehouseDetailService {
  constructor(
    @InjectModel('FabricWarehouseDetail')
    private readonly fabricWarehouseDetailModel: Model<FabricWarehouseDetail>,
    @InjectModel('FabricWarehouse')
    private readonly fabricWarehouseModel: Model<FabricWarehouse>
  ) {}

  // 创建布料入库明细
  async create(
    createFabricWarehouseDetailDto: CreateFabricWarehouseDetailDto
  ): Promise<FabricWarehouseDetail> {
    // 检查关联的仓库是否存在
    const warehouse = await this.fabricWarehouseModel
      .findById(createFabricWarehouseDetailDto.warehouse_id)
      .exec()
    if (!warehouse) {
      throw new NotFoundException(`仓库ID ${createFabricWarehouseDetailDto.warehouse_id} 不存在`)
    }

    // 创建入库明细
    const detail = new this.fabricWarehouseDetailModel(createFabricWarehouseDetailDto)
    const savedDetail = await detail.save()

    // 更新仓库的总米数
    await this.updateWarehouseTotalMeter(createFabricWarehouseDetailDto.warehouse_id)

    return savedDetail
  }

  // 批量创建布料入库明细
  async createBatch(
    warehouseId: string,
    details: Omit<CreateFabricWarehouseDetailDto, 'warehouse_id'>[]
  ): Promise<FabricWarehouseDetail[]> {
    // 检查关联的仓库是否存在
    const warehouse = await this.fabricWarehouseModel.findById(warehouseId).exec()
    if (!warehouse) {
      throw new NotFoundException(`仓库ID ${warehouseId} 不存在`)
    }

    // 准备批量插入的数据
    const detailsWithWarehouseId = details.map((detail) => ({
      ...detail,
      warehouse_id: warehouseId,
    }))

    // 批量创建入库明细
    const savedDetails = await this.fabricWarehouseDetailModel.insertMany(detailsWithWarehouseId)

    // 更新仓库的总米数
    await this.updateWarehouseTotalMeter(warehouseId)

    return savedDetails
  }

  // 获取指定仓库的所有入库明细
  async findAllByWarehouseId(warehouseId: string): Promise<FabricWarehouseDetail[]> {
    return this.fabricWarehouseDetailModel.find({ warehouse_id: warehouseId }).exec()
  }

  // 获取指定布料的入库明细
  async findAllByFabricId(fabricId: string): Promise<any[]> {
    // 使用聚合查询，关联仓库表获取入库日期
    const details = await this.fabricWarehouseDetailModel.aggregate([
      { $match: { fabric_id: fabricId } },
      {
        $lookup: {
          from: 'fabric_warehouse',
          localField: 'warehouse_id',
          foreignField: '_id',
          as: 'warehouse'
        }
      },
      { $unwind: '$warehouse' },
      {
        $project: {
          _id: 1,
          fabric_id: 1,
          fabric_name: 1,
          meter: 1,
          remark: 1,
          date: '$warehouse.date_in',
          supplier: '$warehouse.supplier',
          warehouse_id: 1,
          createTime: 1
        }
      },
      { $sort: { date: -1 } } // 按入库日期倒序排列
    ]).exec()

    return details
  }

  // 获取单个入库明细
  async findOne(id: string): Promise<FabricWarehouseDetail> {
    const detail = await this.fabricWarehouseDetailModel.findById(id).exec()
    if (!detail) {
      throw new NotFoundException(`入库明细ID ${id} 不存在`)
    }
    return detail
  }

  // 更新入库明细
  async update(
    id: string,
    updateFabricWarehouseDetailDto: UpdateFabricWarehouseDetailDto
  ): Promise<FabricWarehouseDetail> {
    const updatedDetail = await this.fabricWarehouseDetailModel
      .findByIdAndUpdate(id, updateFabricWarehouseDetailDto, { new: true })
      .exec()

    if (!updatedDetail) {
      throw new NotFoundException(`入库明细ID ${id} 不存在`)
    }

    // 如果更新了米数，需要更新仓库的总米数
    if (updateFabricWarehouseDetailDto.meter !== undefined) {
      await this.updateWarehouseTotalMeter(updatedDetail.warehouse_id.toString())
    }

    return updatedDetail
  }

  // 删除入库明细
  async remove(id: string): Promise<FabricWarehouseDetail> {
    const detail = await this.fabricWarehouseDetailModel.findById(id).exec()
    if (!detail) {
      throw new NotFoundException(`入库明细ID ${id} 不存在`)
    }

    const warehouseId = detail.warehouse_id.toString()
    const deletedDetail = await this.fabricWarehouseDetailModel.findByIdAndDelete(id).exec()

    // 更新仓库的总米数
    await this.updateWarehouseTotalMeter(warehouseId)

    return deletedDetail!
  }

  // 更新仓库的总米数
  private async updateWarehouseTotalMeter(warehouseId: string): Promise<void> {
    // 计算该仓库下所有入库明细的米数总和
    const result = await this.fabricWarehouseDetailModel.aggregate([
      { $match: { warehouse_id: warehouseId } },
      { $group: { _id: null, total_meter: { $sum: '$meter' } } },
    ])

    const totalMeter = result.length > 0 ? result[0].total_meter : 0

    // 更新仓库的总米数
    await this.fabricWarehouseModel.findByIdAndUpdate(warehouseId, { total_meter: totalMeter })
  }
}
