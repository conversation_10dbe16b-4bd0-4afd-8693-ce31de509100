import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { BillOfMaterials } from '../../models/billOfMaterials.model'
import { Material } from '../../models/material.model'
import { CreateBomDto } from './dto/create-bom.dto'
import { UpdateBomDto } from './dto/update-bom.dto'
import { QueryBomDto } from './dto/query-bom.dto'

@Injectable()
export class BomService {
  constructor(
    @InjectModel('BillOfMaterials') private readonly bomModel: Model<BillOfMaterials>,
    @InjectModel('Material') private readonly materialModel: Model<Material>,
  ) {}

  // 获取物料清单列表，支持分页和筛选
  async findAll(queryParams: QueryBomDto) {
    console.log('后端收到的查询参数：', JSON.stringify(queryParams))

    const { clothing_id, bom_year, status, page = 1, limit = 10 } = queryParams

    const skip = (page - 1) * limit

    // 构建查询条件
    const filter: any = {}

    if (clothing_id) {
      filter.clothing_id = clothing_id
    }

    if (bom_year) {
      filter.bom_year = bom_year
    }

    if (status) {
      filter.status = status
    }

    console.log('构建的查询条件：', JSON.stringify(filter))

    // 执行查询
    const total = await this.bomModel.countDocuments(filter).exec()
    const data = await this.bomModel
      .find(filter)
      .sort({ bom_year: -1, bom_id: 1 }) // 按年份倒序、编号升序排序
      .skip(skip)
      .limit(limit)
      .exec()

    console.log(`查询结果：找到 ${total} 条记录`)
    return {
      data: {
        total,
        page: Number(page),
        limit: Number(limit),
        bomList: data as BillOfMaterials[],
      },
    }
  }

  // 获取单个物料清单
  async findOne(id: string): Promise<BillOfMaterials> {
    const bom = await this.bomModel.findById(id).exec()
    if (!bom) {
      throw new NotFoundException(`物料清单ID ${id} 不存在`)
    }
    return bom
  }

  // 根据服装ID获取物料清单
  async findByClothingId(clothingId: string, year?: string): Promise<BillOfMaterials | null> {
    const filter: any = { clothing_id: clothingId }
    if (year) {
      filter.bom_year = year
    }

    const bom = await this.bomModel
      .findOne(filter)
      .sort({ bom_year: -1, version: -1 }) // 获取最新年份和版本
      .exec()

    return bom
  }

  // 创建物料清单
  async create(createBomDto: CreateBomDto): Promise<BillOfMaterials> {
    // 检查是否已存在相同的bom_id
    const existingBom = await this.bomModel.findOne({ bom_id: createBomDto.bom_id }).exec()
    if (existingBom) {
      throw new Error(`物料清单ID ${createBomDto.bom_id} 已存在，请使用更新操作`)
    }

    // 计算总成本
    if (createBomDto.bom_items && createBomDto.bom_items.length > 0) {
      createBomDto.total_material_cost = await this.calculateTotalCost(
        createBomDto.bom_items,
        createBomDto.bom_year
      )
    }

    const createdBom = new this.bomModel(createBomDto)
    return createdBom.save()
  }

  // 保存物料清单（存在则更新，不存在则创建）
  async saveOrUpdate(bomData: CreateBomDto): Promise<BillOfMaterials> {
    console.log('保存物料清单，bom_id:', bomData.bom_id)

    // 计算总成本
    if (bomData.bom_items && bomData.bom_items.length > 0) {
      bomData.total_material_cost = await this.calculateTotalCost(
        bomData.bom_items,
        bomData.bom_year
      )
    }

    // 检查是否已存在
    const existingBom = await this.bomModel.findOne({ bom_id: bomData.bom_id }).exec()

    if (existingBom) {
      console.log('物料清单已存在，执行更新操作')
      // 更新现有记录
      const updateData = {
        ...bomData,
        lastChangeTime: new Date(),
      }

      const updatedBom = await this.bomModel
        .findOneAndUpdate(
          { bom_id: bomData.bom_id },
          updateData,
          { new: true, runValidators: true }
        )
        .exec()

      if (!updatedBom) {
        throw new NotFoundException(`更新物料清单失败，ID: ${bomData.bom_id}`)
      }

      return updatedBom
    } else {
      console.log('物料清单不存在，执行创建操作')
      // 创建新记录
      const createdBom = new this.bomModel(bomData)
      return createdBom.save()
    }
  }

  // 更新物料清单
  async update(id: string, updateBomDto: UpdateBomDto): Promise<BillOfMaterials> {
    // 重新计算总成本
    if (updateBomDto.bom_items && updateBomDto.bom_items.length > 0) {
      const bom = await this.bomModel.findById(id).exec()
      if (bom) {
        updateBomDto.total_material_cost = await this.calculateTotalCost(
          updateBomDto.bom_items,
          updateBomDto.bom_year || bom.bom_year
        )
      }
    }

    const updatedBom = await this.bomModel
      .findByIdAndUpdate(id, updateBomDto, { new: true })
      .exec()

    if (!updatedBom) {
      throw new NotFoundException(`物料清单ID ${id} 不存在`)
    }

    return updatedBom
  }

  // 删除物料清单
  async remove(id: string): Promise<BillOfMaterials> {
    const deletedBom = await this.bomModel.findByIdAndDelete(id).exec()

    if (!deletedBom) {
      throw new NotFoundException(`物料清单ID ${id} 不存在`)
    }

    return deletedBom
  }

  // 计算物料清单成本
  async calculateCost(bomId: string, year: string) {
    console.log('计算物料清单成本，BOM ID:', bomId, '年份:', year)

    // 修复查询条件：使用bom_id字段而不是_id字段
    const bom = await this.bomModel.findOne({ bom_id: bomId }).exec()
    if (!bom) {
      throw new NotFoundException(`物料清单 ${bomId} 不存在`)
    }

    console.log('找到物料清单:', bom.bom_id, '物料项数量:', bom.bom_items?.length || 0)

    if (!bom.bom_items || bom.bom_items.length === 0) {
      console.log('物料清单为空，返回零成本')
      return {
        data: {
          total_cost: 0,
          item_costs: [],
        },
      }
    }

    const itemCosts = await this.calculateItemCosts(bom.bom_items, year)
    const totalCost = itemCosts.reduce((sum, item) => sum + item.total_cost, 0)

    console.log('成本计算完成，总成本:', totalCost)

    // 计算完成后自动更新物料清单的成本信息
    await this.bomModel.findOneAndUpdate(
      { bom_id: bomId },
      {
        total_material_cost: totalCost,
        lastChangeTime: new Date(),
        // 更新物料项的成本信息
        bom_items: bom.bom_items.map(item => {
          const costInfo = itemCosts.find(cost => cost.material_id === item.material_id)
          return {
            ...item,
            unit_price: costInfo?.unit_price || item.unit_price,
            total_cost: costInfo?.total_cost || item.total_cost,
          }
        })
      },
      { new: true }
    ).exec()

    console.log('物料清单成本信息已更新到数据库')

    return {
      data: {
        total_cost: totalCost,
        item_costs: itemCosts,
      },
    }
  }

  // 复制物料清单
  async copyBom(sourceBomId: string, targetClothingId: string, targetYear: string): Promise<BillOfMaterials> {
    console.log('复制物料清单，源BOM ID:', sourceBomId)

    // 修复查询条件：使用bom_id字段而不是_id字段
    const sourceBom = await this.bomModel.findOne({ bom_id: sourceBomId }).exec()
    if (!sourceBom) {
      throw new NotFoundException(`物料清单 ${sourceBomId} 不存在`)
    }

    console.log('找到源物料清单:', sourceBom.bom_id)

    // 生成新的BOM ID
    const newBomId = `BOM_${targetClothingId}_${targetYear}`
    console.log('生成新的BOM ID:', newBomId)

    const newBomData = {
      bom_id: newBomId,
      clothing_id: targetClothingId,
      bom_year: targetYear,
      version: '1.0',
      bom_items: sourceBom.bom_items,
      status: 'draft',
      remark: `从 ${sourceBom.bom_id} 复制`,
    }

    // 使用saveOrUpdate方法，避免重复键错误
    console.log('开始保存复制的物料清单')
    return this.saveOrUpdate(newBomData)
  }

  // 获取年份选项
  async getYearOptions() {
    const result = await this.bomModel.distinct('bom_year').exec()
    const stringResult = result as string[]
    stringResult.sort((a, b) => b.localeCompare(a))

    return {
      data: {
        data: stringResult,
        total: stringResult.length,
      },
    }
  }

  // 私有方法：计算总成本
  private async calculateTotalCost(bomItems: any[], year: string): Promise<number> {
    const itemCosts = await this.calculateItemCosts(bomItems, year)
    return itemCosts.reduce((sum, item) => sum + item.total_cost, 0)
  }

  // 私有方法：计算各项成本
  private async calculateItemCosts(bomItems: any[], year: string) {
    const itemCosts = []

    for (const item of bomItems) {
      const material = await this.materialModel.findOne({ material_id: item.material_id }).exec()
      let unitPrice = item.unit_price || 0

      if (material && material.yearly_prices) {
        // 查找指定年份的价格
        const yearlyPrice = material.yearly_prices.find(yp => yp.year === year)
        if (yearlyPrice) {
          unitPrice = yearlyPrice.price
        } else if (material.current_price) {
          unitPrice = material.current_price
        }
      }

      const totalCost = item.quantity * unitPrice

      itemCosts.push({
        material_id: item.material_id,
        material_name: material?.material_name || '',
        quantity: item.quantity,
        unit: item.unit,
        unit_price: unitPrice,
        total_cost: totalCost,
      })
    }

    return itemCosts
  }
}
