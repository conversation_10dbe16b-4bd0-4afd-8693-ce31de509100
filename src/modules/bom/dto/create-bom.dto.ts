import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsString, IsNumber, IsOptional, IsArray } from 'class-validator'
import { BomItem } from '../../../models/billOfMaterials.model'

export class CreateBomDto {
  @ApiProperty({ description: '物料清单编号' })
  @IsString()
  bom_id: string

  @ApiProperty({ description: '服装ID' })
  @IsString()
  clothing_id: string

  @ApiProperty({ description: '所属年份' })
  @IsString()
  bom_year: string

  @ApiPropertyOptional({ description: '版本号' })
  @IsOptional()
  @IsString()
  version?: string

  @ApiPropertyOptional({ description: '物料清单项' })
  @IsOptional()
  @IsArray()
  bom_items?: BomItem[]

  @ApiPropertyOptional({ description: '总物料成本' })
  @IsOptional()
  @IsNumber()
  total_material_cost?: number

  @ApiPropertyOptional({ description: '状态' })
  @IsOptional()
  @IsString()
  status?: string

  @ApiPropertyOptional({ description: '备注' })
  @IsOptional()
  @IsString()
  remark?: string
}
