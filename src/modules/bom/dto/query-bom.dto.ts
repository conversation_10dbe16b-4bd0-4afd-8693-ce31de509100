import { ApiPropertyOptional } from '@nestjs/swagger'
import { IsOptional, IsString, IsNumber } from 'class-validator'
import { Transform } from 'class-transformer'

export class QueryBomDto {
  @ApiPropertyOptional({ description: '服装ID' })
  @IsOptional()
  @IsString()
  clothing_id?: string

  @ApiPropertyOptional({ description: '所属年份' })
  @IsOptional()
  @IsString()
  bom_year?: string

  @ApiPropertyOptional({ description: '状态' })
  @IsOptional()
  @IsString()
  status?: string

  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  page?: number = 1

  @ApiPropertyOptional({ description: '每页数量', default: 10 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  limit?: number = 10
}
