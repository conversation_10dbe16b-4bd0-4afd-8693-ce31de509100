import { Modu<PERSON> } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { BomService } from './bom.service'
import { BomController } from './bom.controller'
import { BillOfMaterialsSchema } from '../../models/billOfMaterials.model'
import { MaterialSchema } from '../../models/material.model'

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'BillOfMaterials', schema: BillOfMaterialsSchema },
      { name: 'Material', schema: MaterialSchema },
    ]),
  ],
  controllers: [BomController],
  providers: [BomService],
  exports: [BomService],
})
export class BomModule {}
