import { Module } from '@nestjs/common'
import { ModelsModule } from '../../models/models.module'
import { DivisionWorkController } from './division-work.controller'
import { DivisionWorkService } from './division-work.service'

@Module({
  imports: [ModelsModule],
  controllers: [DivisionWorkController],
  providers: [DivisionWorkService],
  exports: [DivisionWorkService],
})
export class DivisionWorkModule {}
