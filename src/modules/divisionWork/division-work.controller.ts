import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpException,
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger'
import { DivisionWorkService } from './division-work.service'
import { CreateDivisionWorkDto, UpdateDivisionWorkDto, QueryDivisionWorkDto } from './dto'
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard'

@ApiTags('分工管理')
@Controller('/division-work')
@UseGuards(JwtAuthGuard)
export class DivisionWorkController {
  constructor(private readonly divisionWorkService: DivisionWorkService) {}

  @Post()
  @ApiOperation({ summary: '创建分工' })
  async create(@Body() createDivisionWorkDto: CreateDivisionWorkDto) {
    try {
      const result = await this.divisionWorkService.create(createDivisionWorkDto)
      return {
        code: HttpStatus.CREATED,
        data: result,
        message: '创建分工成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '创建分工失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Get('year-options')
  @ApiOperation({ summary: '获取年份选项列表' })
  async getYearOptions() {
    try {
      const years = await this.divisionWorkService.getYearOptions()
      return { data: { years } }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取年份选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Get('group-options')
  @ApiOperation({ summary: '获取组名选项列表' })
  async getGroupOptions() {
    try {
      const groups = await this.divisionWorkService.getGroupOptions()
      return { data: { groups } }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取组名选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Get()
  @ApiOperation({ summary: '获取分工列表' })
  async findAll(@Query() queryDivisionWorkDto: QueryDivisionWorkDto) {
    try {
      console.log('前端传入的查询参数：', JSON.stringify(queryDivisionWorkDto))
      const result = await this.divisionWorkService.findAll(queryDivisionWorkDto)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '获取分工列表成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取分工列表失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Get(':id')
  @ApiOperation({ summary: '获取分工详情' })
  @ApiParam({ name: 'id', description: '分工ID' })
  async findOne(@Param('id') id: string) {
    try {
      const result = await this.divisionWorkService.findOne(id)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '获取分工详情成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取分工详情失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Get('clothing/:clothingId')
  @ApiOperation({ summary: '根据服装ID获取分工' })
  @ApiParam({ name: 'clothingId', description: '服装ID' })
  async findByClothingId(@Param('clothingId') clothingId: string) {
    try {
      const result = await this.divisionWorkService.findByClothingId(clothingId)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '获取分工成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取分工失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新分工' })
  @ApiParam({ name: 'id', description: '分工ID' })
  async update(@Param('id') id: string, @Body() updateDivisionWorkDto: UpdateDivisionWorkDto) {
    try {
      const result = await this.divisionWorkService.update(id, updateDivisionWorkDto)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '更新分工成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '更新分工失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除分工' })
  @ApiParam({ name: 'id', description: '分工ID' })
  async remove(@Param('id') id: string) {
    try {
      const result = await this.divisionWorkService.remove(id)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '删除分工成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '删除分工失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }
}
