import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsString, IsNumber, IsOptional } from 'class-validator'
import { Type } from 'class-transformer'

export class CreateDivisionWorkDto {
  @ApiProperty({ description: '所属年份' })
  @IsString()
  division_work_year: string

  @ApiProperty({ description: '分组工作编号' })
  @IsString()
  division_work_id: string

  @ApiProperty({ description: '服装编号' })
  @IsString()
  clothing_id: string

  @ApiPropertyOptional({ description: '组名' })
  @IsString()
  @IsOptional()
  group_name?: string

  @ApiPropertyOptional({ description: '件数' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  pcs?: number

  @ApiPropertyOptional({ description: '是否完成 (0-未完成, 1-已完成)' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  is_complete?: number

  @ApiPropertyOptional({ description: '工艺细节' })
  @IsString()
  @IsOptional()
  craft_details?: string
}
