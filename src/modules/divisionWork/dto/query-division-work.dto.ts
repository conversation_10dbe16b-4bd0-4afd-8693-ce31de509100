import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsOptional, IsArray, IsNumber } from 'class-validator'
import { Type, Transform } from 'class-transformer'

export class QueryDivisionWorkDto {
  @ApiProperty({ description: '所属年份', required: false })
  @IsString()
  @IsOptional()
  @Transform(({ value }: { value: any }) => (value === '' ? undefined : value))
  division_work_year?: string

  @ApiProperty({ description: '所属年份列表', required: false, type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform(({ value }: { value: any }) => {
    if (typeof value === 'string') {
      return value.split(',').filter(Boolean)
    }
    return value
  })
  division_work_years?: string[]

  @ApiProperty({ description: '分工编号', required: false })
  @IsString()
  @IsOptional()
  @Transform(({ value }: { value: any }) => (value === '' ? undefined : value))
  division_work_id?: string

  @ApiProperty({ description: '服装编号', required: false })
  @IsString()
  @IsOptional()
  @Transform(({ value }: { value: any }) => (value === '' ? undefined : value))
  clothing_id?: string

  @ApiProperty({ description: '服装名称', required: false })
  @IsString()
  @IsOptional()
  @Transform(({ value }: { value: any }) => (value === '' ? undefined : value))
  clothing_name?: string

  @ApiProperty({ description: '组名', required: false })
  @IsString()
  @IsOptional()
  @Transform(({ value }: { value: any }) => (value === '' ? undefined : value))
  group_name?: string

  @ApiProperty({ description: '是否完成 (0-未完成, 1-已完成)', required: false })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  @Transform(({ value }: { value: any }) => {
    if (value === undefined || value === '') return undefined
    return Number(value)
  })
  is_complete?: number

  @ApiProperty({ description: '页码', default: 1, required: false })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  page?: number = 1

  @ApiProperty({ description: '每页数量', default: 10, required: false })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  limit?: number = 10
}
