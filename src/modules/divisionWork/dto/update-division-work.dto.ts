import { ApiPropertyOptional } from '@nestjs/swagger'
import { IsString, IsNumber, IsOptional } from 'class-validator'
import { Type } from 'class-transformer'

export class UpdateDivisionWorkDto {
  @ApiPropertyOptional({ description: '所属年份' })
  @IsString()
  @IsOptional()
  division_work_year?: string

  @ApiPropertyOptional({ description: '服装编号' })
  @IsString()
  @IsOptional()
  clothing_id?: string

  @ApiPropertyOptional({ description: '组名' })
  @IsString()
  @IsOptional()
  group_name?: string

  @ApiPropertyOptional({ description: '件数' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  pcs?: number

  @ApiPropertyOptional({ description: '是否完成 (0-未完成, 1-已完成)' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  is_complete?: number

  @ApiPropertyOptional({ description: '工艺细节' })
  @IsString()
  @IsOptional()
  craft_details?: string
}
