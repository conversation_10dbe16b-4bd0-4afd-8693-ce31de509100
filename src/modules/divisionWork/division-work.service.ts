import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, PipelineStage } from 'mongoose'
import { DivisionWork } from '../../models/divisionWork.model'
import { Clothing } from '../../models/clothing.model'
import { CreateDivisionWorkDto, UpdateDivisionWorkDto, QueryDivisionWorkDto } from './dto'

@Injectable()
export class DivisionWorkService {
  constructor(
    @InjectModel('DivisionWork') private readonly divisionWorkModel: Model<DivisionWork>,
    @InjectModel('Clothing') private readonly clothingModel: Model<Clothing>
  ) {}

  // 创建分工
  async create(createDivisionWorkDto: CreateDivisionWorkDto): Promise<DivisionWork> {
    const divisionWork = new this.divisionWorkModel(createDivisionWorkDto)
    return divisionWork.save()
  }

  // 获取分工列表
  async findAll(queryDivisionWorkDto: QueryDivisionWorkDto): Promise<{
    total: number
    page: number
    limit: number
    data: any[]
  }> {
    console.log('前端传入的查询参数：', JSON.stringify(queryDivisionWorkDto))

    // 处理可能的 division_work_years[] 参数
    const rawQuery = queryDivisionWorkDto as any // 使用 any 类型绕过 TypeScript 检查
    let yearsArray: string[] = []

    if (rawQuery['division_work_years[]']) {
      // 如果是字符串，转换为数组
      if (typeof rawQuery['division_work_years[]'] === 'string') {
        yearsArray = [rawQuery['division_work_years[]']]
      } else if (Array.isArray(rawQuery['division_work_years[]'])) {
        yearsArray = rawQuery['division_work_years[]']
      }
      // 将处理后的数组赋值给 division_work_years
      queryDivisionWorkDto.division_work_years = yearsArray
    }

    const {
      division_work_year,
      division_work_years,
      division_work_id,
      clothing_id,
      clothing_name,
      group_name,
      is_complete,
      page = 1,
      limit = 10,
    } = queryDivisionWorkDto

    // 构建查询条件
    const matchStage: any = {}

    // 添加年份条件
    if (division_work_year) {
      matchStage.division_work_year = division_work_year
    } else if (division_work_years && division_work_years.length > 0) {
      matchStage.division_work_year = { $in: division_work_years }
    }
    console.log('添加的年份条件：', matchStage.division_work_year)

    // 添加分工编号条件
    if (division_work_id) {
      matchStage.division_work_id = { $regex: division_work_id, $options: 'i' }
    }

    // 添加服装编号条件
    if (clothing_id) {
      matchStage.clothing_id = { $regex: clothing_id, $options: 'i' }
    }

    // 添加组名条件
    if (group_name) {
      matchStage.group_name = { $regex: group_name, $options: 'i' }
    }

    // 添加是否完成条件
    if (is_complete !== undefined) {
      const numericValue = Number(is_complete)

      // 如果是查询已完成的记录 (is_complete = 1)
      if (numericValue === 1) {
        matchStage.is_complete = 1
      }
      // 如果是查询未完成的记录 (is_complete = 0)，包括所有非1的情况
      else if (numericValue === 0) {
        matchStage.is_complete = { $ne: 1 }
      }

      console.log('添加的完成状态条件：', JSON.stringify(matchStage.is_complete), '(类型：', typeof matchStage.is_complete, ')')
    }

    // 计算分页
    const skip = (page - 1) * limit

    console.log('构建的数据库查询条件：', JSON.stringify(matchStage))

    // 构建基础聚合管道（不包含分页）
    const basePipeline: PipelineStage[] = [
      // 匹配条件
      { $match: matchStage },
      // 按 _id 倒序排序
      { $sort: { _id: -1 } },
      // 与 clothing 集合关联
      {
        $lookup: {
          from: 'clothing',
          localField: 'clothing_id',
          foreignField: 'clothing_id',
          as: 'clothing_info'
        }
      },
      // 展开 clothing_info 数组
      {
        $unwind: {
          path: '$clothing_info',
          preserveNullAndEmptyArrays: true
        }
      }
    ]

    // 如果有服装名称查询条件，添加额外的匹配条件
    if (clothing_name) {
      console.log('添加服装名称条件：', clothing_name)
      basePipeline.push({
        $match: {
          'clothing_info.clothing_name': { $regex: clothing_name, $options: 'i' }
        }
      } as PipelineStage)
    }

    // 完整的查询管道（包含分页和投影）
    const pipeline: PipelineStage[] = [
      ...basePipeline,
      // 分页
      { $skip: skip },
      { $limit: Number(limit) },
      // 添加 clothing 集合中的字段
      {
        $project: {
          _id: 1,
          division_work_year: 1,
          division_work_id: 1,
          clothing_id: 1,
          group_name: 1,
          pcs: 1,
          is_complete: 1,
          createTime: 1,
          lastChangeTime: 1,
          clothing_name: '$clothing_info.clothing_name',
          style: '$clothing_info.style',
          pocket_type: '$clothing_info.pocket_type',
          craft_details: '$clothing_info.craft_details'
        }
      }
    ]

    // 执行聚合查询
    const data = await this.divisionWorkModel.aggregate(pipeline).exec()

    // 使用基础管道创建计数管道
    const countPipeline: PipelineStage[] = [...basePipeline]

    // 添加计数阶段
    countPipeline.push({ $count: 'total' } as PipelineStage)

    console.log('计数管道：', JSON.stringify(countPipeline))

    const totalResult = await this.divisionWorkModel.aggregate(countPipeline).exec()
    const total = totalResult.length > 0 ? totalResult[0].total : 0

    console.log(`查询结果：找到 ${total} 条记录`)
    console.log('返回的数据：', data)

    return {
      total,
      page: Number(page),
      limit: Number(limit),
      data,
    }
  }

  // 获取单个分工
  async findOne(id: string): Promise<DivisionWork> {
    const divisionWork = await this.divisionWorkModel.findById(id).exec()
    if (!divisionWork) {
      throw new NotFoundException(`分工ID ${id} 不存在`)
    }
    return divisionWork
  }

  // 更新分工
  async update(id: string, updateDivisionWorkDto: UpdateDivisionWorkDto): Promise<DivisionWork> {
    const updatedDivisionWork = await this.divisionWorkModel
      .findByIdAndUpdate(id, { ...updateDivisionWorkDto, lastChangeTime: new Date() }, { new: true })
      .exec()
    if (!updatedDivisionWork) {
      throw new NotFoundException(`分工ID ${id} 不存在`)
    }
    return updatedDivisionWork
  }

  // 删除分工
  async remove(id: string): Promise<{ success: boolean; message: string }> {
    const result = await this.divisionWorkModel.findByIdAndDelete(id).exec()
    if (!result) {
      throw new NotFoundException(`分工ID ${id} 不存在`)
    }
    return { success: true, message: '删除成功' }
  }

  // 获取年份选项
  async getYearOptions(): Promise<string[]> {
    const years = await this.divisionWorkModel.distinct('division_work_year').exec()
    return years.sort((a, b) => b.localeCompare(a)) // 降序排列
  }

  // 获取组名选项
  async getGroupOptions(): Promise<string[]> {
    const groups = await this.divisionWorkModel.distinct('group_name').exec()
    return groups.filter(group => group) // 过滤掉空值
  }

  // 根据服装ID查询分工
  async findByClothingId(clothingId: string): Promise<DivisionWork[]> {
    return this.divisionWorkModel.find({ clothing_id: clothingId }).exec()
  }
}
