import { IsString, IsNotEmpty, IsOptional } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'

export class MiniProgramLoginDto {
  @ApiProperty({ description: '微信授权码', required: false })
  @IsOptional()
  @IsString()
  code?: string

  @ApiProperty({ description: '微信OpenID', required: false })
  @IsOptional()
  @IsString()
  wxOpenId?: string

  @ApiProperty({ description: '用户名', required: false })
  @IsOptional()
  @IsString()
  userName?: string

  @ApiProperty({ description: '密码', required: false })
  @IsOptional()
  @IsString()
  userPwd?: string

  @ApiProperty({ description: '微信昵称', required: false })
  @IsOptional()
  @IsString()
  wxNickName?: string
}

export class GetOpenIdDto {
  @ApiProperty({ description: '微信授权码' })
  @IsString()
  @IsNotEmpty({ message: '授权码不能为空' })
  code: string
}

export class AddUserDto {
  @ApiProperty({ description: '用户名' })
  @IsString()
  @IsNotEmpty({ message: '用户名不能为空' })
  userName: string

  @ApiProperty({ description: '密码' })
  @IsString()
  @IsNotEmpty({ message: '密码不能为空' })
  userPwd: string

  @ApiProperty({ description: '微信OpenID' })
  @IsString()
  @IsNotEmpty({ message: 'OpenID不能为空' })
  wxOpenId: string

  @ApiProperty({ description: '微信昵称' })
  @IsString()
  @IsNotEmpty({ message: '微信昵称不能为空' })
  wxNickName: string

  @ApiProperty({ description: '操作类型' })
  @IsString()
  @IsNotEmpty({ message: '操作类型不能为空' })
  action: string
}
