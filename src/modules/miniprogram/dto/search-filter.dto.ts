import { IsString, IsOptional, IsEnum } from 'class-validator'

// 搜索类型枚举
export enum SearchType {
  CLOTHING = 'clothing',
  OEM_CLOTHING = 'oem_clothing'
}

// 组合搜索筛选条件 DTO
export class SearchFilterDto {
  @IsEnum(SearchType)
  search_type: SearchType // 搜索类型：clothing 或 oem_clothing

  // 本厂服装筛选字段
  @IsString()
  @IsOptional()
  clothing_year?: string

  @IsString()
  @IsOptional()
  supplier?: string // 通过 fabric_group_id 关联查询

  @IsString()
  @IsOptional()
  group_classification?: string // 通过 fabric_group_id 关联查询

  @IsString()
  @IsOptional()
  long_or_short_sleeve?: string

  @IsString()
  @IsOptional()
  size?: string

  @IsString()
  @IsOptional()
  style?: string

  @IsString()
  @IsOptional()
  pocket_type?: string

  // OEM服装筛选字段
  @IsString()
  @IsOptional()
  oem_clothing_year?: string

  @IsString()
  @IsOptional()
  oem_supplier?: string

  @IsString()
  @IsOptional()
  classification?: string

  @IsString()
  @IsOptional()
  oem_size?: string

  @IsString()
  @IsOptional()
  oem_style?: string
}

// 可用筛选选项接口
export interface AvailableFilters {
  // 本厂服装选项
  clothing_years?: string[]
  suppliers?: string[]
  group_classifications?: string[]
  long_or_short_sleeves?: string[]
  sizes?: string[]
  styles?: string[]
  pocket_types?: string[]

  // OEM服装选项
  oem_clothing_years?: string[]
  oem_suppliers?: string[]
  classifications?: string[]
  oem_sizes?: string[]
  oem_styles?: string[]
}
