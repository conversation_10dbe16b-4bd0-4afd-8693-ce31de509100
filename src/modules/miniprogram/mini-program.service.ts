import { Injectable, UnauthorizedException, ConflictException, HttpException, HttpStatus } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { JwtService } from '@nestjs/jwt'
import { ConfigService } from '@nestjs/config'
import { User } from '../../models/user.model'
import { Clothing } from '../../models/clothing.model'
import { OemClothing } from '../../models/oemClothing.model'
import { FabricGroup } from '../../models/fabricGroup.model'
import { Transportation } from '../../models/transportation.model'
import { TransportationDetail } from '../../models/transportationDetail.model'
import { Warehouse } from '../../models/warehouse.model'

import { Product } from '../../models/product.model'
import { Package } from '../../models/package.model'
import { OperationLog } from '../../models/operationLog.model'
import { MiniProgramLoginDto, GetOpenIdDto, AddUserDto, SearchFilterDto, AvailableFilters, SearchType } from './dto'
import { WarehouseManagementService } from './warehouse/warehouse-management.service'
import * as bcrypt from 'bcrypt'
import axios from 'axios'

@Injectable()
export class MiniProgramService {
  constructor(
    @InjectModel('User') private userModel: Model<User>,
    @InjectModel('Clothing') private clothingModel: Model<Clothing>,
    @InjectModel('OemClothing') private oemClothingModel: Model<OemClothing>,
    @InjectModel('FabricGroup') private fabricGroupModel: Model<FabricGroup>,
    @InjectModel('Transportation') private transportationModel: Model<Transportation>,
    @InjectModel('TransportationDetail') private transportationDetailModel: Model<TransportationDetail>,
    @InjectModel('Warehouse') private warehouseModel: Model<Warehouse>,
    // 新仓库管理系统模型
    @InjectModel('Product') private productModel: Model<Product>,
    @InjectModel('Package') private packageModel: Model<Package>,
    @InjectModel('OperationLog') private operationLogModel: Model<OperationLog>,
    private jwtService: JwtService,
    private configService: ConfigService,
    private warehouseManagementService: WarehouseManagementService
  ) {}

  /**
   * 获取微信OpenID
   */
  async getOpenId(getOpenIdDto: GetOpenIdDto) {
    try {
      const { code } = getOpenIdDto
      const appId = this.configService.get<string>('WECHAT_APPID')
      const appSecret = this.configService.get<string>('WECHAT_SECRET')

      if (!appId || !appSecret) {
        throw new HttpException('微信配置未设置', HttpStatus.INTERNAL_SERVER_ERROR)
      }

      const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${appId}&secret=${appSecret}&js_code=${code}&grant_type=authorization_code`

      const response = await axios.get(url)

      if (response.data.errcode) {
        throw new HttpException(`微信授权失败: ${response.data.errmsg}`, HttpStatus.BAD_REQUEST)
      }

      return {
        code: 200,
        data: {
          openid: response.data.openid,
          session_key: response.data.session_key
        },
        message: '获取OpenID成功'
      }
    } catch (error) {
      console.error('获取OpenID失败:', error)
      throw new HttpException('获取OpenID失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 微信小程序登录
   */
  async login(loginDto: MiniProgramLoginDto) {
    try {
      const { wxOpenId, userName, userPwd } = loginDto

      // 如果只有 wxOpenId，尝试通过 OpenID 登录
      if (wxOpenId && !userName && !userPwd) {
        const user = await this.userModel.findOne({ wxOpenId }).exec()
        if (user) {
          const token = this.generateToken(user)
          return {
            code: 200,
            data: { token },
            message: '登录成功'
          }
        } else {
          return {
            code: 404,
            message: '用户不存在，请先注册'
          }
        }
      }

      // 如果有用户名和密码，进行常规登录验证
      if (userName && userPwd) {
        const user = await this.validateUser(userName, userPwd)

        // 如果提供了 wxOpenId，更新用户的 OpenID
        if (wxOpenId && user) {
          await this.userModel.findByIdAndUpdate(user.id, { wxOpenId }).exec()
        }

        const token = this.generateToken(user)
        return {
          code: 200,
          data: { token },
          message: '登录成功'
        }
      }

      throw new UnauthorizedException('登录参数不完整')
    } catch (error) {
      console.error('登录失败:', error)
      if (error instanceof UnauthorizedException) {
        return {
          code: 401,
          message: error.message
        }
      }
      throw new HttpException('登录失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 添加新用户
   */
  async addNewUser(addUserDto: AddUserDto) {
    try {
      const { userName, userPwd, wxOpenId, wxNickName } = addUserDto

      // 检查用户是否已存在
      const existingUser = await this.userModel.findOne({
        $or: [{ userName }, { wxOpenId }]
      }).exec()

      if (existingUser) {
        throw new ConflictException('用户已存在')
      }

      // 创建新用户
      const hashedPassword = this.hashPassword(userPwd)
      const now = new Date()

      const newUser = new this.userModel({
        userName,
        userPwd: hashedPassword,
        wxOpenId,
        wxNickName,
        createTime: now,
        lastLoginTime: now
      })

      await newUser.save()

      return {
        code: 200,
        message: '用户创建成功'
      }
    } catch (error) {
      console.error('创建用户失败:', error)
      if (error instanceof ConflictException) {
        return {
          code: 409,
          message: error.message
        }
      }
      throw new HttpException('创建用户失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 生成JWT令牌
   */
  private generateToken(user: any) {
    const payload = {
      sub: user._id || user.id,
      userName: user.userName,
      wxOpenId: user.wxOpenId
    }
    return this.jwtService.sign(payload)
  }

  /**
   * 验证用户
   */
  private async validateUser(userName: string, password: string) {
    const user = await this.userModel.findOne({ userName }).exec()

    if (!user) {
      throw new UnauthorizedException('账号或密码错误')
    }

    if (!user.userPwd) {
      throw new UnauthorizedException('账号或密码错误')
    }

    const isPasswordValid = this.comparePassword(password, user.userPwd)

    if (!isPasswordValid) {
      throw new UnauthorizedException('账号或密码错误')
    }

    // 更新最后登录时间
    await this.userModel.findByIdAndUpdate(user._id, { lastLoginTime: new Date() }).exec()

    const { userPwd, ...result } = user.toObject()
    return result
  }

  /**
   * 密码哈希
   */
  private hashPassword(password: string): string {
    return bcrypt.hashSync(password, 10)
  }

  /**
   * 密码比较
   */
  private comparePassword(password: string, hash: string): boolean {
    return bcrypt.compareSync(password, hash)
  }

  // ==================== 仓库管理相关方法 ====================

  /**
   * 获取仓库列表
   */
  async getWarehouseList(params: any = {}) {
    try {
      const { page = 1, limit = 20, region, status = 'active' } = params
      const skip = (page - 1) * limit

      const query: any = { status }
      if (region) {
        query.region = region
      }

      const warehouses = await this.warehouseModel
        .find(query)
        .sort({ createTime: -1 })
        .skip(skip)
        .limit(limit)
        .exec()

      const total = await this.warehouseModel.countDocuments(query).exec()

      return {
        code: 200,
        data: {
          list: warehouses,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        },
        message: '获取仓库列表成功'
      }
    } catch (error) {
      console.error('获取仓库列表失败:', error)
      throw new HttpException('获取仓库列表失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 获取仓库详情
   */
  async getWarehouseDetail(warehouseId: string) {
    try {
      const warehouse = await this.warehouseModel.findOne({ warehouse_id: warehouseId }).exec()

      if (!warehouse) {
        return {
          code: 404,
          message: '仓库不存在'
        }
      }

      return {
        code: 200,
        data: warehouse,
        message: '获取仓库详情成功'
      }
    } catch (error) {
      console.error('获取仓库详情失败:', error)
      throw new HttpException('获取仓库详情失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 创建仓库
   */
  async createWarehouse(warehouseData: any) {
    try {
      // 生成仓库ID
      const warehouseId = `WH${Date.now()}`

      const newWarehouse = new this.warehouseModel({
        ...warehouseData,
        warehouse_id: warehouseId,
        createTime: new Date(),
        lastChangeTime: new Date()
      })

      await newWarehouse.save()

      return {
        code: 200,
        data: newWarehouse,
        message: '创建仓库成功'
      }
    } catch (error) {
      console.error('创建仓库失败:', error)
      throw new HttpException('创建仓库失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 更新仓库信息
   */
  async updateWarehouse(warehouseId: string, updateData: any) {
    try {
      const updatedWarehouse = await this.warehouseModel
        .findOneAndUpdate(
          { warehouse_id: warehouseId },
          { ...updateData, lastChangeTime: new Date() },
          { new: true }
        )
        .exec()

      if (!updatedWarehouse) {
        return {
          code: 404,
          message: '仓库不存在'
        }
      }

      return {
        code: 200,
        data: updatedWarehouse,
        message: '更新仓库成功'
      }
    } catch (error) {
      console.error('更新仓库失败:', error)
      throw new HttpException('更新仓库失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 删除仓库（软删除）- 使用新的仓库管理系统
   */
  async deleteWarehouse(warehouseId: string) {
    try {
      // 检查仓库是否有库存 - 使用新的包裹模型
      const hasStock = await this.packageModel
        .findOne({ warehouse_id: warehouseId, status: { $in: ['in_stock', 'partially_shipped'] } })
        .exec()

      if (hasStock) {
        return {
          code: 400,
          message: '仓库内有库存，无法删除'
        }
      }

      const deletedWarehouse = await this.warehouseModel
        .findOneAndUpdate(
          { warehouse_id: warehouseId },
          { status: 'deleted', lastChangeTime: new Date() },
          { new: true }
        )
        .exec()

      if (!deletedWarehouse) {
        return {
          code: 404,
          message: '仓库不存在'
        }
      }

      return {
        code: 200,
        message: '删除仓库成功'
      }
    } catch (error) {
      console.error('删除仓库失败:', error)
      throw new HttpException('删除仓库失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  // ==================== 入库管理相关方法 ====================

  /**
   * 创建入库单 - 使用新的仓库管理系统
   */
  async createInboundOrder(orderData: any) {
    try {
      console.log('新仓库管理系统 - 接收到的入库单数据:', JSON.stringify(orderData, null, 2))

      // 使用新的仓库管理服务进行入库操作
      const result = await this.warehouseManagementService.inbound({
        transportation_id: orderData.transportation_id,
        inbound_items: orderData.inbound_items,
        operator: orderData.operator,
        operation_date: orderData.operation_date
      })
      console.log('新仓库管理系统 - 入库操作结果:', JSON.stringify(result, null, 2))


      if (result.code === 200) {
        // 更新货运单状态
        await this.transportationModel
          .findOneAndUpdate(
            { transportation_id: orderData.transportation_id },
            { inbound_status: orderData.inbound_status }
          )
          .exec()
      }

      return result
    } catch (error) {
      console.error('创建入库单失败:', error)
      throw new HttpException('创建入库单失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 获取入库单列表 - 使用新的仓库管理系统
   */
  async getInboundOrderList(params: any = {}) {
    try {
      const { page = 1, limit = 20, warehouse_id, operation_type = 'inbound' } = params

      // 使用新的操作日志获取入库记录
      const result = await this.warehouseManagementService.getOperationLogsSummary({
        warehouse_id,
        operation_type,
        page,
        limit
      })

      return result
    } catch (error) {
      console.error('获取入库单列表失败:', error)
      throw new HttpException('获取入库单列表失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 获取入库单详情 - 使用新的仓库管理系统
   */
  async getInboundOrderDetail(inboundOrderId: string) {
    try {
      // 通过操作日志查找入库记录
      const result = await this.operationLogModel
        .findOne({
          operation_type: 'inbound',
          'details.inbound_order_id': inboundOrderId
        })
        .exec()

      if (!result) {
        return {
          code: 404,
          message: '入库单不存在'
        }
      }

      return {
        code: 200,
        data: result,
        message: '获取入库单详情成功'
      }
    } catch (error) {
      console.error('获取入库单详情失败:', error)
      throw new HttpException('获取入库单详情失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  // ==================== 出库管理相关方法 ====================
  // 注意：出库功能现在使用新的仓库管理系统，通过 WarehouseManagementService 处理

  /**
   * 创建出库单 - 使用新的仓库管理系统
   */
  async createOutboundOrder(orderData: any) {
    try {
      // 使用新的仓库管理服务进行出库操作
      const result = await this.warehouseManagementService.outbound({
        warehouse_id: orderData.warehouse_id,
        items: orderData.outbound_items,
        operator: orderData.operator,
        notes: orderData.notes || '微信小程序出库操作',
        operation_date: orderData.operation_date
      })

      return result
    } catch (error) {
      console.error('创建出库单失败:', error)
      throw new HttpException('创建出库单失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 获取出库单列表 - 使用新的仓库管理系统
   */
  async getOutboundOrderList(params: any = {}) {
    try {
      const { page = 1, limit = 20, warehouse_id, operation_type = 'outbound' } = params

      // 使用新的操作日志获取出库记录
      const result = await this.warehouseManagementService.getOperationLogsSummary({
        warehouse_id,
        operation_type,
        page,
        limit
      })

      return result
    } catch (error) {
      console.error('获取出库单列表失败:', error)
      throw new HttpException('获取出库单列表失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 获取出库单详情 - 使用新的仓库管理系统
   */
  async getOutboundOrderDetail(outboundOrderId: string) {
    try {
      // 通过操作日志查找出库记录
      const result = await this.operationLogModel
        .findOne({
          operation_type: 'outbound',
          'details.outbound_order_id': outboundOrderId
        })
        .exec()

      if (!result) {
        return {
          code: 404,
          message: '出库单不存在'
        }
      }

      return {
        code: 200,
        data: result,
        message: '获取出库单详情成功'
      }
    } catch (error) {
      console.error('获取出库单详情失败:', error)
      throw new HttpException('获取出库单详情失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 获取组合搜索的可用筛选选项
   * @param activeFilters 当前已选择的筛选条件
   * @returns 返回所有字段的可用选项
   */
  async getAvailableSearchFilters(activeFilters: SearchFilterDto): Promise<AvailableFilters> {
    try {
      if (activeFilters.search_type === SearchType.CLOTHING) {
        return await this.getClothingAvailableFilters(activeFilters)
      } else {
        return await this.getOemClothingAvailableFilters(activeFilters)
      }
    } catch (error) {
      console.error('获取筛选选项失败:', error)
      throw new HttpException('获取筛选选项失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 获取本厂服装的可用筛选选项
   */
  private async getClothingAvailableFilters(activeFilters: SearchFilterDto): Promise<AvailableFilters> {
    // 构建匹配条件
    const matchConditions: any = {}

    // 处理已选择的筛选条件
    if (activeFilters.clothing_year) {
      matchConditions.clothing_year = activeFilters.clothing_year
    }
    if (activeFilters.long_or_short_sleeve) {
      matchConditions.long_or_short_sleeve = activeFilters.long_or_short_sleeve
    }
    if (activeFilters.size) {
      matchConditions.size = activeFilters.size
    }
    if (activeFilters.style) {
      matchConditions.style = activeFilters.style
    }
    if (activeFilters.pocket_type) {
      matchConditions.pocket_type = activeFilters.pocket_type
    }

    // 构建聚合管道
    const aggregationPipeline: any[] = [
      { $match: matchConditions },
      {
        $lookup: {
          from: 'fabric_group',
          localField: 'fabric_group_id',
          foreignField: 'fabric_group_id',
          as: 'fabricGroupInfo'
        }
      },
      {
        $addFields: {
          supplier: { $arrayElemAt: ['$fabricGroupInfo.supplier', 0] },
          group_classification: { $arrayElemAt: ['$fabricGroupInfo.group_classification', 0] }
        }
      }
    ]

    // 如果有供应商或分类筛选，添加额外的匹配条件
    const additionalMatch: any = {}
    if (activeFilters.supplier) {
      additionalMatch.supplier = activeFilters.supplier
    }
    if (activeFilters.group_classification) {
      additionalMatch.group_classification = { $in: [activeFilters.group_classification] }
    }

    if (Object.keys(additionalMatch).length > 0) {
      aggregationPipeline.push({ $match: additionalMatch })
    }

    // 添加分组阶段获取所有唯一组合
    aggregationPipeline.push({
      $group: {
        _id: {
          clothing_year: '$clothing_year',
          supplier: '$supplier',
          group_classification: '$group_classification',
          long_or_short_sleeve: '$long_or_short_sleeve',
          size: '$size',
          style: '$style',
          pocket_type: '$pocket_type'
        }
      }
    })

    const aggregationResult = await this.clothingModel.aggregate(aggregationPipeline)

    // 处理聚合结果，提取每个字段的唯一值
    const availableOptions = {
      clothing_years: new Set<string>(),
      suppliers: new Set<string>(),
      group_classifications: new Set<string>(),
      long_or_short_sleeves: new Set<string>(),
      sizes: new Set<string>(),
      styles: new Set<string>(),
      pocket_types: new Set<string>()
    }

    aggregationResult.forEach(group => {
      const data = group._id
      if (data.clothing_year) availableOptions.clothing_years.add(data.clothing_year)
      if (data.supplier) availableOptions.suppliers.add(data.supplier)
      if (data.group_classification && Array.isArray(data.group_classification)) {
        data.group_classification.forEach((classification: string) => {
          if (classification) availableOptions.group_classifications.add(classification)
        })
      }
      if (data.long_or_short_sleeve) availableOptions.long_or_short_sleeves.add(data.long_or_short_sleeve)
      if (data.size) availableOptions.sizes.add(data.size)
      if (data.style) availableOptions.styles.add(data.style)
      if (data.pocket_type) availableOptions.pocket_types.add(data.pocket_type)
    })

    return {
      clothing_years: Array.from(availableOptions.clothing_years).sort(),
      suppliers: Array.from(availableOptions.suppliers).sort(),
      group_classifications: Array.from(availableOptions.group_classifications).sort(),
      long_or_short_sleeves: Array.from(availableOptions.long_or_short_sleeves).sort(),
      sizes: Array.from(availableOptions.sizes).sort(),
      styles: Array.from(availableOptions.styles).sort(),
      pocket_types: Array.from(availableOptions.pocket_types).sort()
    }
  }

  /**
   * 获取OEM服装的可用筛选选项
   */
  private async getOemClothingAvailableFilters(activeFilters: SearchFilterDto): Promise<AvailableFilters> {
    // 构建匹配条件
    const matchConditions: any = {}

    // 处理已选择的筛选条件
    if (activeFilters.oem_clothing_year) {
      matchConditions.oem_clothing_year = activeFilters.oem_clothing_year
    }
    if (activeFilters.oem_supplier) {
      matchConditions.oem_supplier = activeFilters.oem_supplier
    }
    if (activeFilters.classification) {
      matchConditions.classification = activeFilters.classification
    }
    if (activeFilters.oem_size) {
      matchConditions.size = activeFilters.oem_size
    }
    if (activeFilters.oem_style) {
      matchConditions.style = activeFilters.oem_style
    }

    // 构建聚合管道
    const aggregationPipeline: any[] = [
      { $match: matchConditions },
      {
        $group: {
          _id: {
            oem_clothing_year: '$oem_clothing_year',
            oem_supplier: '$oem_supplier',
            classification: '$classification',
            size: '$size',
            style: '$style'
          }
        }
      }
    ]

    const aggregationResult = await this.oemClothingModel.aggregate(aggregationPipeline)

    // 处理聚合结果，提取每个字段的唯一值
    const availableOptions = {
      oem_clothing_years: new Set<string>(),
      oem_suppliers: new Set<string>(),
      classifications: new Set<string>(),
      oem_sizes: new Set<string>(),
      oem_styles: new Set<string>()
    }

    aggregationResult.forEach(group => {
      const data = group._id
      if (data.oem_clothing_year) availableOptions.oem_clothing_years.add(data.oem_clothing_year)
      if (data.oem_supplier) availableOptions.oem_suppliers.add(data.oem_supplier)
      if (data.classification) availableOptions.classifications.add(data.classification)
      if (data.size) availableOptions.oem_sizes.add(data.size)
      if (data.style) availableOptions.oem_styles.add(data.style)
    })

    return {
      oem_clothing_years: Array.from(availableOptions.oem_clothing_years).sort(),
      oem_suppliers: Array.from(availableOptions.oem_suppliers).sort(),
      classifications: Array.from(availableOptions.classifications).sort(),
      oem_sizes: Array.from(availableOptions.oem_sizes).sort(),
      oem_styles: Array.from(availableOptions.oem_styles).sort()
    }
  }
}
