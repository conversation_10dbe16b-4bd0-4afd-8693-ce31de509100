import { Module } from '@nestjs/common'
import { WarehouseManagementController } from './warehouse-management.controller'
import { WarehouseManagementService } from './warehouse-management.service'
import { InboundService } from './services/inbound.service'
import { OutboundService } from './services/outbound.service'
import { TransferService } from './services/transfer.service'
import { InventoryService } from './services/inventory.service'
import { ModelsModule } from '../../../models/models.module'

@Module({
  imports: [ModelsModule],
  controllers: [WarehouseManagementController],
  providers: [
    WarehouseManagementService,
    InboundService,
    OutboundService,
    TransferService,
    InventoryService
  ],
  exports: [WarehouseManagementService],
})
export class WarehouseManagementModule {}
