import { Injectable, HttpException, HttpStatus } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { Product } from '../../../../models/product.model'
import { Package } from '../../../../models/package.model'
import { OperationLog } from '../../../../models/operationLog.model'
import { Warehouse } from '../../../../models/warehouse.model'
import { Clothing } from '../../../../models/clothing.model'
import { OemClothing } from '../../../../models/oemClothing.model'
import { Transportation } from '../../../../models/transportation.model'

@Injectable()
export class TransferService {
  constructor(
    @InjectModel('Product') private productModel: Model<Product>,
    @InjectModel('Package') private packageModel: Model<Package>,
    @InjectModel('OperationLog') private operationLogModel: Model<OperationLog>,
    @InjectModel('Warehouse') private warehouseModel: Model<Warehouse>,
    @InjectModel('Clothing') private clothingModel: Model<Clothing>,
    @InjectModel('OemClothing') private oemClothingModel: Model<OemClothing>,
    @InjectModel('Transportation') private transportationModel: Model<Transportation>
  ) {}

  /**
   * 移库操作 - 不使用事务（适用于单机MongoDB）
   */
  async transfer(transferData: {
    package_codes: string[]
    from_warehouse_id: string
    to_warehouse_id: string
    to_location_code?: string
    operator: string
    notes?: string
    operation_date?: string
  }) {
    try {
      // 验证源仓库和目标仓库不能相同
      if (transferData.from_warehouse_id === transferData.to_warehouse_id) {
        throw new HttpException('源仓库和目标仓库不能相同', HttpStatus.BAD_REQUEST)
      }

      // 验证仓库是否存在
      const [fromWarehouse, toWarehouse] = await Promise.all([
        this.warehouseModel.findOne({ warehouse_id: transferData.from_warehouse_id }),
        this.warehouseModel.findOne({ warehouse_id: transferData.to_warehouse_id })
      ])

      if (!fromWarehouse) {
        throw new HttpException(`源仓库 ${transferData.from_warehouse_id} 不存在`, HttpStatus.BAD_REQUEST)
      }
      if (!toWarehouse) {
        throw new HttpException(`目标仓库 ${transferData.to_warehouse_id} 不存在`, HttpStatus.BAD_REQUEST)
      }

      const updatedPackages = []
      const operationLogs = []
      const failedPackages = []

      for (const packageCode of transferData.package_codes) {
        try {
          // 查找包裹并验证状态
          const packageDoc = await this.packageModel.findOne({
            package_code: packageCode,
            warehouse_id: transferData.from_warehouse_id,
            status: { $in: ['in_stock', 'partially_shipped'] }
          })

          if (!packageDoc) {
            console.warn(`包裹 ${packageCode} 不存在或无法移库`)
            failedPackages.push({ packageCode, reason: '包裹不存在或无法移库' })
            continue
          }

          // 检查包裹是否已在目标仓库
          if (packageDoc.warehouse_id === transferData.to_warehouse_id) {
            console.log(`包裹 ${packageCode} 已在目标仓库 ${transferData.to_warehouse_id}，无需移库`)
            continue
          }

          const fromLocation = packageDoc.location_code
          const toLocation = transferData.to_location_code || fromLocation

          // 更新包裹位置
          const updatedPackage = await this.packageModel.findOneAndUpdate(
            { package_code: packageCode },
            {
              warehouse_id: transferData.to_warehouse_id,
              location_code: toLocation,
              last_updated_at: new Date()
            },
            { new: true }
          )

          if (!updatedPackage) {
            console.warn(`更新包裹 ${packageCode} 的位置失败`)
            failedPackages.push({ packageCode, reason: '更新包裹位置失败' })
            continue
          }

          updatedPackages.push(updatedPackage)

          // 获取包裹内容信息用于日志记录
          // 移库操作：移出记录为负数，移入记录为正数
          const contentsChangesOut = updatedPackage.contents.map(content => ({
            sku: content.sku,
            product_name: content.name,
            quantity_change: -content.current_quantity, // 移出：负数
            before_quantity: content.current_quantity,
            after_quantity: 0 // 从源仓库移出，数量变为0
          }))

          const contentsChangesIn = updatedPackage.contents.map(content => ({
            sku: content.sku,
            product_name: content.name,
            quantity_change: content.current_quantity, // 移入：正数
            before_quantity: 0, // 移入目标仓库，之前数量为0
            after_quantity: content.current_quantity
          }))

          // 准备移库操作日志数据 - 创建两条记录：移出和移入
          const timestamp = transferData.operation_date ? new Date(transferData.operation_date) : new Date()

          // 移出日志记录
          const transferOutLog = {
            timestamp,
            operation_type: 'transfer_out' as const,
            operator_name: transferData.operator,
            warehouse_id: transferData.from_warehouse_id,
            package_code: packageCode,
            contents_changes: contentsChangesOut, // 记录移出的包裹内容信息
            details: {
              from_warehouse_id: transferData.from_warehouse_id,
              to_warehouse_id: transferData.to_warehouse_id,
              from_location: fromLocation,
              to_location: toLocation,
              notes: transferData.notes || `包裹从 ${fromWarehouse.name} 移出到 ${toWarehouse.name}`
            }
          }

          // 移入日志记录
          const transferInLog = {
            timestamp,
            operation_type: 'transfer_in' as const,
            operator_name: transferData.operator,
            warehouse_id: transferData.to_warehouse_id,
            package_code: packageCode,
            contents_changes: contentsChangesIn, // 记录移入的包裹内容信息
            details: {
              from_warehouse_id: transferData.from_warehouse_id,
              to_warehouse_id: transferData.to_warehouse_id,
              from_location: fromLocation,
              to_location: toLocation,
              notes: transferData.notes || `包裹从 ${fromWarehouse.name} 移入到 ${toWarehouse.name}`
            }
          }

          operationLogs.push(transferOutLog, transferInLog)
        } catch (packageError: any) {
          console.error(`处理包裹 ${packageCode} 时出错:`, packageError)
          failedPackages.push({ packageCode, reason: packageError.message || '处理失败' })
        }
      }

      // 批量创建操作日志
      if (operationLogs.length > 0) {
        try {
          await this.operationLogModel.insertMany(operationLogs)
        } catch (logError) {
          console.error('创建操作日志失败:', logError)
          // 日志创建失败不影响移库操作的成功
        }
      }

      const successCount = updatedPackages.length
      const failedCount = failedPackages.length
      const totalCount = transferData.package_codes.length

      console.log(`移库操作完成：成功 ${successCount}/${totalCount} 个包裹移至 ${transferData.to_warehouse_id}`)

      if (failedCount > 0) {
        console.warn('部分包裹移库失败:', failedPackages)
      }

      return {
        code: 200,
        data: {
          transferred_packages: successCount,
          failed_packages: failedCount,
          packages: updatedPackages,
          failed_details: failedPackages,
          from_warehouse: fromWarehouse.name,
          to_warehouse: toWarehouse.name
        },
        message: failedCount > 0
          ? `移库部分成功：${successCount}个成功，${failedCount}个失败`
          : '移库成功'
      }
    } catch (error: any) {
      console.error('移库操作失败:', error)
      throw new HttpException(error.message || '移库操作失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 移库冲正操作
   */
  async reverseTransferOperation(reverseData: {
    package_code: string
    operator: string
    notes?: string
    operation_date?: string
  }) {
    try {
      // 查找包裹
      const packageDoc = await this.packageModel.findOne({
        package_code: reverseData.package_code
      })

      if (!packageDoc) {
        throw new HttpException(`包裹 ${reverseData.package_code} 不存在`, HttpStatus.BAD_REQUEST)
      }

      // 查找最近的移库操作日志，获取原始仓库信息
      const transferLogs = await this.operationLogModel.find({
        package_code: reverseData.package_code,
        operation_type: { $in: ['transfer_out', 'transfer_in'] }
      }).sort({ timestamp: -1 }).limit(2)

      if (transferLogs.length < 2) {
        throw new HttpException(
          `包裹 ${reverseData.package_code} 没有找到完整的移库记录`,
          HttpStatus.BAD_REQUEST
        )
      }

      // 获取原始仓库ID（transfer_out记录中的warehouse_id）
      const transferOutLog = transferLogs.find(log => log.operation_type === 'transfer_out')
      const transferInLog = transferLogs.find(log => log.operation_type === 'transfer_in')

      if (!transferOutLog || !transferInLog) {
        throw new HttpException(
          `包裹 ${reverseData.package_code} 移库记录不完整`,
          HttpStatus.BAD_REQUEST
        )
      }

      const originalWarehouseId = transferOutLog.warehouse_id
      const originalLocation = transferOutLog.details?.from_location || 'A-01-F1-R1'

      // 验证仓库是否存在
      const originalWarehouse = await this.warehouseModel.findOne({
        warehouse_id: originalWarehouseId
      })

      if (!originalWarehouse) {
        throw new HttpException(
          `原始仓库 ${originalWarehouseId} 不存在`,
          HttpStatus.BAD_REQUEST
        )
      }

      // 将包裹移回原始仓库
      await this.packageModel.updateOne(
        { package_code: reverseData.package_code },
        {
          $set: {
            warehouse_id: originalWarehouseId,
            location_code: originalLocation,
            last_updated_at: new Date()
          }
        }
      )

      // 创建冲正日志 - 移出当前仓库
      const contentsChangesOut = packageDoc.contents.map((content: any) => ({
        sku: content.sku,
        product_name: content.name,
        quantity_change: -content.current_quantity,
        before_quantity: content.current_quantity,
        after_quantity: 0
      }))

      // 创建冲正日志 - 移入原始仓库
      const contentsChangesIn = packageDoc.contents.map((content: any) => ({
        sku: content.sku,
        product_name: content.name,
        quantity_change: content.current_quantity,
        before_quantity: 0,
        after_quantity: content.current_quantity
      }))

      const timestamp = reverseData.operation_date ? new Date(reverseData.operation_date) : new Date()

      // 冲正移出日志
      await this.operationLogModel.create({
        timestamp,
        operation_type: 'reversal',
        operator_name: reverseData.operator,
        warehouse_id: packageDoc.warehouse_id,
        package_code: reverseData.package_code,
        contents_changes: contentsChangesOut,
        details: {
          from_warehouse_id: packageDoc.warehouse_id,
          to_warehouse_id: originalWarehouseId,
          original_transfer_out_log_id: transferOutLog._id,
          original_transfer_in_log_id: transferInLog._id,
          notes: reverseData.notes || '移库冲正操作 - 移出'
        },
        is_reversal: true
      })

      // 冲正移入日志
      await this.operationLogModel.create({
        timestamp,
        operation_type: 'reversal',
        operator_name: reverseData.operator,
        warehouse_id: originalWarehouseId,
        package_code: reverseData.package_code,
        contents_changes: contentsChangesIn,
        details: {
          from_warehouse_id: packageDoc.warehouse_id,
          to_warehouse_id: originalWarehouseId,
          original_transfer_out_log_id: transferOutLog._id,
          original_transfer_in_log_id: transferInLog._id,
          notes: reverseData.notes || '移库冲正操作 - 移入'
        },
        is_reversal: true
      })

      return {
        code: 200,
        data: {
          reversed_package: reverseData.package_code,
          from_warehouse_id: packageDoc.warehouse_id,
          to_warehouse_id: originalWarehouseId,
          from_warehouse_name: (await this.warehouseModel.findOne({ warehouse_id: packageDoc.warehouse_id }))?.name,
          to_warehouse_name: originalWarehouse.name
        },
        message: '移库冲正成功'
      }
    } catch (error: any) {
      console.error('移库冲正操作失败:', error)
      throw new HttpException(error.message || '移库冲正操作失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
