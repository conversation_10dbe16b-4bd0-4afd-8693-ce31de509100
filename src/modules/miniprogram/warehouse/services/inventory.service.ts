import { Injectable, HttpException, HttpStatus } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { Product } from '../../../../models/product.model'
import { Package } from '../../../../models/package.model'
import { OperationLog } from '../../../../models/operationLog.model'
import { Warehouse } from '../../../../models/warehouse.model'
import { Clothing } from '../../../../models/clothing.model'
import { OemClothing } from '../../../../models/oemClothing.model'
import { Transportation } from '../../../../models/transportation.model'

@Injectable()
export class InventoryService {
  constructor(
    @InjectModel('Product') private productModel: Model<Product>,
    @InjectModel('Package') private packageModel: Model<Package>,
    @InjectModel('OperationLog') private operationLogModel: Model<OperationLog>,
    @InjectModel('Warehouse') private warehouseModel: Model<Warehouse>,
    @InjectModel('Clothing') private clothingModel: Model<Clothing>,
    @InjectModel('OemClothing') private oemClothingModel: Model<OemClothing>,
    @InjectModel('Transportation') private transportationModel: Model<Transportation>
  ) {}

  /**
   * 记录操作日志
   */
  private async logOperation(logData: any, operationDate?: string) {
    const timestamp = operationDate ? new Date(operationDate) : new Date()
    await this.operationLogModel.create({
      ...logData,
      timestamp
    })
  }

  /**
   * 更新服装库存数据
   */
  private async updateClothingStock(
    operationType: 'inbound' | 'outbound' | 'transfer' | 'inventory_gain' | 'inventory_loss',
    clothingUpdates: Array<{
      clothing_id?: string
      oem_clothing_id?: string
      quantity: number
    }>
  ) {
    try {
      for (const update of clothingUpdates) {
        if (update.clothing_id) {
          // 普通服装更新
          if (operationType === 'inbound' || operationType === 'inventory_gain') {
            // 入库或盘盈：增加到货数和库存数
            await this.clothingModel.updateOne(
              { clothing_id: update.clothing_id },
              {
                $inc: {
                  arrival_quantity: operationType === 'inbound' ? update.quantity : 0,
                  stock_quantity: update.quantity
                }
              }
            )
          } else if (operationType === 'outbound' || operationType === 'inventory_loss') {
            // 出库或盘亏：减少库存数
            await this.clothingModel.updateOne(
              { clothing_id: update.clothing_id },
              {
                $inc: {
                  stock_quantity: -update.quantity
                }
              }
            )
          }
        }

        if (update.oem_clothing_id) {
          // OEM服装更新
          if (operationType === 'inbound' || operationType === 'inventory_gain') {
            // 入库或盘盈：增加到货数和库存数
            await this.oemClothingModel.updateOne(
              { oem_clothing_id: update.oem_clothing_id },
              {
                $inc: {
                  arrival_quantity: operationType === 'inbound' ? update.quantity : 0,
                  stock_quantity: update.quantity
                }
              }
            )
          } else if (operationType === 'outbound' || operationType === 'inventory_loss') {
            // 出库或盘亏：减少库存数
            await this.oemClothingModel.updateOne(
              { oem_clothing_id: update.oem_clothing_id },
              {
                $inc: {
                  stock_quantity: -update.quantity
                }
              }
            )
          }
        }
      }
    } catch (error) {
      console.error('更新服装库存失败:', error)
      throw error
    }
  }

  /**
   * 查找或创建产品
   */
  private async findOrCreateProduct(item: any): Promise<Product> {
    // 先尝试查找现有产品
    let product = null
    
    if (item.oem === '是' && item.clothing_id) {
      product = await this.productModel.findOne({ oem_clothing_id: item.clothing_id })
    } else if (item.clothing_id) {
      product = await this.productModel.findOne({ clothing_id: item.clothing_id })
    }

    // 如果找不到，创建新产品
    if (!product) {
      const sku = item.oem === '是' ? `OEM_${item.clothing_id}` : `CLO_${item.clothing_id}`
      
      product = await this.productModel.create({
        sku,
        name: item.clothing_name || '未知产品',
        description: '',
        unit: '件',
        specs: {},
        clothing_id: item.oem === '是' ? undefined : item.clothing_id,
        oem_clothing_id: item.oem === '是' ? item.clothing_id : undefined,
        is_oem: item.oem === '是',
        images: [],
        status: 'active'
      })
    }

    return product
  }

  /**
   * 生成包裹分类码
   * @param contents 包裹内容数组
   * @returns 分类码字符串
   */
  private generateClassificationCode(contents: Array<{ sku: string; original_quantity: number }>): string {
    // 按SKU排序确保分类码的一致性
    const sortedContents = contents
      .map(content => `${content.sku}_${content.original_quantity}`)
      .sort()

    return sortedContents.join('_')
  }

  /**
   * 盘存操作 - 支持盘盈和盘亏
   */
  async inventoryCheck(inventoryData: {
    warehouse_id: string
    checks: Array<{
      contents: Array<{
        sku: string
        name: string
        original_quantity: number
        oem: string
      }>
      system_quantity: number // 账面库存数量（包数）
      actual_quantity: number // 实际库存数量（包数）
    }>
    operator: string
    notes?: string
    operation_date?: string
  }) {
    try {
      const results: Array<{
        contents: any[]
        system_quantity: number
        actual_quantity: number
        difference: number
        operation_type: 'inventory_surplus' | 'inventory_deficit' | 'no_change'
        affected_packages: string[]
      }> = []

      for (const check of inventoryData.checks) {
        const difference = check.actual_quantity - check.system_quantity

        if (difference === 0) {
          // 无差异，不需要处理
          results.push({
            contents: check.contents,
            system_quantity: check.system_quantity,
            actual_quantity: check.actual_quantity,
            difference: 0,
            operation_type: 'no_change',
            affected_packages: []
          })
          continue
        }

        const affectedPackages: string[] = []

        if (difference > 0) {
          // 盘盈：实际库存多于账面库存
          await this.handleInventorySurplus(
            inventoryData.warehouse_id,
            check.contents,
            difference,
            inventoryData.operator,
            affectedPackages,
            inventoryData.operation_date
          )

          results.push({
            contents: check.contents,
            system_quantity: check.system_quantity,
            actual_quantity: check.actual_quantity,
            difference,
            operation_type: 'inventory_surplus',
            affected_packages: affectedPackages
          })
        } else {
          // 盘亏：实际库存少于账面库存
          await this.handleInventoryDeficit(
            inventoryData.warehouse_id,
            check.contents,
            Math.abs(difference),
            inventoryData.operator,
            affectedPackages,
            inventoryData.operation_date
          )

          results.push({
            contents: check.contents,
            system_quantity: check.system_quantity,
            actual_quantity: check.actual_quantity,
            difference,
            operation_type: 'inventory_deficit',
            affected_packages: affectedPackages
          })
        }
      }

      // 更新服装库存数据（盘存操作）
      const clothingUpdates: Array<{
        clothing_id?: string
        oem_clothing_id?: string
        quantity: number
      }> = []

      // 统计盘盈和盘亏的服装数量变化
      const clothingGainMap = new Map<string, number>()
      const oemClothingGainMap = new Map<string, number>()
      const clothingLossMap = new Map<string, number>()
      const oemClothingLossMap = new Map<string, number>()

      for (const result of results) {
        if (result.operation_type === 'inventory_surplus') {
          // 盘盈：增加库存
          for (const content of result.contents) {
            const quantity = content.original_quantity * result.difference
            if (content.sku.includes('_') && content.sku.split('_')[0] === 'oem') {
              const currentQuantity = oemClothingGainMap.get(content.sku) || 0
              oemClothingGainMap.set(content.sku, currentQuantity + quantity)
            } else {
              const currentQuantity = clothingGainMap.get(content.sku) || 0
              clothingGainMap.set(content.sku, currentQuantity + quantity)
            }
          }
        } else if (result.operation_type === 'inventory_deficit') {
          // 盘亏：减少库存
          for (const content of result.contents) {
            const quantity = content.original_quantity * Math.abs(result.difference)
            if (content.sku.includes('_') && content.sku.split('_')[0] === 'oem') {
              const currentQuantity = oemClothingLossMap.get(content.sku) || 0
              oemClothingLossMap.set(content.sku, currentQuantity + quantity)
            } else {
              const currentQuantity = clothingLossMap.get(content.sku) || 0
              clothingLossMap.set(content.sku, currentQuantity + quantity)
            }
          }
        }
      }

      // 处理盘盈更新
      for (const [clothingId, quantity] of clothingGainMap) {
        clothingUpdates.push({ clothing_id: clothingId, quantity })
      }
      for (const [oemClothingId, quantity] of oemClothingGainMap) {
        clothingUpdates.push({ oem_clothing_id: oemClothingId, quantity })
      }

      // 处理盘亏更新
      for (const [clothingId, quantity] of clothingLossMap) {
        clothingUpdates.push({ clothing_id: clothingId, quantity })
      }
      for (const [oemClothingId, quantity] of oemClothingLossMap) {
        clothingUpdates.push({ oem_clothing_id: oemClothingId, quantity })
      }

      // 执行库存更新
      if (clothingUpdates.length > 0) {
        // 分别处理盘盈和盘亏
        const gainUpdates = clothingUpdates.filter((_, index) =>
          index < clothingGainMap.size + oemClothingGainMap.size
        )
        const lossUpdates = clothingUpdates.filter((_, index) =>
          index >= clothingGainMap.size + oemClothingGainMap.size
        )

        if (gainUpdates.length > 0) {
          await this.updateClothingStock('inventory_gain', gainUpdates)
        }
        if (lossUpdates.length > 0) {
          await this.updateClothingStock('inventory_loss', lossUpdates)
        }
      }

      return {
        code: 200,
        data: {
          checked_items: inventoryData.checks.length,
          results
        },
        message: '盘存完成'
      }
    } catch (error: any) {
      console.error('盘存操作失败:', error)
      throw new HttpException(error.message || '盘存操作失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 处理盘盈操作
   */
  private async handleInventorySurplus(
    warehouseId: string,
    contents: Array<{
      sku: string
      name: string
      original_quantity: number
      oem: string
    }>,
    surplusQuantity: number,
    operator: string,
    affectedPackages: string[],
    operationDate?: string
  ) {
    try {
      // 查找本仓库中相同contents但状态为shipped的包裹
      // 按照package_code倒序排列（因为出库是正序）
      const shippedPackages = await this.packageModel.find({
        warehouse_id: warehouseId,
        status: 'shipped',
        'contents.sku': { $in: contents.map(c => c.sku) }
      }).sort({ package_code: -1 }).lean()

      // 筛选出contents完全匹配的包裹（只比较sku和name、original_quantity）
      const matchingPackages = shippedPackages.filter(pkg => {
        if (pkg.contents.length !== contents.length) return false
        return contents.every(checkContent =>
          pkg.contents.some(pkgContent =>
            pkgContent.sku === checkContent.sku &&
            pkgContent.name === checkContent.name &&
            pkgContent.original_quantity === checkContent.original_quantity
          )
        )
      })

      if (matchingPackages.length === 0) {
        // 没有找到匹配的已出库包裹，创建新包裹
        await this.createSurplusPackages(warehouseId, contents, surplusQuantity, operator, affectedPackages, operationDate)
      } else {
        // 找到匹配的已出库包裹，恢复它们
        await this.restoreShippedPackages(matchingPackages, surplusQuantity, operator, affectedPackages, operationDate)
      }
    } catch (error) {
      console.error('处理盘盈操作失败:', error)
      throw error
    }
  }

  /**
   * 创建盘盈包裹
   */
  private async createSurplusPackages(
    warehouseId: string,
    contents: Array<{
      sku: string
      name: string
      original_quantity: number
      oem: string
    }>,
    surplusQuantity: number,
    operator: string,
    affectedPackages: string[],
    operationDate?: string
  ) {
    for (let i = 0; i < surplusQuantity; i++) {
      const packageCode = `SURPLUS-${warehouseId}-${Date.now()}-${i + 1}`

      // 创建包裹内容
      const packageContents = []
      for (const content of contents) {
        // 查找或创建产品
        const product = await this.findOrCreateProduct({
          clothing_id: content.sku.replace(/^(OEM_|CLO_)/, ''),
          clothing_name: content.name,
          oem: content.oem
        })

        packageContents.push({
          product_id: product._id,
          sku: content.sku,
          name: content.name,
          original_quantity: content.original_quantity,
          current_quantity: content.original_quantity,
          clothing_id: content.oem === '是' ? undefined : content.sku.replace(/^CLO_/, ''),
          oem_clothing_id: content.oem === '是' ? content.sku.replace(/^OEM_/, '') : undefined,
          is_oem: content.oem === '是'
        })
      }

      // 生成分类码
      const classificationCode = this.generateClassificationCode(packageContents)

      // 创建包裹
      const packageData = {
        package_code: packageCode,
        classification_code: classificationCode,
        package_type: packageContents.length > 1 ? 'mixed' : 'single',
        inbound_batch_code: `SURPLUS-${Date.now()}`,
        warehouse_id: warehouseId,
        location_code: 'A-01-F1-R1',
        status: 'in_stock',
        remaining_percentage: 1.0,
        contents: packageContents,
        transportation_id: '',
        series_number: 0,
        supplier: '盘盈',
        inbound_at: new Date(),
        last_updated_at: new Date()
      }

      const createdPackage = await this.packageModel.create(packageData)
      affectedPackages.push(createdPackage.package_code)

      // 记录盘盈日志
      const contentsChanges = packageContents.map(content => ({
        sku: content.sku,
        product_name: content.name,
        quantity_change: content.original_quantity,
        before_quantity: 0,
        after_quantity: content.original_quantity
      }))

      await this.logOperation({
        operation_type: 'inventory_surplus',
        operator_name: operator,
        warehouse_id: warehouseId,
        package_code: packageCode,
        contents_changes: contentsChanges,
        details: {
          classification_code: classificationCode,
          notes: '盘盈操作 - 创建新包裹'
        }
      }, operationDate)
    }
  }

  /**
   * 恢复已出库包裹 - 支持部分包裹
   */
  private async restoreShippedPackages(
    matchingPackages: any[],
    surplusQuantity: number,
    operator: string,
    affectedPackages: string[],
    operationDate?: string
  ) {
    let remainingSurplus = surplusQuantity
    let packageIndex = 0

    while (remainingSurplus > 0 && packageIndex < matchingPackages.length) {
      const pkg = matchingPackages[packageIndex]

      if (remainingSurplus >= 1) {
        // 完整包裹恢复
        await this.packageModel.updateOne(
          { package_code: pkg.package_code },
          {
            $set: {
              status: 'in_stock',
              remaining_percentage: 1.0,
              last_updated_at: new Date(),
              'contents.$[].current_quantity': '$contents.original_quantity'
            }
          }
        )

        affectedPackages.push(pkg.package_code)

        // 记录盘盈日志
        const contentsChanges = pkg.contents.map((content: any) => ({
          sku: content.sku,
          product_name: content.name,
          quantity_change: content.original_quantity,
          before_quantity: 0,
          after_quantity: content.original_quantity
        }))

        await this.logOperation({
          operation_type: 'inventory_surplus',
          operator_name: operator,
          warehouse_id: pkg.warehouse_id,
          package_code: pkg.package_code,
          contents_changes: contentsChanges,
          details: {
            classification_code: pkg.classification_code,
            notes: '盘盈操作 - 恢复已出库包裹（完整包）'
          }
        }, operationDate)

        remainingSurplus -= 1
      } else {
        // 部分包裹恢复
        const partialPercentage = remainingSurplus

        await this.packageModel.updateOne(
          { package_code: pkg.package_code },
          {
            $set: {
              status: 'partially_shipped',
              remaining_percentage: partialPercentage,
              last_updated_at: new Date()
            }
          }
        )

        // 更新包裹内容的current_quantity
        const updatedPackage = await this.packageModel.findOne({ package_code: pkg.package_code })
        if (updatedPackage) {
          for (let i = 0; i < updatedPackage.contents.length; i++) {
            updatedPackage.contents[i].current_quantity = Math.round(
              updatedPackage.contents[i].original_quantity * partialPercentage
            )
          }
          await updatedPackage.save()
        }

        affectedPackages.push(pkg.package_code)

        // 记录盘盈日志
        const contentsChanges = pkg.contents.map((content: any) => ({
          sku: content.sku,
          product_name: content.name,
          quantity_change: Math.round(content.original_quantity * partialPercentage),
          before_quantity: 0,
          after_quantity: Math.round(content.original_quantity * partialPercentage)
        }))

        await this.logOperation({
          operation_type: 'inventory_surplus',
          operator_name: operator,
          warehouse_id: pkg.warehouse_id,
          package_code: pkg.package_code,
          contents_changes: contentsChanges,
          details: {
            classification_code: pkg.classification_code,
            partial_percentage: partialPercentage,
            notes: `盘盈操作 - 恢复已出库包裹（部分包：${partialPercentage * 100}%）`
          }
        }, operationDate)

        remainingSurplus = 0
      }

      packageIndex++
    }
  }

  /**
   * 处理盘亏操作
   */
  private async handleInventoryDeficit(
    warehouseId: string,
    contents: Array<{
      sku: string
      name: string
      original_quantity: number
      oem: string
    }>,
    deficitQuantity: number,
    operator: string,
    affectedPackages: string[],
    operationDate?: string
  ) {
    try {
      // 查找本仓库中相同contents的可用包裹
      const availablePackages = await this.packageModel.find({
        warehouse_id: warehouseId,
        status: { $in: ['in_stock', 'partially_shipped'] },
        'contents.sku': { $in: contents.map(c => c.sku) }
      }).sort({ package_code: 1 }).lean()

      // 筛选出contents完全匹配的包裹
      const matchingPackages = availablePackages.filter(pkg => {
        if (pkg.contents.length !== contents.length) return false
        return contents.every(checkContent =>
          pkg.contents.some(pkgContent =>
            pkgContent.sku === checkContent.sku &&
            pkgContent.name === checkContent.name &&
            pkgContent.original_quantity === checkContent.original_quantity
          )
        )
      })

      // 计算可用包裹的总数量（包括部分包裹）
      let totalAvailableQuantity = 0
      for (const pkg of matchingPackages) {
        totalAvailableQuantity += pkg.remaining_percentage || 1.0
      }

      if (totalAvailableQuantity < deficitQuantity) {
        throw new HttpException(
          `盘亏数量 ${deficitQuantity} 超过可用包裹总数 ${totalAvailableQuantity}`,
          HttpStatus.BAD_REQUEST
        )
      }

      // 处理盘亏包裹 - 支持部分包裹
      let remainingDeficit = deficitQuantity
      let packageIndex = 0

      while (remainingDeficit > 0 && packageIndex < matchingPackages.length) {
        const pkg = matchingPackages[packageIndex]
        const currentRemaining = pkg.remaining_percentage || 1.0

        if (remainingDeficit >= currentRemaining) {
          // 完全盘亏这个包裹
          await this.packageModel.updateOne(
            { package_code: pkg.package_code },
            {
              $set: {
                status: 'shipped',
                remaining_percentage: 0,
                last_updated_at: new Date(),
                'contents.$[].current_quantity': 0
              }
            }
          )

          affectedPackages.push(pkg.package_code)

          // 记录盘亏日志
          const contentsChanges = pkg.contents.map((content: any) => ({
            sku: content.sku,
            product_name: content.name,
            quantity_change: -Math.round(content.original_quantity * currentRemaining),
            before_quantity: Math.round(content.original_quantity * currentRemaining),
            after_quantity: 0
          }))

          await this.logOperation({
            operation_type: 'inventory_deficit',
            operator_name: operator,
            warehouse_id: warehouseId,
            package_code: pkg.package_code,
            contents_changes: contentsChanges,
            details: {
              classification_code: pkg.classification_code,
              notes: `盘亏操作 - 完全盘亏（原剩余：${currentRemaining * 100}%）`
            }
          }, operationDate)

          remainingDeficit -= currentRemaining
        } else {
          // 部分盘亏这个包裹
          const newRemainingPercentage = currentRemaining - remainingDeficit

          await this.packageModel.updateOne(
            { package_code: pkg.package_code },
            {
              $set: {
                status: newRemainingPercentage > 0 ? 'partially_shipped' : 'shipped',
                remaining_percentage: newRemainingPercentage,
                last_updated_at: new Date()
              }
            }
          )

          // 更新包裹内容的current_quantity
          const updatedPackage = await this.packageModel.findOne({ package_code: pkg.package_code })
          if (updatedPackage) {
            for (let i = 0; i < updatedPackage.contents.length; i++) {
              updatedPackage.contents[i].current_quantity = Math.round(
                updatedPackage.contents[i].original_quantity * newRemainingPercentage
              )
            }
            await updatedPackage.save()
          }

          affectedPackages.push(pkg.package_code)

          // 记录盘亏日志
          const contentsChanges = pkg.contents.map((content: any) => ({
            sku: content.sku,
            product_name: content.name,
            quantity_change: -Math.round(content.original_quantity * remainingDeficit),
            before_quantity: Math.round(content.original_quantity * currentRemaining),
            after_quantity: Math.round(content.original_quantity * newRemainingPercentage)
          }))

          await this.logOperation({
            operation_type: 'inventory_deficit',
            operator_name: operator,
            warehouse_id: warehouseId,
            package_code: pkg.package_code,
            contents_changes: contentsChanges,
            details: {
              classification_code: pkg.classification_code,
              partial_percentage: remainingDeficit,
              notes: `盘亏操作 - 部分盘亏（盘亏：${remainingDeficit * 100}%，剩余：${newRemainingPercentage * 100}%）`
            }
          }, operationDate)

          remainingDeficit = 0
        }

        packageIndex++
      }
    } catch (error) {
      console.error('处理盘亏操作失败:', error)
      throw error
    }
  }

  /**
   * 搜索已出库的服装（用于盘盈操作）
   */
  async searchShippedClothing(params: {
    warehouse_id: string
    clothing_name?: string
    page?: number
    limit?: number
  }) {
    try {
      const { warehouse_id, clothing_name, page = 1, limit = 20 } = params
      const skip = (page - 1) * limit

      // 构建匹配条件
      const matchStage: any = {
        warehouse_id,
        status: 'shipped'
      }

      if (clothing_name) {
        matchStage['contents.name'] = { $regex: clothing_name, $options: 'i' }
      }

      // 聚合查询
      const pipeline: any[] = [
        { $match: matchStage },
        { $unwind: '$contents' },
        {
          $group: {
            _id: {
              sku: '$contents.sku',
              name: '$contents.name',
              original_quantity: '$contents.original_quantity'
            },
            total_packages: { $sum: 1 },
            classification_codes: { $addToSet: '$classification_code' }
          }
        },
        {
          $project: {
            _id: 0,
            sku: '$_id.sku',
            name: '$_id.name',
            original_quantity: '$_id.original_quantity',
            total_packages: 1,
            classification_codes: 1
          }
        },
        { $sort: { name: 1 } },
        { $skip: skip },
        { $limit: limit }
      ]

      const results = await this.packageModel.aggregate(pipeline).exec()

      // 获取总数
      const countPipeline = [
        { $match: matchStage },
        { $unwind: '$contents' },
        {
          $group: {
            _id: {
              sku: '$contents.sku',
              name: '$contents.name',
              original_quantity: '$contents.original_quantity'
            }
          }
        },
        { $count: 'total' }
      ]

      const countResult = await this.packageModel.aggregate(countPipeline).exec()
      const total = countResult[0]?.total || 0

      return {
        code: 200,
        data: {
          list: results,
          total,
          page,
          limit
        },
        message: '搜索成功'
      }
    } catch (error: any) {
      console.error('搜索已出库服装失败:', error)
      throw new HttpException(error.message || '搜索已出库服装失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 盘存操作冲正
   */
  async reverseInventoryOperation(originalLog: any, operator: string) {
    try {
      console.log('开始盘存冲正操作，原始日志:', originalLog)

      const operationType = originalLog.operation_type
      const packageCode = originalLog.package_code

      // 查找包裹
      const packageDoc = await this.packageModel.findOne({
        package_code: packageCode
      })

      if (!packageDoc) {
        throw new HttpException(`包裹 ${packageCode} 不存在`, HttpStatus.BAD_REQUEST)
      }

      console.log(`包裹当前状态: ${packageDoc.status}, remaining_percentage: ${packageDoc.remaining_percentage}`)

      // 从原始日志中获取操作前的状态信息
      const originalDetails = originalLog.details || {}
      const originalPartialPercentage = originalDetails.partial_percentage

      if (operationType === 'inventory_surplus') {
        // 盘盈冲正：需要根据原始操作恢复到之前的状态
        if (originalPartialPercentage) {
          // 原来是部分包裹恢复，现在要冲正回部分出库状态
          const newRemainingPercentage = 1.0 - originalPartialPercentage

          await this.packageModel.updateOne(
            { package_code: packageCode },
            {
              $set: {
                status: newRemainingPercentage > 0 ? 'partially_shipped' : 'shipped',
                remaining_percentage: newRemainingPercentage,
                last_updated_at: new Date()
              }
            }
          )

          // 更新包裹内容的current_quantity
          const updatedPackage = await this.packageModel.findOne({ package_code: packageCode })
          if (updatedPackage) {
            for (let i = 0; i < updatedPackage.contents.length; i++) {
              updatedPackage.contents[i].current_quantity = Math.round(
                updatedPackage.contents[i].original_quantity * newRemainingPercentage
              )
            }
            await updatedPackage.save()
          }

          console.log(`盘盈冲正：包裹 ${packageCode} 状态已改为 ${newRemainingPercentage > 0 ? 'partially_shipped' : 'shipped'}，remaining_percentage 已改为 ${newRemainingPercentage}`)
        } else {
          // 原来是完整包裹恢复，现在要冲正回完全出库状态
          await this.packageModel.updateOne(
            { package_code: packageCode },
            {
              $set: {
                status: 'shipped',
                remaining_percentage: 0,
                last_updated_at: new Date(),
                'contents.$[].current_quantity': 0
              }
            }
          )

          console.log(`盘盈冲正：包裹 ${packageCode} 状态已改为 shipped，remaining_percentage 已改为 0`)
        }

      } else if (operationType === 'inventory_deficit') {
        // 盘亏冲正：需要根据原始操作恢复到之前的状态
        if (originalPartialPercentage) {
          // 原来是部分盘亏，现在要恢复部分数量
          const beforePercentage = originalLog.contents_changes[0]?.before_quantity /
                                  (packageDoc.contents[0]?.original_quantity || 1)

          await this.packageModel.updateOne(
            { package_code: packageCode },
            {
              $set: {
                status: beforePercentage >= 1.0 ? 'in_stock' : 'partially_shipped',
                remaining_percentage: beforePercentage,
                last_updated_at: new Date()
              }
            }
          )

          // 恢复包裹内容的current_quantity
          const updatedPackage = await this.packageModel.findOne({ package_code: packageCode })
          if (updatedPackage) {
            for (let i = 0; i < updatedPackage.contents.length; i++) {
              updatedPackage.contents[i].current_quantity = Math.round(
                updatedPackage.contents[i].original_quantity * beforePercentage
              )
            }
            await updatedPackage.save()
          }

          console.log(`盘亏冲正：包裹 ${packageCode} 状态已改为 ${beforePercentage >= 1.0 ? 'in_stock' : 'partially_shipped'}，remaining_percentage 已改为 ${beforePercentage}`)
        } else {
          // 原来是完整盘亏，现在要恢复为完整包裹
          await this.packageModel.updateOne(
            { package_code: packageCode },
            {
              $set: {
                status: 'in_stock',
                remaining_percentage: 1.0,
                last_updated_at: new Date()
              }
            }
          )

          // 恢复包裹内容的current_quantity为original_quantity
          const updatedPackage = await this.packageModel.findOne({ package_code: packageCode })
          if (updatedPackage) {
            for (let i = 0; i < updatedPackage.contents.length; i++) {
              updatedPackage.contents[i].current_quantity = updatedPackage.contents[i].original_quantity
            }
            await updatedPackage.save()
          }

          console.log(`盘亏冲正：包裹 ${packageCode} 状态已改为 in_stock，remaining_percentage 已改为 1.0`)
        }
      }

      // 创建冲正日志
      const contentsChanges = originalLog.contents_changes.map((change: any) => ({
        sku: change.sku,
        product_name: change.product_name,
        quantity_change: -change.quantity_change, // 反向操作
        before_quantity: change.after_quantity,
        after_quantity: change.before_quantity
      }))

      await this.logOperation({
        operation_type: 'reversal',
        operator_name: operator,
        warehouse_id: originalLog.warehouse_id,
        package_code: packageCode,
        contents_changes: contentsChanges,
        details: {
          original_operation_type: operationType,
          original_classification_code: originalLog.details?.classification_code,
          original_partial_percentage: originalPartialPercentage,
          notes: `${operationType === 'inventory_surplus' ? '盘盈' : '盘亏'}冲正操作`
        },
        is_reversal: true,
        timestamp: new Date()
      })

      console.log(`已创建冲正日志，操作类型: reversal`)

      // 反向更新服装库存数据
      for (const change of originalLog.contents_changes) {
        const sku = change.sku
        const quantityChange = -change.quantity_change // 反向操作

        console.log(`处理SKU: ${sku}, 数量变化: ${quantityChange}`)

        // 更新服装库存数据
        const isOemClothing = sku.includes('_') && sku.split('_')[0].toLowerCase() === 'oem'
        if (isOemClothing) {
          const oemClothingId = sku.split('_')[1]
          console.log(`更新OEM服装库存: ${oemClothingId}`)
          await this.oemClothingModel.findOneAndUpdate(
            { oem_clothing_id: oemClothingId },
            { $inc: { stock_quantity: quantityChange } }
          )
        } else {
          // 从SKU中提取clothing_id
          const clothingId = sku.replace(/^CLO_/, '')
          console.log(`更新普通服装库存: ${clothingId}`)
          await this.clothingModel.findOneAndUpdate(
            { clothing_id: clothingId },
            { $inc: { stock_quantity: quantityChange } }
          )
        }
      }

      console.log('盘存冲正操作完成')

    } catch (error) {
      console.error('盘存冲正操作失败:', error)
      throw error
    }
  }
}
