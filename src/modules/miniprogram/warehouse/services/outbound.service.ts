import { Injectable, HttpException, HttpStatus } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { Product } from '../../../../models/product.model'
import { Package } from '../../../../models/package.model'
import { OperationLog } from '../../../../models/operationLog.model'
import { Warehouse } from '../../../../models/warehouse.model'
import { Clothing } from '../../../../models/clothing.model'
import { OemClothing } from '../../../../models/oemClothing.model'
import { Transportation } from '../../../../models/transportation.model'

@Injectable()
export class OutboundService {
  constructor(
    @InjectModel('Product') private productModel: Model<Product>,
    @InjectModel('Package') private packageModel: Model<Package>,
    @InjectModel('OperationLog') private operationLogModel: Model<OperationLog>,
    @InjectModel('Warehouse') private warehouseModel: Model<Warehouse>,
    @InjectModel('Clothing') private clothingModel: Model<Clothing>,
    @InjectModel('OemClothing') private oemClothingModel: Model<OemClothing>,
    @InjectModel('Transportation') private transportationModel: Model<Transportation>
  ) {}

  /**
   * 记录操作日志
   */
  private async logOperation(logData: any, operationDate?: string) {
    const timestamp = operationDate ? new Date(operationDate) : new Date()
    await this.operationLogModel.create({
      ...logData,
      timestamp,
    })
  }

  /**
   * 更新服装库存数据
   */
  private async updateClothingStock(
    operationType: 'inbound' | 'outbound' | 'transfer' | 'inventory_gain' | 'inventory_loss',
    clothingUpdates: Array<{
      clothing_id?: string
      oem_clothing_id?: string
      quantity: number
    }>
  ) {
    try {
      for (const update of clothingUpdates) {
        if (update.clothing_id) {
          // 普通服装更新
          if (operationType === 'inbound' || operationType === 'inventory_gain') {
            // 入库或盘盈：增加到货数和库存数
            await this.clothingModel.updateOne(
              { clothing_id: update.clothing_id },
              {
                $inc: {
                  arrival_quantity: operationType === 'inbound' ? update.quantity : 0,
                  stock_quantity: update.quantity,
                },
              }
            )
          } else if (operationType === 'outbound' || operationType === 'inventory_loss') {
            // 出库或盘亏：减少库存数
            await this.clothingModel.updateOne(
              { clothing_id: update.clothing_id },
              {
                $inc: {
                  stock_quantity: -update.quantity,
                },
              }
            )
          }
        }

        if (update.oem_clothing_id) {
          // OEM服装更新
          if (operationType === 'inbound' || operationType === 'inventory_gain') {
            // 入库或盘盈：增加到货数和库存数
            await this.oemClothingModel.updateOne(
              { oem_clothing_id: update.oem_clothing_id },
              {
                $inc: {
                  arrival_quantity: operationType === 'inbound' ? update.quantity : 0,
                  stock_quantity: update.quantity,
                },
              }
            )
          } else if (operationType === 'outbound' || operationType === 'inventory_loss') {
            // 出库或盘亏：减少库存数
            await this.oemClothingModel.updateOne(
              { oem_clothing_id: update.oem_clothing_id },
              {
                $inc: {
                  stock_quantity: -update.quantity,
                },
              }
            )
          }
        }
      }
    } catch (error) {
      console.error('更新服装库存失败:', error)
      throw error
    }
  }

  /**
   * 计算出库包裹数
   * @param contentsChanges 内容变化数组
   * @param packageDoc 包裹文档
   * @returns 出库包裹数（保留2位小数）
   */
  private calculateOutboundPackageCount(contentsChanges: any[], packageDoc: any): number {
    // 分子：当前出库操作中所有 quantity_change 的绝对值之和
    const totalQuantityChange = contentsChanges.reduce((sum, change) => {
      return sum + Math.abs(change.quantity_change)
    }, 0)

    // 分母：对应包裹的所有 original_quantity 之和
    const totalOriginalQuantity = packageDoc.contents.reduce((sum: number, content: any) => {
      return sum + content.original_quantity
    }, 0)

    // 计算包裹数
    if (totalOriginalQuantity === 0) {
      return 0
    }

    const packageCount = totalQuantityChange / totalOriginalQuantity

    // 四舍五入保留2位小数
    return Math.round(packageCount * 100) / 100
  }

  /**
   * 处理完整包裹出库
   */
  private async processFullPackageOutbound(
    packageDoc: any,
    outboundData: any,
    operationLogs: any[]
  ) {
    // 记录变化
    const contentsChanges = packageDoc.contents.map((content: any) => ({
      sku: content.sku,
      product_name: content.name,
      quantity_change: -content.current_quantity,
      before_quantity: content.current_quantity,
      after_quantity: 0,
    }))

    // 更新包裹状态为已出库
    await this.packageModel.updateOne(
      { package_code: packageDoc.package_code },
      {
        $set: {
          status: 'shipped',
          remaining_percentage: 0,
          last_updated_at: new Date(),
          'contents.$[].current_quantity': 0,
        },
      }
    )

    // 计算出库包裹数
    const outboundPackageCount = this.calculateOutboundPackageCount(contentsChanges, packageDoc)

    // 记录操作日志
    operationLogs.push({
      operation_type: 'outbound',
      operator_name: outboundData.operator,
      warehouse_id: outboundData.warehouse_id,
      package_code: packageDoc.package_code,
      contents_changes: contentsChanges,
      outbound_package_count: outboundPackageCount,
      details: {
        classification_code: packageDoc.classification_code,
        outbound_type: 'full_package',
        notes: outboundData.notes || '完整包裹出库',
      },
    })
  }

  /**
   * 处理部分包裹出库
   */
  private async processPartialPackageOutbound(
    packageDoc: any,
    outboundPercentage: number,
    outboundData: any,
    operationLogs: any[]
  ) {
    // 计算每个contents项的出库数量
    const contentsChanges: any[] = []
    const updateOperations: any = {
      $set: {
        last_updated_at: new Date(),
      },
    }

    packageDoc.contents.forEach((content: any, index: number) => {
      const outboundQuantity = content.original_quantity * outboundPercentage
      const newQuantity = Math.max(0, content.current_quantity - outboundQuantity)

      contentsChanges.push({
        sku: content.sku,
        product_name: content.name,
        quantity_change: -outboundQuantity,
        before_quantity: content.current_quantity,
        after_quantity: newQuantity,
      })

      updateOperations.$set[`contents.${index}.current_quantity`] = newQuantity
    })

    // 计算新的剩余百分比
    const newRemainingPercentage = Math.max(0, packageDoc.remaining_percentage - outboundPercentage)
    const newStatus = newRemainingPercentage === 0 ? 'shipped' : 'partially_shipped'

    updateOperations.$set.status = newStatus
    updateOperations.$set.remaining_percentage = newRemainingPercentage

    // 更新包裹
    await this.packageModel.updateOne({ package_code: packageDoc.package_code }, updateOperations)

    // 计算出库包裹数
    const outboundPackageCount = this.calculateOutboundPackageCount(contentsChanges, packageDoc)

    // 记录操作日志
    operationLogs.push({
      operation_type: 'outbound',
      operator_name: outboundData.operator,
      warehouse_id: outboundData.warehouse_id,
      package_code: packageDoc.package_code,
      contents_changes: contentsChanges,
      outbound_package_count: outboundPackageCount,
      details: {
        classification_code: packageDoc.classification_code,
        outbound_type: 'partial_package',
        outbound_percentage: outboundPercentage,
        notes: outboundData.notes || '部分包裹出库',
      },
    })
  }

  /**
   * 出库操作 - 新的classification_code为中心的出库逻辑
   * 接收按classification_code统计的出库数据，执行整数和小数分离逻辑
   */
  async outbound(outboundData: {
    warehouse_id: string
    items: Array<{
      classification_code: string
      package_count: number // 要出库的包裹数（可以是小数）
    }>
    operator: string
    notes?: string
    operation_date?: string
  }) {
    try {
      const operationLogs: any[] = []
      const processedPackages: string[] = []

      for (const item of outboundData.items) {
        console.log(
          `开始处理classification_code: ${item.classification_code}, 出库包裹数: ${item.package_count}`
        )

        // 分离整数和小数部分
        const integerPart = Math.floor(item.package_count)
        const decimalPart = item.package_count - integerPart

        console.log(`整数部分: ${integerPart}, 小数部分: ${decimalPart}`)

        // 查找该classification_code的所有可用包裹
        const availablePackages = await this.packageModel
          .find({
            warehouse_id: outboundData.warehouse_id,
            classification_code: item.classification_code,
            status: { $in: ['in_stock', 'partially_shipped'] },
          })
          .sort({
            status: 1, // in_stock优先（字母序）
            inbound_at: 1, // FIFO原则
          })

        if (availablePackages.length === 0) {
          throw new HttpException(
            `分类码 ${item.classification_code} 没有可用库存`,
            HttpStatus.BAD_REQUEST
          )
        }

        // 验证库存是否足够
        const totalAvailablePackages = availablePackages.reduce((sum, pkg) => {
          return sum + (pkg.status === 'in_stock' ? 1 : pkg.remaining_percentage)
        }, 0)

        if (totalAvailablePackages < item.package_count) {
          throw new HttpException(
            `分类码 ${item.classification_code} 库存不足，可用: ${totalAvailablePackages.toFixed(2)}包，需要: ${item.package_count}包`,
            HttpStatus.BAD_REQUEST
          )
        }

        // 处理整数部分：优先出库in_stock状态的包裹
        let remainingIntegerPart = integerPart
        const inStockPackages = availablePackages.filter((pkg) => pkg.status === 'in_stock')

        for (const packageDoc of inStockPackages) {
          if (remainingIntegerPart <= 0) break

          // 整包出库
          await this.processFullPackageOutbound(packageDoc, outboundData, operationLogs)
          processedPackages.push(packageDoc.package_code)
          remainingIntegerPart--
        }

        // 如果整数部分还有剩余，使用partially_shipped状态的包裹
        if (remainingIntegerPart > 0) {
          const partiallyShippedPackages = availablePackages.filter(
            (pkg) => pkg.status === 'partially_shipped'
          )

          for (const packageDoc of partiallyShippedPackages) {
            if (remainingIntegerPart <= 0) break

            if (packageDoc.remaining_percentage >= 1) {
              // 这个包裹可以当作完整包裹出库
              await this.processFullPackageOutbound(packageDoc, outboundData, operationLogs)
              processedPackages.push(packageDoc.package_code)
              remainingIntegerPart--
            }
          }
        }

        // 处理小数部分：优先使用partially_shipped状态的包裹
        if (decimalPart > 0) {
          let remainingDecimalPart = decimalPart
          const partiallyShippedPackages = availablePackages.filter(
            (pkg) =>
              pkg.status === 'partially_shipped' && !processedPackages.includes(pkg.package_code)
          )

          for (const packageDoc of partiallyShippedPackages) {
            if (remainingDecimalPart <= 0) break

            const availableFromThisPackage = packageDoc.remaining_percentage
            const outboundFromThisPackage = Math.min(remainingDecimalPart, availableFromThisPackage)

            await this.processPartialPackageOutbound(
              packageDoc,
              outboundFromThisPackage,
              outboundData,
              operationLogs
            )
            processedPackages.push(packageDoc.package_code)
            remainingDecimalPart -= outboundFromThisPackage
          }

          // 如果小数部分还有剩余，使用in_stock状态的包裹
          if (remainingDecimalPart > 0) {
            const unusedInStockPackages = inStockPackages.filter(
              (pkg) => !processedPackages.includes(pkg.package_code)
            )

            for (const packageDoc of unusedInStockPackages) {
              if (remainingDecimalPart <= 0) break

              await this.processPartialPackageOutbound(
                packageDoc,
                remainingDecimalPart,
                outboundData,
                operationLogs
              )
              processedPackages.push(packageDoc.package_code)
              remainingDecimalPart = 0 // 一个完整包裹足够处理剩余的小数部分
            }
          }
        }

        console.log(`classification_code ${item.classification_code} 处理完成`)
      }

      // 批量记录操作日志
      for (const logData of operationLogs) {
        await this.logOperation(logData, outboundData.operation_date)
      }

      // 更新服装库存数据（出库减少库存）
      if (operationLogs.length > 0) {
        const clothingUpdates: Array<{
          clothing_id?: string
          oem_clothing_id?: string
          quantity: number
        }> = []

        // 统计每个服装的出库数量
        const clothingQuantityMap = new Map<string, number>()
        const oemClothingQuantityMap = new Map<string, number>()

        // 从包裹内容变化中提取库存更新信息
        for (const log of operationLogs) {
          if (log.contents_changes) {
            // 需要获取包裹信息来确定服装ID
            const packageDoc = await this.packageModel.findOne({ package_code: log.package_code }).exec()
            if (packageDoc) {
              for (const change of log.contents_changes) {
                // 找到对应的包裹内容
                const content = packageDoc.contents.find((c: any) => c.sku === change.sku)
                if (content) {
                  const outboundQuantity = Math.abs(change.quantity_change) // 取绝对值，因为quantity_change是负数

                  if (content.clothing_id) {
                    const currentQuantity = clothingQuantityMap.get(content.clothing_id) || 0
                    clothingQuantityMap.set(content.clothing_id, currentQuantity + outboundQuantity)
                  }
                  if (content.oem_clothing_id) {
                    const currentQuantity = oemClothingQuantityMap.get(content.oem_clothing_id) || 0
                    oemClothingQuantityMap.set(content.oem_clothing_id, currentQuantity + outboundQuantity)
                  }
                }
              }
            }
          }
        }

        // 构建更新数据
        for (const [clothingId, quantity] of clothingQuantityMap) {
          clothingUpdates.push({ clothing_id: clothingId, quantity })
        }
        for (const [oemClothingId, quantity] of oemClothingQuantityMap) {
          clothingUpdates.push({ oem_clothing_id: oemClothingId, quantity })
        }

        // 执行库存更新
        if (clothingUpdates.length > 0) {
          console.log('准备更新服装库存:', clothingUpdates)
          await this.updateClothingStock('outbound', clothingUpdates)
        }
      }

      return {
        code: 200,
        data: {
          processed_classification_codes: outboundData.items.length,
          total_processed_packages: processedPackages.length,
          operation_logs: operationLogs.map((log) => ({
            package_code: log.package_code,
            classification_code: log.details?.classification_code,
            outbound_type: log.details?.outbound_type,
          })),
        },
        message: '出库成功',
      }
    } catch (error: any) {
      console.error('出库操作失败:', error)
      throw new HttpException(error.message || '出库操作失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 批量出库操作 - 统一接口处理多仓库多类型包裹
   */
  async batchOutbound(batchOutboundData: {
    warehouses: Array<{
      warehouse_id: string
      warehouse_name: string
      warehouse_address?: string
      items: Array<{
        classification_code: string
        package_count: number
        total_pieces: number
        package_type: 'single' | 'mixed'
        contents: Array<{
          sku: string
          name: string
          outbound_quantity: number
          current_quantity?: number
          original_quantity?: number
        }>
      }>
      total_packages: number
      total_pieces: number
    }>
    total_packages: number
    total_pieces: number
    operator?: string
    notes?: string
    operation_date?: string
  }) {
    try {
      const results: any[] = []

      // 按仓库循环处理
      for (const warehouse of batchOutboundData.warehouses) {
        console.log(`开始处理仓库: ${warehouse.warehouse_id} - ${warehouse.warehouse_name}`)

        // 按package_type分组处理
        const singleItems = warehouse.items.filter((item) => item.package_type === 'single')
        const mixedItems = warehouse.items.filter((item) => item.package_type === 'mixed')

        const warehouseResult = {
          warehouse_id: warehouse.warehouse_id,
          warehouse_name: warehouse.warehouse_name,
          single_items_result: null as any,
          mixed_items_result: null as any,
          total_processed: 0,
        }

        // 处理单一包裹
        if (singleItems.length > 0) {
          console.log(`处理${singleItems.length}个单一包裹项`)
          const singleOutboundData = {
            warehouse_id: warehouse.warehouse_id,
            items: singleItems.map((item) => ({
              classification_code: item.classification_code,
              package_count: item.package_count,
            })),
            operator: batchOutboundData.operator || 'unknown',
            notes: batchOutboundData.notes || '批量出库-单一包裹',
            operation_date: batchOutboundData.operation_date,
          }

          warehouseResult.single_items_result = await this.outbound(singleOutboundData)
          warehouseResult.total_processed += singleItems.length
        }

        // 处理混合包裹
        if (mixedItems.length > 0) {
          console.log(`处理${mixedItems.length}个混合包裹项`)
          const mixedOutboundData = {
            warehouse_id: warehouse.warehouse_id,
            items: mixedItems,
            operator: batchOutboundData.operator || 'unknown',
            notes: batchOutboundData.notes || '批量出库-混合包裹',
            operation_date: batchOutboundData.operation_date,
          }

          warehouseResult.mixed_items_result = await this.mixedPackageOutbound(mixedOutboundData)
          warehouseResult.total_processed += mixedItems.length
        }

        results.push(warehouseResult)
        console.log(
          `仓库 ${warehouse.warehouse_id} 处理完成，共处理 ${warehouseResult.total_processed} 项`
        )
      }

      return {
        code: 200,
        data: {
          processed_warehouses: batchOutboundData.warehouses.length,
          warehouse_results: results,
          total_items: batchOutboundData.warehouses.reduce((sum, w) => sum + w.items.length, 0),
        },
        message: '批量出库成功',
      }
    } catch (error: any) {
      console.error('批量出库操作失败:', error)
      throw new HttpException(error.message || '批量出库操作失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 混合包裹出库操作
   * 根据contents中的数据出货sku相同的数量（outbound_quantity）
   */
  async mixedPackageOutbound(outboundData: {
    warehouse_id: string
    items: Array<{
      classification_code: string
      package_count: number
      total_pieces: number
      contents: Array<{
        sku: string
        name: string
        outbound_quantity: number
        current_quantity?: number
        original_quantity?: number
      }>
    }>
    operator: string
    notes?: string
    operation_date?: string
  }) {
    try {
      const operationLogs: any[] = []
      const processedPackages: string[] = []

      for (const item of outboundData.items) {
        console.log(`开始处理混合包裹 classification_code: ${item.classification_code}`)

        // 1. 找到classification_code相同的包裹且status不等于shipped
        const availablePackages = await this.packageModel
          .find({
            warehouse_id: outboundData.warehouse_id,
            classification_code: item.classification_code,
            status: { $ne: 'shipped' },
          })
          .sort({
            package_code: 1, // 按package_code升序排列，确保选择最小的
          })

        if (availablePackages.length === 0) {
          throw new HttpException(
            `分类码 ${item.classification_code} 没有可用的混合包裹`,
            HttpStatus.BAD_REQUEST
          )
        }

        // 2. 找到package_code最小的包裹
        const targetPackage = availablePackages[0]
        console.log(`选择包裹: ${targetPackage.package_code}`)

        // 3. 根据contents中的数据出货sku相同的数量
        const contentsChanges: any[] = []
        const updateOperations: any = {
          $set: {
            last_updated_at: new Date(),
          },
        }

        let hasValidOutbound = false

        for (const outboundContent of item.contents) {
          // 在包裹中找到对应的sku
          const packageContentIndex = targetPackage.contents.findIndex(
            (content: any) => content.sku === outboundContent.sku
          )

          if (packageContentIndex === -1) {
            console.warn(`包裹 ${targetPackage.package_code} 中未找到 SKU: ${outboundContent.sku}`)
            continue
          }

          const packageContent = targetPackage.contents[packageContentIndex]
          const outboundQuantity = outboundContent.outbound_quantity

          if (outboundQuantity <= 0) {
            continue
          }

          if (outboundQuantity > packageContent.current_quantity) {
            throw new HttpException(
              `SKU ${outboundContent.sku} 出库数量 ${outboundQuantity} 超过可用库存 ${packageContent.current_quantity}`,
              HttpStatus.BAD_REQUEST
            )
          }

          const newQuantity = packageContent.current_quantity - outboundQuantity

          contentsChanges.push({
            sku: outboundContent.sku,
            product_name: outboundContent.name,
            quantity_change: -outboundQuantity,
            before_quantity: packageContent.current_quantity,
            after_quantity: newQuantity,
          })

          updateOperations.$set[`contents.${packageContentIndex}.current_quantity`] = newQuantity
          hasValidOutbound = true
        }

        if (!hasValidOutbound) {
          console.warn(`包裹 ${targetPackage.package_code} 没有有效的出库操作`)
          continue
        }

        // 计算新的剩余百分比
        const totalOriginalQuantity = targetPackage.contents.reduce(
          (sum: number, content: any) => sum + content.original_quantity,
          0
        )
        const totalCurrentQuantity = targetPackage.contents.reduce(
          (sum: number, content: any, index: number) => {
            const updatedQuantity = updateOperations.$set[`contents.${index}.current_quantity`]
            return (
              sum + (updatedQuantity !== undefined ? updatedQuantity : content.current_quantity)
            )
          },
          0
        )

        const newRemainingPercentage =
          totalOriginalQuantity > 0 ? totalCurrentQuantity / totalOriginalQuantity : 0

        const newStatus = newRemainingPercentage === 0 ? 'shipped' : 'partially_shipped'

        updateOperations.$set.status = newStatus
        updateOperations.$set.remaining_percentage = newRemainingPercentage

        // 更新包裹
        await this.packageModel.updateOne(
          { package_code: targetPackage.package_code },
          updateOperations
        )

        // 计算出库包裹数
        const outboundPackageCount = this.calculateOutboundPackageCount(contentsChanges, targetPackage)

        // 记录操作日志
        operationLogs.push({
          operation_type: 'outbound',
          operator_name: outboundData.operator,
          warehouse_id: outboundData.warehouse_id,
          package_code: targetPackage.package_code,
          contents_changes: contentsChanges,
          outbound_package_count: outboundPackageCount,
          details: {
            classification_code: item.classification_code,
            outbound_type: 'mixed_package',
            notes: outboundData.notes || '混合包裹出库',
          },
        })

        processedPackages.push(targetPackage.package_code)
        console.log(`混合包裹 ${item.classification_code} 处理完成`)
      }

      // 批量记录操作日志
      for (const logData of operationLogs) {
        await this.logOperation(logData, outboundData.operation_date)
      }

      // 更新服装库存数据（出库减少库存）
      if (operationLogs.length > 0) {
        const clothingUpdates: Array<{
          clothing_id?: string
          oem_clothing_id?: string
          quantity: number
        }> = []

        // 统计每个服装的出库数量
        const clothingQuantityMap = new Map<string, number>()
        const oemClothingQuantityMap = new Map<string, number>()

        // 从包裹内容变化中提取库存更新信息
        for (const log of operationLogs) {
          if (log.contents_changes) {
            // 需要获取包裹信息来确定服装ID
            const packageDoc = await this.packageModel.findOne({ package_code: log.package_code }).exec()
            if (packageDoc) {
              for (const change of log.contents_changes) {
                // 找到对应的包裹内容
                const content = packageDoc.contents.find((c: any) => c.sku === change.sku)
                if (content) {
                  const outboundQuantity = Math.abs(change.quantity_change) // 取绝对值，因为quantity_change是负数

                  if (content.clothing_id) {
                    const currentQuantity = clothingQuantityMap.get(content.clothing_id) || 0
                    clothingQuantityMap.set(content.clothing_id, currentQuantity + outboundQuantity)
                  }
                  if (content.oem_clothing_id) {
                    const currentQuantity = oemClothingQuantityMap.get(content.oem_clothing_id) || 0
                    oemClothingQuantityMap.set(content.oem_clothing_id, currentQuantity + outboundQuantity)
                  }
                }
              }
            }
          }
        }

        // 构建更新数据
        for (const [clothingId, quantity] of clothingQuantityMap) {
          clothingUpdates.push({ clothing_id: clothingId, quantity })
        }
        for (const [oemClothingId, quantity] of oemClothingQuantityMap) {
          clothingUpdates.push({ oem_clothing_id: oemClothingId, quantity })
        }

        // 执行库存更新
        if (clothingUpdates.length > 0) {
          console.log('准备更新服装库存（混合包裹）:', clothingUpdates)
          await this.updateClothingStock('outbound', clothingUpdates)
        }
      }

      return {
        code: 200,
        data: {
          processed_classification_codes: outboundData.items.length,
          total_processed_packages: processedPackages.length,
          operation_logs: operationLogs.map((log) => ({
            package_code: log.package_code,
            classification_code: log.details?.classification_code,
            outbound_type: log.details?.outbound_type,
          })),
        },
        message: '混合包裹出库成功',
      }
    } catch (error: any) {
      console.error('混合包裹出库操作失败:', error)
      throw new HttpException(
        error.message || '混合包裹出库操作失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 出库冲正操作
   */
  async reverseOutboundOperation(reverseData: {
    package_code: string
    operator: string
    notes?: string
    operation_date?: string
    original_log_id?: string // 添加原始日志ID参数
  }) {
    try {
      // 查找包裹
      const packageDoc = await this.packageModel.findOne({
        package_code: reverseData.package_code,
      })

      if (!packageDoc) {
        throw new HttpException(`包裹 ${reverseData.package_code} 不存在`, HttpStatus.BAD_REQUEST)
      }

      // 验证包裹状态（只能冲正已出库的包裹）
      if (packageDoc.status !== 'shipped' && packageDoc.status !== 'partially_shipped') {
        throw new HttpException(
          `包裹 ${reverseData.package_code} 状态为 ${packageDoc.status}，无法执行出库冲正`,
          HttpStatus.BAD_REQUEST
        )
      }

      // 查找要冲正的出库日志
      // 如果提供了原始日志ID，直接查找该日志
      const targetOutboundLog = await this.operationLogModel.findById(reverseData.original_log_id)

      if (!targetOutboundLog) {
        throw new HttpException(
          `包裹 ${reverseData.package_code} 没有找到可冲正的出库记录`,
          HttpStatus.BAD_REQUEST
        )
      }

      console.log(
        `找到要冲正的出库日志: ${targetOutboundLog._id}, 时间: ${targetOutboundLog.timestamp}`
      )
      console.log('出库日志内容变化:', JSON.stringify(targetOutboundLog.contents_changes, null, 2))
      console.log('包裹当前内容:', JSON.stringify(packageDoc.contents, null, 2))

      // 根据出库日志恢复数量
      const restoredContents = packageDoc.contents.map((content: any) => {
        // 查找该SKU在出库日志中的变化
        const outboundChange = targetOutboundLog.contents_changes.find(
          (change: any) => change.sku === content.sku
        )

        if (outboundChange) {
          // 恢复出库的数量
          // outboundChange.quantity_change 是负数（如-54），所以要减去它来恢复
          const restoredQuantity = content.current_quantity - outboundChange.quantity_change
          console.log(
            `恢复 ${content.sku} 数量: ${content.current_quantity} - (${outboundChange.quantity_change}) = ${restoredQuantity}`
          )
          // 确保返回纯对象，避免Mongoose文档的内部属性
          return {
            product_id: content.product_id,
            sku: content.sku,
            name: content.name,
            original_quantity: content.original_quantity,
            current_quantity: restoredQuantity,
            clothing_id: content.clothing_id,
            oem_clothing_id: content.oem_clothing_id,
            is_oem: content.is_oem,
          }
        }

        // 确保返回纯对象，避免Mongoose文档的内部属性
        return {
          product_id: content.product_id,
          sku: content.sku,
          name: content.name,
          original_quantity: content.original_quantity,
          current_quantity: content.current_quantity,
          clothing_id: content.clothing_id,
          oem_clothing_id: content.oem_clothing_id,
          is_oem: content.is_oem,
        }
      })

      // 计算新的剩余百分比
      const totalOriginalQuantity = restoredContents.reduce(
        (sum: number, content: any) => sum + (content.original_quantity || 0),
        0
      )
      const totalCurrentQuantity = restoredContents.reduce(
        (sum: number, content: any) => sum + (content.current_quantity || 0),
        0
      )

      console.log(`总原始数量: ${totalOriginalQuantity}, 总当前数量: ${totalCurrentQuantity}`)

      const newRemainingPercentage =
        totalOriginalQuantity > 0 ? totalCurrentQuantity / totalOriginalQuantity : 0

      const newStatus = newRemainingPercentage === 1.0 ? 'in_stock' : 'partially_shipped'
      console.log(`包裹状态: ${newStatus}, 剩余百分比: ${newRemainingPercentage}`)
      console.log('恢复后的内容:', JSON.stringify(restoredContents, null, 2))

      // 更新包裹
      await this.packageModel.updateOne(
        { package_code: reverseData.package_code },
        {
          $set: {
            status: newStatus,
            remaining_percentage: newRemainingPercentage,
            contents: restoredContents,
            last_updated_at: new Date(),
          },
        }
      )

      // 记录冲正日志
      const contentsChanges = packageDoc.contents.map((content: any) => {
        // 查找该SKU在出库日志中的变化
        const outboundChange = targetOutboundLog.contents_changes.find(
          (change: any) => change.sku === content.sku
        )

        if (outboundChange) {
          // 恢复的数量就是出库时减少的数量（取反）
          const restoredQuantity = -outboundChange.quantity_change
          const afterQuantity = content.current_quantity + restoredQuantity

          return {
            sku: content.sku,
            product_name: content.name,
            quantity_change: restoredQuantity,
            before_quantity: content.current_quantity,
            after_quantity: afterQuantity,
          }
        }

        // 如果没有找到对应的出库记录，则没有变化
        return {
          sku: content.sku,
          product_name: content.name,
          quantity_change: 0,
          before_quantity: content.current_quantity,
          after_quantity: content.current_quantity,
        }
      })

      await this.logOperation(
        {
          operation_type: 'reversal',
          operator_name: reverseData.operator,
          warehouse_id: packageDoc.warehouse_id,
          package_code: reverseData.package_code,
          contents_changes: contentsChanges,
          details: {
            original_status: packageDoc.status,
            original_remaining_percentage: packageDoc.remaining_percentage,
            classification_code: packageDoc.classification_code,
            notes: reverseData.notes || '出库冲正操作',
          },
          is_reversal: true,
        },
        reverseData.operation_date
      )

      // 更新服装库存数据（增加库存）
      const clothingUpdates: Array<{
        clothing_id?: string
        oem_clothing_id?: string
        quantity: number
      }> = []

      for (const contentChange of contentsChanges) {
        if (contentChange.quantity_change > 0) {
          // 查找对应的包裹内容
          const content = packageDoc.contents.find((c: any) => c.sku === contentChange.sku)
          if (content) {
            if (content.clothing_id) {
              clothingUpdates.push({
                clothing_id: content.clothing_id,
                quantity: contentChange.quantity_change,
              })
            }
            if (content.oem_clothing_id) {
              clothingUpdates.push({
                oem_clothing_id: content.oem_clothing_id,
                quantity: contentChange.quantity_change,
              })
            }
          }
        }
      }

      if (clothingUpdates.length > 0) {
        await this.updateClothingStock('inventory_gain', clothingUpdates)
      }

      return {
        code: 200,
        data: {
          reversed_package: reverseData.package_code,
          original_status: packageDoc.status,
          restored_status: newStatus,
          restored_remaining_percentage: newRemainingPercentage,
          affected_contents: contentsChanges,
        },
        message: '出库冲正成功',
      }
    } catch (error: any) {
      console.error('出库冲正操作失败:', error)
      throw new HttpException(error.message || '出库冲正操作失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 获取每日出库汇总数据
   * 按日期汇总出库数量，用于日历组件显示
   */
  async getDailyOutboundSummary(params: {
    year: number
    month: number
    warehouse_id?: string
    clothing_id?: string
    oem_clothing_id?: string
  }) {
    try {
      const { year, month, warehouse_id, clothing_id, oem_clothing_id } = params

      // 计算查询的月份的起止日期
      const startDate = new Date(year, month - 1, 1) // 月份从0开始，所以要减1
      const endDate = new Date(year, month, 0) // 下个月的第0天就是当前月的最后一天

      // 设置时间为当天的开始和结束
      startDate.setHours(0, 0, 0, 0)
      endDate.setHours(23, 59, 59, 999)

      console.log(`查询${year}年${month}月的出库汇总数据，起始日期: ${startDate.toISOString()}, 结束日期: ${endDate.toISOString()}`)

      // 构建查询条件
      const matchStage: any = {
        operation_type: 'outbound',
        timestamp: { $gte: startDate, $lte: endDate },
        is_reversal: { $ne: true },
        is_reversed: { $ne: true }
      }

      // 如果指定了仓库ID，则添加仓库筛选条件
      if (warehouse_id) {
        matchStage.warehouse_id = warehouse_id
      }

      // 如果指定了服装ID，需要通过包裹内容进行过滤
      if (clothing_id || oem_clothing_id) {
        // 先查找包含指定服装的包裹代码
        const packageQuery: any = {}
        if (clothing_id) {
          packageQuery['contents.clothing_id'] = clothing_id
        }
        if (oem_clothing_id) {
          packageQuery['contents.oem_clothing_id'] = oem_clothing_id
        }

        const packages = await this.packageModel.find(packageQuery, { package_code: 1 }).exec()
        const packageCodes = packages.map(pkg => pkg.package_code)

        if (packageCodes.length === 0) {
          // 如果没有找到相关包裹，返回空结果
          return {
            code: 200,
            message: '获取每日出库汇总成功',
            data: {}
          }
        }

        // 添加包裹代码筛选条件
        matchStage.package_code = { $in: packageCodes }
        console.log(`按服装ID过滤，找到${packageCodes.length}个相关包裹`)
      }

      // 使用聚合管道按日期汇总出库包裹数量
      const dailySummary = await this.operationLogModel.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: {
              year: { $year: '$timestamp' },
              month: { $month: '$timestamp' },
              day: { $dayOfMonth: '$timestamp' }
            },
            total_packages: { $sum: { $ifNull: ['$outbound_package_count', 0] } }, // 使用新的outbound_package_count字段
            operation_count: { $sum: 1 } // 记录操作次数
          }
        },
        {
          $project: {
            _id: 0,
            date: {
              $concat: [
                { $toString: '$_id.year' }, '-',
                { $toString: { $cond: [{ $lt: ['$_id.month', 10] }, { $concat: ['0', { $toString: '$_id.month' }] }, { $toString: '$_id.month' }] } }, '-',
                { $toString: { $cond: [{ $lt: ['$_id.day', 10] }, { $concat: ['0', { $toString: '$_id.day' }] }, { $toString: '$_id.day' }] } }
              ]
            },
            outbound_count: { $round: ['$total_packages', 2] }, // 包裹数量，保留2位小数
            operation_count: '$operation_count'
          }
        },
        { $sort: { date: 1 } }
      ])

      // 转换为前端需要的格式 {date: count}
      const result: Record<string, number> = {}
      dailySummary.forEach((item: any) => {
        result[item.date] = item.outbound_count
      })

      return {
        code: 200,
        message: '获取每日出库汇总成功',
        data: result
      }
    } catch (error) {
      console.error('获取每日出库汇总失败:', error)
      throw new HttpException('获取每日出库汇总失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
