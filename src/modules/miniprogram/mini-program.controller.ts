import { Controller, Post, Get, Body, Query, UseGuards, HttpCode, HttpStatus } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger'
import { MiniProgramService } from './mini-program.service'
import { ClothingService } from '../clothing/clothing.service'
import { OemClothingService } from '../oemClothing/oem-clothing.service'
import { TransportationService } from '../transportation/transportation.service'

import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard'
import { MiniProgramLoginDto, GetOpenIdDto, AddUserDto } from './dto'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { Package } from '../../models/package.model'
import { OperationLog } from '../../models/operationLog.model'
import { Warehouse } from '../../models/warehouse.model'

@ApiTags('微信小程序')
@Controller('miniprogram')
export class MiniProgramController {
  constructor(
    private readonly miniProgramService: MiniProgramService,
    private readonly clothingService: ClothingService,
    private readonly oemClothingService: OemClothingService,
    private readonly transportationService: TransportationService,

    @InjectModel('Package') private readonly packageModel: Model<Package>,
    @InjectModel('OperationLog') private readonly operationLogModel: Model<OperationLog>,
    @InjectModel('Warehouse') private readonly warehouseModel: Model<Warehouse>
  ) {}

  // 获取微信OpenID
  @Post('openid')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '获取微信OpenID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getOpenId(@Body() getOpenIdDto: GetOpenIdDto) {
    return this.miniProgramService.getOpenId(getOpenIdDto)
  }

  // 微信小程序登录
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '微信小程序登录' })
  @ApiResponse({ status: 200, description: '登录成功' })
  @ApiResponse({ status: 401, description: '登录失败' })
  async login(@Body() loginDto: MiniProgramLoginDto) {
    return this.miniProgramService.login(loginDto)
  }

  // 添加新用户
  @Post('operate')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '添加新用户' })
  @ApiResponse({ status: 200, description: '用户创建成功' })
  @ApiResponse({ status: 409, description: '用户已存在' })
  async addNewUser(@Body() addUserDto: AddUserDto) {
    return this.miniProgramService.addNewUser(addUserDto)
  }

  // 货运单列表（小程序专用）
  @Get('transportationList')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取货运单列表（小程序专用）' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getTransportationList(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @Query('arrived') arrived?: string, // 改为字符串类型
    @Query('supplier') supplier?: string,
    @Query("inbound_status") inbound_status?: string
  ) {
    const params = {
      page: parseInt(page),
      limit: parseInt(limit),
      supplier: supplier || '',
      arrived: arrived || '', // "0" 未到货, "1" 已到货, "" 全部
      inbound_status: inbound_status || ''
    }

    console.log('小程序前端传入的查询参数：', JSON.stringify(params))
    return this.transportationService.getMiniProgramTransportationList(params)
  }

  // 货运单详情（显示所有明细包括已入库的）
  @Get('transportationDetail')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取货运单详情（显示所有明细包括已入库的）' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getProcessedTransportationDetails(@Query('id') id: string) {
    try {
      const result = await this.transportationService.getAllTransportationDetails(id)
      return {
        code: 200,
        data: result,
        message: '获取货运详情成功'
      }
    } catch (error) {
      return {
        code: 500,
        data: null,
        message: error instanceof Error ? error.message : '获取货运详情失败'
      }
    }
  }

  // 入库页面的货运单详情（扁平化结构）
  @Get('inboundTransportationDetail')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取入库页面的货运单详情（扁平化结构）' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getInboundTransportationDetails(@Query('id') id: string) {
    try {
      const result = await this.transportationService.getProcessedTransportationDetails(id)
      return {
        code: 200,
        data: result,
        message: '获取货运详情成功'
      }
    } catch (error) {
      return {
        code: 500,
        data: null,
        message: error instanceof Error ? error.message : '获取货运详情失败'
      }
    }
  }

  // 货运公司列表（小程序专用）
  @Get('transportationSupplierOptions')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取货运公司列表（小程序专用）' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getTransportationCompanyList() {
    // 使用小程序专用的方法获取供应商列表
    return this.transportationService.getMiniProgramSupplierOptions()
  }

  // 更新到货日期
  @Post('updateArrivedDate')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新到货日期' })
  @ApiResponse({ status: 200, description: '更新成功' })
  async updateArrivedDate(@Body() updateData: { id: string; arrived_date: string }) {
    return this.transportationService.updateArrivedDate(updateData.id, updateData.arrived_date)
  }

  // 根据clothingID获取服装信息
  @Get('clothingInfo')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据clothingID获取服装信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getClothingInfo(@Query('clothing_id') clothing_id: string) {
    return this.clothingService.getClothingById(clothing_id)
  }

  // 根据OemClothingID获取OEM服装信息
  @Get('oemClothingInfo')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据OemClothingID获取OEM服装信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getOemClothingInfo(@Query('oem_clothing_id') oem_clothing_id: string) {
    console.log('oem_clothing_id', oem_clothing_id)
    return this.oemClothingService.getOemClothingByOemClothingId(oem_clothing_id)
  }

  // 搜索普通服装
  @Get('searchClothing')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '搜索普通服装' })
  @ApiResponse({ status: 200, description: '搜索成功' })
  async searchClothing(
    @Query('clothing_name') clothing_name?: string,
    @Query('clothing_code') clothing_code?: string,
    @Query('clothing_year') clothing_year?: string,
    @Query('supplier') supplier?: string,
    @Query('group_classification') group_classification?: string,
    @Query('long_or_short_sleeve') long_or_short_sleeve?: string,
    @Query('size') size?: string,
    @Query('style') style?: string,
    @Query('pocket_type') pocket_type?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number
  ) {
    const queryParams = {
      clothing_name,
      clothing_code,
      // 不再自动添加"年"字，因为前端传来的值已经包含"年"字
      clothing_year,
      supplier,
      group_classification,
      long_or_short_sleeve,
      size,
      style,
      pocket_type,
      page: page || 1,
      limit: limit || 20
    }
    console.log('搜索本厂服装参数:', queryParams)
    const result = await this.clothingService.getClothingList(queryParams)
    return {
      code: 200,
      data: result,
      message: '搜索成功'
    }
  }

  // 搜索OEM服装
  @Get('searchOemClothing')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '搜索OEM服装' })
  @ApiResponse({ status: 200, description: '搜索成功' })
  async searchOemClothing(
    @Query('oem_clothing_name') oem_clothing_name?: string,
    @Query('oem_clothing_code') oem_clothing_code?: string,
    @Query('oem_clothing_year') oem_clothing_year?: string,
    @Query('oem_supplier') oem_supplier?: string,
    @Query('classification') classification?: string,
    @Query('size') size?: string,
    @Query('style') style?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number
  ) {
    const queryParams = {
      oem_clothing_name,
      oem_clothing_code,
      // 不再自动添加"年"字，因为前端传来的值已经包含"年"字
      oem_clothing_year,
      oem_supplier,
      classification,
      size,
      style,
      page: page || 1,
      limit: limit || 20
    }
    console.log('搜索OEM服装参数:', queryParams)
    const result = await this.oemClothingService.getOemClothingList(queryParams)
    return {
      code: 200,
      data: result,
      message: '搜索成功'
    }
  }

  // 修改价格
  @Post('changePrice')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '修改价格' })
  @ApiResponse({ status: 200, description: '修改成功' })
  async changePrice(@Body() priceData: { transportation_id: string; price: number }) {
    const { transportation_id, price } = priceData
    return this.transportationService.updatePrice(transportation_id, price)
  }

  // ==================== 仓库管理相关API ====================

  // 获取仓库列表
  @Get('warehouseList')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取仓库列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getWarehouseList(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20',
    @Query('region') region?: string,
    @Query('status') status?: string
  ) {
    const params = {
      page: parseInt(page),
      limit: parseInt(limit),
      region,
      status
    }
    return this.miniProgramService.getWarehouseList(params)
  }

  // 获取仓库详情
  @Get('warehouseDetail')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取仓库详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getWarehouseDetail(@Query('warehouse_id') warehouse_id: string) {
    return this.miniProgramService.getWarehouseDetail(warehouse_id)
  }

  // 创建仓库
  @Post('createWarehouse')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建仓库' })
  @ApiResponse({ status: 200, description: '创建成功' })
  async createWarehouse(@Body() warehouseData: any) {
    return this.miniProgramService.createWarehouse(warehouseData)
  }

  // 更新仓库信息
  @Post('updateWarehouse')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新仓库信息' })
  @ApiResponse({ status: 200, description: '更新成功' })
  async updateWarehouse(@Body() updateData: { warehouse_id: string; [key: string]: any }) {
    const { warehouse_id, ...data } = updateData
    return this.miniProgramService.updateWarehouse(warehouse_id, data)
  }

  // 删除仓库
  @Post('deleteWarehouse')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除仓库' })
  @ApiResponse({ status: 200, description: '删除成功' })
  async deleteWarehouse(@Body() deleteData: { warehouse_id: string }) {
    return this.miniProgramService.deleteWarehouse(deleteData.warehouse_id)
  }


  // 注意：包裹内容详情现在通过新的仓库管理系统获取

  // ==================== 入库管理相关API ====================

  // 创建入库单
  @Post('createInboundOrder')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建入库单' })
  @ApiResponse({ status: 200, description: '创建成功' })
  async createInboundOrder(@Body() orderData: any) {
    return this.miniProgramService.createInboundOrder(orderData)
  }

  // 获取入库单列表
  @Get('inboundOrderList')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取入库单列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getInboundOrderList(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20',
    @Query('transportation_id') transportation_id?: string,
    @Query('status') status?: string
  ) {
    const params = {
      page: parseInt(page),
      limit: parseInt(limit),
      transportation_id,
      status
    }
    return this.miniProgramService.getInboundOrderList(params)
  }

  // 获取入库单详情
  @Get('inboundOrderDetail')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取入库单详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getInboundOrderDetail(@Query('inbound_order_id') inbound_order_id: string) {
    return this.miniProgramService.getInboundOrderDetail(inbound_order_id)
  }

  // ==================== 出库管理相关API ====================

  // 创建出库单
  @Post('createOutboundOrder')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建出库单' })
  @ApiResponse({ status: 200, description: '创建成功' })
  async createOutboundOrder(@Body() orderData: any) {
    return this.miniProgramService.createOutboundOrder(orderData)
  }

  // 获取出库单列表
  @Get('outboundOrderList')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取出库单列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getOutboundOrderList(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20',
    @Query('status') status?: string
  ) {
    const params = {
      page: parseInt(page),
      limit: parseInt(limit),
      status
    }
    return this.miniProgramService.getOutboundOrderList(params)
  }

  // 获取出库单详情
  @Get('outboundOrderDetail')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取出库单详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getOutboundOrderDetail(@Query('outbound_order_id') outbound_order_id: string) {
    return this.miniProgramService.getOutboundOrderDetail(outbound_order_id)
  }

  // ==================== 服装详情相关 API ====================

  // 获取服装详情
  @Get('getClothingDetail')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取服装详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getClothingDetail(@Query('clothing_id') clothing_id: string) {
    try {
      // 获取基础服装信息（包含到货数和库存数）
      const clothingInfo = await this.clothingService.getClothingById(clothing_id)

      if (!clothingInfo) {
        return {
          code: 404,
          message: '服装信息不存在',
          data: null
        }
      }

      // 从packages集合中统计包裹信息（用于显示包裹数量）
      const packages = await this.packageModel.find({
        'contents.clothing_id': clothing_id,
        status: { $in: ['in_stock', 'partially_shipped'] }
      })

      // 计算包裹统计数据
      let totalPackages = 0
      let totalPieces = 0

      packages.forEach(pkg => {
        const relevantContents = pkg.contents.filter(content => content.clothing_id === clothing_id)
        if (relevantContents.length > 0) {
          totalPackages++
          relevantContents.forEach(content => {
            totalPieces += content.current_quantity
          })
        }
      })

      // 组合返回数据，优先使用数据库中的到货数和库存数
      const detailInfo = {
        ...clothingInfo,
        arrival_quantity: clothingInfo.arrival_quantity || 0,
        stock_quantity: clothingInfo.stock_quantity || 0,
        total_packages: totalPackages,
        total_pieces: totalPieces
      }

      return {
        code: 200,
        message: '获取成功',
        data: detailInfo
      }
    } catch (error) {
      console.error('获取服装详情失败:', error)
      return {
        code: 500,
        message: '获取服装详情失败',
        data: null
      }
    }
  }

  // 获取OEM服装详情
  @Get('getOemClothingDetail')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取OEM服装详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getOemClothingDetail(@Query('oem_clothing_id') oem_clothing_id: string) {
    try {
      // 获取基础OEM服装信息（包含到货数和库存数）
      const oemClothingInfo = await this.oemClothingService.getOemClothingByOemClothingId(oem_clothing_id)

      if (!oemClothingInfo) {
        return {
          code: 404,
          message: 'OEM服装信息不存在',
          data: null
        }
      }

      // 从packages集合中统计包裹信息（用于显示包裹数量）
      const packages = await this.packageModel.find({
        'contents.oem_clothing_id': oem_clothing_id,
        status: { $in: ['in_stock', 'partially_shipped'] }
      })

      // 计算包裹统计数据
      let totalPackages = 0
      let totalPieces = 0

      packages.forEach(pkg => {
        const relevantContents = pkg.contents.filter(content => content.oem_clothing_id === oem_clothing_id)
        if (relevantContents.length > 0) {
          totalPackages++
          relevantContents.forEach(content => {
            totalPieces += content.current_quantity
          })
        }
      })

      // 组合返回数据，优先使用数据库中的到货数和库存数
      const detailInfo = {
        ...oemClothingInfo,
        arrival_quantity: oemClothingInfo.arrival_quantity || 0,
        stock_quantity: oemClothingInfo.stock_quantity || 0,
        total_packages: totalPackages,
        total_pieces: totalPieces
      }

      return {
        code: 200,
        message: '获取成功',
        data: detailInfo
      }
    } catch (error) {
      console.error('获取OEM服装详情失败:', error)
      return {
        code: 500,
        message: '获取OEM服装详情失败',
        data: null
      }
    }
  }

  // 获取服装库存明细
  @Get('getClothingInventory')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取服装库存明细' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getClothingInventory(
    @Query('clothing_id') clothing_id?: string,
    @Query('oem_clothing_id') oem_clothing_id?: string
  ) {
    try {
      if (!clothing_id && !oem_clothing_id) {
        return {
          code: 400,
          message: '请提供服装ID或OEM服装ID',
          data: null
        }
      }

      // 构建查询条件
      let matchCondition: any = {}
      if (clothing_id) {
        matchCondition = { 'contents.clothing_id': clothing_id }
      } else if (oem_clothing_id) {
        matchCondition = { 'contents.oem_clothing_id': oem_clothing_id }
      }

      // 查询包裹并按仓库聚合
      const inventoryData = await this.packageModel.aggregate([
        {
          $match: {
            ...matchCondition,
            status: { $in: ['in_stock', 'partially_shipped'] }
          }
        },
        {
          $lookup: {
            from: 'warehouses',
            localField: 'warehouse_id',
            foreignField: 'warehouse_id',
            as: 'warehouseInfo'
          }
        },
        {
          $unwind: '$warehouseInfo'
        },
        {
          $addFields: {
            // 保存完整的contents数组用于后续计算
            all_contents: '$contents'
          }
        },
        {
          $unwind: '$contents'
        },
        {
          $match: clothing_id
            ? { 'contents.clothing_id': clothing_id }
            : { 'contents.oem_clothing_id': oem_clothing_id }
        },
        {
          $group: {
            _id: {
              warehouse_id: '$warehouse_id',
              package_code: '$package_code'
            },
            warehouse_name: { $first: '$warehouseInfo.name' },
            warehouse_address: { $first: '$warehouseInfo.address' },
            package_type: { $first: '$package_type' },
            status: { $first: '$status' },
            remaining_percentage: { $first: '$remaining_percentage' },
            target_current_quantity: { $sum: '$contents.current_quantity' },
            target_original_quantity: { $sum: '$contents.original_quantity' },
            // 保留完整的contents数组用于计算
            all_contents: { $first: '$all_contents' }
          }
        },
        {
          $addFields: {
            // 计算包裹中所有服装的总original_quantity（用于混合包裹计算）
            package_total_original_quantity: {
              $sum: {
                $map: {
                  input: '$all_contents',
                  as: 'content',
                  in: '$$content.original_quantity'
                }
              }
            }
          }
        },
        {
          $addFields: {
            // 计算该服装在此包裹中的包裹数占比
            package_ratio: {
              $cond: {
                if: { $eq: ['$package_type', 'mixed'] },
                then: {
                  $cond: {
                    if: { $gt: ['$package_total_original_quantity', 0] },
                    then: { $divide: ['$target_original_quantity', '$package_total_original_quantity'] },
                    else: 0
                  }
                },
                else: 1 // 单一包裹，占比为1
              }
            }
          }
        },
        {
          $addFields: {
            // 计算实际的包裹数：考虑包裹状态和占比
            actual_package_count: {
              $cond: {
                if: { $eq: ['$target_current_quantity', 0] },
                then: 0, // 如果当前数量为0，包裹数为0
                else: {
                  $cond: {
                    if: { $eq: ['$package_type', 'mixed'] },
                    then: {
                      // 混合包裹：包裹数 = package_ratio × (current_quantity / original_quantity)
                      // 如果服装完全剩余，则占用其在包裹中的完整占比
                      // 如果服装部分出库，则按比例计算
                      $multiply: [
                        '$package_ratio',
                        { $divide: ['$target_current_quantity', '$target_original_quantity'] }
                      ]
                    },
                    else: {
                      // 单一包裹：直接使用包裹状态
                      $cond: {
                        if: { $eq: ['$status', 'in_stock'] },
                        then: 1,
                        else: '$remaining_percentage'
                      }
                    }
                  }
                }
              }
            }
          }
        },
        {
          $group: {
            _id: '$_id.warehouse_id',
            warehouse_name: { $first: '$warehouse_name' },
            warehouse_address: { $first: '$warehouse_address' },
            total_packages: { $sum: '$actual_package_count' },
            total_pieces: { $sum: '$target_current_quantity' },
            total_original_pieces: { $sum: '$target_original_quantity' }
          }
        },
        {
          $addFields: {
            remaining_percentage: {
              $round: [
                {
                  $multiply: [
                    { $divide: ['$total_pieces', '$total_original_pieces'] },
                    100
                  ]
                },
                2
              ]
            }
          }
        },
        {
          // 过滤掉库存数量为0的仓库记录
          $match: {
            total_pieces: { $gt: 0 }
          }
        },
        {
          $project: {
            warehouse_id: '$_id',
            warehouse_name: 1,
            warehouse_address: 1,
            total_packages: 1,
            total_pieces: 1,
            remaining_percentage: 1,
            _id: 0
          }
        },
        {
          $sort: { warehouse_name: 1 }
        }
      ])

      return {
        code: 200,
        message: '获取成功',
        data: inventoryData
      }
    } catch (error) {
      console.error('获取库存明细失败:', error)
      return {
        code: 500,
        message: '获取库存明细失败',
        data: null
      }
    }
  }

  // 获取服装仓库日志
  @Get('getClothingLogs')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取服装仓库日志' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getClothingLogs(
    @Query('clothing_id') clothing_id?: string,
    @Query('oem_clothing_id') oem_clothing_id?: string,
    @Query('page') page: string = '1',
    @Query('pageSize') pageSize: string = '20'
  ) {
    try {
      if (!clothing_id && !oem_clothing_id) {
        return {
          code: 400,
          message: '请提供服装ID或OEM服装ID',
          data: null
        }
      }

      const pageNum = parseInt(page)
      const pageSizeNum = parseInt(pageSize)
      const skip = (pageNum - 1) * pageSizeNum

      // 构建查询条件 - 通过SKU查询
      let skuPattern: string
      if (clothing_id) {
        skuPattern = clothing_id
      } else {
        skuPattern = `oem_${oem_clothing_id}`
      }

      // 查询操作日志
      const logs = await this.operationLogModel.aggregate([
        {
          $match: {
            'contents_changes.sku': { $regex: skuPattern, $options: 'i' },
            // 排除冲正记录和被冲正的记录
            is_reversal: { $ne: true },
            is_reversed: { $ne: true }
          }
        },
        {
          $lookup: {
            from: 'warehouses',
            localField: 'warehouse_id',
            foreignField: 'warehouse_id',
            as: 'warehouseInfo'
          }
        },
        {
          $unwind: '$warehouseInfo'
        },
        {
          $addFields: {
            operation_date: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: '$timestamp'
              }
            },
            operation_type_text: {
              $switch: {
                branches: [
                  { case: { $eq: ['$operation_type', 'inbound'] }, then: '入库' },
                  { case: { $eq: ['$operation_type', 'outbound'] }, then: '出库' },
                  { case: { $eq: ['$operation_type', 'transfer_in'] }, then: '移入' },
                  { case: { $eq: ['$operation_type', 'transfer_out'] }, then: '移出' },
                  { case: { $eq: ['$operation_type', 'inventory_surplus'] }, then: '盘盈' },
                  { case: { $eq: ['$operation_type', 'inventory_deficit'] }, then: '盘亏' }
                ],
                default: '未知'
              }
            },
            warehouse_name: '$warehouseInfo.name',
            // 计算相关服装的数量变化
            quantity: {
              $sum: {
                $map: {
                  input: {
                    $filter: {
                      input: '$contents_changes',
                      cond: { $regexMatch: { input: '$$this.sku', regex: skuPattern, options: 'i' } }
                    }
                  },
                  as: 'change',
                  in: { $abs: '$$change.quantity_change' }
                }
              }
            }
          }
        },
        {
          $project: {
            _id: 1,
            id: { $toString: '$_id' }, // 添加字符串格式的id字段
            operation_date: 1,
            operation_type: 1,
            operation_type_text: 1,
            warehouse_name: 1,
            quantity: 1,
            unit: '件',
            timestamp: 1
          }
        },
        {
          $sort: { timestamp: -1 }
        },
        {
          $skip: skip
        },
        {
          $limit: pageSizeNum
        }
      ])

      return {
        code: 200,
        message: '获取成功',
        data: logs
      }
    } catch (error) {
      console.error('获取仓库日志失败:', error)
      return {
        code: 500,
        message: '获取仓库日志失败',
        data: null
      }
    }
  }


}
