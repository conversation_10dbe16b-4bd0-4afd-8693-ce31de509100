import { ApiPropertyOptional } from '@nestjs/swagger'
import { IsString, IsNumber, IsOptional, IsArray, ValidateNested, IsMongoId } from 'class-validator'
import { Type } from 'class-transformer'
import { WorkDetailDto } from './create-division-work-assign.dto'

export class UpdateDivisionWorkAssignDto {
  @ApiPropertyOptional({ description: '分工ID' })
  @IsString()
  @IsOptional()
  division_work_id?: string

  @ApiPropertyOptional({ description: '所属年份' })
  @IsString()
  @IsOptional()
  division_work_year?: string

  @ApiPropertyOptional({ description: '员工ID' })
  @IsString()
  @IsOptional()
  staff_id?: string

  @ApiPropertyOptional({ description: '员工姓名' })
  @IsString()
  @IsOptional()
  staff_name?: string

  @ApiPropertyOptional({ description: '总价' })
  @IsString()
  @IsOptional()
  totalPrice?: string

  @ApiPropertyOptional({ description: '工序明细', type: [WorkDetailDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkDetailDto)
  @IsOptional()
  work_detail?: WorkDetailDto[]
}
