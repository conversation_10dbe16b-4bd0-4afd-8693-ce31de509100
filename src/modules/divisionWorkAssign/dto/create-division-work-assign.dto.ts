import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsString, IsNumber, IsOptional, IsArray, ValidateNested, IsMongoId } from 'class-validator'
import { Type } from 'class-transformer'

// 工序明细DTO
export class WorkDetailDto {
  @ApiProperty({ description: '工序ID' })
  @IsString()
  work_id: string

  @ApiProperty({ description: '工序名称' })
  @IsString()
  work_name: string

  @ApiProperty({ description: '件数' })
  @IsNumber()
  @Type(() => Number)
  pcs: number
}

export class CreateDivisionWorkAssignDto {
  @ApiProperty({ description: '分工ID' })
  @IsString()
  division_work_id: string

  @ApiProperty({ description: '所属年份' })
  @IsString()
  division_work_year: string

  @ApiProperty({ description: '员工ID' })
  @IsString()
  staff_id: string

  @ApiProperty({ description: '员工姓名' })
  @IsString()
  staff_name: string

  @ApiPropertyOptional({ description: '总价' })
  @IsString()
  @IsOptional()
  totalPrice?: string

  @ApiProperty({ description: '工序明细', type: [WorkDetailDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkDetailDto)
  work_detail: WorkDetailDto[]
}
