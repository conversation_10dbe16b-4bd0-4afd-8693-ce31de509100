import { ApiProperty } from '@nestjs/swagger'
import { IsArray, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { CreateDivisionWorkAssignDto } from './create-division-work-assign.dto'

export class BatchUpdateDivisionWorkAssignDto {
  @ApiProperty({ description: '分工分配列表', type: [CreateDivisionWorkAssignDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateDivisionWorkAssignDto)
  assigns: CreateDivisionWorkAssignDto[]
}
