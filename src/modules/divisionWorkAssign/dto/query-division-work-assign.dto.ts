import { ApiPropertyOptional } from '@nestjs/swagger'
import { IsString, IsNumber, IsOptional, IsMongoId } from 'class-validator'
import { Type } from 'class-transformer'

export class QueryDivisionWorkAssignDto {
  @ApiPropertyOptional({ description: '分工ID' })
  @IsString()
  @IsOptional()
  division_work_id?: string

  @ApiPropertyOptional({ description: '所属年份' })
  @IsString()
  @IsOptional()
  division_work_year?: string

  @ApiPropertyOptional({ description: '员工ID' })
  @IsString()
  @IsOptional()
  staff_id?: string

  @ApiPropertyOptional({ description: '员工姓名' })
  @IsString()
  @IsOptional()
  staff_name?: string

  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  page?: number

  @ApiPropertyOptional({ description: '每页条数', default: 10 })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  limit?: number
}
