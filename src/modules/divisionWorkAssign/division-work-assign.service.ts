import { Injectable, NotFoundException, InternalServerErrorException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, PipelineStage } from 'mongoose'
import { DivisionWorkAssign } from '../../models/divisionWorkAssign.model'
import { DivisionWork } from '../../models/divisionWork.model'
import { Staff } from '../../models/staff.model'
import { Work } from '../../models/work.model'
import {
  CreateDivisionWorkAssignDto,
  UpdateDivisionWorkAssignDto,
  QueryDivisionWorkAssignDto,
} from './dto'

@Injectable()
export class DivisionWorkAssignService {
  constructor(
    @InjectModel('DivisionWorkAssign')
    private readonly divisionWorkAssignModel: Model<DivisionWorkAssign>,
    @InjectModel('DivisionWork') private readonly divisionWorkModel: Model<DivisionWork>,
    @InjectModel('Staff') private readonly staffModel: Model<Staff>,
    @InjectModel('Work') private readonly workModel: Model<Work>
  ) {}

  // 创建分工分配
  async create(
    createDivisionWorkAssignDto: CreateDivisionWorkAssignDto
  ): Promise<DivisionWorkAssign> {
    // 验证分工ID是否存在
    const divisionWork = await this.divisionWorkModel
      .findOne({ division_work_id: createDivisionWorkAssignDto.division_work_id })
      .exec()
    if (!divisionWork) {
      throw new NotFoundException(`分工ID ${createDivisionWorkAssignDto.division_work_id} 不存在`)
    }

    // 验证员工ID是否存在
    const staff = await this.staffModel
      .findOne({ staff_id: createDivisionWorkAssignDto.staff_id })
      .exec()
    if (!staff) {
      throw new NotFoundException(`员工ID ${createDivisionWorkAssignDto.staff_id} 不存在`)
    }

    // 创建并保存分工分配
    const divisionWorkAssign = new this.divisionWorkAssignModel(createDivisionWorkAssignDto)
    return divisionWorkAssign.save()
  }

  // 获取分工分配列表
  async findAll(queryDivisionWorkAssignDto: QueryDivisionWorkAssignDto): Promise<{
    total: number
    page: number
    limit: number
    data: any[]
  }> {
    const {
      division_work_id,
      division_work_year,
      staff_id,
      staff_name,
      page = 1,
      limit = 10,
    } = queryDivisionWorkAssignDto

    // 构建查询条件
    const matchStage: any = {}

    // 添加分工ID条件
    if (division_work_id) {
      matchStage.division_work_id = division_work_id
    }

    // 添加所属年份条件
    if (division_work_year) {
      matchStage.division_work_year = division_work_year
    }

    // 添加员工ID条件
    if (staff_id) {
      matchStage.staff_id = { $regex: staff_id, $options: 'i' }
    }

    // 添加员工姓名条件
    if (staff_name) {
      matchStage.staff_name = { $regex: staff_name, $options: 'i' }
    }

    // 计算分页
    const skip = (page - 1) * limit

    // 构建聚合管道
    const pipeline: PipelineStage[] = [
      // 匹配条件
      { $match: matchStage },
      // 按 _id 倒序排序
      { $sort: { _id: -1 } },
      // 分页
      { $skip: skip },
      { $limit: Number(limit) },
      // 与 division_work 集合关联
      {
        $lookup: {
          from: 'division_work',
          localField: 'division_work_id',
          foreignField: 'division_work_id',
          as: 'division_work_info',
        },
      },
      // 展开 division_work_info 数组
      {
        $unwind: {
          path: '$division_work_info',
          preserveNullAndEmptyArrays: true,
        },
      },
      // 添加 division_work 集合中的字段
      {
        $project: {
          _id: 1,
          division_work_id: 1,
          division_work_year: 1,
          staff_id: 1,
          staff_name: 1,
          totalPrice: 1,
          work_detail: 1,
          createTime: 1,
          lastChangeTime: 1,
          clothing_id: '$division_work_info.clothing_id',
          group_name: '$division_work_info.group_name',
          pcs: '$division_work_info.pcs',
          is_complete: '$division_work_info.is_complete',
        },
      },
    ]

    // 执行聚合查询
    const data = await this.divisionWorkAssignModel.aggregate(pipeline).exec()

    // 计算总数
    const countPipeline: PipelineStage[] = [{ $match: matchStage }, { $count: 'total' }]
    const totalResult = await this.divisionWorkAssignModel.aggregate(countPipeline).exec()
    const total = totalResult.length > 0 ? totalResult[0].total : 0

    return {
      total,
      page: Number(page),
      limit: Number(limit),
      data,
    }
  }

  // 获取单个分工分配
  async findOne(id: string): Promise<DivisionWorkAssign> {
    const divisionWorkAssign = await this.divisionWorkAssignModel.findById(id).exec()
    if (!divisionWorkAssign) {
      throw new NotFoundException(`分工分配ID ${id} 不存在`)
    }
    return divisionWorkAssign
  }

  // 根据分工ID获取分工分配列表
  async findByDivisionWorkId(divisionWorkId: string): Promise<DivisionWorkAssign[]> {
    return this.divisionWorkAssignModel.find({ division_work_id: divisionWorkId }).exec()
  }

  // 更新分工分配
  async update(
    id: string,
    updateDivisionWorkAssignDto: UpdateDivisionWorkAssignDto
  ): Promise<DivisionWorkAssign> {
    // 如果更新了分工ID，验证其是否存在
    if (updateDivisionWorkAssignDto.division_work_id) {
      const divisionWork = await this.divisionWorkModel
        .findOne({ division_work_id: updateDivisionWorkAssignDto.division_work_id })
        .exec()
      if (!divisionWork) {
        throw new NotFoundException(`分工ID ${updateDivisionWorkAssignDto.division_work_id} 不存在`)
      }
    }

    // 如果更新了员工ID，验证其是否存在
    if (updateDivisionWorkAssignDto.staff_id) {
      const staff = await this.staffModel
        .findOne({ staff_id: updateDivisionWorkAssignDto.staff_id })
        .exec()
      if (!staff) {
        throw new NotFoundException(`员工ID ${updateDivisionWorkAssignDto.staff_id} 不存在`)
      }
    }

    const updatedDivisionWorkAssign = await this.divisionWorkAssignModel
      .findByIdAndUpdate(
        id,
        { ...updateDivisionWorkAssignDto, lastChangeTime: new Date() },
        { new: true }
      )
      .exec()
    if (!updatedDivisionWorkAssign) {
      throw new NotFoundException(`分工分配ID ${id} 不存在`)
    }
    return updatedDivisionWorkAssign
  }

  // 删除分工分配
  async remove(id: string): Promise<{ success: boolean; message: string }> {
    const result = await this.divisionWorkAssignModel.findByIdAndDelete(id).exec()
    if (!result) {
      throw new NotFoundException(`分工分配ID ${id} 不存在`)
    }
    return { success: true, message: '删除成功' }
  }

  // 批量更新分工分配
  async batchUpdate(assigns: CreateDivisionWorkAssignDto[]) {
    try {
      const operations = assigns.map((assign) => {
        return {
          updateOne: {
            filter: {
              division_work_id: assign.division_work_id,
              staff_id: assign.staff_id,
            },
            update: {
              $set: {
                division_work_year: assign.division_work_year,
                staff_name: assign.staff_name,
                totalPrice: assign.totalPrice,
                work_detail: assign.work_detail,
                lastChangeTime: new Date(),
              },
            },
            upsert: true, // 如果不存在则创建
          },
        }
      })

      const result = await this.divisionWorkAssignModel.bulkWrite(operations)

      return {
        matched: result.matchedCount,
        modified: result.modifiedCount,
        upserted: result.upsertedCount,
      }
    } catch (error: any) {
      throw new InternalServerErrorException(`批量更新分工分配失败: ${error.message}`)
    }
  }

  // 批量创建分工分配
  async batchCreate(assigns: CreateDivisionWorkAssignDto[]) {
    try {
      const result = await this.divisionWorkAssignModel.insertMany(assigns)
      return result
    } catch (error: any) {
      throw new InternalServerErrorException(`批量创建分工分配失败: ${error.message}`)
    }
  }
}
