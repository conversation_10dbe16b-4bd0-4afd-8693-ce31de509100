import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpException,
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiParam, ApiQuery, ApiResponse } from '@nestjs/swagger'
import { DivisionWorkAssignService } from './division-work-assign.service'
import {
  CreateDivisionWorkAssignDto,
  UpdateDivisionWorkAssignDto,
  QueryDivisionWorkAssignDto,
  BatchUpdateDivisionWorkAssignDto,
} from './dto'
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard'

@ApiTags('分工分配管理')
@Controller('/division-work-assign')
@UseGuards(JwtAuthGuard)
export class DivisionWorkAssignController {
  constructor(private readonly divisionWorkAssignService: DivisionWorkAssignService) {}

  @Post()
  @ApiOperation({ summary: '创建分工分配' })
  async create(@Body() createDivisionWorkAssignDto: CreateDivisionWorkAssignDto) {
    try {
      const result = await this.divisionWorkAssignService.create(createDivisionWorkAssignDto)
      return {
        code: HttpStatus.CREATED,
        data: result,
        message: '创建分工分配成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '创建分工分配失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Get()
  @ApiOperation({ summary: '获取分工分配列表' })
  async findAll(@Query() queryDivisionWorkAssignDto: QueryDivisionWorkAssignDto) {
    try {
      const result = await this.divisionWorkAssignService.findAll(queryDivisionWorkAssignDto)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '获取分工分配列表成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取分工分配列表失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Get(':id')
  @ApiOperation({ summary: '获取分工分配详情' })
  @ApiParam({ name: 'id', description: '分工分配ID' })
  async findOne(@Param('id') id: string) {
    try {
      const result = await this.divisionWorkAssignService.findOne(id)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '获取分工分配详情成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取分工分配详情失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Get('division-work/:divisionWorkId')
  @ApiOperation({ summary: '根据分工ID获取分工分配列表' })
  @ApiParam({ name: 'divisionWorkId', description: '分工ID' })
  async findByDivisionWorkId(@Param('divisionWorkId') divisionWorkId: string) {
    try {
      const result = await this.divisionWorkAssignService.findByDivisionWorkId(divisionWorkId)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '获取分工分配列表成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取分工分配列表失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新分工分配' })
  @ApiParam({ name: 'id', description: '分工分配ID' })
  async update(
    @Param('id') id: string,
    @Body() updateDivisionWorkAssignDto: UpdateDivisionWorkAssignDto
  ) {
    try {
      const result = await this.divisionWorkAssignService.update(id, updateDivisionWorkAssignDto)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '更新分工分配成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '更新分工分配失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除分工分配' })
  @ApiParam({ name: 'id', description: '分工分配ID' })
  async remove(@Param('id') id: string) {
    try {
      const result = await this.divisionWorkAssignService.remove(id)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '删除分工分配成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '删除分工分配失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Post('batch-update')
  @ApiOperation({ summary: '批量更新分工分配' })
  @ApiResponse({ status: 200, description: '批量更新成功' })
  async batchUpdate(@Body() batchUpdateDto: BatchUpdateDivisionWorkAssignDto) {
    try {
      const result = await this.divisionWorkAssignService.batchUpdate(batchUpdateDto.assigns)
      return {
        data: {
          code: HttpStatus.OK,
          data: result,
          message: '批量更新分工分配成功',
        },
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '批量更新分工分配失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Post('batch-create')
  @ApiOperation({ summary: '批量创建分工分配' })
  @ApiResponse({ status: 200, description: '批量创建成功' })
  async batchCreate(@Body() batchCreateDto: BatchUpdateDivisionWorkAssignDto) {
    try {
      const result = await this.divisionWorkAssignService.batchCreate(batchCreateDto.assigns)
      return {
        data: {
          code: HttpStatus.OK,
          data: result,
          message: '批量创建分工分配成功',
        },
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '批量创建分工分配失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }
}
