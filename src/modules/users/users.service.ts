import { Injectable, ConflictException, NotFoundException, UnauthorizedException } from '@nestjs/common'
import { CreateUserDto } from './dto/create-user.dto'
import * as bcrypt from 'bcrypt'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { User } from '../../models/user.model'

@Injectable()
export class UsersService {
  constructor(@InjectModel('User') private userModel: Model<User>) {}

  private hashPassword(password: string): string {
    // 使用 bcrypt 进行哈希，盐值轮数为 10
    return bcrypt.hashSync(password, 10)
  }

  private comparePassword(password: string, hash: string): boolean {
    // 使用 bcrypt 比较密码
    return bcrypt.compareSync(password, hash)
  }
  // 创建用户
  async create(createUserDto: CreateUserDto) {
    try {
      // 检查用户名是否已存在
      const existingUser = await this.userModel
        .findOne({
          userName: createUserDto.userName,
        })
        .exec()

      if (existingUser) {
        throw new ConflictException('用户名已存在')
      }

      // 加密密码
      const hashedPassword = this.hashPassword(createUserDto.userPwd)

      // 创建用户
      const now = new Date()
      const newUser = new this.userModel({
        userName: createUserDto.userName,
        userPwd: hashedPassword,
        createTime: now,
        lastLoginTime: now,
      })

      const savedUser = await newUser.save()
      const userObject = savedUser.toObject()

      // 不返回密码
      const { userPwd, ...result } = userObject
      return {
        ...result,
        id: result._id.toString(),
      }
    } catch (error) {
      console.error('创建用户错误:', error)
      throw error
    }
  }

  // 根据用户名查找用户
  async findByUsername(userName: string) {
    try {
      const user = await this.userModel
        .findOne({
          userName,
        })
        .exec()

      if (!user) {
        throw new UnauthorizedException('用户不存在')
      }

      const userObj = user.toObject()
      return {
        ...userObj,
        id: userObj._id.toString(),
      }
    } catch (error) {
      console.error('查找用户错误:', error)
      throw error
    }
  }
  // 根据ID查找用户
  async findById(id: string) {
    try {
      const user = await this.userModel.findById(id).exec()

      if (!user) {
        throw new NotFoundException('用户不存在')
      }

      const userObj = user.toObject()
      const { userPwd, ...result } = userObj
      return {
        ...result,
        id: result._id.toString(),
      }
    } catch (error) {
      console.error('根据ID查找用户错误:', error)
      throw error
    }
  }
  // 验证用户
  async validateUser(userName: string, password: string) {
    try {
      const user = await this.findByUsername(userName)

      if (!user.userPwd) {
        throw new UnauthorizedException('账号或密码错误')
      }

      // 使用 comparePassword 方法验证密码
      const isPasswordValid = this.comparePassword(password, user.userPwd)

      if (!isPasswordValid) {
        throw new UnauthorizedException('账号或密码错误')
      }

      const { userPwd, ...result } = user
      return result
    } catch (error) {
      console.error('验证用户错误:', error)
      throw new UnauthorizedException('账号或密码错误')
    }
  }
}
