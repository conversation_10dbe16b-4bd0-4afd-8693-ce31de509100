import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { Staff } from '../../models/staff.model'
import { DivisionWorkComplete } from '../../models/divisionWorkComplete.model'
import { DivisionWork } from '../../models/divisionWork.model'
import { Clothing } from '../../models/clothing.model'
import { Work } from '../../models/work.model'
import { CreateStaffDto, UpdateStaffDto, QueryStaffDto } from './dto'

// 定义返回类型接口
interface StaffListResponse {
  total: number
  page: number
  limit: number
  data: Staff[]
}

// 定义工资明细数据接口
interface SalaryDetail {
  组名: string
  服装名称: string
  工资明细: string
  总价: string
}

// 定义工资总计数据接口
interface WorkTotal {
  组名: string
  工序名称: string
  工序单价: string
  总件数: number
}

@Injectable()
export class StaffService {
  constructor(
    @InjectModel('Staff') private readonly staffModel: Model<Staff>,
    @InjectModel('DivisionWorkComplete')
    private readonly divisionWorkCompleteModel: Model<DivisionWorkComplete>,
    @InjectModel('DivisionWork') private readonly divisionWorkModel: Model<DivisionWork>,
    @InjectModel('Clothing') private readonly clothingModel: Model<Clothing>,
    @InjectModel('Work') private readonly workModel: Model<Work>
  ) {}

  // 创建员工
  async create(createStaffDto: CreateStaffDto): Promise<Staff> {
    const staff = new this.staffModel(createStaffDto)
    return staff.save()
  }

  // 获取员工列表，支持分页和筛选
  async findAll(queryParams: QueryStaffDto) {
    console.log('后端收到的查询参数：', JSON.stringify(queryParams))
    const startTime = Date.now()

    const {
      staff_id,
      name,
      pinyin,
      gender,
      id_number,
      tel,
      add,
      post,
      posts,
      floor,
      floors,
      clearing,
      page = 1,
      limit = 10,
    } = queryParams

    const skip = (page - 1) * limit

    // 构建查询条件
    const filter: any = {}

    // 处理员工编号和姓名条件
    if (staff_id) {
      filter.staff_id = { $regex: staff_id, $options: 'i' }
    }

    if (name) {
      filter.name = { $regex: name, $options: 'i' }
    }

    if (pinyin) {
      filter.pinyin = { $regex: pinyin, $options: 'i' }
    }

    if (gender) {
      filter.gender = gender
    }

    if (id_number) {
      filter.id_number = { $regex: id_number, $options: 'i' }
    }

    if (tel) {
      filter.tel = { $regex: tel, $options: 'i' }
    }

    if (add) {
      filter.add = { $regex: add, $options: 'i' }
    }

    // 处理职位条件
    if (post) {
      filter.post = post
    } else if (posts) {
      // 处理可能是字符串的情况
      const postsArray = Array.isArray(posts) ? posts : String(posts).split(',')
      if (postsArray.length > 0 && postsArray[0] !== '') {
        filter.post = { $in: postsArray }
      }
    }

    // 处理楼层/部门条件
    if (floor) {
      filter.floor = floor
    } else if (floors) {
      // 处理可能是字符串的情况
      const floorsArray = Array.isArray(floors) ? floors : String(floors).split(',')
      if (floorsArray.length > 0 && floorsArray[0] !== '') {
        filter.floor = { $in: floorsArray }
      }
    }

    // 处理结算状态
    if (clearing !== undefined) {
      filter.clearing = clearing
    }

    console.log('构建的查询条件：', JSON.stringify(filter))

    try {
      // 优化查询：根据是否需要总数来决定是否执行countDocuments
      let total = 0
      let data: Staff[] = []

      // 选择要返回的字段，减少数据传输量
      const projection = {
        staff_id: 1,
        name: 1,
        pinyin: 1,
        gender: 1,
        post: 1,
        floor: 1,
        clearing: 1,
        _id: 1
      }

      // 如果是大数据量查询（limit > 100），则不计算总数，提高性能
      if (limit > 100) {
        // 对于大数据量查询，只返回必要的字段
        data = await this.staffModel
          .find(filter, projection)
          .sort({ staff_id: -1 })
          .limit(limit)
          .exec()

        total = data.length // 使用返回的数据长度作为总数
      } else {
        // 对于小数据量查询，同时获取总数和数据
        const [countResult, dataResult] = await Promise.all([
          this.staffModel.countDocuments(filter).exec(),
          this.staffModel
            .find(filter)
            .sort({ staff_id: -1 })
            .skip(skip)
            .limit(limit)
            .exec()
        ])

        total = countResult
        data = dataResult
      }

      const endTime = Date.now()
      console.log(`查询结果：找到 ${total} 条记录，查询耗时: ${endTime - startTime}ms`)

      const response = {
        data: {
          total,
          page: Number(page),
          limit: Number(limit),
          staffList: data as Staff[],
        },
      }
      return response
    } catch (error) {
      console.error('查询员工列表失败：', error)
      throw error
    }
  }

  // 获取单个员工
  async findOne(id: string): Promise<Staff> {
    const staff = await this.staffModel.findById(id).exec()
    if (!staff) {
      throw new NotFoundException(`员工ID ${id} 不存在`)
    }
    return staff
  }

  // 更新员工
  async update(id: string, updateStaffDto: UpdateStaffDto): Promise<Staff> {
    const updatedStaff = await this.staffModel
      .findByIdAndUpdate(id, updateStaffDto, { new: true })
      .exec()

    if (!updatedStaff) {
      throw new NotFoundException(`员工ID ${id} 不存在`)
    }

    return updatedStaff
  }

  // 删除员工
  async remove(id: string): Promise<Staff> {
    const deletedStaff = await this.staffModel.findByIdAndDelete(id).exec()

    if (!deletedStaff) {
      throw new NotFoundException(`员工ID ${id} 不存在`)
    }

    return deletedStaff
  }

  // 获取楼层/部门选项
  async getDepartmentOptions(positions: string = ''): Promise<{ data: string[]; total: number }> {
    const filter: any = {}

    if (positions) {
      const positionArray = positions.split(',')
      filter.post = { $in: positionArray }
    }

    const result = await this.staffModel.distinct('floor', filter).exec()
    return {
      data: result.filter(Boolean) as string[], // 过滤掉空值
      total: result.filter(Boolean).length,
    }
  }

  // 获取职位选项
  async getPositionOptions(departments: string = ''): Promise<{ data: string[]; total: number }> {
    const filter: any = {}

    if (departments) {
      const departmentArray = departments.split(',')
      filter.floor = { $in: departmentArray }
    }

    const result = await this.staffModel.distinct('post', filter).exec()
    return {
      data: result.filter(Boolean) as string[], // 过滤掉空值
      total: result.filter(Boolean).length,
    }
  }

  // 获取性别选项
  async getStatusOptions(): Promise<{ data: string[]; total: number }> {
    const result = await this.staffModel.distinct('gender').exec()
    return {
      data: result.filter(Boolean) as string[], // 过滤掉空值
      total: result.filter(Boolean).length,
    }
  }

  // 导入JSON数据
  async importJsonData(
    data: any[]
  ): Promise<{ success: boolean; message: string; count?: number }> {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return { success: false, message: '数据不能为空' }
    }

    try {
      // 处理数据，确保必填字段存在
      const processedData = data.map((item) => ({
        staff_id: item.staff_id || `${Date.now()}${Math.floor(Math.random() * 1000)}`,
        name: item.name || '未命名',
        pinyin: item.pinyin || '',
        gender: item.gender || '',
        id_number: item.id_number || '',
        tel: item.tel || '',
        add: item.add || '',
        bank_card_number: item.bank_card_number || '',
        post: item.post || '',
        floor: item.floor || '',
        clearing: item.clearing || false,
        salary: item.salary || 0,
        lastChangeTime: item.lastChangeTime ? new Date(item.lastChangeTime) : null,
      }))

      // 批量插入数据
      const result = await this.staffModel.insertMany(processedData, { ordered: false })
      return {
        success: true,
        message: `成功导入 ${result.length} 条员工数据`,
        count: result.length,
      }
    } catch (error) {
      console.error('导入员工数据失败：', error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      return { success: false, message: `导入员工数据失败: ${errorMessage}` }
    }
  }

  // 导出单个员工工资
  async exportSalary(year: string, staffId?: string) {
    try {
      // 定义返回数据类型
      type ExportSalaryResponseType = {
        data: {
          staffSalaryData: SalaryDetail[]
          totalData: WorkTotal[]
        }
        message: string
      }

      // 1. 构建查询条件
      const query: any = { division_work_year: year }

      // 如果指定了员工ID，则添加到查询条件中
      if (staffId) {
        query.staff_id = staffId
      } else {
        return {
          data: {
            staffSalaryData: [],
            totalData: [],
          },
          message: '请先选择员工。',
        }
      }

      // 2. 获取指定年份的分工完成数据
      const divisionWorkCompleteList = await this.divisionWorkCompleteModel.find(query).exec()

      console.log(`查询到 ${divisionWorkCompleteList.length} 条分工完成数据`)

      if (!divisionWorkCompleteList || divisionWorkCompleteList.length === 0) {
        return {
          data: {
            staffSalaryData: [],
            totalData: [],
          },
          message: `没有找到 ${year} 年员工ID为 ${staffId} 的分工完成数据`,
        }
      }

      // 3. 获取所有相关的分工数据
      const divisionWorkIds = [
        ...new Set(divisionWorkCompleteList.map((item) => item.division_work_id)),
      ]
      const divisionWorkList = await this.divisionWorkModel
        .find({ division_work_id: { $in: divisionWorkIds } })
        .exec()

      // 创建分工ID到分工数据的映射
      const divisionWorkMap = new Map()
      divisionWorkList.forEach((item) => {
        divisionWorkMap.set(item.division_work_id, item)
      })

      // 4. 获取所有相关的服装数据
      const clothingIds = [...new Set(divisionWorkList.map((item) => item.clothing_id))]
      const clothingList = await this.clothingModel
        .find({ clothing_id: { $in: clothingIds } })
        .exec()

      // 创建服装ID到服装名称的映射
      const clothingMap = new Map()
      clothingList.forEach((item) => {
        clothingMap.set(item.clothing_id, item.clothing_name)
      })

      // 5. 收集所有工序ID，用于查询工序信息
      const workIds = new Set<string>()
      for (const complete of divisionWorkCompleteList) {
        if (complete.work_detail && Array.isArray(complete.work_detail)) {
          for (const detail of complete.work_detail) {
            if (detail.work_id) {
              workIds.add(detail.work_id)
            }
          }
        }
      }

      // 6. 查询工序信息，获取工序名称和单价
      const workList = await this.workModel.find({ work_id: { $in: Array.from(workIds) } }).exec()

      // 创建工序ID到工序数据的映射
      const workMap = new Map()
      workList.forEach((item) => {
        workMap.set(item.work_id, item)
      })

      // 7. 处理明细数据
      const staffSalaryData: SalaryDetail[] = []

      for (const complete of divisionWorkCompleteList) {
        try {
          const divisionWork = divisionWorkMap.get(complete.division_work_id)
          if (!divisionWork) {
            console.warn(`未找到分工ID为 ${complete.division_work_id} 的分工数据`)
            continue
          }

          const groupName = divisionWork.group_name || ''
          const clothingId = divisionWork.clothing_id
          const clothingName = clothingMap.get(clothingId) || clothingId

          // 检查工序明细是否存在
          if (!complete.work_detail || !Array.isArray(complete.work_detail)) {
            console.warn(
              `员工 ${complete.staff_id}(${complete.staff_name}) 的工序明细不存在或格式不正确`
            )
            continue
          }

          // 补全工序名称并排序
          const workDetails = complete.work_detail
            .map((detail) => {
              console.log('detail1111:', detail)
              let workName = detail.work_name
              if (!workName) {
                // 如果工序名称为空，尝试从工序映射中获取
                const work = workMap.get(detail.work_id)
                workName = work ? work.work_name : `未知工序-${detail.work_id || ''}`
              }
              return {
                work_id: detail.work_id,
                pcs: detail.pcs,
                work_name: workName,
              }
            })
            .sort((a, b) => {
              // 按工序名称排序
              return (a.work_name || '').localeCompare(b.work_name || '')
            })

          // 构建工序明细字符串
          const workDetailStr = workDetails
            .map((detail) => `${detail.work_name} ${detail.pcs}`)
            .join(' | ')
          console.log('workDetailStr1111222:', workDetailStr)

          staffSalaryData.push({
            组名: groupName,
            服装名称: clothingName,
            工资明细: workDetailStr,
            总价: complete.totalPrice || '0',
          })
        } catch (error) {
          console.error(
            `处理员工 ${complete.staff_id || '未知'}(${complete.staff_name || '未知'}) 的工资明细数据时出错:`,
            error
          )
        }
      }

      // 8. 处理总计数据
      // 按组名和工序ID两个条件同时满足进行分类汇总总件数
      const groupWorkTotals = new Map<string, WorkTotal>()

      for (const complete of divisionWorkCompleteList) {
        const divisionWork = divisionWorkMap.get(complete.division_work_id)
        if (!divisionWork) continue

        const groupName = divisionWork.group_name || ''

        // 检查工序明细是否存在
        if (!complete.work_detail || !Array.isArray(complete.work_detail)) {
          continue
        }

        for (const detail of complete.work_detail) {
          // 获取工序名称，如果为空则从工序映射中获取
          let workName = detail.work_name || ''
          let workPrice = '0'

          if (!workName || workName.includes('未知工序')) {
            const work = workMap.get(detail.work_id)
            if (work) {
              workName = work.work_name
              workPrice = work.work_price.toString()
            } else {
              workName = `未知工序-${detail.work_id || ''}`
            }
          } else {
            // 尝试从工序映射中获取工序单价
            const work = workMap.get(detail.work_id)
            if (work) {
              workPrice = work.work_price.toString()
            }
          }

          const key = `${groupName}-${detail.work_id}`

          const currentTotal = groupWorkTotals.get(key) || {
            组名: groupName,
            工序名称: workName,
            工序单价: workPrice,
            总件数: 0,
          }

          currentTotal.总件数 += detail.pcs || 0
          groupWorkTotals.set(key, currentTotal)
        }
      }

      // 转换为数组并排序
      const totalData: WorkTotal[] = Array.from(groupWorkTotals.values()).sort((a, b) => {
        // 先按组名排序，再按工序名称排序
        const groupCompare = a.组名.localeCompare(b.组名)
        if (groupCompare !== 0) return groupCompare
        return a.工序名称.localeCompare(b.工序名称)
      })

      return {
        data: {
          staffSalaryData,
          totalData,
        },
        message: '导出员工工资数据成功',
      }
    } catch (error) {
      console.error('导出员工工资数据失败：', error)
      throw error
    }
  }

  //利用exportSalary方法批量导出员工工资为Excel文件，并打包成zip压缩包
  async exportSalaryZip(year: string) {
    try {
      // 导入所需的库
      const XLSX = require('xlsx');
      const archiver = require('archiver');
      const fs = require('fs');
      const path = require('path');
      const os = require('os');

      // 1. 获取所有员工的工资数据
      const staffList = await this.staffModel.find().exec();

      if (!staffList || staffList.length === 0) {
        throw new Error('没有找到员工数据');
      }

      // 2. 创建临时目录用于存储Excel文件
      const tempDir = path.join(os.tmpdir(), `staff_salary_${Date.now()}`);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // 3. 为每个员工导出工资数据并生成Excel文件
      const excelFiles = [];

      for (const staff of staffList) {
        // 获取员工工资数据
        const salaryData = await this.exportSalary(year, staff.staff_id);

        // 如果没有数据，跳过该员工
        if (!salaryData.data ||
            !salaryData.data.staffSalaryData ||
            salaryData.data.staffSalaryData.length === 0) {
          continue;
        }

        // 创建工作簿
        const workbook = XLSX.utils.book_new();

        // 创建明细工作表
        const detailsData = salaryData.data.staffSalaryData;
        const detailsSheet = XLSX.utils.json_to_sheet(detailsData);

        // 设置明细工作表的列宽
        const detailsColWidths = [
          { wch: 15 }, // 组名
          { wch: 25 }, // 服装名称
          { wch: 50 }, // 工资明细
          { wch: 10 }, // 总价
        ];
        detailsSheet['!cols'] = detailsColWidths;

        // 添加明细工作表到工作簿
        XLSX.utils.book_append_sheet(workbook, detailsSheet, '明细');

        // 创建总计工作表
        const totalData = salaryData.data.totalData;
        const totalSheet = XLSX.utils.json_to_sheet(totalData);

        // 设置总计工作表的列宽
        const totalColWidths = [
          { wch: 15 }, // 组名
          { wch: 20 }, // 工序名称
          { wch: 10 }, // 工序单价
          { wch: 10 }, // 总件数
        ];
        totalSheet['!cols'] = totalColWidths;

        // 添加总计工作表到工作簿
        XLSX.utils.book_append_sheet(workbook, totalSheet, '总计');

        // 生成文件名：职位+员工姓名+导出日期
        const currentDate = new Date().toISOString().split('T')[0]; // 格式：YYYY-MM-DD
        const fileName = `${staff.post || '未知职位'}_${staff.name}_${currentDate}.xlsx`;
        const filePath = path.join(tempDir, fileName);

        // 写入Excel文件
        XLSX.writeFile(workbook, filePath);
        excelFiles.push(filePath);
      }

      // 4. 创建zip文件
      const zipFileName = `员工工资_${year}_${new Date().toISOString().split('T')[0]}.zip`;
      const zipFilePath = path.join(tempDir, zipFileName);
      const output = fs.createWriteStream(zipFilePath);
      const archive = archiver('zip', {
        zlib: { level: 9 } // 设置压缩级别
      });

      // 设置压缩完成的处理函数
      const zipPromise = new Promise((resolve, reject) => {
        output.on('close', () => {
          resolve(zipFilePath);
        });

        archive.on('error', (err: Error) => {
          reject(err);
        });
      });

      // 将输出流连接到归档
      archive.pipe(output);

      // 添加Excel文件到zip
      for (const file of excelFiles) {
        archive.file(file, { name: path.basename(file) });
      }

      // 完成归档
      await archive.finalize();

      // 等待压缩完成
      const finalZipPath = await zipPromise;

      // 5. 读取zip文件内容
      const zipContent = fs.readFileSync(finalZipPath);

      // 6. 清理临时文件
      for (const file of excelFiles) {
        fs.unlinkSync(file);
      }
      fs.unlinkSync(zipFilePath);
      fs.rmdirSync(tempDir);

      // 7. 返回zip文件内容和文件名
      return {
        fileName: zipFileName,
        content: zipContent,
        contentType: 'application/zip',
        message: `成功导出 ${excelFiles.length} 个员工的工资数据`
      };
    } catch (error) {
      console.error('导出员工工资数据失败：', error);
      throw error;
    }
  }

}
