import { IsString, IsOptional, IsDate, IsBoolean, IsN<PERSON>ber } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'

export class CreateStaffDto {
  @ApiProperty({ description: '员工编号', example: '182' })
  @IsString()
  staff_id: string

  @ApiProperty({ description: '员工姓名', example: '王美娟' })
  @IsString()
  name: string

  @ApiProperty({ description: '拼音简写', example: 'WMJ', required: false })
  @IsString()
  @IsOptional()
  pinyin?: string

  @ApiProperty({ description: '性别', example: '女', required: false })
  @IsString()
  @IsOptional()
  gender?: string

  @ApiProperty({ description: '身份证号', example: '330726196305212342', required: false })
  @IsString()
  @IsOptional()
  id_number?: string

  @ApiProperty({ description: '电话', example: '***********', required: false })
  @IsString()
  @IsOptional()
  tel?: string

  @ApiProperty({ description: '地址', example: '郑家坞镇西山下村', required: false })
  @IsString()
  @IsOptional()
  add?: string

  @ApiProperty({ description: '银行卡号', example: '****************', required: false })
  @IsString()
  @IsOptional()
  bank_card_number?: string

  @ApiProperty({ description: '职位', example: '管理', required: false })
  @IsString()
  @IsOptional()
  post?: string

  @ApiProperty({ description: '楼层/部门', example: '管理', required: false })
  @IsString()
  @IsOptional()
  floor?: string

  @ApiProperty({ description: '是否结算', example: false, required: false })
  @IsBoolean()
  @IsOptional()
  clearing?: boolean

  @ApiProperty({ description: '工资', example: 5000, required: false })
  @IsNumber()
  @IsOptional()
  salary?: number

  @ApiProperty({ description: '最后变更时间', example: '2023-01-01', required: false })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  lastChangeTime?: Date
}
