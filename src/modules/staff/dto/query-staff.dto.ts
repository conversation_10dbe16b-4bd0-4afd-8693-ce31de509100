import { IsString, IsOptional, IsArray, IsNumber, Min, IsBoolean } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'

export class QueryStaffDto {
  @ApiProperty({ description: '员工编号', required: false })
  @IsString()
  @IsOptional()
  staff_id?: string

  @ApiProperty({ description: '员工姓名', required: false })
  @IsString()
  @IsOptional()
  name?: string

  @ApiProperty({ description: '拼音简写', required: false })
  @IsString()
  @IsOptional()
  pinyin?: string

  @ApiProperty({ description: '性别', required: false })
  @IsString()
  @IsOptional()
  gender?: string

  @ApiProperty({ description: '身份证号', required: false })
  @IsString()
  @IsOptional()
  id_number?: string

  @ApiProperty({ description: '电话', required: false })
  @IsString()
  @IsOptional()
  tel?: string

  @ApiProperty({ description: '地址', required: false })
  @IsString()
  @IsOptional()
  add?: string

  @ApiProperty({ description: '职位', required: false })
  @IsString()
  @IsOptional()
  post?: string

  @ApiProperty({ description: '职位列表', required: false, type: [String] })
  @IsArray()
  @IsOptional()
  posts?: string[]

  @ApiProperty({ description: '楼层/部门', required: false })
  @IsString()
  @IsOptional()
  floor?: string

  @ApiProperty({ description: '楼层/部门列表', required: false, type: [String] })
  @IsArray()
  @IsOptional()
  floors?: string[]

  @ApiProperty({ description: '是否结算', required: false })
  @IsBoolean()
  @IsOptional()
  clearing?: boolean

  @ApiProperty({ description: '页码', required: false, default: 1 })
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  @IsOptional()
  page?: number = 1

  @ApiProperty({ description: '每页条数', required: false, default: 10 })
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  @IsOptional()
  limit?: number = 10
}
