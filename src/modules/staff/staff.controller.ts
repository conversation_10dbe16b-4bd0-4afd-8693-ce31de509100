import { Controller, Get, Post, Body, Patch, Param, Delete, Query, Res, Header } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger'
import { Response } from 'express'
import { StaffService } from './staff.service'
import { CreateStaffDto, UpdateStaffDto, QueryStaffDto } from './dto'

@ApiTags('员工管理')
@Controller('/staff')
export class StaffController {
  constructor(private readonly staffService: StaffService) {}

  @Post()
  @ApiOperation({ summary: '创建员工' })
  create(@Body() createStaffDto: CreateStaffDto) {
    return this.staffService.create(createStaffDto)
  }

  @Get()
  @ApiOperation({ summary: '获取员工列表' })
  findAll(@Query() query: any) {
    // 处理数组参数
    const queryStaffDto: QueryStaffDto = {} as any

    // 复制基本参数
    queryStaffDto.pinyin = query.pinyin
    queryStaffDto.page = query.page ? parseInt(query.page) : 1
    queryStaffDto.limit = query.limit ? parseInt(query.limit) : 10

    // 处理楼层/部门参数
    if (query['floors[]']) {
      // 如果是数组
      if (Array.isArray(query['floors[]'])) {
        queryStaffDto.floors = query['floors[]']
      } else {
        // 如果是单个值
        queryStaffDto.floors = [query['floors[]']]
      }
    } else if (query.floors) {
      if (Array.isArray(query.floors)) {
        queryStaffDto.floors = query.floors
      } else if (typeof query.floors === 'string') {
        queryStaffDto.floors = query.floors.split(',')
      }
    }

    // 处理职位参数
    if (query['posts[]']) {
      // 如果是数组
      if (Array.isArray(query['posts[]'])) {
        queryStaffDto.posts = query['posts[]']
      } else {
        // 如果是单个值
        queryStaffDto.posts = [query['posts[]']]
      }
    } else if (query.posts) {
      if (Array.isArray(query.posts)) {
        queryStaffDto.posts = query.posts
      } else if (typeof query.posts === 'string') {
        queryStaffDto.posts = query.posts.split(',')
      }
    }

    console.log('处理后的查询参数：', queryStaffDto)
    return this.staffService.findAll(queryStaffDto)
  }

  @Post('import-json')
  @ApiOperation({ summary: '导入JSON数据', description: '接收前端传来的JSON数据，批量写入数据库' })
  async importJson(@Body() importJsonDto: any) {
    console.log('接收到 JSON 数据导入请求:', {
      sheetName: importJsonDto.sheetName,
      fileName: importJsonDto.fileName,
      totalRecords: importJsonDto.totalRecords,
      dataLength: importJsonDto.data?.length || 0,
    })

    // 检查数据是否存在
    if (
      !importJsonDto.data ||
      !Array.isArray(importJsonDto.data) ||
      importJsonDto.data.length === 0
    ) {
      const response = { success: false, message: '数据不能为空' }
      console.log('返回结果(数据为空):', response)
      return response
    }

    try {
      // 打印数据示例以便于调试
      console.log('数据示例:', JSON.stringify(importJsonDto.data.slice(0, 1)))

      // 调用服务层方法处理数据
      const result = await this.staffService.importJsonData(importJsonDto.data)
      console.log('导入成功，返回结果:', result)
      return result
    } catch (error) {
      console.error('处理导入数据时出错:', error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      const response = { success: false, message: `处理导入数据时出错: ${errorMessage}` }
      console.log('返回结果(错误):', response)
      return response
    }
  }

  @Get('floor-options')
  @ApiOperation({ summary: '获取楼层/部门选项列表' })
  @ApiQuery({ name: 'post', required: false, description: '职位过滤，多个用逗号分隔' })
  getFloorOptions(@Query('post') posts: string) {
    return this.staffService.getDepartmentOptions(posts)
  }

  @Get('post-options')
  @ApiOperation({ summary: '获取职位选项列表' })
  @ApiQuery({ name: 'floor', required: false, description: '楼层/部门过滤，多个用逗号分隔' })
  getPostOptions(@Query('floor') floors: string) {
    return this.staffService.getPositionOptions(floors)
  }

  @Get('gender-options')
  @ApiOperation({ summary: '获取性别选项列表' })
  getGenderOptions() {
    return this.staffService.getStatusOptions()
  }

  @Get('export-salary')
  @ApiOperation({ summary: '导出员工工资' })
  @ApiQuery({ name: 'year', required: true, description: '年份' })
  @ApiQuery({ name: 'staff_id', required: false, description: '员工ID，如果提供则只导出该员工的工资' })
  async exportSalary(@Query('year') year: string, @Query('staff_id') staffId?: string) {
    return this.staffService.exportSalary(year, staffId)
  }

  @Get('export-salary-zip')
  @ApiOperation({ summary: '批量导出所有员工工资并打包成ZIP' })
  @ApiQuery({ name: 'year', required: true, description: '年份' })
  @Header('Content-Type', 'application/zip')
  async exportSalaryZip(@Query('year') year: string, @Res() res: Response) {
    try {
      const result = await this.staffService.exportSalaryZip(year);

      // 设置响应头，对文件名进行编码
      const encodedFileName = encodeURIComponent(result.fileName);
      res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodedFileName}`);
      res.setHeader('Content-Type', result.contentType);

      // 发送文件内容
      res.send(result.content);
    } catch (error) {
      console.error('导出员工工资ZIP失败:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : '导出员工工资ZIP失败'
      });
    }
  }

  @Get(':id')
  @ApiOperation({ summary: '获取员工详情' })
  @ApiParam({ name: 'id', description: '员工ID' })
  findOne(@Param('id') id: string) {
    return this.staffService.findOne(id)
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新员工' })
  @ApiParam({ name: 'id', description: '员工ID' })
  update(@Param('id') id: string, @Body() updateStaffDto: UpdateStaffDto) {
    return this.staffService.update(id, updateStaffDto)
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除员工' })
  @ApiParam({ name: 'id', description: '员工ID' })
  remove(@Param('id') id: string) {
    return this.staffService.remove(id)
  }
}
