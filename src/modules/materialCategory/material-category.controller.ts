import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  Put,
  HttpStatus,
  HttpException,
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger'
import { MaterialCategoryService } from './material-category.service'
import { CreateMaterialCategoryDto } from './dto/create-material-category.dto'
import { UpdateMaterialCategoryDto } from './dto/update-material-category.dto'
import { QueryMaterialCategoryDto } from './dto/query-material-category.dto'

@ApiTags('物料分类管理')
@Controller('material-category')
export class MaterialCategoryController {
  constructor(private readonly materialCategoryService: MaterialCategoryService) {}

  @Post()
  @ApiOperation({ summary: '创建物料分类' })
  @ApiResponse({ status: 201, description: '分类创建成功' })
  async create(@Body() createMaterialCategoryDto: CreateMaterialCategoryDto) {
    try {
      const result = await this.materialCategoryService.create(createMaterialCategoryDto)
      return {
        code: HttpStatus.CREATED,
        data: result,
        message: '创建分类成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '创建分类失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Get()
  @ApiOperation({ summary: '获取物料分类列表' })
  @ApiResponse({ status: 200, description: '获取分类列表成功' })
  async findAll(@Query() queryParams: QueryMaterialCategoryDto) {
    try {
      const result = await this.materialCategoryService.findAll(queryParams)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '获取分类列表成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取分类列表失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Get('tree')
  @ApiOperation({ summary: '获取树形分类结构' })
  @ApiResponse({ status: 200, description: '获取树形结构成功' })
  findTree() {
    return this.materialCategoryService.findTree()
  }

  @Get('options')
  @ApiOperation({ summary: '获取分类选项' })
  @ApiResponse({ status: 200, description: '获取分类选项成功' })
  async getOptions(@Query('level') level?: number) {
    try {
      const result = await this.materialCategoryService.getOptions(level)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '获取分类选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取分类选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Get('children/:parentId')
  @ApiOperation({ summary: '获取子分类列表' })
  @ApiResponse({ status: 200, description: '获取子分类成功' })
  getChildren(@Param('parentId') parentId: string) {
    return this.materialCategoryService.getChildren(parentId)
  }

  @Get('generate-id')
  @ApiOperation({ summary: '生成分类编号' })
  @ApiResponse({ status: 200, description: '生成分类编号成功' })
  async generateCategoryId(
    @Query('level') level: string,
    @Query('parentCategoryId') parentCategoryId?: string
  ) {
    try {
      const levelNum = parseInt(level) || 1
      const categoryId = await this.materialCategoryService.generateCategoryId(levelNum, parentCategoryId)
      return {
        code: HttpStatus.OK,
        data: { category_id: categoryId },
        message: '生成分类编号成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '生成分类编号失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Get(':id')
  @ApiOperation({ summary: '获取分类详情' })
  @ApiResponse({ status: 200, description: '获取分类详情成功' })
  findOne(@Param('id') id: string) {
    return this.materialCategoryService.findOne(id)
  }

  @Put(':id')
  @ApiOperation({ summary: '更新分类' })
  @ApiResponse({ status: 200, description: '分类更新成功' })
  async update(@Param('id') id: string, @Body() updateMaterialCategoryDto: UpdateMaterialCategoryDto) {
    try {
      const result = await this.materialCategoryService.update(id, updateMaterialCategoryDto)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '更新分类成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '更新分类失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除分类' })
  @ApiResponse({ status: 200, description: '分类删除成功' })
  remove(@Param('id') id: string) {
    return this.materialCategoryService.remove(id)
  }
}
