import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsString, IsNumber, IsOptional } from 'class-validator'

export class CreateMaterialCategoryDto {
  @ApiProperty({ description: '分类编号' })
  @IsString()
  category_id: string

  @ApiProperty({ description: '分类名称' })
  @IsString()
  category_name: string

  @ApiPropertyOptional({ description: '父分类ID' })
  @IsOptional()
  @IsString()
  parent_category_id?: string

  @ApiProperty({ description: '分类级别' })
  @IsNumber()
  level: number

  @ApiPropertyOptional({ description: '排序顺序' })
  @IsOptional()
  @IsNumber()
  sort_order?: number

  @ApiPropertyOptional({ description: '描述' })
  @IsOptional()
  @IsString()
  description?: string

  @ApiPropertyOptional({ description: '状态' })
  @IsOptional()
  @IsString()
  state?: string
}
