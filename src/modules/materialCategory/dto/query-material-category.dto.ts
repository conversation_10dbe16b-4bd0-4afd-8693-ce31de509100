import { ApiPropertyOptional } from '@nestjs/swagger'
import { IsOptional, IsString, IsNumber } from 'class-validator'
import { Transform } from 'class-transformer'

export class QueryMaterialCategoryDto {
  @ApiPropertyOptional({ description: '分类名称' })
  @IsOptional()
  @IsString()
  category_name?: string

  @ApiPropertyOptional({ description: '分类级别' })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  level?: number

  @ApiPropertyOptional({ description: '父分类ID' })
  @IsOptional()
  @IsString()
  parent_category_id?: string

  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  page?: number = 1

  @ApiPropertyOptional({ description: '每页数量', default: 10 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  limit?: number = 10
}
