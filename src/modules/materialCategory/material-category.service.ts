import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { MaterialCategory } from '../../models/materialCategory.model'
import { CreateMaterialCategoryDto } from './dto/create-material-category.dto'
import { UpdateMaterialCategoryDto } from './dto/update-material-category.dto'
import { QueryMaterialCategoryDto } from './dto/query-material-category.dto'

@Injectable()
export class MaterialCategoryService {
  constructor(
    @InjectModel('MaterialCategory') private readonly materialCategoryModel: Model<MaterialCategory>,
  ) {}

  // 获取物料分类列表，支持分页和筛选
  async findAll(queryParams: QueryMaterialCategoryDto) {
    console.log('后端收到的查询参数：', JSON.stringify(queryParams))

    const { category_name, level, parent_category_id, page = 1, limit = 10 } = queryParams

    const skip = (page - 1) * limit

    // 构建查询条件
    const filter: any = {}

    if (category_name) {
      filter.category_name = { $regex: category_name, $options: 'i' }
    }

    if (level) {
      filter.level = level
    }

    if (parent_category_id) {
      filter.parent_category_id = parent_category_id
    }

    console.log('构建的查询条件：', JSON.stringify(filter))

    // 执行查询
    const total = await this.materialCategoryModel.countDocuments(filter).exec()
    const data = await this.materialCategoryModel
      .find(filter)
      .sort({ level: 1, sort_order: 1, category_id: 1 }) // 按级别、排序、编号排序
      .skip(skip)
      .limit(limit)
      .exec()

    console.log(`查询结果：找到 ${total} 条记录`)
    return {
      total,
      page: Number(page),
      limit: Number(limit),
      categoryList: data as MaterialCategory[],
    }
  }

  // 获取树形结构的分类列表
  async findTree(): Promise<MaterialCategory[]> {
    // 获取所有分类
    const allCategories = await this.materialCategoryModel
      .find({ state: '1' })
      .sort({ level: 1, sort_order: 1, category_id: 1 })
      .exec()

    // 构建树形结构
    const categoryMap = new Map<string, MaterialCategory & { children: MaterialCategory[] }>()
    const rootCategories: (MaterialCategory & { children: MaterialCategory[] })[] = []

    // 初始化所有分类
    allCategories.forEach(category => {
      const categoryWithChildren = {
        ...category.toObject(),
        children: []
      }
      categoryMap.set(category.category_id, categoryWithChildren)
    })

    // 构建父子关系
    allCategories.forEach(category => {
      const categoryWithChildren = categoryMap.get(category.category_id)!
      
      if (category.parent_category_id) {
        const parent = categoryMap.get(category.parent_category_id)
        if (parent) {
          parent.children.push(categoryWithChildren)
        }
      } else {
        rootCategories.push(categoryWithChildren)
      }
    })

    return rootCategories
  }

  // 获取单个分类
  async findOne(id: string): Promise<MaterialCategory> {
    const category = await this.materialCategoryModel.findById(id).exec()
    if (!category) {
      throw new NotFoundException(`分类ID ${id} 不存在`)
    }
    return category
  }

  // 创建分类
  async create(createMaterialCategoryDto: CreateMaterialCategoryDto): Promise<MaterialCategory> {
    const createdCategory = new this.materialCategoryModel(createMaterialCategoryDto)
    return createdCategory.save()
  }

  // 更新分类
  async update(id: string, updateMaterialCategoryDto: UpdateMaterialCategoryDto): Promise<MaterialCategory> {
    const updatedCategory = await this.materialCategoryModel
      .findByIdAndUpdate(id, updateMaterialCategoryDto, { new: true })
      .exec()

    if (!updatedCategory) {
      throw new NotFoundException(`分类ID ${id} 不存在`)
    }

    return updatedCategory
  }

  // 删除分类
  async remove(id: string): Promise<MaterialCategory> {
    // 检查是否有子分类
    const childCount = await this.materialCategoryModel.countDocuments({ parent_category_id: id }).exec()
    if (childCount > 0) {
      throw new Error('该分类下还有子分类，无法删除')
    }

    const deletedCategory = await this.materialCategoryModel.findByIdAndDelete(id).exec()

    if (!deletedCategory) {
      throw new NotFoundException(`分类ID ${id} 不存在`)
    }

    return deletedCategory
  }

  // 获取子分类列表
  async getChildren(parentId: string): Promise<MaterialCategory[]> {
    return this.materialCategoryModel
      .find({ parent_category_id: parentId, state: '1' })
      .sort({ sort_order: 1, category_id: 1 })
      .exec()
  }

  // 获取分类选项（用于下拉选择）
  async getOptions(level?: number): Promise<MaterialCategory[]> {
    const filter: any = { state: '1' }
    if (level) {
      filter.level = level
    }

    return this.materialCategoryModel
      .find(filter)
      .sort({ level: 1, sort_order: 1, category_id: 1 })
      .exec()
  }

  // 生成分类编号
  async generateCategoryId(level: number, parentCategoryId?: string): Promise<string> {
    let newCategoryId = ''

    if (level === 1) {
      // 主类别：CAT001, CAT002, CAT003...
      const categories = await this.materialCategoryModel
        .find({ level: 1 })
        .sort({ category_id: 1 })
        .exec()

      const maxNum = categories.reduce((max: number, cat: any) => {
        const match = cat.category_id.match(/^CAT(\d+)$/)
        if (match) {
          const num = parseInt(match[1])
          return num > max ? num : max
        }
        return max
      }, 0)

      newCategoryId = `CAT${String(maxNum + 1).padStart(3, '0')}`
    } else if (level === 2 && parentCategoryId) {
      // 子类别：CAT001001, CAT001002...
      const categories = await this.materialCategoryModel
        .find({ parent_category_id: parentCategoryId })
        .sort({ category_id: 1 })
        .exec()

      const maxNum = categories.reduce((max: number, cat: any) => {
        const match = cat.category_id.match(new RegExp(`^${parentCategoryId}(\\d+)$`))
        if (match) {
          const num = parseInt(match[1])
          return num > max ? num : max
        }
        return max
      }, 0)

      newCategoryId = `${parentCategoryId}${String(maxNum + 1).padStart(3, '0')}`
    } else if (level === 3 && parentCategoryId) {
      // 具体物料项：CAT001001001, CAT001001002...
      const categories = await this.materialCategoryModel
        .find({ parent_category_id: parentCategoryId })
        .sort({ category_id: 1 })
        .exec()

      const maxNum = categories.reduce((max: number, cat: any) => {
        const match = cat.category_id.match(new RegExp(`^${parentCategoryId}(\\d+)$`))
        if (match) {
          const num = parseInt(match[1])
          return num > max ? num : max
        }
        return max
      }, 0)

      newCategoryId = `${parentCategoryId}${String(maxNum + 1).padStart(3, '0')}`
    }

    // 如果没有生成编号，返回默认值
    if (!newCategoryId) {
      if (level === 1) {
        newCategoryId = 'CAT001'
      } else if (parentCategoryId) {
        newCategoryId = `${parentCategoryId}001`
      }
    }

    // 确保生成的编号唯一
    const existingCategory = await this.materialCategoryModel.findOne({ category_id: newCategoryId }).exec()
    if (existingCategory) {
      // 如果编号已存在，在末尾加1
      const match = newCategoryId.match(/(\d+)$/)
      if (match) {
        const num = parseInt(match[1]) + 1
        const prefix = newCategoryId.substring(0, newCategoryId.length - match[1].length)
        newCategoryId = `${prefix}${String(num).padStart(match[1].length, '0')}`
      }
    }

    return newCategoryId
  }
}
