import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { MaterialCategoryService } from './material-category.service'
import { MaterialCategoryController } from './material-category.controller'
import { MaterialCategorySchema } from '../../models/materialCategory.model'

@Module({
  imports: [
    MongooseModule.forFeature([{ name: 'MaterialCategory', schema: MaterialCategorySchema }]),
  ],
  controllers: [MaterialCategoryController],
  providers: [MaterialCategoryService],
  exports: [MaterialCategoryService],
})
export class MaterialCategoryModule {}
