import { Module } from '@nestjs/common'
import { OemClothingController } from './oem-clothing.controller'
import { OemClothingService } from './oem-clothing.service'
import { ModelsModule } from '../../models/models.module'

@Module({
  imports: [ModelsModule],
  controllers: [OemClothingController],
  providers: [OemClothingService],
  exports: [OemClothingService],
})
export class OemClothingModule {}
