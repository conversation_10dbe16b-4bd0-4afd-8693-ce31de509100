import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpException,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common'
import { OemClothingService } from './oem-clothing.service'
import { OemClothing } from '../../models/oemClothing.model'
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard'
import {
  CreateOemClothingDto,
  UpdateOemClothingDto,
  QueryOemClothingDto,
  OemClothingResponseDto,
  OemClothingListResponseDto,
  OemClothingYearOptionsResponseDto,
  ImportOemClothingBatchResponseDto,
} from './dto'

@Controller('oem-clothing')
@UseGuards(JwtAuthGuard)
export class OemClothingController {
  constructor(private readonly oemClothingService: OemClothingService) {}

  /**
   * 获取OEM服装列表
   * @param query 查询参数
   * @returns OEM服装列表和总数
   */
  @Get()
  @UsePipes(new ValidationPipe({ transform: true }))
  async getOemClothingList(@Query() query: QueryOemClothingDto) {
    try {
      console.log('前端传入的查询参数：', JSON.stringify(query))
      const result = await this.oemClothingService.getOemClothingList(query)

      return {
        code: HttpStatus.OK,
        data: result,
        message: '获取OEM服装列表成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取OEM服装列表失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 获取OEM服装年份选项
   * @param supplier 供应商筛选
   * @param classification 分类筛选
   * @returns 年份选项列表
   */
  @Get('options/years')
  async getOemClothingYearOptions(
    @Query('supplier') supplier?: string,
    @Query('classification') classification?: string
  ) {
    try {
      const years = await this.oemClothingService.getOemClothingYearOptions(
        supplier,
        classification
      )
      return {
        code: HttpStatus.OK,
        data: years,
        message: '获取OEM服装年份选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取OEM服装年份选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 获取供应商选项
   * @param year 年份筛选
   * @param classification 分类筛选
   * @returns 供应商选项列表
   */
  @Get('options/suppliers')
  async getSupplierOptions(
    @Query('year') year?: string,
    @Query('classification') classification?: string
  ) {
    try {
      const suppliers = await this.oemClothingService.getSupplierOptions(year, classification)
      return {
        code: HttpStatus.OK,
        data: suppliers,
        message: '获取供应商选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取供应商选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 获取分类选项
   * @param year 年份筛选
   * @param supplier 供应商筛选
   * @returns 分类选项列表
   */
  @Get('options/classifications')
  async getClassificationOptions(
    @Query('year') year?: string,
    @Query('supplier') supplier?: string
  ) {
    try {
      const classifications = await this.oemClothingService.getClassificationOptions(year, supplier)
      return {
        code: HttpStatus.OK,
        data: classifications,
        message: '获取分类选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取分类选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 获取尺码、款式选项
   * @returns 尺码、款式选项列表
   */
  @Get('options/two')
  async getTwoOptions() {
    try {
      const twoOptions = await this.oemClothingService.getTwoOptions()
      return {
        code: HttpStatus.OK,
        data: twoOptions,
        message: '获取尺码、款式选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取尺码、款式选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 获取尺码选项
   * @returns 尺码选项列表
   */
  @Get('options/sizes')
  async getSizeOptions() {
    try {
      const twoOptions = await this.oemClothingService.getTwoOptions()
      return {
        code: HttpStatus.OK,
        data: twoOptions.sizes,
        message: '获取尺码选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取尺码选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 获取款式选项
   * @returns 款式选项列表
   */
  @Get('options/styles')
  async getStyleOptions() {
    try {
      const twoOptions = await this.oemClothingService.getTwoOptions()
      return {
        code: HttpStatus.OK,
        data: twoOptions.styles,
        message: '获取款式选项成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '获取款式选项失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 根据ID获取OEM服装
   * @param id OEM服装ID
   * @returns OEM服装
   */
  @Get(':id')
  async getOemClothingById(@Param('id') id: string) {
    try {
      const oemClothing = await this.oemClothingService.getOemClothingById(id)
      return {
        code: HttpStatus.OK,
        data: oemClothing,
        message: '获取OEM服装成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.NOT_FOUND,
          message: error.message || '获取OEM服装失败',
        },
        HttpStatus.NOT_FOUND
      )
    }
  }

  /**
   * 创建OEM服装
   * @param oemClothingData OEM服装数据
   * @returns 创建的OEM服装
   */
  @Post()
  @UsePipes(new ValidationPipe({ transform: true }))
  async createOemClothing(@Body() oemClothingData: CreateOemClothingDto) {
    try {
      const newOemClothing = await this.oemClothingService.createOemClothing(oemClothingData)
      return {
        code: HttpStatus.CREATED,
        data: newOemClothing,
        message: '创建OEM服装成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.BAD_REQUEST,
          message: '创建OEM服装失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.BAD_REQUEST
      )
    }
  }

  /**
   * 更新OEM服装
   * @param id OEM服装ID
   * @param oemClothingData 更新的OEM服装数据
   * @returns 更新后的OEM服装
   */
  @Put(':id')
  @UsePipes(new ValidationPipe({ transform: true }))
  async updateOemClothing(@Param('id') id: string, @Body() oemClothingData: UpdateOemClothingDto) {
    try {
      const updatedOemClothing = await this.oemClothingService.updateOemClothing(
        id,
        oemClothingData
      )
      return {
        code: HttpStatus.OK,
        data: updatedOemClothing,
        message: '更新OEM服装成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.NOT_FOUND,
          message: error.message || '更新OEM服装失败',
        },
        HttpStatus.NOT_FOUND
      )
    }
  }

  /**
   * 删除OEM服装
   * @param id OEM服装ID
   * @returns 删除结果
   */
  @Delete(':id')
  async deleteOemClothing(@Param('id') id: string) {
    try {
      const result = await this.oemClothingService.deleteOemClothing(id)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '删除OEM服装成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.NOT_FOUND,
          message: error.message || '删除OEM服装失败',
        },
        HttpStatus.NOT_FOUND
      )
    }
  }

  /**
   * 导入JSON数据
   * @param importData 导入数据
   * @returns 导入结果
   */
  @Post('import-json')
  async importJsonData(@Body() importData: any) {
    try {
      const result = await this.oemClothingService.importJsonData(importData)
      console.log('导入JSON数据结果:', result)
      return {
        code: HttpStatus.OK,
        data: result,
        message: '导入JSON数据成功',
      }
    } catch (error: any) {
      throw new HttpException(
        {
          code: HttpStatus.BAD_REQUEST,
          message: '导入JSON数据失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.BAD_REQUEST
      )
    }
  }

  /**
   * 根据OEM服装名称和年份查找OEM服装
   * @param data 包含OEM服装名称数组和服装年份
   * @returns 查询到的OEM服装列表
   */
  @Post('find-by-names')
  async findOemClothingByNames(@Body() data: { oem_clothing_names: string[]; oem_clothing_year: string }) {
    try {
      console.log('接收到根据服装名称查找OEM服装请求:', JSON.stringify(data))

      const result = await this.oemClothingService.findOemClothingByNames(data)

      return {
        code: HttpStatus.OK,
        data: result,
        message: '查找OEM服装成功',
      }
    } catch (error: any) {
      console.error('根据服装名称查找OEM服装失败:', error.message)
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: '根据服装名称查找OEM服装失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }

  /**
   * 数据校准 - 重新统计并更新指定OEM服装的入库数和出库数
   * @param calibrateData 校准数据，包含查询条件或服装ID列表
   * @returns 校准结果
   */
  @Post('calibrate-data')
  async calibrateOemClothingData(@Body() calibrateData: {
    oemClothingIds?: string[];
    queryParams?: any
  }) {
    try {
      console.log('开始OEM服装数据校准...', calibrateData)

      const result = await this.oemClothingService.calibrateData(calibrateData)

      console.log('OEM服装数据校准完成:', result)

      return {
        code: HttpStatus.OK,
        data: result,
        message: 'OEM服装数据校准成功',
      }
    } catch (error: any) {
      console.error('OEM服装数据校准失败:', error.message)
      throw new HttpException(
        {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'OEM服装数据校准失败: ' + (error.message || '未知错误'),
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }
}
