import { Type } from 'class-transformer'
import {
  IsString,
  IsNumber,
  IsOptional,
  IsArray,
  ValidateNested,
  IsDate,
} from 'class-validator'
import { Expose } from 'class-transformer'
import { OemClothingImageDto } from './oem-clothing-image.dto'

/**
 * OEM服装响应 DTO
 */
export class OemClothingResponseDto {
  @Expose()
  _id: string

  @Expose()
  @IsString()
  oem_clothing_year: string

  @Expose()
  @IsString()
  oem_clothing_id: string

  @Expose()
  @IsString()
  oem_clothing_name: string

  @Expose()
  @IsString()
  @IsOptional()
  oem_supplier?: string

  @Expose()
  @IsString()
  @IsOptional()
  classification?: string

  @Expose()
  @IsString()
  @IsOptional()
  style?: string

  @Expose()
  @IsString()
  @IsOptional()
  size?: string

  @Expose()
  @IsNumber()
  @IsOptional()
  price?: number

  @Expose()
  @IsNumber()
  @IsOptional()
  in_pcs?: number

  @Expose()
  @IsNumber()
  @IsOptional()
  order_quantity?: number

  @Expose()
  @IsNumber()
  @IsOptional()
  shipments?: number

  @Expose()
  @IsString()
  @IsOptional()
  printed?: string

  @Expose()
  @IsString()
  @IsOptional()
  remark?: string

  @Expose()
  @IsString()
  @IsOptional()
  state?: string

  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OemClothingImageDto)
  @IsOptional()
  img?: OemClothingImageDto[]

  @Expose()
  @IsDate()
  @IsOptional()
  createTime?: Date

  @Expose()
  @IsDate()
  @IsOptional()
  lastChangeTime?: Date
}

/**
 * OEM服装列表响应 DTO
 */
export class OemClothingListResponseDto {
  @Expose()
  @IsNumber()
  total: number

  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OemClothingResponseDto)
  oemClothingList: OemClothingResponseDto[]
}

/**
 * OEM服装年份选项响应 DTO
 */
export class OemClothingYearOptionsResponseDto {
  @Expose()
  @IsArray()
  @IsString({ each: true })
  years: string[]
}

/**
 * 导入OEM服装批量响应 DTO
 */
export class ImportOemClothingBatchResponseDto {
  @Expose()
  @IsNumber()
  count: number

  @Expose()
  @IsString()
  message: string

  @Expose()
  success: boolean
}
