import { Type } from 'class-transformer'
import {
  IsString,
  IsNumber,
  IsOptional,
  IsArray,
  ValidateNested,
  IsDate,
} from 'class-validator'
import { OemClothingImageDto } from './oem-clothing-image.dto'

/**
 * 创建OEM服装 DTO
 */
export class CreateOemClothingDto {
  @IsString({ message: '所属年份必须是字符串' })
  oem_clothing_year: string

  @IsString({ message: '服装编号必须是字符串' })
  oem_clothing_id: string

  @IsString({ message: '服装名称必须是字符串' })
  oem_clothing_name: string

  @IsString({ message: '供应商必须是字符串' })
  @IsOptional()
  oem_supplier?: string

  @IsString({ message: '分类必须是字符串' })
  @IsOptional()
  classification?: string

  @IsString({ message: '款式必须是字符串' })
  @IsOptional()
  style?: string

  @IsString({ message: '尺码必须是字符串' })
  @IsOptional()
  size?: string

  @IsNumber({}, { message: '价格必须是数字' })
  @IsOptional()
  price?: number

  @IsNumber({}, { message: '入库数量必须是数字' })
  @IsOptional()
  in_pcs?: number

  @IsNumber({}, { message: '订单数量必须是数字' })
  @IsOptional()
  order_quantity?: number

  @IsNumber({}, { message: '出货数必须是数字' })
  @IsOptional()
  shipments?: number

  @IsString({ message: '印花必须是字符串' })
  @IsOptional()
  printed?: string

  @IsString({ message: '备注必须是字符串' })
  @IsOptional()
  remark?: string

  @IsString({ message: '状态必须是字符串' })
  @IsOptional()
  state?: string

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OemClothingImageDto)
  @IsOptional()
  img?: OemClothingImageDto[]
}
