import { Transform, Type } from 'class-transformer'
import { IsString, IsNumber, IsOptional, IsArray } from 'class-validator'

/**
 * 查询OEM服装 DTO
 */
export class QueryOemClothingDto {
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => (value ? parseInt(value) : 1))
  page?: number = 1

  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => (value ? parseInt(value) : 10))
  limit?: number = 10

  @IsString()
  @IsOptional()
  oem_clothing_year?: string

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Type(() => String)
  oem_clothing_years?: string[]

  @IsString()
  @IsOptional()
  oem_supplier?: string

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Type(() => String)
  oem_suppliers?: string[]

  @IsString()
  @IsOptional()
  classification?: string

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Type(() => String)
  classifications?: string[]

  @IsString()
  @IsOptional()
  oem_clothing_name?: string

  @IsString()
  @IsOptional()
  state?: string
}
