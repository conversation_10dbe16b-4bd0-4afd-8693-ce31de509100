import { ApiProperty } from '@nestjs/swagger'
import { IsArray, IsOptional, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { UpdateTransportationDto } from './update-transportation.dto'
import { CreateTransportationDetailDto } from './create-transportation-detail.dto'

export class UpdateTransportationWithDetailsDto {
  @ApiProperty({ description: '发货信息', type: UpdateTransportationDto })
  @ValidateNested()
  @Type(() => UpdateTransportationDto)
  transportation: UpdateTransportationDto

  @ApiProperty({
    description: '发货明细列表',
    type: [CreateTransportationDetailDto],
    required: false,
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateTransportationDetailDto)
  details?: Omit<CreateTransportationDetailDto, 'transportation_id'>[]
}
