import { ApiProperty } from '@nestjs/swagger'
import { Expose, Type } from 'class-transformer'
import { IsArray, IsDate, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator'

/**
 * 发货信息响应 DTO
 */
export class TransportationResponseDto {
  @Expose()
  _id: string

  @Expose()
  @IsString()
  transportation_id: string

  @Expose()
  @IsString()
  transportation_year: string

  @Expose()
  @IsDate()
  date_out: Date

  @Expose()
  @IsDate()
  @IsOptional()
  date_arrived?: Date

  @Expose()
  @IsString()
  supplier: string

  @Expose()
  @IsNumber()
  @IsOptional()
  total_package_quantity?: number

  @Expose()
  @IsNumber()
  @IsOptional()
  total_pcs?: number

  @Expose()
  @IsNumber()
  @IsOptional()
  weight?: number

  @Expose()
  @IsNumber()
  @IsOptional()
  price?: number

  @Expose()
  @IsString()
  @IsOptional()
  shipping_method?: string

  @Expose()
  @IsString()
  @IsOptional()
  remark?: string

  @Expose()
  @IsArray()
  @IsOptional()
  transportation_img?: Array<{ url: string; Key: string }>

  @Expose()
  @IsDate()
  @IsOptional()
  createTime?: Date
}
