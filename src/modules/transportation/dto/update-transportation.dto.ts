import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsOptional, IsDate, IsNumber, IsArray } from 'class-validator'
import { Type } from 'class-transformer'

export class UpdateTransportationDto {
  @ApiProperty({ description: '发货编号', required: false })
  @IsString()
  @IsOptional()
  transportation_id?: string

  @ApiProperty({ description: '发货年份', required: false })
  @IsString()
  @IsOptional()
  transportation_year?: string

  @ApiProperty({ description: '发货日期', required: false })
  @IsDate()
  @IsOptional()
  @Type(() => Date)
  date_out?: Date

  @ApiProperty({ description: '到货日期', required: false })
  @IsDate()
  @IsOptional()
  @Type(() => Date)
  date_arrived?: Date

  @ApiProperty({ description: '运输公司', required: false })
  @IsString()
  @IsOptional()
  supplier?: string

  @ApiProperty({ description: '包裹数量', required: false })
  @IsNumber()
  @IsOptional()
  total_package_quantity?: number

  @ApiProperty({ description: '总件数', required: false })
  @IsNumber()
  @IsOptional()
  total_pcs?: number

  @ApiProperty({ description: '重量', required: false })
  @IsNumber()
  @IsOptional()
  weight?: number

  @ApiProperty({ description: '价格', required: false })
  @IsNumber()
  @IsOptional()
  price?: number

  @ApiProperty({ description: '运输方式', required: false })
  @IsString()
  @IsOptional()
  shipping_method?: string

  @ApiProperty({ description: '备注', required: false })
  @IsString()
  @IsOptional()
  remark?: string

  @ApiProperty({ description: '发货图片', required: false, type: 'array' })
  @IsArray()
  @IsOptional()
  transportation_img?: Array<{ url: string; Key: string }>
}
