import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsNotEmpty, IsOptional, IsNumber } from 'class-validator'
import { Type } from 'class-transformer'

export class CreateTransportationDetailDto {
  @ApiProperty({ description: '发货ID' })
  @IsString()
  @IsNotEmpty({ message: '发货ID不能为空' })
  transportation_id: string

  @ApiProperty({ description: '序号', required: false })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  series_number?: number

  @ApiProperty({ description: '服装名称' })
  @IsString()
  @IsNotEmpty({ message: '服装名称不能为空' })
  clothing_name: string

  @ApiProperty({ description: '包裹数量', required: false })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  package_quantity?: number

  @ApiProperty({ description: '每包数量', required: false })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  QUP?: number

  @ApiProperty({ description: '出货数量' })
  @IsNumber()
  @IsNotEmpty({ message: '出货数量不能为空' })
  @Type(() => Number)
  out_pcs: number

  @ApiProperty({ description: '是否OEM', required: false })
  @IsString()
  @IsOptional()
  oem?: string

  @ApiProperty({ description: '服装编号' })
  @IsString()
  @IsNotEmpty({ message: '服装编号不能为空' })
  clothing_id: string

  @ApiProperty({ description: '款式', required: false })
  @IsString()
  @IsOptional()
  style?: string
}
