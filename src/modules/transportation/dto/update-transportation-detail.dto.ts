import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsOptional, IsNumber } from 'class-validator'
import { Type } from 'class-transformer'

export class UpdateTransportationDetailDto {
  @ApiProperty({ description: '发货ID', required: false })
  @IsString()
  @IsOptional()
  transportation_id?: string

  @ApiProperty({ description: '序号', required: false })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  series_number?: number

  @ApiProperty({ description: '服装名称', required: false })
  @IsString()
  @IsOptional()
  clothing_name?: string

  @ApiProperty({ description: '包裹数量', required: false })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  package_quantity?: number

  @ApiProperty({ description: '每包数量', required: false })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  QUP?: number

  @ApiProperty({ description: '出货数量', required: false })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  out_pcs?: number

  @ApiProperty({ description: '是否OEM', required: false })
  @IsString()
  @IsOptional()
  oem?: string

  @ApiProperty({ description: '服装编号', required: false })
  @IsString()
  @IsOptional()
  clothing_id?: string

  @ApiProperty({ description: '款式', required: false })
  @IsString()
  @IsOptional()
  style?: string
}
