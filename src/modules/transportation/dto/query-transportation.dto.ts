import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsOptional, IsArray, IsNumber } from 'class-validator'
import { Type } from 'class-transformer'

export class QueryTransportationDto {
  @ApiProperty({ description: '发货编号', required: false })
  @IsString()
  @IsOptional()
  transportation_id?: string

  @ApiProperty({ description: '发货年份', required: false })
  @IsString()
  @IsOptional()
  transportation_year?: string

  @ApiProperty({ description: '发货年份列表', required: false, type: [String] })
  @IsArray()
  @IsOptional()
  transportation_years?: string[]

  @ApiProperty({ description: '发货日期开始', required: false })
  @IsString()
  @IsOptional()
  date_out_start?: string

  @ApiProperty({ description: '发货日期结束', required: false })
  @IsString()
  @IsOptional()
  date_out_end?: string

  @ApiProperty({ description: '运输公司', required: false })
  @IsString()
  @IsOptional()
  supplier?: string

  @ApiProperty({ description: '运输公司列表', required: false, type: [String] })
  @IsArray()
  @IsOptional()
  suppliers?: string[]

  @ApiProperty({ description: '页码', required: false, default: 1 })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  page?: number = 1

  @ApiProperty({ description: '每页数量', required: false, default: 10 })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  limit?: number = 10
}
