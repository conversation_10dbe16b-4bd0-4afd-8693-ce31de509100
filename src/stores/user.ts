import { defineStore } from 'pinia'
import { login, register, getUserInfo } from '@/api/user'
import type { UserInfo, RegisterParams } from '@/api/user'
import { ref } from 'vue'

export const useUserStore = defineStore('user', () => {
  const token = ref<string>('')
  const userInfo = ref<UserInfo | null>(null)
  const loading = ref<boolean>(false)

  // 登录
  const loginAction = async (userName: string, userPwd: string) => {
    loading.value = true
    try {
      const res = await login({ userName, userPwd })
      token.value = res.token
      userInfo.value = res.user
      localStorage.setItem('token', res.token)
      return res
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注册
  const registerAction = async (
    userName: string,
    userPwd: string,
    email?: string,
    phone?: string
  ) => {
    loading.value = true
    try {
      const data: RegisterParams = { userName, userPwd }
      if (email) data.email = email
      if (phone) data.phone = phone
      
      const res = await register(data)
      return res
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取用户信息
  const getUserInfoAction = async () => {
    loading.value = true
    try {
      const user = await getUserInfo()
      userInfo.value = user
      return user
    } catch (error) {
      logout()
      throw error
    } finally {
      loading.value = false
    }
  }

  // 退出登录
  const logout = () => {
    token.value = ''
    userInfo.value = null
    localStorage.removeItem('token')
  }

  // 初始化
  const init = async () => {
    const localToken = localStorage.getItem('token')
    if (localToken) {
      token.value = localToken
      try {
        await getUserInfoAction()
      } catch (error) {
        console.error('获取用户信息失败:', error)
        logout()
      }
    }
  }

  return {
    token,
    userInfo,
    loading,
    loginAction,
    registerAction,
    getUserInfoAction,
    logout,
    init,
  }
})
