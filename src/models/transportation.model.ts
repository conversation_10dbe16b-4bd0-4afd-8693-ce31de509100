import { Schema, model, Types } from 'mongoose'

/**
 * 发货信息模型
 */
export const TransportationSchema = new Schema(
  {
    transportation_id: {
      type: String,
      required: true,
      unique: true,
    },
    transportation_year: {
      type: String,
      required: true,
    },
    date_out: {
      type: Date,
      required: true,
    },
    date_arrived: {
      type: Date,
      required: false,
    },
    supplier: {
      type: String,
      required: true,
    },
    total_package_quantity: {
      type: Number,
      required: false,
      default: 0,
    },
    total_pcs: {
      type: Number,
      required: false,
      default: 0,
    },
    weight: {
      type: Number,
      required: false,
    },
    price: {
      type: Number,
      required: false,
    },
    shipping_method: {
      type: String,
      required: false,
    },
    remark: {
      type: String,
      required: false,
    },
    transportation_img: [
      {
        url: {
          type: String,
          required: false,
        },
        Key: {
          type: String,
          required: false,
        },
      },
    ],
    inbound_status: {
      type: String,
      required: false,
      default: 'not_inbound', // not_inbound: 未入库, partial_inbound: 部分入库, full_inbound: 全部入库
    },
    createTime: {
      type: Date,
      default: Date.now,
    },
  },
  {
    collection: 'transportation', // 指定集合名称为 transportation
    versionKey: false, // 不使用 __v 字段
    timestamps: true, // 自动管理 createdAt 和 updatedAt
  }
)

// 定义图片接口
export interface TransportationImage {
  url: string
  Key: string
}

// 定义发货信息接口
export interface Transportation {
  _id?: Types.ObjectId
  id?: string
  transportation_id: string
  transportation_year: string
  date_out: Date
  date_arrived?: Date
  supplier: string
  total_package_quantity?: number
  total_pcs?: number
  weight?: number
  price?: number
  shipping_method?: string
  remark?: string
  transportation_img?: TransportationImage[]
  inbound_status?: string
  createTime?: Date
}

// 模型名称为 'Transportation'，与 MongooseModule.forFeature 中注册的名称保持一致
export const TransportationModel = model<Transportation>('Transportation', TransportationSchema)
