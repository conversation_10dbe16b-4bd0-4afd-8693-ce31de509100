import { Schema, model, Types } from 'mongoose'

/**
 * 操作日志模型 - 以包裹为主体，记录包裹发生的事件
 * 设计思想：每个日志记录一个包裹的一次操作事件
 */
export const OperationLogSchema = new Schema(
  {
    timestamp: {
      type: Date,
      required: true,
      default: Date.now,
      description: '操作发生时间'
    },
    operation_type: {
      type: String,
      required: true,
      enum: ['inbound', 'outbound', 'transfer_out', 'transfer_in', 'inventory_surplus', 'inventory_deficit', 'reversal'],
      description: '操作类型: inbound(入库), outbound(出库), transfer_out(移库-移出), transfer_in(移库-移入),  inventory_surplus(盘盈), inventory_deficit(盘亏), reversal(冲正)'
    },
    operator_name: {
      type: String,
      required: true,
      default: 'unknown',
      description: '操作员'
    },
    warehouse_id: {
      type: String,
      required: true,
      description: '操作的仓库ID'
    },
    package_code: {
      type: String,
      required: true,
      description: '操作的包裹编码'
    },
    // 包裹内容变化记录
    contents_changes: [{
      sku: {
        type: String,
        required: true,
        description: '产品SKU'
      },
      product_name: {
        type: String,
        required: true,
        description: '产品名称'
      },
      quantity_change: {
        type: Number,
        required: true,
        description: '数量变化（正数为增加，负数为减少）'
      },
      before_quantity: {
        type: Number,
        required: false,
        default: 0,
        description: '操作前数量'
      },
      after_quantity: {
        type: Number,
        required: false,
        default: 0,
        description: '操作后数量'
      }
    }],
    details: {
      // 入库操作详情
      inbound_batch_code: {
        type: String,
        description: '入库批次号'
      },
      transportation_id: {
        type: String,
        description: '货运单ID'
      },
      series_number: {
        type: Number,
        description: '系列号'
      },

      // 出库操作详情
      order_number: {
        type: String,
        description: '订单号'
      },

      // 移库操作详情
      from_location: {
        type: String,
        description: '原位置'
      },
      to_location: {
        type: String,
        description: '目标位置'
      },
      from_warehouse_id: {
        type: String,
        description: '移出仓库ID'
      },
      to_warehouse_id: {
        type: String,
        description: '移入仓库ID'
      },

      // 盘存操作详情
      discrepancy_reason: {
        type: String,
        description: '差异原因'
      },

      notes: {
        type: String,
        description: '备注信息'
      },

      // 冲正操作详情
      reversed_log_id: {
        type: String,
        description: '被冲正的日志ID'
      }
    },

    // 出库包裹数（仅出库操作使用）
    outbound_package_count: {
      type: Number,
      required: false,
      min: 0,
      description: '出库包裹数，计算公式：出库数量变化总和 ÷ 包裹原始数量总和'
    },

    // 冲正状态
    is_reversed: {
      type: Boolean,
      default: false,
      description: '是否已被冲正'
    },
    is_reversal: {
      type: Boolean,
      default: false,
      description: '是否为冲正记录'
    }
  },
  {
    collection: 'operation_logs',
    versionKey: false,
    timestamps: false // 使用自定义的timestamp字段
  }
)

// 创建索引
OperationLogSchema.index({ timestamp: -1 })
OperationLogSchema.index({ operation_type: 1 })
OperationLogSchema.index({ warehouse_id: 1, timestamp: -1 })
OperationLogSchema.index({ package_code: 1 })
OperationLogSchema.index({ 'details.transportation_id': 1 })
OperationLogSchema.index({ 'details.inbound_batch_code': 1 })
OperationLogSchema.index({ 'contents_changes.sku': 1 })

// 定义内容变化接口
export interface ContentsChange {
  sku: string
  product_name: string
  quantity_change: number
  before_quantity?: number
  after_quantity?: number
}

// 定义操作详情接口
export interface OperationDetails {
  // 入库操作详情
  inbound_batch_code?: string
  transportation_id?: string
  series_number?: number

  // 出库操作详情
  order_number?: string

  // 移库操作详情
  from_location?: string
  to_location?: string
  from_warehouse_id?: string
  to_warehouse_id?: string

  // 盘存操作详情
  discrepancy_reason?: string

  notes?: string

  // 冲正操作详情
  reversed_log_id?: string
}

// 定义操作日志接口
export interface OperationLog {
  _id?: Types.ObjectId
  id?: string
  timestamp: Date
  operation_type: 'inbound' | 'outbound' | 'transfer_out' | 'transfer_in' | 'inventory_surplus' | 'inventory_deficit' | 'reversal'
  operator_name: string
  warehouse_id: string
  package_code: string
  contents_changes: ContentsChange[]
  details: OperationDetails
  outbound_package_count?: number
  is_reversed?: boolean
  is_reversal?: boolean
}

export const OperationLogModel = model<OperationLog>('OperationLog', OperationLogSchema)
