import { Schema, model, Types } from 'mongoose'

/**
 * OEM服装入库模型
 */
export const OemClothingIncomingSchema = new Schema(
  {
    oem_clothing_incoming_id: {
      type: String,
      required: true,
      unique: true,
    },
    oem_clothing_incoming_year: {
      type: String,
      required: true,
    },
    date_in: {
      type: Date,
      required: true,
    },
    supplier: {
      type: String,
      required: true,
    },
    remark: {
      type: String,
      required: false,
    },
    createTime: {
      type: Date,
      default: Date.now,
    },
  },
  {
    collection: 'oem_clothing_incoming', // 指定集合名称为 oem_clothing_incoming
    versionKey: false, // 不使用 __v 字段
    timestamps: true, // 自动管理 createdAt 和 updatedAt
  }
)

export interface OemClothingIncoming {
  _id?: Types.ObjectId
  id?: string
  oem_clothing_incoming_id: string
  oem_clothing_incoming_year: string
  date_in: Date
  supplier: string
  remark?: string
  createTime?: Date
}

// 模型名称为 'OemClothingIncoming'，与 MongooseModule.forFeature 中注册的名称保持一致
export const OemClothingIncomingModel = model<OemClothingIncoming>('OemClothingIncoming', OemClothingIncomingSchema)
