import { Schema, model, Types } from 'mongoose'

/**
 * OEM服装入库明细模型
 */
export const OemClothingIncomingDetailSchema = new Schema(
  {
    incoming_id: {
      type: Schema.Types.ObjectId,
      ref: 'OemClothingIncoming',
      required: true,
    },
    oem_clothing_id: {
      type: String,
      required: true,
    },
    oem_clothing_name: {
      type: String,
      required: true,
    },
    style: {
      type: String,
      required: false,
    },
    price: {
      type: Number,
      required: false,
    },
    in_pcs: {
      type: Number,
      required: true,
    },
    money: {
      type: String,
      required: false,
    },
    remark: {
      type: String,
      required: false,
    },
    createTime: {
      type: Date,
      default: Date.now,
    },
  },
  {
    collection: 'oem_clothing_incoming_detail', // 指定集合名称为 oem_clothing_incoming_detail
    versionKey: false, // 不使用 __v 字段
    timestamps: true, // 自动管理 createdAt 和 updatedAt
  }
)

export interface OemClothingIncomingDetail {
  _id?: Types.ObjectId
  id?: string
  incoming_id: any // 关联到 OemClothingIncoming 的 _id
  oem_clothing_id: string
  oem_clothing_name: string
  style?: string
  price?: number
  in_pcs: number
  money?: string
  remark?: string
  createTime?: Date
}

// 模型名称为 'OemClothingIncomingDetail'，与 MongooseModule.forFeature 中注册的名称保持一致
export const OemClothingIncomingDetailModel = model<OemClothingIncomingDetail>(
  'OemClothingIncomingDetail',
  OemClothingIncomingDetailSchema
)
