import { Schema, model, Types } from 'mongoose'

/**
 * 仓库模型
 */
export const WarehouseSchema = new Schema(
  {
    warehouse_id: {
      type: String,
      required: true,
      unique: true,
    },
    name: {
      type: String,
      required: true,
    },
    address: {
      type: String,
      required: true,
    },
    region: {
      type: String,
      required: false,
    },
    capacity: {
      type: Number,
      required: false,
      default: 0,
    },
    current_stock: {
      type: Number,
      required: false,
      default: 0,
    },
    rent_price: {
      type: Number,
      required: false,
    },
    rent_date: {
      type: Date,
      required: false,
    },
    return_date: {
      type: Date,
      required: false,
    },
    contact_person: {
      type: String,
      required: false,
    },
    contact_phone: {
      type: String,
      required: false,
    },
    status: {
      type: String,
      required: false,
      default: 'active', // active, inactive, deleted
    },
    remark: {
      type: String,
      required: false,
    },
    createTime: {
      type: Date,
      default: Date.now,
    },
    lastChangeTime: {
      type: Date,
      required: false,
    },
  },
  {
    collection: 'warehouses',
    versionKey: false,
    timestamps: true,
  }
)

// 定义仓库接口
export interface Warehouse {
  _id?: Types.ObjectId
  id?: string
  warehouse_id: string
  name: string
  address: string
  region?: string
  capacity?: number
  current_stock?: number
  rent_price?: number
  rent_date?: Date
  return_date?: Date
  contact_person?: string
  contact_phone?: string
  status?: string
  remark?: string
  createTime?: Date
  lastChangeTime?: Date
}

export const WarehouseModel = model<Warehouse>('Warehouse', WarehouseSchema)
