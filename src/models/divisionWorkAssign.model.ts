import { Schema, model, Types } from 'mongoose'

/**
 * 分工分配模型
 */
export const DivisionWorkAssignSchema = new Schema(
  {
    division_work_id: {
      type: String,
      required: true,
    },
    division_work_year: {
      type: String,
      required: true,
    },
    staff_id: {
      type: String,
      required: true,
    },
    staff_name: {
      type: String,
      required: true,
    },
    totalPrice: {
      type: String,
      required: false,
      default: '0',
    },
    work_detail: [
      {
        work_id: {
          type: String,
          required: true,
        },
        work_name: {
          type: String,
          required: true,
        },
        pcs: {
          type: Number,
          required: true,
          default: 0,
        },
      },
    ],
    createTime: {
      type: Date,
      default: Date.now,
    },
    lastChangeTime: {
      type: Date,
      required: false,
    },
  },
  {
    collection: 'division_work_assign', // 指定集合名称为 division_work_assign
    versionKey: false, // 不使用 __v 字段
    timestamps: true, // 自动管理 createdAt 和 updatedAt
  }
)

// 定义工序明细接口
export interface WorkDetail {
  work_id: string
  work_name: string
  pcs: number
}

// 定义分工分配接口
export interface DivisionWorkAssign {
  _id?: Types.ObjectId
  id?: string
  division_work_id: string
  division_work_year: string
  staff_id: string
  staff_name: string
  totalPrice?: string
  work_detail: WorkDetail[]
  createTime?: Date
  lastChangeTime?: Date
}

// 模型名称为 'DivisionWorkAssign'，与 MongooseModule.forFeature 中注册的名称保持一致
export const DivisionWorkAssignModel = model<DivisionWorkAssign>('DivisionWorkAssign', DivisionWorkAssignSchema)
