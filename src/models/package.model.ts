import { Schema, model, Types } from 'mongoose'

/**
 * 包裹内容子文档模式
 */
const PackageContentSchema = new Schema({
  product_id: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'Product',
    description: '关联到products集合'
  },
  sku: {
    type: String,
    required: true,
    description: '为方便查询，冗余存储SKU'
  },
  name: {
    type: String,
    required: true,
    description: '冗余存储名称'
  },
  original_quantity: {
    type: Number,
    required: true,
    min: 0,
    description: '初始入库数量'
  },
  current_quantity: {
    type: Number,
    required: true,
    min: 0,
    description: '当前剩余数量'
  },
  // 兼容现有系统的字段
  clothing_id: {
    type: String,
    required: false,
    description: '关联的服装ID（兼容现有系统）'
  },
  oem_clothing_id: {
    type: String,
    required: false,
    description: '关联的OEM服装ID（兼容现有系统）'
  },
  is_oem: {
    type: Boolean,
    required: false,
    default: false,
    description: '是否为OEM产品'
  }
}, { _id: false })

/**
 * 包裹库存模型 - 新仓库管理系统的核心集合
 * 代表仓库中的实际库存，每个文档对应一个独立的、可追踪的物理包裹
 */
export const PackageSchema = new Schema(
  {
    package_code: {
      type: String,
      required: true,
      unique: true,
      description: '包裹的唯一识别码，用于扫描和操作'
    },
    classification_code: {
      type: String,
      required: true,
      description: '包裹分类码，用于归类包裹。格式：单个包裹为sku_original_quantity，混合包裹为多个sku_original_quantity用_连接'
    },
    package_type: {
      type: String,
      required: true,
      enum: ['single', 'mixed'],
      description: '包裹类型：single(单一包裹), mixed(混合包裹)'
    },
    inbound_batch_code: {
      type: String,
      required: true,
      description: '入库批次号，用于追溯和FIFO'
    },
    warehouse_id: {
      type: String,
      required: true,
      description: '关联到warehouses集合'
    },
    location_code: {
      type: String,
      required: false,
      description: '在仓库中的具体位置（如货架号）'
    },
    status: {
      type: String,
      required: true,
      default: 'in_stock',
      enum: ['in_stock', 'partially_shipped', 'shipped'],
      description: '包裹状态: in_stock(在库), partially_shipped(部分出库), shipped(已出库)'
    },
    remaining_percentage: {
      type: Number,
      required: true,
      min: 0,
      max: 1,
      default: 1,
      description: '当前包裹剩余百分比 (sum(contents.current_quantity) / sum(contents.original_quantity))'
    },
    contents: [PackageContentSchema],
    // 关联信息
    transportation_id: {
      type: String,
      required: false,
      description: '关联的货运单ID'
    },
    series_number: {
      type: Number,
      required: false,
      description: '货运单中的系列号'
    },
    supplier: {
      type: String,
      required: false,
      description: '供应商'
    },
    inbound_at: {
      type: Date,
      required: true,
      default: Date.now,
      description: '入库时间'
    },
    last_updated_at: {
      type: Date,
      required: true,
      default: Date.now,
      description: '最后更新时间'
    }
  },
  {
    collection: 'packages',
    versionKey: false,
    timestamps: { createdAt: 'inbound_at', updatedAt: 'last_updated_at' }
  }
)

// 创建索引
// package_code 已通过 unique: true 自动创建唯一索引，无需重复定义
PackageSchema.index({ classification_code: 1 })
PackageSchema.index({ package_type: 1 })
PackageSchema.index({ inbound_batch_code: 1 })
PackageSchema.index({ warehouse_id: 1 })
PackageSchema.index({ location_code: 1 })
PackageSchema.index({ status: 1 })
PackageSchema.index({ transportation_id: 1 })
PackageSchema.index({ 'contents.sku': 1 })
PackageSchema.index({ 'contents.product_id': 1 })
PackageSchema.index({ inbound_at: -1 })

// 定义包裹内容接口
export interface PackageContent {
  product_id: Types.ObjectId
  sku: string
  name: string
  original_quantity: number
  current_quantity: number
  clothing_id?: string
  oem_clothing_id?: string
  is_oem?: boolean
}

// 定义包裹接口
export interface Package {
  _id?: Types.ObjectId
  id?: string
  package_code: string
  classification_code: string
  package_type: string
  inbound_batch_code: string
  warehouse_id: string
  location_code?: string
  status: string
  remaining_percentage: number
  contents: PackageContent[]
  transportation_id?: string
  series_number?: number
  supplier?: string
  inbound_at?: Date
  last_updated_at?: Date
}

export const PackageModel = model<Package>('Package', PackageSchema)
