import { Schema, model, Types } from 'mongoose'

/**
 * 布料入库明细模型
 */
export const FabricWarehouseDetailSchema = new Schema(
  {
    warehouse_id: {
      type: Schema.Types.ObjectId,
      ref: 'FabricWarehouse',
      required: true,
    },
    fabric_id: {
      type: String,
      required: true,
    },
    fabric_name: {
      type: String,
      required: true,
    },
    meter: {
      type: Number,
      required: true,
    },
    classification: {
      type: String,
      required: false,
    },
    remark: {
      type: String,
      required: false,
    },
    createTime: {
      type: Date,
      default: Date.now,
    },
  },
  {
    collection: 'fabric_warehouse_detail', // 指定集合名称为 fabric_warehouse_detail
    versionKey: false, // 不使用 __v 字段
    timestamps: true, // 自动管理 createdAt 和 updatedAt
  }
)

export interface FabricWarehouseDetail {
  _id?: Types.ObjectId
  id?: string
  warehouse_id: any // 关联到 FabricWarehouse 的 _id
  fabric_id: string
  fabric_name: string
  meter: number
  classification?: string
  remark?: string
  createTime?: Date
}

// 模型名称为 'FabricWarehouseDetail'，与 MongooseModule.forFeature 中注册的名称保持一致
export const FabricWarehouseDetailModel = model<FabricWarehouseDetail>(
  'FabricWarehouseDetail',
  FabricWarehouseDetailSchema
)
