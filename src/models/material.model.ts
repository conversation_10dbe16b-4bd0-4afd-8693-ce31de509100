import { Schema, model, Types } from 'mongoose'

/**
 * 物料信息模型
 */
export const MaterialSchema = new Schema(
  {
    material_id: {
      type: String,
      required: true,
      unique: true,
    },
    material_name: {
      type: String,
      required: true,
    },
    category_id: {
      type: String,
      required: true,
    },
    specification: {
      type: String,
      required: false, // 规格型号
    },
    unit: {
      type: String,
      required: true, // 计量单位：米、个、公斤等
    },
    current_price: {
      type: Number,
      required: false,
      default: 0,
    },
    // 年度价格配置 - 参考工序的yearly_prices设计
    yearly_prices: [
      {
        year: {
          type: String,
          required: true,
        },
        price: {
          type: Number,
          required: true,
        },
        effective_date: {
          type: Date,
          required: false,
        },
        supplier: {
          type: String,
          required: false, // 供应商
        }
      }
    ],
    description: {
      type: String,
      required: false,
    },
    state: {
      type: String,
      required: false,
      default: '1',
    },
    createTime: {
      type: Date,
      default: Date.now,
    },
    lastChangeTime: {
      type: Date,
      required: false,
    },
  },
  {
    collection: 'material',
    versionKey: false,
    timestamps: true,
  }
)

// 年度价格接口
export interface MaterialYearlyPrice {
  year: string
  price: number
  effective_date?: Date
  supplier?: string
}

export interface Material {
  _id?: Types.ObjectId
  id?: string
  material_id: string
  material_name: string
  category_id: string
  specification?: string
  unit: string
  current_price?: number
  yearly_prices?: MaterialYearlyPrice[]
  description?: string
  state?: string
  createTime?: Date
  lastChangeTime?: Date
}

// 模型名称为 'Material'
export const MaterialModel = model<Material>('Material', MaterialSchema)
