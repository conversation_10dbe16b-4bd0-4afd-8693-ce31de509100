import { Schema, model, Types } from 'mongoose'

/**
 * 物料分类模型 - 支持多层级分类
 */
export const MaterialCategorySchema = new Schema(
  {
    category_id: {
      type: String,
      required: true,
      unique: true,
    },
    category_name: {
      type: String,
      required: true,
    },
    parent_category_id: {
      type: String,
      required: false, // 顶级分类为null
    },
    level: {
      type: Number,
      required: true,
      default: 1, // 1:主类别, 2:子类别, 3:具体物料项
    },
    sort_order: {
      type: Number,
      required: false,
      default: 0,
    },
    description: {
      type: String,
      required: false,
    },
    state: {
      type: String,
      required: false,
      default: '1', // 1:启用, 0:禁用
    },
    createTime: {
      type: Date,
      default: Date.now,
    },
    lastChangeTime: {
      type: Date,
      required: false,
    },
  },
  {
    collection: 'material_category',
    versionKey: false,
    timestamps: true,
  }
)

export interface MaterialCategory {
  _id?: Types.ObjectId
  id?: string
  category_id: string
  category_name: string
  parent_category_id?: string
  level: number
  sort_order?: number
  description?: string
  state?: string
  createTime?: Date
  lastChangeTime?: Date
}

// 模型名称为 'MaterialCategory'
export const MaterialCategoryModel = model<MaterialCategory>('MaterialCategory', MaterialCategorySchema)
