import { Schema, model, Types } from 'mongoose'

/**
 * 布料模型
 */
export const FabricSchema = new Schema(
  {
    fabric_year: {
      type: String,
      required: true,
    },
    fabric_id: {
      type: String,
      required: true,
      unique: true,
    },
    fabric_name: {
      type: String,
      required: true,
    },
    supplier: {
      type: String,
      required: true,
    },
    classification: {
      type: String,
      required: false,
    },
    order_quantity: {
      type: Number,
      required: false,
    },
    remark: {
      type: String,
      required: false,
    },
    createTime: {
      type: Date,
      default: Date.now,
    },
    in_quantity: {
      type: Number,
      default: 0,
    },
  },
  {
    collection: 'fabric', // 修改集合名称为 fabric
    versionKey: false, // 不使用 __v 字段
    timestamps: true, // 自动管理 createdAt 和 updatedAt
  }
)

export interface Fabric {
  _id?: Types.ObjectId
  id?: string
  fabric_year: string
  fabric_id: string
  fabric_name: string
  supplier: string
  classification?: string
  order_quantity?: number
  remark?: string
  in_quantity?: number
  createTime?: Date
}

// 模型名称为 'Fabric'，与 MongooseModule.forFeature 中注册的名称保持一致
export const FabricModel = model<Fabric>('Fabric', FabricSchema)
