import { Schema, model, Types } from 'mongoose'

/**
 * 产品主数据模型 - 新仓库管理系统核心模型
 * 定义仓库中所有可能出现的货物种类，作为标准产品目录
 */
export const ProductSchema = new Schema(
  {
    sku: {
      type: String,
      required: true,
      description: '货物唯一编码 (Stock Keeping Unit)'
    },
    name: {
      type: String,
      required: true,
      description: '产品名称'
    },
    description: {
      type: String,
      required: false,
      description: '产品描述'
    },
    unit: {
      type: String,
      required: true,
      default: '件',
      description: '计量单位'
    },
    specs: {
      type: Schema.Types.Mixed,
      required: false,
      description: '其他规格信息，如品牌、重量、尺寸等'
    },
    // 关联到原有的服装系统
    clothing_id: {
      type: String,
      required: false,
      description: '关联的服装ID（用于兼容现有系统）'
    },
    oem_clothing_id: {
      type: String,
      required: false,
      description: '关联的OEM服装ID（用于兼容现有系统）'
    },
    is_oem: {
      type: Boolean,
      required: false,
      default: false,
      description: '是否为OEM产品'
    },
    // 图片信息
    images: [{
      type: String,
      description: '产品图片URL'
    }],
    // 状态
    status: {
      type: String,
      required: false,
      default: 'active',
      enum: ['active', 'inactive', 'deleted'],
      description: '产品状态'
    },
    created_at: {
      type: Date,
      default: Date.now,
      description: '创建时间'
    },
    updated_at: {
      type: Date,
      default: Date.now,
      description: '更新时间'
    }
  },
  {
    collection: 'products',
    versionKey: false,
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
  }
)

// 创建索引
ProductSchema.index({ sku: 1 }, { unique: true })
ProductSchema.index({ name: 1 })
ProductSchema.index({ clothing_id: 1 })
ProductSchema.index({ oem_clothing_id: 1 })
ProductSchema.index({ status: 1 })

// 定义产品接口
export interface Product {
  _id?: Types.ObjectId
  id?: string
  sku: string
  name: string
  description?: string
  unit: string
  specs?: any
  clothing_id?: string
  oem_clothing_id?: string
  is_oem?: boolean
  images?: string[]
  status?: string
  created_at?: Date
  updated_at?: Date
}

export const ProductModel = model<Product>('Product', ProductSchema)
