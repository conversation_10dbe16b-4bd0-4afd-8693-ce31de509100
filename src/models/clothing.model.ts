import { Schema, model, Types } from 'mongoose'

/**
 * 服装模型
 */
export const ClothingSchema = new Schema(
  {
    clothing_year: {
      type: String,
      required: true,
    },
    clipping_year: {
      type: String,
      required: false,
    },
    make_year: {
      type: String,
      required: false,
    },
    clothing_id: {
      type: String,
      required: true,
      unique: true,
    },
    clothing_name: {
      type: String,
      required: true,
    },
    long_or_short_sleeve: {
      type: String,
      required: false,
    },
    size: {
      type: String,
      required: false,
    },
    style: {
      type: String,
      required: false,
    },
    pocket_type: {
      type: String,
      required: false,
    },
    fabric_group_id: {
      type: String,
      required: false,
    },
    order_quantity: {
      type: Number,
      required: false,
      default: 0,
    },
    clipping_pcs: {
      type: Number,
      required: false,
      default: 0,
    },
    fabric_usage_per_clothing: {
      type: Number,
      required: false,
    },
    memo: {
      type: String,
      required: false,
    },
    printed: {
      type: String,
      required: false,
    },
    shipments: {
      type: Number,
      required: false,
      default: 0,
    },
    arrival_quantity: {
      type: Number,
      required: false,
      default: 0,
    },
    stock_quantity: {
      type: Number,
      required: false,
      default: 0,
    },
    state: {
      type: String,
      required: false,
      default: '0',
    },
    craft_details: {
      type: String,
      required: false,
    },
    total_material_cost: {
      type: Number,
      required: false,
      default: 0,
    },
    img: [
      {
        url: {
          type: String,
          required: false,
        },
        Key: {
          type: String,
          required: false,
        },
      },
    ],
    colorPcs: [
      {
        fabric_id: {
          type: String,
          required: false,
        },
        pcs: {
          type: String,
          required: false,
        },
      },
    ],
    createTime: {
      type: Date,
      default: Date.now,
    },
    lastChangeTime: {
      type: Date,
      required: false,
    },
  },
  {
    collection: 'clothing', // 指定集合名称为 clothing
    versionKey: false, // 不使用 __v 字段
    timestamps: true, // 自动管理 createdAt 和 updatedAt
  }
)

// 定义图片接口
export interface ClothingImage {
  url: string
  Key: string
}

// 定义颜色件数接口
export interface ColorPcs {
  fabric_id: string
  pcs: string
}

// 定义服装接口
export interface Clothing {
  _id?: Types.ObjectId
  id?: string
  clothing_year: string
  clipping_year?: string
  make_year?: string
  clothing_id: string
  clothing_name: string
  long_or_short_sleeve?: string
  size?: string
  style?: string
  pocket_type?: string
  fabric_group_id?: string
  order_quantity?: number
  clipping_pcs?: number
  fabric_usage_per_clothing?: number
  memo?: string
  printed?: string
  shipments?: number
  arrival_quantity?: number
  stock_quantity?: number
  state?: string
  craft_details?: string
  total_material_cost?: number
  img?: ClothingImage[]
  colorPcs?: ColorPcs[]
  createTime?: Date
  lastChangeTime?: Date
}

// 模型名称为 'Clothing'，与 MongooseModule.forFeature 中注册的名称保持一致
export const ClothingModel = model<Clothing>('Clothing', ClothingSchema)
