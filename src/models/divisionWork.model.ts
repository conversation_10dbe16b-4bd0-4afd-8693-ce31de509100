import { Schema, model, Types } from 'mongoose'

/**
 * 分组工作模型
 */
export const DivisionWorkSchema = new Schema(
  {
    division_work_year: {
      type: String,
      required: true,
    },
    division_work_id: {
      type: String,
      required: true,
      unique: true,
    },
    clothing_id: {
      type: String,
      required: true,
    },
    group_name: {
      type: String,
      required: false,
    },
    pcs: {
      type: Number,
      required: false,
      default: 0,
    },
    is_complete: {
      type: Number,
      required: false,
      default: 0,
    },
    craft_details: {
      type: String,
      required: false,
    },
    createTime: {
      type: Date,
      default: Date.now,
    },
    lastChangeTime: {
      type: Date,
      required: false,
    },
  },
  {
    collection: 'division_work', // 指定集合名称为 division_work
    versionKey: false, // 不使用 __v 字段
    timestamps: true, // 自动管理 createdAt 和 updatedAt
  }
)

export interface DivisionWork {
  _id?: Types.ObjectId
  id?: string
  division_work_year: string
  division_work_id: string
  clothing_id: string
  group_name?: string
  pcs?: number
  is_complete?: number
  craft_details?: string
  createTime?: Date
  lastChangeTime?: Date
}

// 模型名称为 'DivisionWork'，与 MongooseModule.forFeature 中注册的名称保持一致
export const DivisionWorkModel = model<DivisionWork>('DivisionWork', DivisionWorkSchema)
