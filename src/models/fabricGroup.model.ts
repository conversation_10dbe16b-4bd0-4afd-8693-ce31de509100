import { Schema, model, Types } from 'mongoose'

/**
 * 布料组模型
 */
export const FabricGroupSchema = new Schema(
  {
    fabric_group_id: {
      type: String,
      required: true,
      unique: true,
    },
    fabric_group_year: {
      type: String,
      required: true,
    },
    supplier: {
      type: String,
      required: true,
    },
    group_classification: {
      type: [String],
      required: false,
      default: [],
    },
    fabrics: {
      type: [String],
      required: false,
      default: [],
    },
    settlement_state: {
      type: String,
      required: false,
      default: '0',
    },
    state: {
      type: String,
      required: false,
      default: '0',
    },
    img_url: {
      type: String,
      required: false,
    },
    scheduling: {
      type: String,
      required: false,
    },
    createTime: {
      type: Date,
      default: Date.now,
    },
    lastChangeTime: {
      type: Date,
      required: false,
    },
  },
  {
    collection: 'fabric_group', // 指定集合名称为 fabric_group
    versionKey: false, // 不使用 __v 字段
    timestamps: true, // 自动管理 createdAt 和 updatedAt
  }
)

export interface FabricGroup {
  _id?: Types.ObjectId
  id?: string
  fabric_group_id: string
  fabric_group_year: string
  supplier: string
  group_classification?: string[]
  fabrics?: string[]
  settlement_state?: string
  state?: string
  img_url?: string
  scheduling?: string
  createTime?: Date
  lastChangeTime?: Date
}

// 模型名称为 'FabricGroup'，与 MongooseModule.forFeature 中注册的名称保持一致
export const FabricGroupModel = model<FabricGroup>('FabricGroup', FabricGroupSchema)
