import { Schema, model, Types } from 'mongoose'

/**
 * OEM服装模型
 */
export const OemClothingSchema = new Schema(
  {
    oem_clothing_year: {
      type: String,
      required: true,
    },
    oem_clothing_id: {
      type: String,
      required: true,
      unique: true,
    },
    oem_clothing_name: {
      type: String,
      required: true,
    },
    oem_supplier: {
      type: String,
      required: false,
    },
    classification: {
      type: String,
      required: false,
    },
    style: {
      type: String,
      required: false,
    },
    size: {
      type: String,
      required: false,
    },
    price: {
      type: Number,
      required: false,
    },
    in_pcs: {
      type: Number,
      required: false,
      default: 0,
    },
    order_quantity: {
      type: Number,
      required: false,
      default: 0,
    },
    shipments: {
      type: Number,
      required: false,
      default: 0,
    },
    arrival_quantity: {
      type: Number,
      required: false,
      default: 0,
    },
    stock_quantity: {
      type: Number,
      required: false,
      default: 0,
    },
    printed: {
      type: String,
      required: false,
    },
    remark: {
      type: String,
      required: false,
    },
    state: {
      type: String,
      required: false,
      default: '0',
    },
    img: [
      {
        url: {
          type: String,
          required: false,
        },
        Key: {
          type: String,
          required: false,
        },
      },
    ],
    createTime: {
      type: Date,
      default: Date.now,
    },
    lastChangeTime: {
      type: Date,
      required: false,
    },
  },
  {
    collection: 'oem_clothing', // 指定集合名称为 oem_clothing
    versionKey: false, // 不使用 __v 字段
    timestamps: true, // 自动管理 createdAt 和 updatedAt
  }
)

// 定义图片接口
export interface OemClothingImage {
  url: string
  Key: string
}

// 定义OEM服装接口
export interface OemClothing {
  _id?: Types.ObjectId
  id?: string
  oem_clothing_year: string
  oem_clothing_id: string
  oem_clothing_name: string
  oem_supplier?: string
  classification?: string
  style?: string
  size?: string
  price?: number
  in_pcs?: number
  order_quantity?: number
  shipments?: number
  arrival_quantity?: number
  stock_quantity?: number
  printed?: string
  remark?: string
  state?: string
  img?: OemClothingImage[]
  createTime?: Date
  lastChangeTime?: Date
}

// 模型名称为 'OemClothing'，与 MongooseModule.forFeature 中注册的名称保持一致
export const OemClothingModel = model<OemClothing>('OemClothing', OemClothingSchema)
