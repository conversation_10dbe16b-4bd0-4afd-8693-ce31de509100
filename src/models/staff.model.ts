import { Schema, model } from 'mongoose'

/**
 * 员工模型
 */
export const StaffSchema = new Schema(
  {
    staff_id: {
      type: String,
      required: true,
      unique: true,
    },
    name: {
      type: String,
      required: true,
    },
    pinyin: {
      type: String,
      required: false,
    },
    gender: {
      type: String,
      required: false,
    },
    id_number: {
      type: String,
      required: false,
    },
    tel: {
      type: String,
      required: false,
    },
    add: {
      type: String,
      required: false,
    },
    bank_card_number: {
      type: String,
      required: false,
    },
    post: {
      type: String,
      required: false,
    },
    floor: {
      type: String,
      required: false,
    },
    clearing: {
      type: Boolean,
      default: false,
    },
    salary: {
      type: Number,
      required: false,
    },
    createTime: {
      type: Date,
      default: Date.now,
    },
    lastChangeTime: {
      type: Date,
      required: false,
    },
  },
  {
    collection: 'staff', // 指定集合名称为 staff
    versionKey: false, // 不使用 __v 字段
    timestamps: true, // 自动管理 createdAt 和 updatedAt
  }
)

// 添加索引以提高查询性能
StaffSchema.index({ staff_id: -1 }) // 员工ID降序索引，用于排序
StaffSchema.index({ name: 1 }) // 员工姓名索引，用于搜索
StaffSchema.index({ pinyin: 1 }) // 拼音索引，用于搜索
StaffSchema.index({ post: 1 }) // 职位索引，用于筛选
StaffSchema.index({ floor: 1 }) // 楼层/部门索引，用于筛选
StaffSchema.index({ clearing: 1 }) // 结算状态索引，用于筛选

import { Types } from 'mongoose'

export interface Staff {
  _id?: Types.ObjectId
  id?: string
  staff_id: string
  name: string
  pinyin?: string
  gender?: string
  id_number?: string
  tel?: string
  add?: string
  bank_card_number?: string
  post?: string
  floor?: string
  clearing?: boolean
  salary?: number
  createTime?: Date
  lastChangeTime?: Date
}

// 模型名称为 'Staff'，与 MongooseModule.forFeature 中注册的名称保持一致
export const StaffModel = model<Staff>('Staff', StaffSchema)
