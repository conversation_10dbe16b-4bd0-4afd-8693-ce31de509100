import { Schema, model, Types } from 'mongoose'

/**
 * 工作模型
 */
export const WorkSchema = new Schema(
  {
    work_id: {
      type: String,
      required: true,
      unique: true,
    },
    work_name: {
      type: String,
      required: true,
    },
    work_price: {
      type: Number,
      required: true,
    },
    hourly_output: {
      type: Number,
      required: false,
      default: 0,
    },
    remark: {
      type: String,
      required: false,
    },
    // 年度工价配置
    yearly_prices: [
      {
        year: {
          type: String,
          required: true,
        },
        price: {
          type: Number,
          required: true,
        },
        effective_date: {
          type: Date,
          required: false,
        }
      }
    ],
    createTime: {
      type: Date,
      default: Date.now,
    },
    lastChangeTime: {
      type: Date,
      required: false,
    },
  },
  {
    collection: 'work', // 指定集合名称为 work
    versionKey: false, // 不使用 __v 字段
    timestamps: true, // 自动管理 createdAt 和 updatedAt
  }
)

// 定义年度工价接口
export interface YearlyPrice {
  year: string
  price: number
  effective_date?: Date
}

// 定义工作接口
export interface Work {
  _id?: Types.ObjectId
  id?: string
  work_id: string
  work_name: string
  work_price: number
  hourly_output?: number
  remark?: string
  yearly_prices?: YearlyPrice[]
  createTime?: Date
  lastChangeTime?: Date
}

// 模型名称为 'Work'，与 MongooseModule.forFeature 中注册的名称保持一致
export const WorkModel = model<Work>('Work', WorkSchema)