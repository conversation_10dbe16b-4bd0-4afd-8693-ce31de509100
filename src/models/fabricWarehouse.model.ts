import { Schema, model, Types } from 'mongoose'

/**
 * 布料仓库模型
 */
export const FabricWarehouseSchema = new Schema(
  {
    fabric_warehouse_id: {
      type: String,
      required: true,
      unique: true,
    },
    fabric_warehouse_year: {
      type: String,
      required: true,
    },
    date_in: {
      type: Date,
      required: true,
    },
    supplier: {
      type: String,
      required: true,
    },
    remark: {
      type: String,
      required: false,
    },
    total_meter: {
      type: Number,
      default: 0,
    },
    createTime: {
      type: Date,
      default: Date.now,
    },
  },
  {
    collection: 'fabric_warehouse', // 指定集合名称为 fabric_warehouse
    versionKey: false, // 不使用 __v 字段
    timestamps: true, // 自动管理 createdAt 和 updatedAt
  }
)

export interface FabricWarehouse {
  _id?: Types.ObjectId
  id?: string
  fabric_warehouse_id: string
  fabric_warehouse_year: string
  date_in: Date
  supplier: string
  remark?: string
  total_meter?: number
  createTime?: Date
}

// 模型名称为 'FabricWarehouse'，与 MongooseModule.forFeature 中注册的名称保持一致
export const FabricWarehouseModel = model<FabricWarehouse>('FabricWarehouse', FabricWarehouseSchema)
