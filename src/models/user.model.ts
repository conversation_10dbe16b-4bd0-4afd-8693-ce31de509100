import { Schema, model, Types } from 'mongoose'

/**
 * 用户模型
 */
export const UserSchema = new Schema(
  {
    userName: {
      type: String,
      required: true,
    },
    userPwd: {
      type: String,
      required: false,
    },
    email: {
      type: String,
      required: false,
    },
    phone: {
      type: String,
      required: false,
    },
    wxNickName: {
      type: String,
      required: false,
    },
    wxOpenId: {
      type: String,
      required: false,
      unique: true,
      sparse: true,
    },
    createTime: {
      type: Date,
      default: Date.now,
    },
    lastLoginTime: {
      type: Date,
      default: Date.now,
    },
    remark: {
      type: String,
      required: false,
    },
  },
  {
    collection: 'users', // 指定集合名称
    versionKey: false, // 不使用 __v 字段
  }
)

export interface User {
  _id?: Types.ObjectId
  id?: string
  userName: string
  userPwd?: string
  email?: string
  phone?: string
  wxNickName?: string
  wxOpenId?: string
  createTime?: Date
  lastLoginTime?: Date
  remark?: string
}

// 模型名称为 'User'，与 MongooseModule.forFeature 中注册的名称保持一致
export const UserModel = model<User>('User', UserSchema) 