<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-content">
        <div class="login-header">
          <div class="logo">JY</div>
        </div>
        
        <el-form :model="form" :rules="rules" ref="formRef" class="login-form" @keyup.enter="handleLogin">
          <el-form-item prop="userName">
            <el-input
              v-model="form.userName"
              placeholder="用户名"
              :prefix-icon="User"
              autocomplete="off"
              size="large"
            />
          </el-form-item>
          
          <el-form-item prop="userPwd">
            <el-input
              v-model="form.userPwd"
              type="password"
              placeholder="密码"
              show-password
              :prefix-icon="Lock"
              autocomplete="off"
              size="large"
            />
          </el-form-item>
          
          <div class="form-actions">
            <el-button 
              type="primary" 
              @click="handleLogin" 
              :loading="userStore.loading" 
              class="login-button"
              size="large" 
              round
            >
              登录
            </el-button>
            
            <div class="register-link">
              还没有账号？ 
              <a @click="$router.push('/register')">立即注册</a>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()
const formRef = ref()

const form = reactive({
  userName: '',
  userPwd: '',
})

const rules = {
  userName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  userPwd: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' },
  ],
}

const handleLogin = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    await userStore.loginAction(form.userName, form.userPwd)
    ElMessage.success('登录成功')
    router.push('/')
  } catch (error: any) {
    console.error('登录错误:', error)
  }
}

onMounted(() => {
  // 如果已经登录则跳转到首页
  if (userStore.token) {
    router.push('/')
  }
})
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.login-card {
  width: 420px;
  max-width: 90%;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  animation: card-appear 0.5s ease-out;
}

@keyframes card-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-content {
  padding: 40px;
}

.login-header {
  text-align: center;
  margin-bottom: 36px;
}

.logo {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: white;
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 1px;
}

h1 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.subtitle {
  color: #666;
  font-size: 14px;
  margin-bottom: 0;
}

.login-form {
  margin-top: 30px;
}

.form-actions {
  margin-top: 30px;
}

.login-button {
  width: 100%;
  font-size: 16px;
  padding: 12px 0;
  font-weight: 500;
  background: linear-gradient(to right, #667eea, #764ba2);
  border: none;
}

.login-button:hover {
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.register-link {
  text-align: center;
  margin-top: 20px;
  font-size: 14px;
  color: #666;
}

.register-link a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s;
}

.register-link a:hover {
  color: #764ba2;
}

:deep(.el-input__wrapper) {
  padding: 0 15px;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1) inset;
  border-radius: 8px;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #667eea inset !important;
}

:deep(.el-input__inner) {
  height: 48px;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
}
</style>
