<template>
  <div class="register-container">
    <div class="register-card">
      <div class="register-content">
        <div class="register-header">
          <div class="logo">JY</div>
        </div>

        <el-form
          :model="form"
          :rules="rules"
          ref="formRef"
          class="register-form"
          @keyup.enter="handleRegister"
        >
          <el-form-item prop="userName">
            <el-input
              v-model="form.userName"
              placeholder="用户名"
              :prefix-icon="User"
              autocomplete="off"
              size="large"
            />
          </el-form-item>

          <el-form-item prop="userPwd">
            <el-input
              v-model="form.userPwd"
              type="password"
              placeholder="密码"
              show-password
              :prefix-icon="Lock"
              autocomplete="off"
              size="large"
            />
          </el-form-item>

          <el-form-item prop="confirmPwd">
            <el-input
              v-model="form.confirmPwd"
              type="password"
              placeholder="确认密码"
              show-password
              :prefix-icon="Lock"
              autocomplete="off"
              size="large"
            />
          </el-form-item>

          <el-form-item prop="registerCode">
            <el-input
              v-model="form.registerCode"
              placeholder="注册码"
              :prefix-icon="Key"
              autocomplete="off"
              size="large"
            />
          </el-form-item>

          <div class="form-actions">
            <el-button
              type="primary"
              @click="handleRegister"
              :loading="userStore.loading"
              class="register-button"
              size="large"
              round
            >
              注册
            </el-button>

            <div class="login-link">
              已有账号？
              <a @click="$router.push('/login')">返回登录</a>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { User, Lock, Key } from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()
const formRef = ref()

const form = reactive({
  userName: '',
  userPwd: '',
  confirmPwd: '',
  registerCode: '',
  email: '',
  phone: '',
})

const validatePass = (_rule: any, value: string, callback: Function) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else if (value.length < 6) {
    callback(new Error('密码长度不能少于6个字符'))
  } else {
    if (form.confirmPwd !== '') {
      formRef.value?.validateField('confirmPwd')
    }
    callback()
  }
}

const validatePass2 = (_rule: any, value: string, callback: Function) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== form.userPwd) {
    callback(new Error('两次输入密码不一致!'))
  } else {
    callback()
  }
}

// 注册码验证函数
const validateRegisterCode = (_rule: any, value: string, callback: Function) => {
  // 这里可以设置您指定的注册码，或者从配置中读取
  const validRegisterCode = 'anpinglu102' // 您可以替换为您想要的注册码

  if (value === '') {
    callback(new Error('请输入注册码'))
  } else if (value !== validRegisterCode) {
    callback(new Error('注册码不正确'))
  } else {
    callback()
  }
}

const rules = {
  userName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  userPwd: [{ required: true, validator: validatePass, trigger: 'blur' }],
  confirmPwd: [{ required: true, validator: validatePass2, trigger: 'blur' }],
  registerCode: [{ required: true, validator: validateRegisterCode, trigger: 'blur' }],
  email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }],
  phone: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }],
}

const handleRegister = async () => {
  if (!formRef.value) return

  try {
    // 验证表单，这会触发所有验证规则，包括注册码验证
    await formRef.value.validate()

    // 如果验证通过，则调用注册接口
    await userStore.registerAction(
      form.userName,
      form.userPwd,
      form.email || undefined,
      form.phone || undefined
    )

    ElMessage.success('注册成功，请登录')
    router.push('/login')
  } catch (error: any) {
    console.error('注册错误:', error)
    if (error.response && error.response.data) {
      ElMessage.error(error.response.data.message || '注册失败')
    } else {
      ElMessage.error(error.message || '注册失败，请稍后再试')
    }
  }
}

onMounted(() => {
  // 如果已经登录则跳转到首页
  if (userStore.token) {
    router.push('/')
  }
})
</script>

<style scoped>
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.register-card {
  width: 420px;
  max-width: 90%;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  animation: card-appear 0.5s ease-out;
}

@keyframes card-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.register-content {
  padding: 40px;
}

.register-header {
  text-align: center;
  margin-bottom: 36px;
}

.logo {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: white;
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 1px;
}

h1 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.subtitle {
  color: #666;
  font-size: 14px;
  margin-bottom: 0;
}

.register-form {
  margin-top: 30px;
}

.form-actions {
  margin-top: 30px;
}

.register-button {
  width: 100%;
  font-size: 16px;
  padding: 12px 0;
  font-weight: 500;
  background: linear-gradient(to right, #667eea, #764ba2);
  border: none;
}

.register-button:hover {
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.login-link {
  text-align: center;
  margin-top: 20px;
  font-size: 14px;
  color: #666;
}

.login-link a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s;
}

.login-link a:hover {
  color: #764ba2;
}

:deep(.el-input__wrapper) {
  padding: 0 15px;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1) inset;
  border-radius: 8px;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #667eea inset !important;
}

:deep(.el-input__inner) {
  height: 48px;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
}
</style>
