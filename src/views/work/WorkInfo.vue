<template>
  <PageTemplate
    :queryParams="queryParams"
    :tableData="tableData"
    :total="total"
    :loading="loading"
    :tableHeight="650"
    @query="handleQuery"
    @reset="handleReset"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  >
    <!-- 查询区域左侧 -->
    <template #query-form-left>
      <el-form-item label="工序编号" prop="work_id">
        <el-input
          v-model="queryParams.work_id"
          placeholder="输入工序编号"
          clearable
          style="width: 150px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工序名称" prop="work_name">
        <el-input
          v-model="queryParams.work_name"
          placeholder="输入工序名称"
          clearable
          style="width: 150px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
    </template>

    <!-- 查询区域右侧 -->
    <template #query-form-right>
      <el-button type="primary" plain @click="handleAdd">新增工序</el-button>
    </template>

    <!-- 表格列 -->
    <template #table-columns>
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="work_id" label="工序编号" width="120" align="center" />
      <el-table-column prop="work_name" label="工序名称" width="250" align="center" />
      <el-table-column prop="work_price" label="工序单价" width="100" align="center" />
      <el-table-column prop="hourly_output" label="小时产量" width="100" align="center" />
      <el-table-column prop="remark" label="备注" min-width="150" align="center" />
      <el-table-column label="操作" width="180" align="center" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </template>

    <!-- 分页区域左侧 -->
    <template #pagination-left>
      <div class="table-tools">
        <el-upload
          class="upload-button"
          :action="null"
          :auto-upload="false"
          :on-change="handleFileChange"
          accept=".xlsx,.xls"
          :show-file-list="false"
        >
          <el-button type="success" plain size="small">Excel导入</el-button>
        </el-upload>

        <el-button type="warning" plain size="small" @click="handleExport">Excel导出</el-button>
      </div>
    </template>
  </PageTemplate>

  <!-- 工序对话框 -->
  <WorkDialog
    v-model:visible="dialogVisible"
    :title="dialogTitle"
    :work="currentWork"
    @success="handleDialogSuccess"
  />

  <!-- 工序导入对话框 -->
  <WorkImport
    v-model:visible="importDialogVisible"
    :file="importFile"
    @import-success="handleImportSuccess"
  />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 导入通用页面模板
import PageTemplate from '@/components/common/PageTemplate.vue'
// 导入工序对话框组件
import WorkDialog from './components/WorkDialog.vue'
// 导入工序导入组件
import WorkImport from './components/WorkImport.vue'
// 导入API
import { getWorkList, deleteWork } from '@/api/work'
// 引入xlsx库
import * as XLSX from 'xlsx'

// 定义本地类型
interface Work {
  _id?: string
  id?: string
  work_id: string
  work_name: string
  work_price: number
  hourly_output?: number
  remark?: string
  yearly_prices?: any[]
  createTime?: string | Date
  lastChangeTime?: string | Date
}

interface QueryWorkParams {
  work_id?: string
  work_name?: string
  years?: string[]
  page?: number
  limit?: number
}

// 加载状态
const loading = ref(false)

// 表格数据
const tableData = ref<Work[]>([])

// 总数
const total = ref(0)

// 查询参数
const queryParams = reactive<QueryWorkParams>({
  work_id: '',
  work_name: '',
  years: [],
  page: 1,
  limit: 10,
})

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('新增工序')
const currentWork = ref<Work | null>(null)

// 导入相关
const importDialogVisible = ref(false)
const importFile = ref<File | null>(null)

// 页面加载时获取数据
onMounted(async () => {
  await Promise.all([fetchWorkList()])
})

// 获取工序列表
const fetchWorkList = async () => {
  try {
    loading.value = true
    const res = await getWorkList(queryParams)
    // 检查返回的数据格式
    console.log('工序列表 API 返回数据:', res)

    // 后端返回的格式是 {data: {total, page, limit, workList}}
    if (res.data) {
      tableData.value = res.data.workList || []
      total.value = res.data.total || 0
    } else {
      console.error('API 返回数据格式不正确:', res)
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取工序列表失败:', error)
    ElMessage.error('获取工序列表失败')
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  queryParams.page = 1
  fetchWorkList()
}

// 重置
const handleReset = () => {
  queryParams.work_id = ''
  queryParams.work_name = ''
  queryParams.years = []
  queryParams.page = 1
  fetchWorkList()
}

// 每页数量变化
const handleSizeChange = (val: number) => {
  queryParams.limit = val
  fetchWorkList()
}

// 页码变化
const handleCurrentChange = (val: number) => {
  queryParams.page = val
  fetchWorkList()
}

// 新增工序
const handleAdd = () => {
  currentWork.value = null
  dialogTitle.value = '新增工序'
  dialogVisible.value = true
}

// 编辑工序
const handleEdit = (row: Work) => {
  currentWork.value = { ...row }
  dialogTitle.value = '编辑工序'
  dialogVisible.value = true
}

// 删除工序
const handleDelete = (row: Work) => {
  ElMessageBox.confirm(`确定要删除工序 ${row.work_name} 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        await deleteWork(row._id as string)
        ElMessage.success('删除成功')
        fetchWorkList()
      } catch (error) {
        console.error('删除工序失败:', error)
        ElMessage.error('删除工序失败')
      }
    })
    .catch(() => {
      // 取消删除
    })
}

// 对话框提交成功
const handleDialogSuccess = () => {
  fetchWorkList()
}

// 文件变化
const handleFileChange = (uploadFile: any) => {
  importFile.value = uploadFile.raw as File
  importDialogVisible.value = true
}

// 导入成功
const handleImportSuccess = () => {
  fetchWorkList()
}

// 导出Excel
const handleExport = () => {
  // 创建工作簿
  const wb = XLSX.utils.book_new()

  // 准备导出数据
  const exportData = tableData.value.map((item: Work) => ({
    工序编号: item.work_id,
    工序名称: item.work_name,
    工序单价: item.work_price,
    小时产量: item.hourly_output,
    备注: item.remark,
  }))

  // 创建工作表
  const ws = XLSX.utils.json_to_sheet(exportData)

  // 设置列宽
  const colWidths = [
    { wch: 15 }, // 工序编号
    { wch: 20 }, // 工序名称
    { wch: 10 }, // 工序单价
    { wch: 10 }, // 小时产量
    { wch: 30 }, // 备注
  ]
  ws['!cols'] = colWidths

  // 将工作表添加到工作簿
  XLSX.utils.book_append_sheet(wb, ws, '工序数据')

  // 导出工作簿
  XLSX.writeFile(wb, '工序数据.xlsx')
}
</script>

<style scoped>
.table-tools {
  display: flex;
  gap: 10px;
}
</style>
