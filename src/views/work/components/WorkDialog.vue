<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="50%"
    destroy-on-close
    align-center
    top="5vh"
    @closed="handleDialogClosed"
    class="work-dialog"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-position="top"
      :validate-on-rule-change="false"
      class="work-form"
    >
      <div class="form-layout">
        <!-- 左侧：基本信息部分 -->
        <div class="form-left-section">
          <div class="form-header">
            <h3 class="form-subtitle">基本信息</h3>
          </div>

          <div class="form-grid">
            <el-form-item label="工序编号" prop="work_id">
              <el-input
                v-model="form.work_id"
                placeholder="请输入工序编号"
                :disabled="isEdit"
                prefix-icon="Document"
              />
            </el-form-item>

            <el-form-item label="工序名称" prop="work_name">
              <el-input v-model="form.work_name" placeholder="请输入工序名称" prefix-icon="Edit" />
            </el-form-item>

            <el-form-item label="工序单价" prop="work_price">
              <el-input
                v-model="form.work_price"
                placeholder="请输入工序单价"
                prefix-icon="Money"
              >
                <template #append>分</template>
              </el-input>
            </el-form-item>

            <el-form-item label="小时产量" prop="hourly_output">
              <el-input
                v-model="form.hourly_output"
                placeholder="请输入小时产量"
                prefix-icon="Timer"
              >
                <template #append>件/小时</template>
              </el-input>
            </el-form-item>
          </div>

          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              placeholder="请输入备注信息"
              type="textarea"
              :rows="2"
            />
          </el-form-item>
        </div>

        <!-- 右侧：年度工价配置部分 -->
        <div class="form-right-section">
          <div class="form-header yearly-prices-header">
            <h3 class="form-subtitle">年度工价配置</h3>
            <el-button type="primary" size="small" plain @click="addYearlyPrice" icon="Plus">
              添加年度工价
            </el-button>
          </div>

          <div class="yearly-prices-container">
            <el-table
              v-if="form.yearly_prices.length > 0"
              :data="form.yearly_prices"
              border
              stripe
              size="small"
              class="yearly-prices-table"
              max-height="450"
            >
              <el-table-column label="年份" prop="year" min-width="100">
                <template #default="{ row, $index }">
                  <el-form-item :prop="'yearly_prices.' + $index + '.year'" class="table-form-item">
                    <el-input
                      v-model="row.year"
                      placeholder="请输入年份"
                      :disabled="!isLatestPrice($index)"
                    />
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column label="单价(分)" prop="price" min-width="120">
                <template #default="{ row, $index }">
                  <el-form-item :prop="'yearly_prices.' + $index + '.price'" class="table-form-item">
                    <el-input
                      v-model="row.price"
                      placeholder="请输入单价"
                      :disabled="!isLatestPrice($index)"
                      @input="updateLatestPrice($index, row.price)"
                    />
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column label="生效日期" prop="effective_date" min-width="180">
                <template #default="{ row, $index }">
                  <el-form-item :prop="'yearly_prices.' + $index + '.effective_date'" class="table-form-item">
                    <el-date-picker
                      v-model="row.effective_date"
                      type="date"
                      placeholder="选择生效日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"
                      :disabled="!isLatestPrice($index)"
                      @change="handleDateChange"
                    />
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="80" fixed="right">
                <template #default="{ $index }">
                  <el-button
                    type="danger"
                    size="small"
                    icon="Delete"
                    circle
                    @click="removeYearlyPrice($index)"
                  />
                </template>
              </el-table-column>
            </el-table>

            <el-empty
              v-else
              description="暂无年度工价配置"
              :image-size="100"
              class="yearly-prices-empty"
            />
          </div>
        </div>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { createWork, updateWork } from '@/api/work'

// 定义本地类型
interface YearlyPrice {
  year: string
  price: number
  effective_date?: string | Date
}

interface Work {
  _id?: string
  id?: string
  work_id: string
  work_name: string
  work_price: number
  hourly_output?: number
  remark?: string
  yearly_prices?: YearlyPrice[]
  createTime?: string | Date
  lastChangeTime?: string | Date
}

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '新增工序',
  },
  work: {
    type: Object as () => Work | null,
    default: null,
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'success'])

// 计算属性：是否为编辑模式
const isEdit = computed(() => !!props.work?._id)

// 表单引用
const formRef = ref<FormInstance>()

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
})

// 提交状态
const submitting = ref(false)

// 表单数据
const form = reactive<{
  work_id: string
  work_name: string
  work_price: number
  hourly_output: number
  remark: string
  yearly_prices: YearlyPrice[]
}>({
  work_id: '',
  work_name: '',
  work_price: 0,
  hourly_output: 0,
  remark: '',
  yearly_prices: [],
})

// 表单验证规则
const rules = reactive<FormRules>({
  work_id: [{ required: true, message: '请输入工序编号', trigger: 'blur' }],
  work_name: [{ required: true, message: '请输入工序名称', trigger: 'blur' }],
  work_price: [
    { required: true, message: '请输入工序单价', trigger: 'blur' },
  ],
  hourly_output: [{ type: 'number', message: '小时产量必须为数字', trigger: 'blur' }],
})

// 重置表单
const resetForm = () => {
  form.work_id = ''
  form.work_name = ''
  form.work_price = 0
  form.hourly_output = 0
  form.remark = ''
  form.yearly_prices = []
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 监听工序数据变化
watch(
  () => props.work,
  (newVal) => {
    if (newVal) {
      // 复制工序数据到表单
      form.work_id = newVal.work_id || ''
      form.work_name = newVal.work_name || ''
      form.work_price = newVal.work_price || 0
      form.hourly_output = newVal.hourly_output || 0
      form.remark = newVal.remark || ''
      form.yearly_prices = newVal.yearly_prices ? [...newVal.yearly_prices] : []

      // 如果有年度工价配置，按生效日期倒序排序
      if (form.yearly_prices.length > 0) {
        sortYearlyPrices()
      }
    } else {
      // 重置表单
      resetForm()
    }
  },
  { immediate: true }
)

// 添加年度工价
const addYearlyPrice = () => {
  form.yearly_prices.push({
    year: new Date().getFullYear().toString(),
    price: form.work_price,
    effective_date: new Date().toISOString().split('T')[0],
  })
  // 添加后按生效日期倒序排序
  sortYearlyPrices()
}

// 移除年度工价
const removeYearlyPrice = (index: number) => {
  ElMessageBox.confirm(
    '确定要删除该年度工价配置吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      form.yearly_prices.splice(index, 1)
      // 如果删除后还有价格配置，更新工序单价为最新的价格
      updateWorkPrice()
      ElMessage.success('删除成功')
    })
    .catch(() => {
      // 用户取消删除，不做任何操作
    })
}

// 按生效日期倒序排序年度工价
const sortYearlyPrices = () => {
  form.yearly_prices.sort((a, b) => {
    const dateA = a.effective_date ? new Date(a.effective_date).getTime() : 0
    const dateB = b.effective_date ? new Date(b.effective_date).getTime() : 0
    return dateB - dateA // 倒序排列
  })
  // 排序后更新工序单价
  updateWorkPrice()
}

// 更新工序单价为最新生效日期的价格
const updateWorkPrice = () => {
  if (form.yearly_prices.length > 0) {
    // 已经按日期倒序排列，所以第一个是最新的
    form.work_price = form.yearly_prices[0].price
  }
}

// 判断是否为最新价格（只有最新的可以编辑）
const isLatestPrice = (index: number): boolean => {
  return index === 0 // 因为已经按日期倒序排列，所以索引0是最新的
}

// 更新最新价格时同步更新工序单价
const updateLatestPrice = (index: number, price: number) => {
  if (isLatestPrice(index)) {
    form.work_price = price
  }
}

// 处理日期变更，重新排序
const handleDateChange = () => {
  sortYearlyPrices()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.error('请检查表单填写是否正确')
      return
    }

    try {
      submitting.value = true

      // 构建提交数据
      const submitData = {
        work_id: form.work_id,
        work_name: form.work_name,
        work_price: Number(form.work_price),
        hourly_output: Number(form.hourly_output),
        remark: form.remark,
        yearly_prices: form.yearly_prices,
      }

      // 根据是否有ID判断是新增还是编辑
      if (props.work?._id) {
        // 编辑
        const res = await updateWork(props.work._id, submitData)
        ElMessage.success('工序更新成功')
        emit('success', res.data)
      } else {
        // 新增
        const res = await createWork(submitData)
        ElMessage.success('工序创建成功')
        emit('success', res.data)
      }

      // 关闭对话框
      dialogVisible.value = false
    } catch (error: any) {
      console.error('提交工序数据失败:', error)
      ElMessage.error(error.message || '操作失败，请重试')
    } finally {
      submitting.value = false
    }
  })
}

// 取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 对话框关闭
const handleDialogClosed = () => {
  resetForm()
}
</script>

<style scoped>
.work-dialog :deep(.el-dialog__body) {
  padding: 0 20px 20px;
}

.work-form {
  max-height: 65vh;
  overflow-y: auto;
  padding: 0;
}

/* 左右布局容器 */
.form-layout {
  display: flex;
  gap: 20px;
}

.form-left-section {
  flex: 0.4; /* 缩小基本信息区域的宽度 */
  min-width: 0; /* 防止内容溢出 */
}

.form-right-section {
  flex: 0.6; /* 增大年度工价配置区域的宽度 */
  min-width: 0; /* 防止内容溢出 */
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.yearly-prices-header {
  margin-top: 0; /* 修改为0，因为现在是左右布局 */
}

.form-subtitle {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr); /* 修改为单列，因为左侧空间变小了 */
  gap: 15px;
}

.yearly-prices-container {
  margin-top: 10px;
  background-color: #fafafa;
  border-radius: 4px;
  padding: 15px;
  height: 450px; /* 固定高度，包含表格和空状态 */
}

.yearly-prices-table {
  width: 100%;
  margin-bottom: 0;
}

.yearly-prices-empty {
  margin-top: 30px;
}

/* 表格内的表单项样式 */
.table-form-item {
  margin-bottom: 0 !important;
}

.table-form-item :deep(.el-form-item__label) {
  display: none; /* 隐藏表单项标签，因为表格已有列标题 */
}

.table-form-item :deep(.el-form-item__content) {
  margin-left: 0 !important;
}

/* 表格样式优化 */
.yearly-prices-table :deep(.el-table__header) {
  font-weight: bold;
  background-color: #f5f7fa;
}

.yearly-prices-table :deep(.el-table__row) {
  height: 60px; /* 调整行高 */
}

.yearly-prices-table :deep(.el-input__inner) {
  height: 36px; /* 调整输入框高度 */
}

.yearly-prices-table :deep(.el-date-editor.el-input) {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding-top: 10px;
}

/* 响应式布局 */
@media (max-width: 992px) {
  /* 在中等屏幕上切换为上下布局 */
  .form-layout {
    flex-direction: column;
  }

  .form-grid {
    grid-template-columns: repeat(2, 1fr); /* 恢复为两列 */
  }

  .yearly-prices-header {
    margin-top: 25px; /* 恢复上边距 */
  }
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr; /* 小屏幕上改为单列 */
  }

  .yearly-price-content {
    grid-template-columns: 1fr;
    gap: 10px;
  }
}
</style>
