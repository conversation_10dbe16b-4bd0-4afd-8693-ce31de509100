<template>
  <el-dialog
    v-model="dialogVisible"
    title="导入工序数据"
    width="50%"
    destroy-on-close
    align-center
    center
  >
    <div class="import-container">
      <!-- 步骤条 -->
      <el-steps :active="activeStep" finish-status="success" simple>
        <el-step title="上传文件" />
        <el-step title="预览数据" />
        <el-step title="导入结果" />
      </el-steps>

      <!-- 步骤1: 上传文件 -->
      <div v-if="activeStep === 0" class="step-content">
        <div class="upload-area">
          <div class="upload-info">
            <el-icon :size="48" color="#409EFF"><Document /></el-icon>
            <div class="file-name">{{ fileName || '请选择Excel文件' }}</div>
          </div>
          <el-upload
            class="upload-button"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleFileChange"
            accept=".xlsx,.xls"
          >
            <el-button type="primary" :icon="UploadFilled">选择文件</el-button>
          </el-upload>
        </div>
        <div class="template-download">
          <el-link type="primary" :underline="false" @click="downloadTemplate">
            下载导入模板
          </el-link>
        </div>
      </div>

      <!-- 步骤2: 预览数据 -->
      <div v-if="activeStep === 1" class="step-content">
        <div class="preview-info">
          <div class="preview-file-info">
            <span>文件名: {{ fileName }}</span>
            <span>工作表: {{ sheetName }}</span>
            <span>记录数: {{ previewData.length }}</span>
          </div>
          <div class="preview-actions">
            <el-button type="primary" @click="handleImport" :loading="importing">
              确认导入
            </el-button>
          </div>
        </div>
        <div class="preview-table">
          <el-table
            :data="previewData.slice(0, 10)"
            border
            style="width: 100%"
            max-height="300"
            :scrollbar-always-on="true"
          >
            <el-table-column prop="work_id" label="工序编号" width="120" />
            <el-table-column prop="work_name" label="工序名称" width="150" />
            <el-table-column prop="work_price" label="工序单价" width="100" />
            <el-table-column prop="hourly_output" label="小时产量" width="100" />
            <el-table-column prop="remark" label="备注" min-width="150" />
          </el-table>
          <div v-if="previewData.length > 10" class="preview-more">
            仅显示前10条记录，共{{ previewData.length }}条
          </div>
        </div>
      </div>

      <!-- 步骤3: 导入结果 -->
      <div v-if="activeStep === 2" class="step-content">
        <div class="result-container">
          <div class="result-icon">
            <el-icon :size="64" :color="importResult.success ? '#67C23A' : '#F56C6C'">
              <component :is="importResult.success ? 'CheckCircle' : 'CircleClose'" />
            </el-icon>
          </div>
          <div class="result-message">{{ importResult.message }}</div>
          <div v-if="importResult.count" class="result-count">
            成功导入 {{ importResult.count }} 条记录
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ activeStep === 2 ? '关闭' : '取消' }}</el-button>
        <el-button
          v-if="activeStep === 0"
          type="primary"
          @click="goToNextStep"
          :disabled="!file"
        >
          下一步
        </el-button>
        <el-button v-if="activeStep === 1" @click="goToPrevStep">上一步</el-button>
        <el-button v-if="activeStep === 2" type="primary" @click="handleComplete">
          完成
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, UploadFilled } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import { importJsonData } from '@/api/work'

// 定义本地类型
interface ImportResponse {
  success: boolean
  message: string
  count?: number
}

// 字段映射：中文标题 -> 英文字段
const fieldMapping = {
  工序编号: 'work_id',
  工序名称: 'work_name',
  工序单价: 'work_price',
  小时产量: 'hourly_output',
  备注: 'remark',
}

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  file: {
    type: Object as () => File | null,
    default: null,
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'import-success'])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
})

// 当前步骤
const activeStep = ref(0)

// 文件相关
const file = ref<File | null>(props.file)
const fileName = ref('')
const sheetName = ref('')

// 预览数据
const previewData = ref<any[]>([])

// 导入状态
const importing = ref(false)

// 导入结果
const importResult = ref<ImportResponse>({
  success: false,
  message: '',
})

// 监听文件变化
const handleFileChange = (uploadFile: any) => {
  file.value = uploadFile.raw as File
  fileName.value = uploadFile.name
  if (file.value) {
    parseExcel(file.value)
  }
}

// 解析Excel文件
const parseExcel = async (file: File) => {
  if (!file) return

  try {
    const data = await file.arrayBuffer()
    const workbook = XLSX.read(data)

    // 获取第一个工作表
    const firstSheetName = workbook.SheetNames[0]
    sheetName.value = firstSheetName

    const worksheet = workbook.Sheets[firstSheetName]

    // 将工作表转换为JSON
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

    // 检查是否有数据
    if (jsonData.length <= 1) {
      ElMessage.warning('Excel文件中没有数据')
      return
    }

    // 获取表头（第一行）
    const headers = jsonData[0] as string[]

    // 将数据转换为对象数组
    const result = []
    for (let i = 1; i < jsonData.length; i++) {
      const row = jsonData[i] as any[]

      // 跳过空行
      if (row.length === 0 || row.every(cell => cell === undefined || cell === null || cell === '')) {
        continue
      }

      const item: any = {}
      for (let j = 0; j < headers.length; j++) {
        const header = headers[j]
        const value = row[j]

        // 使用字段映射转换中文表头为英文字段名
        const fieldName = fieldMapping[header as keyof typeof fieldMapping] || header
        item[fieldName] = value
      }

      result.push(item)
    }

    previewData.value = result
    console.log('解析的数据:', result)
  } catch (error) {
    console.error('解析Excel文件失败:', error)
    ElMessage.error('解析Excel文件失败')
  }
}

// 下载模板
const downloadTemplate = () => {
  // 创建工作簿
  const wb = XLSX.utils.book_new()

  // 创建工作表数据
  const wsData = [
    ['工序编号', '工序名称', '工序单价', '小时产量', '备注'],
    ['GX001', '示例工序1', 10, 5, '示例备注1'],
    ['GX002', '示例工序2', 15, 8, '示例备注2'],
  ]

  // 创建工作表
  const ws = XLSX.utils.aoa_to_sheet(wsData)

  // 将工作表添加到工作簿
  XLSX.utils.book_append_sheet(wb, ws, '工序数据')

  // 导出工作簿
  XLSX.writeFile(wb, '工序导入模板.xlsx')
}

// 执行导入
const handleImport = async () => {
  if (previewData.value.length === 0) {
    ElMessage.warning('没有数据可导入')
    return
  }

  try {
    importing.value = true

    // 准备导入数据
    const importData = {
      data: previewData.value,
      sheetName: sheetName.value,
      fileName: fileName.value,
      totalRecords: previewData.value.length,
    }

    // 调用API导入数据
    const response = await importJsonData(importData)

    // 设置导入结果
    importResult.value = response.data

    // 进入下一步
    activeStep.value = 2

    // 如果导入成功，触发成功事件
    if (response.data.success) {
      emit('import-success')
    }
  } catch (error: any) {
    console.error('导入数据失败:', error)
    importResult.value = {
      success: false,
      message: error.message || '导入失败，请重试',
    }
    activeStep.value = 2
  } finally {
    importing.value = false
  }
}

// 取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 完成
const handleComplete = () => {
  dialogVisible.value = false
  if (importResult.value.success) {
    emit('import-success')
  }
}

// 下一步
const goToNextStep = () => {
  if (activeStep.value < 2) {
    activeStep.value++
  }
}

// 上一步
const goToPrevStep = () => {
  if (activeStep.value > 0) {
    activeStep.value--
  }
}
</script>

<style scoped>
.import-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step-content {
  margin: 20px 0;
  min-height: 200px;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
  padding: 30px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
}

.upload-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.file-name {
  font-size: 16px;
  color: #606266;
}

.template-download {
  margin-top: 10px;
  text-align: center;
}

.preview-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.preview-file-info {
  display: flex;
  gap: 20px;
  color: #606266;
}

.preview-table {
  margin-bottom: 15px;
}

.preview-more {
  margin-top: 10px;
  color: #909399;
  text-align: center;
}

.result-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 15px;
  padding: 30px;
}

.result-message {
  font-size: 18px;
  font-weight: bold;
}

.result-count {
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}
</style>
