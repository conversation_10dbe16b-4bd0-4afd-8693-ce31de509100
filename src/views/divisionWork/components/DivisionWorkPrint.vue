<template>
  <!-- 打印按钮 -->
  <el-button type="primary" plain icon="Printer" @click="handlePrint"> 打印 </el-button>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import type { DivisionWork } from '@/api/divisionWork'

// 定义工序明细接口
interface WorkDetail {
  work_id: string
  work_name: string
  pcs: number
}

// 定义员工工序行接口
interface AssignItem {
  staff_id: string
  staff_name: string
  totalPrice: string
  work_detail: WorkDetail[]
}

// 定义组件属性
const props = defineProps({
  divisionWork: {
    type: Object as () => DivisionWork,
    required: true,
  },
  assignList: {
    type: Array as () => AssignItem[],
    required: true,
  },
})

// 处理打印按钮点击
const handlePrint = () => {
  // 直接打印，不显示预览
  const printWindow = window.open('', '_blank')
  if (!printWindow) {
    ElMessage.error('无法创建打印窗口，请检查浏览器是否阻止了弹出窗口')
    return
  }

  // 准备打印内容
  const printContent = preparePrintContent()

  // 创建完整的HTML内容
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>分工单</title>
      <meta charset="utf-8">
      <style>
        @page {
          margin-top: 40px;
          margin-bottom: 20px;
          margin-left: 20px;
          margin-right: 20px;
        }
        body {
          font-family: "SimSun", "宋体", Arial, sans-serif;
          color: #333;
          font-size: 14px;
          max-width: 900px;
          margin: 0 auto;
        }
        .print-header {
          margin-bottom: 15px;
        }
        .date-info {
          text-align: left;
          font-size: 14px;
          margin-bottom: 10px;
        }
        .title-container {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
        }
        .group-info {
          font-size: 20px;
          font-weight: bold;
          width: 33%;
          text-align: center;
        }
        .title {
          font-size: 22px;
          font-weight: bold;
          width: 33%;
          text-align: center;
        }
        .pcs-info {
          font-size: 20px;
          width: 33%;
          text-align: center;
        }
        .print-table {
          width: 100%;
          border-collapse: collapse;
          border: 1px solid #000;
          table-layout: fixed;
        }
        .print-table th, .print-table td {
          border: 1px solid #000;
          padding: 5px;
          text-align: center;
          vertical-align: middle;
          line-height: 1.3;
        }
        .print-table th {
          font-weight: bold;
          background-color: #f9f9f9;
          height: 30px;
        }
        .seq-col {
          width: 40px;
        }
        .staff-col {
          width: 80px;
        }
        .work-col {
          text-align: left;
        }
        .work-detail-container {
          display: flex;
          flex-wrap: wrap;
          gap: 5px 15px;
          padding: 3px;
        }
        .work-item {
          display: inline-block;
          white-space: nowrap;
          margin-right: 20px;
        }
        .work-name {
          font-size: 14px;
        }
        .work-pcs {
          font-size: 14px;
          margin-left: 3px;
        }
        tr {
          height: 35px;
        }
      </style>
    </head>
    <body>
      ${printContent}
    </body>
    </html>
  `

  // 使用blob和URL.createObjectURL创建一个临时URL
  const blob = new Blob([htmlContent], { type: 'text/html' })
  const url = URL.createObjectURL(blob)

  // 关闭之前的窗口
  printWindow.close()

  // 打开新窗口并加载内容
  const newWindow = window.open(url, '_blank')
  if (!newWindow) {
    ElMessage.error('无法创建打印窗口，请检查浏览器是否阻止了弹出窗口')
    return
  }

  // 等待内容加载完成后自动打印
  newWindow.onload = () => {
    setTimeout(() => {
      newWindow.print()
      // 打印完成后关闭窗口
      newWindow.onafterprint = () => {
        newWindow.close()
      }
    }, 500)
  }

  // 释放URL对象
  setTimeout(() => {
    URL.revokeObjectURL(url)
  }, 1000)
}

// 准备打印内容
const preparePrintContent = () => {
  // 获取分工信息基本信息
  const divisionInfo = {
    id: props.divisionWork.division_work_id || '',
    year: props.divisionWork.division_work_year || '',
    group: props.divisionWork.group_name || '',
    clothingName: props.divisionWork.clothing_name || '',
    clothingId: props.divisionWork.clothing_id || '',
    pcs: props.divisionWork.pcs || '',
    craftDetails: props.divisionWork.craft_details || '',
  }


  //work_detail里的work_name进行排序、对分工明细进行排序
  const sortedAssignList = props.assignList
    .map((assign) => {
      // 对work_detail里的work_name进行排序
      assign.work_detail.sort((a, b) => {
        return a.work_name.localeCompare(b.work_name)
      })
      return assign
    })
    .sort((a, b) => {
      return a.staff_name.localeCompare(b.staff_name)
    })

  // 获取当前日期
  const today = new Date()
  const dateStr = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`

  // 构建HTML内容
  let html = `
    <div class="print-header">
      <div class="date-info">${dateStr}</div>
      <div class="title-container">
        <div class="group-info">${divisionInfo.group}</div>
        <div class="title">${divisionInfo.clothingName} 分工单</div>
        <div class="pcs-info">生产数: ${divisionInfo.pcs}</div>
      </div>
    </div>

    <table class="print-table">
      <thead>
        <tr>
          <th class="seq-col">序号</th>
          <th class="staff-col">员工姓名</th>
          <th class="work-col">工序明细</th>
        </tr>
      </thead>
      <tbody>
        ${sortedAssignList
          .map(
            (assign, index) => `
          <tr>
            <td class="seq-col">${index + 1}</td>
            <td class="staff-col">${assign.staff_name}</td>
            <td class="work-col">
              <div class="work-detail-container">
                ${assign.work_detail
                  .map(
                    (detail) => `
                  <div class="work-item">
                    <span class="work-name">${detail.work_name}</span>
                    ${detail.pcs !== 1 ? `<span class="work-pcs">${detail.pcs}</span>` : ''}
                  </div>
                `
                  )
                  .join('')}
              </div>
            </td>
          </tr>
        `
          )
          .join('')}
      </tbody>
    </table>
  `

  return html
}
</script>
