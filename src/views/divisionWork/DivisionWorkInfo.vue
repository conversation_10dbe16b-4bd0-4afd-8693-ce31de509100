<template>
  <PageTemplate
    :queryParams="queryParams"
    :tableData="tableData"
    :total="total"
    :loading="loading"
    :tableHeight="650"
    @query="handleQuery"
    @reset="handleReset"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  >
    <!-- 查询区域左侧 -->
    <template #query-form-left>
      <el-form-item label="所属年份" prop="division_work_years">
        <el-select
          v-model="queryParams.division_work_years"
          placeholder="选择年份"
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          style="width: 180px"
          @change="handleYearChange"
        >
          <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
        </el-select>
      </el-form-item>
      <el-form-item label="服装名称" prop="clothing_name">
        <el-input
          v-model="queryParams.clothing_name"
          placeholder="输入服装名称"
          clearable
          style="width: 150px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="组名" prop="group_name">
        <el-select
          v-model="queryParams.group_name"
          placeholder="选择组名"
          clearable
          style="width: 120px"
          @change="handleQuery"
        >
          <el-option v-for="group in groupOptions" :key="group" :label="group" :value="group" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="is_complete">
        <el-select
          v-model="queryParams.is_complete"
          placeholder="选择状态"
          clearable
          style="width: 120px"
          @change="handleQuery"
        >
          <el-option :label="'未完成'" :value="0" />
          <el-option :label="'已完成'" :value="1" />
        </el-select>
      </el-form-item>
    </template>

    <!-- 查询区域右侧 -->
    <template #query-form-right>
      <el-button type="success" @click="handleAdd">新增分工</el-button>
    </template>

    <!-- 表格列 -->
    <template #table-columns>
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="division_work_year" label="所属年份" width="100" align="center" />
      <el-table-column prop="group_name" label="组名" width="100" align="center" />
      <el-table-column prop="clothing_name" label="服装名称" width="180" align="center">
        <template #default="scope">
          <el-tooltip
            effect="light"
            placement="top"
            :content="clothingTooltipContent"
            :enterable="false"
            :disabled="!clothingTooltipContent"
            trigger="click"
          >
            <span class="clothing-name" @click="handleGetClothingInfo(scope.row.clothing_id)">
              {{ scope.row.clothing_name }}
            </span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="style" label="款式" width="120" align="center" />
      <el-table-column prop="pocket_type" label="口袋类型" width="120" align="center" />
      <el-table-column prop="craft_details" label="工艺细节" width="150" align="center" />

      <el-table-column prop="pcs" label="件数" width="100" align="center" />
      <el-table-column prop="is_complete" label="状态" width="100" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.is_complete === 1 ? 'success' : 'warning'">
            {{ scope.row.is_complete === 1 ? '已完成' : '未完成' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="320" fixed="right" align="center">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="success" size="small" @click="handleCopy(scope.row)">复制</el-button>
          <el-button type="warning" size="small" @click="handleSettlement(scope.row)"
            >结算</el-button
          >
          <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </template>

    <!-- 分页区域左侧 -->
    <template #pagination-left>
      <el-button type="primary" @click="handleExportExcel">导出Excel</el-button>
    </template>

    <!-- 额外内容插槽 -->
    <template #extra-content>
      <!-- 分工对话框 -->
      <DivisionWorkDialog
        v-model:visible="dialogVisible"
        :type="dialogType"
        :edit-data="currentEditRow"
        :copied-assign-data="copiedAssignData"
        @submit="handleDialogSubmit"
        @cancel="handleDialogCancel"
        @cancelSettlement="handleCancelSettlement"
      />
    </template>
  </PageTemplate>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
// 导入通用页面模板
import PageTemplate from '@/components/common/PageTemplate.vue'
// 导入分工对话框组件
import DivisionWorkDialog from './components/DivisionWorkDialog.vue'
// 导入API
import {
  getDivisionWorkList,
  createDivisionWork,
  updateDivisionWork,
  deleteDivisionWork,
  getDivisionWorkYearOptions,
  getDivisionWorkGroupOptions,
} from '@/api/divisionWork'
// 导入服装API
import { getClothingById } from '@/api/clothing'
// 导入分工分配API
import {
  getDivisionWorkAssignByDivisionWorkId,
  batchCreateDivisionWorkAssign,
  batchUpdateDivisionWorkAssign,
  deleteDivisionWorkAssign,
} from '@/api/divisionWorkAssign'
// 导入分工完成API
import {
  getDivisionWorkCompleteByDivisionWorkId,
  batchCreateDivisionWorkComplete,
  batchUpdateDivisionWorkComplete,
  deleteDivisionWorkComplete,
} from '@/api/divisionWorkComplete'
// 导入类型定义
import type { DivisionWork, QueryDivisionWorkParams } from '@/api/divisionWork'
import type { Clothing } from '@/types/clothing'
// 引入xlsx库
import * as XLSX from 'xlsx'

// 加载状态
const loading = ref(false)
// 表格数据
const tableData = ref<DivisionWork[]>([])
// 总数
const total = ref(0)
// 对话框可见性
const dialogVisible = ref(false)
// 对话框类型（新增/编辑/结算）
const dialogType = ref<'add' | 'edit' | 'settlement'>('add')
// 当前编辑的行数据
const currentEditRow = ref<DivisionWork | null>(null)
// 年份选项
const yearOptions = ref<string[]>([])
// 组名选项
const groupOptions = ref<string[]>([])
// 当前选中的服装信息
const currentClothingInfo = ref<Clothing | null>(null)
// 加载服装信息状态
const loadingClothingInfo = ref(false)
// 服装信息提示内容
const clothingTooltipContent = ref('')
// 复制的分工分配数据
const copiedAssignData = ref<any[]>([])

// 生成分工编号
const generateDivisionWorkId = () => {
  const now = new Date()
  const prefix = 'FG'
  const timestamp = now.getTime()
  return `${prefix}${timestamp}`
}

// 查询参数
const queryParams = reactive<QueryDivisionWorkParams>({
  division_work_years: [],
  division_work_id: '',
  clothing_id: '',
  clothing_name: '',
  group_name: '',
  is_complete: undefined,
  page: 1,
  limit: 10,
})

// 格式化日期
const formatDate = (date: string | Date | undefined) => {
  if (!date) return ''
  const d = new Date(date)
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(
    d.getDate()
  ).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(
    2,
    '0'
  )}`
}

// 查询方法F
const handleQuery = async () => {
  loading.value = true
  try {
    const res = await getDivisionWorkList(queryParams)
    console.log('获取到的分工数据：', res)
    if (res && res.data) {
      tableData.value = res.data.data as unknown as DivisionWork[]
      total.value = (res.data as any).total || 0
    }
  } catch (error) {
    console.error('查询分工失败:', error)
    ElMessage.error('查询分工失败')
  } finally {
    loading.value = false
  }
}

// 获取服装详细信息
const handleGetClothingInfo = async (clothingId: string) => {
  if (!clothingId) return

  loadingClothingInfo.value = true

  try {
    const res = await getClothingById(clothingId)
    console.log('获取到的服装信息：', res)
    if (res.data) {
      const clothingData = res.data as unknown as Clothing
      currentClothingInfo.value = clothingData
      console.log('服装信息：', clothingData)
      // 构建提示内容
      clothingTooltipContent.value = `
${clothingData.supplier || ''} | ${clothingData.group_classification?.join(', ') || ''} | ${clothingData.long_or_short_sleeve || ''} | ${clothingData.size || ''}
      `.trim()
      console.log('提示内容：', clothingTooltipContent.value)
    } else {
      clothingTooltipContent.value = '未找到服装信息'
    }
  } catch (error) {
    console.error('获取服装信息失败:', error)
    clothingTooltipContent.value = '获取服装信息失败'
  } finally {
    loadingClothingInfo.value = false
  }
}

// 重置方法
const handleReset = () => {
  queryParams.division_work_years = []
  queryParams.division_work_id = ''
  queryParams.clothing_id = ''
  queryParams.clothing_name = ''
  queryParams.group_name = ''
  queryParams.is_complete = undefined
  queryParams.page = 1
  queryParams.limit = 10
  handleQuery()
}

// 年份变化处理
const handleYearChange = () => {
  handleQuery()
}

// 分页大小变化
const handleSizeChange = (val: number) => {
  queryParams.limit = val
  handleQuery()
}

// 当前页变化
const handleCurrentChange = (val: number) => {
  queryParams.page = val
  handleQuery()
}

// 新增分工
const handleAdd = () => {
  dialogType.value = 'add'
  currentEditRow.value = null
  // 清空复制的分工分配数据
  copiedAssignData.value = []
  dialogVisible.value = true
}

// 编辑分工
const handleEdit = (row: DivisionWork) => {
  dialogType.value = 'edit'
  currentEditRow.value = { ...row }
  dialogVisible.value = true
}

// 复制分工
const handleCopy = async (row: DivisionWork) => {
  try {
    // 设置为新增模式
    dialogType.value = 'add'

    // 先获取分工分配数据
    if (row._id) {
      // 加载分工分配数据
      const res = await getDivisionWorkAssignByDivisionWorkId(row.division_work_id)
      if (res && res.data) {
        // 创建一个新的分工数据对象，只保留需要的信息
        currentEditRow.value = {
          _id: undefined, // 清除ID，确保是新建
          division_work_id: generateDivisionWorkId(), // 生成新的分工ID
          is_complete: 0, // 设置为未完成状态
          // 不保留服装相关字段
          clothing_id: '', // 清空服装ID
          clothing_name: '', // 清空服装名称
          pcs: 0, // 清空件数
          division_work_year: '', // 清空所属年份

          // 只保留这些字段
          // division_work_year: row.division_work_year, // 保留所属年份
          group_name: row.group_name, // 保留缝制组
          craft_details: row.craft_details, // 保留工艺细节
        }

        // 设置复制的分工分配数据
        if (Array.isArray(res.data)) {
          copiedAssignData.value = res.data
        } else {
          copiedAssignData.value = []
          console.error('获取的分工分配数据不是数组')
        }

        // 打开对话框
        dialogVisible.value = true
      }
    }
  } catch (error) {
    console.error('复制分工失败:', error)
    ElMessage.error('复制分工失败')
  }
}

// 结算分工
const handleSettlement = async (row: DivisionWork) => {
  try {
    // 设置为结算模式
    dialogType.value = 'settlement'

    // 先获取分工分配数据和分工完成数据
    if (row._id) {
      // 加载分工完成数据
      let completeData: any[] = []
      let hasCompleteData = false

      try {
        const completeRes = await getDivisionWorkCompleteByDivisionWorkId(row.division_work_id)
        if (completeRes && completeRes.data && Array.isArray(completeRes.data)) {
          completeData = completeRes.data
          hasCompleteData = completeData.length > 0
        }
      } catch (error) {
        console.error('获取分工完成数据失败，将使用分工分配数据:', error)
      }

      // 如果没有分工完成数据，则加载分工分配数据
      if (!hasCompleteData) {
        const assignRes = await getDivisionWorkAssignByDivisionWorkId(row.division_work_id)
        if (assignRes && assignRes.data && Array.isArray(assignRes.data)) {
          copiedAssignData.value = assignRes.data
        } else {
          copiedAssignData.value = []
          console.error('获取的分工分配数据不是数组')
        }
      } else {
        // 使用已有的分工完成数据
        copiedAssignData.value = completeData
      }

      // 设置当前编辑行数据
      currentEditRow.value = row
      // 添加一个标记，表示是否已经结算过
      ;(currentEditRow.value as any)._hasCompleteData = hasCompleteData

      // 打开对话框
      dialogVisible.value = true
    }
  } catch (error) {
    console.error('结算分工失败:', error)
    ElMessage.error('结算分工失败')
  }
}

// 删除分工
const handleDelete = (row: DivisionWork) => {
  ElMessageBox.confirm('确认删除该分工记录及其所有分工明细和结算数据吗？此操作不可恢复！', '警告', {
    confirmButtonText: '确定删除',
    cancelButtonText: '取消',
    type: 'warning',
    confirmButtonClass: 'el-button--danger',
  })
    .then(async () => {
      try {
        if (!row._id || !row.division_work_id) {
          ElMessage.error('分工ID无效')
          return
        }

        // 显示加载中
        const loading = ElLoading.service({
          lock: true,
          text: '正在删除分工及相关明细和结算数据...',
          background: 'rgba(0, 0, 0, 0.7)',
        })

        try {
          // 1.1 获取该分工下的所有分工明细
          const assignRes = await getDivisionWorkAssignByDivisionWorkId(row.division_work_id)
          const assignItems = Array.isArray(assignRes?.data?.data)
            ? assignRes.data.data
            : Array.isArray(assignRes?.data)
              ? assignRes.data
              : []

          // 1.2 获取该分工下的所有分工完成数据
          const completeRes = await getDivisionWorkCompleteByDivisionWorkId(row.division_work_id)
          const completeItems = Array.isArray(completeRes?.data?.data)
            ? completeRes.data.data
            : Array.isArray(completeRes?.data)
              ? completeRes.data
              : []

          // 2. 删除所有分工明细
          if (assignItems.length > 0) {
            const deletePromises = assignItems.map((assign: any) =>
              assign._id ? deleteDivisionWorkAssign(assign._id) : Promise.resolve()
            )

            await Promise.all(deletePromises)
            console.log(`已删除 ${assignItems.length} 条分工明细`)
          }

          // 2.1 删除所有分工完成数据
          if (completeItems.length > 0) {
            const deletePromises = completeItems.map((item: any) =>
              item._id ? deleteDivisionWorkComplete(item._id) : Promise.resolve()
            )

            await Promise.all(deletePromises)
            console.log(`已删除 ${completeItems.length} 条分工完成数据`)
          }

          // 3. 删除分工主记录
          const res = await deleteDivisionWork(row._id)

          // 关闭加载中
          loading.close()

          if (res && res.data) {
            ElMessage.success(`删除成功：已删除分工记录及 ${assignItems.length} 条分工明细和 ${completeItems.length} 条分工完成数据`)
            handleQuery()
          } else {
            ElMessage.error('删除分工记录失败')
          }
        } catch (error) {
          // 确保加载中被关闭
          loading.close()
          throw error
        }
      } catch (error: any) {
        console.error('删除分工失败:', error)
        ElMessage.error(`删除分工失败: ${error.message || '未知错误'}`)
      }
    })
    .catch(() => {
      // 用户取消删除，不做任何操作
    })
}

// 对话框提交
const handleDialogSubmit = async (formData: any) => {
  try {
    // 验证分工数据
    if (!formData.division_work_id || !formData.division_work_year) {
      ElMessage.warning('分工编号和所属年份不能为空')
      return
    }

    // 提取分工基本信息和分工明细
    const { assign_list = [], ...divisionWorkData } = formData
    const assigns = assign_list
    const hasAssigns = Array.isArray(assigns) && assigns.length > 0

    // 根据对话框类型执行不同的操作
    if (dialogType.value === 'add') {
      // 1. 先创建分工基本信息
      const divisionWorkRes = await createDivisionWork(divisionWorkData)

      if (!divisionWorkRes || !divisionWorkRes.data) {
        ElMessage.error('新增分工基本信息失败')
        return
      }

      ElMessage.success('新增分工基本信息成功')

      // 2. 如果有分工明细，批量创建分工分配
      if (hasAssigns) {
        await handleBatchAssigns(assigns, 'create')
      }

      // 3. 清理状态并刷新数据
      dialogVisible.value = false
      copiedAssignData.value = []
      currentEditRow.value = null
      handleQuery()
    } else if (dialogType.value === 'edit' && currentEditRow.value?._id) {
      // 1. 更新分工基本信息
      const divisionWorkRes = await updateDivisionWork(currentEditRow.value._id, divisionWorkData)

      if (!divisionWorkRes || !divisionWorkRes.data) {
        ElMessage.error('更新分工基本信息失败')
        return
      }

      ElMessage.success('更新分工基本信息成功')

      // 2. 如果有分工明细，批量更新分工分配
      if (hasAssigns) {
        await handleBatchAssigns(assigns, 'update')
      }

      // 3. 关闭对话框并刷新数据
      dialogVisible.value = false
      handleQuery()
    } else if (dialogType.value === 'settlement' && currentEditRow.value?._id) {
      // 结算模式：保存分工完成数据
      if (hasAssigns) {
        try {
          let res
          // 根据是否已经结算过决定使用创建还是更新API
          const hasCompleteData = (currentEditRow.value as any)._hasCompleteData === true

          if (hasCompleteData) {
            // 如果已经结算过，使用更新API
            res = await batchUpdateDivisionWorkComplete({ assigns })
            console.log('更新分工完成数据:', res)
          } else {
            // 如果未结算过，使用创建API
            res = await batchCreateDivisionWorkComplete({ assigns })
            console.log('创建分工完成数据:', res)
          }

          if (res && res.data && res.data.code === 200) {
            ElMessage.success('结算分工成功')

            // 更新分工状态为已完成
            if (currentEditRow.value._id && currentEditRow.value.is_complete !== 1) {
              await updateDivisionWork(currentEditRow.value._id, {
                ...divisionWorkData,
                is_complete: 1,
              })
            }

            // 关闭对话框并刷新数据
            dialogVisible.value = false
            handleQuery()
          } else {
            ElMessage.error(res?.data?.message || '结算分工失败')
          }
        } catch (error: any) {
          console.error('结算分工失败:', error)
          ElMessage.error(`结算分工失败: ${error.message || '未知错误'}`)
        }
      } else {
        ElMessage.warning('没有有效的分工明细数据')
      }
    }
  } catch (error: any) {
    console.error('保存分工失败:', error)
    ElMessage.error(`保存分工失败: ${error.message || '未知错误'}`)
  }
}

// 处理批量分工分配
const handleBatchAssigns = async (assigns: any[], type: 'create' | 'update') => {
  if (!assigns || assigns.length === 0) return

  try {
    // 确保每个分配项都有必要的字段
    const validAssigns = assigns.filter(
      (item) =>
        item.division_work_id &&
        item.division_work_year &&
        item.staff_id &&
        item.staff_name &&
        Array.isArray(item.work_detail) &&
        item.work_detail.length > 0
    )

    if (validAssigns.length === 0) {
      ElMessage.warning('没有有效的分工分配数据')
      return
    }

    // 根据类型选择不同的API
    const apiMethod =
      type === 'create' ? batchCreateDivisionWorkAssign : batchUpdateDivisionWorkAssign

    // 将数组包装成API需要的格式
    const res = await apiMethod({ assigns: validAssigns })

    if (res.data && res.data.code === 200) {
      ElMessage.success(`批量${type === 'create' ? '创建' : '更新'}分工分配成功`)
    } else {
      ElMessage.error(
        (res.data && res.data.message) || `批量${type === 'create' ? '创建' : '更新'}分工分配失败`
      )
    }
  } catch (error: any) {
    console.error(`批量${type === 'create' ? '创建' : '更新'}分工分配失败:`, error)
    ElMessage.error(
      `批量${type === 'create' ? '创建' : '更新'}分工分配失败: ${error.message || '未知错误'}`
    )
  }
}

// 对话框取消
const handleDialogCancel = () => {
  dialogVisible.value = false
  // 清空复制的分工分配数据和当前编辑行
  copiedAssignData.value = []
  if (dialogType.value === 'add') {
    currentEditRow.value = null
  }
}

// 处理取消结算
const handleCancelSettlement = async (divisionWorkId: string) => {
  if (!divisionWorkId) {
    ElMessage.error('分工ID无效，无法取消结算')
    return
  }

  try {
    // 显示加载中
    const loading = ElLoading.service({
      lock: true,
      text: '正在取消结算...',
      background: 'rgba(0, 0, 0, 0.7)',
    })

    try {
      // 1. 根据分工ID删除所有分工完成数据
      const res = await getDivisionWorkCompleteByDivisionWorkId(divisionWorkId)

      if (res && res.data && Array.isArray(res.data)) {
        const completeItems = res.data

        // 删除所有分工完成数据
        if (completeItems.length > 0) {
          const deletePromises = completeItems.map((item: any) =>
            item._id ? deleteDivisionWorkComplete(item._id) : Promise.resolve()
          )

          await Promise.all(deletePromises)
          console.log(`已删除 ${completeItems.length} 条分工完成数据`)
        }

        // 2. 更新分工状态为未完成
        const divisionWork = tableData.value.find(
          (item) => item.division_work_id === divisionWorkId
        )
        if (divisionWork && divisionWork._id) {
          await updateDivisionWork(divisionWork._id, { is_complete: 0 })
        }

        ElMessage.success(`取消结算成功：已删除 ${completeItems.length} 条分工完成数据`)
        // 刷新数据
        handleQuery()
      } else {
        ElMessage.warning('未找到相关的分工完成数据')
      }
    } finally {
      // 关闭加载中
      loading.close()
    }
  } catch (error: any) {
    console.error('取消结算失败:', error)
    ElMessage.error(`取消结算失败: ${error.message || '未知错误'}`)
  }
}

// 导出Excel
const handleExportExcel = () => {
  if (tableData.value.length === 0) {
    ElMessage.warning('没有数据可导出')
    return
  }

  // 准备导出数据
  const exportData = tableData.value.map((item) => ({
    所属年份: item.division_work_year,
    分组编号: item.division_work_id,
    服装编号: item.clothing_id,
    服装名称: item.clothing_name || '',
    款式: item.style || '',
    口袋类型: item.pocket_type || '',
    工艺细节: item.craft_details || '',
    组名: item.group_name || '',
    件数: item.pcs || 0,
    状态: item.is_complete === 1 ? '已完成' : '未完成',
    创建时间: formatDate(item.createTime),
  }))

  // 创建工作簿
  const worksheet = XLSX.utils.json_to_sheet(exportData)
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, '分工信息')

  // 导出文件
  XLSX.writeFile(workbook, `分工信息_${new Date().toISOString().split('T')[0]}.xlsx`)
}

// 初始化
onMounted(async () => {
  try {
    // 获取年份选项
    const yearRes = await getDivisionWorkYearOptions()
    if (yearRes && yearRes.data) {
      yearOptions.value = (yearRes.data as any).years || []
    }

    // 获取组名选项
    const groupRes = await getDivisionWorkGroupOptions()
    if (groupRes && groupRes.data) {
      groupOptions.value = (groupRes.data as any).groups || []
    }

    // 查询数据
    handleQuery()
  } catch (error) {
    console.error('初始化失败:', error)
    ElMessage.error('初始化失败')
  }
})
</script>

<style scoped>
.clothing-name {
  cursor: pointer;
}
</style>
