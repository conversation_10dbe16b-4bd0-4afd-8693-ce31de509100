<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="800px"
    :close-on-click-modal="false"
    @closed="resetForm"
    center
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="material-form">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="物料编号" prop="material_id">
            <el-input
              v-model="form.material_id"
              placeholder="请输入物料编号"
              :disabled="!!material"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="物料名称" prop="material_name">
            <el-input
              v-model="form.material_name"
              placeholder="请输入物料名称"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="分类" prop="category_id">
            <el-select
              v-model="form.category_id"
              placeholder="选择分类"
              style="width: 100%"
              @change="handleCategoryChange"
            >
              <el-option
                v-for="category in categoryOptions"
                :key="category.category_id"
                :label="category.category_name"
                :value="category.category_id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计量单位" prop="unit">
            <el-select
              v-model="form.unit"
              placeholder="选择单位"
              filterable
              allow-create
              style="width: 100%"
            >
              <el-option
                v-for="unit in unitOptions"
                :key="unit"
                :label="unit"
                :value="unit"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="规格型号" prop="specification">
            <el-input
              v-model="form.specification"
              placeholder="请输入规格型号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="当前价格" prop="current_price">
            <el-input-number
              v-model="form.current_price"
              :precision="2"
              :min="0"
              placeholder="请输入当前价格"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="3"
              placeholder="请输入描述"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 年度价格配置 -->
      <el-divider content-position="left">年度价格配置</el-divider>
      <div class="yearly-prices-section">
        <div class="yearly-prices-header">
          <el-button type="primary" size="small" @click="addYearlyPrice">
            添加年度价格
          </el-button>
        </div>

        <div v-if="form.yearly_prices && form.yearly_prices.length > 0" class="yearly-prices-container">
          <el-table
            :data="form.yearly_prices"
            border
            stripe
            size="small"
            class="yearly-prices-table"
            max-height="300"
          >
            <el-table-column label="年份" width="120">
              <template #default="{ row }">
                <el-input
                  v-model="row.year"
                  placeholder="年份"
                  size="small"
                />
              </template>
            </el-table-column>
            <el-table-column label="价格" width="140">
              <template #default="{ row }">
                <el-input-number
                  v-model="row.price"
                  :precision="2"
                  :min="0"
                  placeholder="价格"
                  size="small"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column label="生效日期" width="160">
              <template #default="{ row }">
                <el-date-picker
                  v-model="row.effective_date"
                  type="date"
                  placeholder="生效日期"
                  size="small"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column label="供应商" min-width="120">
              <template #default="{ row }">
                <el-input
                  v-model="row.supplier"
                  placeholder="供应商"
                  size="small"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
              <template #default="{ $index }">
                <el-button
                  type="danger"
                  size="small"
                  link
                  @click="removeYearlyPrice($index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div v-else class="no-yearly-prices">
          <el-empty description="暂无年度价格配置" :image-size="60" />
        </div>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { createMaterial, updateMaterial, getMaterialUnitOptions, generateMaterialId } from '@/api/material'
import { getMaterialCategoryOptions } from '@/api/materialCategory'
import type { Material, MaterialCategory } from '@/types/bom'

// Props
interface Props {
  modelValue: boolean
  title: string
  material?: Material | null
}

const props = withDefaults(defineProps<Props>(), {
  material: null,
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

// 选项数据
const categoryOptions = ref<MaterialCategory[]>([])
const unitOptions = ref<string[]>([])

// 表单数据
const form = reactive<Partial<Material>>({
  material_id: '',
  material_name: '',
  category_id: '',
  specification: '',
  unit: '',
  current_price: undefined,
  description: '',
  yearly_prices: [],
})

// 表单验证规则
const rules: FormRules = {
  material_id: [
    { required: true, message: '请输入物料编号', trigger: 'blur' },
  ],
  material_name: [
    { required: true, message: '请输入物料名称', trigger: 'blur' },
  ],
  category_id: [
    { required: true, message: '请选择分类', trigger: 'change' },
  ],
  unit: [
    { required: true, message: '请选择计量单位', trigger: 'change' },
  ],
}

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val
  }
)

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 监听物料数据变化
watch(
  () => props.material,
  (newVal) => {
    if (newVal) {
      // 复制对象，避免直接修改props
      Object.keys(form).forEach((key) => {
        // @ts-ignore
        form[key] = newVal[key] !== undefined ? newVal[key] : form[key]
      })
    }
  },
  { deep: true, immediate: true }
)

// 初始化
onMounted(async () => {
  await Promise.all([loadCategoryOptions(), loadUnitOptions()])
})

// 加载分类选项
const loadCategoryOptions = async () => {
  try {
    console.log('开始加载分类选项...')
    const res = await getMaterialCategoryOptions()
    console.log('分类选项API响应:', res)
    if (res && res.data && Array.isArray(res.data)) {
      categoryOptions.value = res.data
      console.log('成功加载分类选项:', res.data.length, '个分类')
      console.log('分类选项详情:', res.data)
    } else {
      console.warn('分类选项数据格式异常:', res)
    }
  } catch (error) {
    console.error('加载分类选项失败:', error)
  }
}

// 加载单位选项
const loadUnitOptions = async () => {
  try {
    const res = await getMaterialUnitOptions()
    if (res.data && res.data.data) {
      unitOptions.value = res.data.data
    }
  } catch (error) {
    console.error('加载单位选项失败:', error)
  }
}

// 添加年度价格
const addYearlyPrice = () => {
  if (!form.yearly_prices) {
    form.yearly_prices = []
  }
  form.yearly_prices.push({
    year: new Date().getFullYear().toString(),
    price: 0,
    effective_date: new Date(),
    supplier: '',
  })
}

// 删除年度价格
const removeYearlyPrice = (index: number) => {
  if (form.yearly_prices) {
    form.yearly_prices.splice(index, 1)
  }
}

// 重置表单
const resetForm = () => {
  // 先重置表单数据
  Object.keys(form).forEach((key) => {
    if (key === 'yearly_prices') {
      // @ts-ignore
      form[key] = []
    } else {
      // @ts-ignore
      form[key] = key === 'current_price' ? undefined : ''
    }
  })

  // 延迟清除验证状态，避免在数据重置时触发验证错误
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  })
}

// 分类选择变化处理
const handleCategoryChange = async (categoryId: string) => {
  console.log('分类选择变化:', categoryId)
  console.log('是否为编辑模式:', !!props.material?._id)

  if (!categoryId || props.material?._id) {
    // 如果没有选择分类或者是编辑模式，不自动生成编号
    console.log('跳过编号生成 - 原因:', !categoryId ? '未选择分类' : '编辑模式')
    return
  }

  console.log('开始生成物料编号...')
  try {
    const response: any = await generateMaterialId(categoryId)
    console.log('生成物料编号API响应:', response)
    console.log('响应类型:', typeof response)
    console.log('响应属性:', Object.keys(response || {}))

    if (response && response.code === 200 && response.data && response.data.material_id) {
      console.log('成功生成物料编号:', response.data.material_id)
      form.material_id = response.data.material_id
      ElMessage.success('已自动生成物料编号: ' + response.data.material_id)
    } else {
      console.warn('API响应数据格式异常:', response)
      ElMessage.warning('生成物料编号失败，请手动输入')
    }
  } catch (error: any) {
    console.error('生成物料编号失败:', error)
    console.error('错误详情:', error.response || error.message)
    ElMessage.warning('生成物料编号失败，请手动输入')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  const valid = await formRef.value.validate()
  if (valid) {
    try {
      loading.value = true

      // 准备提交数据
      const submitData = { ...form }

      // 过滤和验证年度价格数据
      if (submitData.yearly_prices && submitData.yearly_prices.length > 0) {
        submitData.yearly_prices = submitData.yearly_prices.filter((item: any) => {
          // 只保留有效的年度价格项（年份和价格都不为空）
          return item.year && item.year.trim() !== '' &&
                 item.price !== null && item.price !== undefined && item.price >= 0
        }).map((item: any) => ({
          year: item.year.toString(),
          price: Number(item.price),
          effective_date: item.effective_date || new Date(),
          supplier: item.supplier || ''
        }))
      } else {
        submitData.yearly_prices = []
      }

      let response
      if (props.material?._id) {
        // 更新
        response = await updateMaterial(props.material._id, submitData)
        if (response && response.data) {
          ElMessage.success('更新物料成功')
          dialogVisible.value = false
          emit('success')
        } else {
          ElMessage.error(response?.data?.message || '更新物料失败')
        }
      } else {
        // 新增
        response = await createMaterial(submitData as any)
        if (response && response.data) {
          ElMessage.success('新增物料成功')
          dialogVisible.value = false
          emit('success')
        } else {
          ElMessage.error(response?.data?.message || '新增物料失败')
        }
      }
    } catch (error: any) {
      console.error('保存物料失败', error)
      ElMessage.error(error.response?.data?.message || '保存物料失败')
    } finally {
      loading.value = false
    }
  } else {
    ElMessage.warning('请填写必填项')
  }
}
</script>

<style scoped>
.material-form {
  padding: 0 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.yearly-prices-section {
  margin-top: 10px;
}

.yearly-prices-header {
  margin-bottom: 15px;
}

.yearly-prices-container {
  margin-top: 15px;
  border-radius: 4px;
  overflow: hidden;
}

.yearly-prices-table {
  width: 100%;
}

.yearly-prices-table :deep(.el-table__body-wrapper) {
  max-height: 300px;
  overflow-y: auto;
}

.yearly-prices-table :deep(.el-input__inner) {
  height: 28px;
  line-height: 28px;
}

.yearly-prices-table :deep(.el-input-number) {
  width: 100%;
}

.yearly-prices-table :deep(.el-input-number .el-input__inner) {
  text-align: left;
}

.yearly-prices-table :deep(.el-date-editor.el-input) {
  width: 100%;
}

.no-yearly-prices {
  margin-top: 20px;
  padding: 20px;
  text-align: center;
  background-color: #fafafa;
  border-radius: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
