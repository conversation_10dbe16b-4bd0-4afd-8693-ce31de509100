<template>
  <el-dialog
    v-model="dialogVisible"
    title="导入物料数据"
    width="600px"
    :close-on-click-modal="false"
    @closed="resetDialog"
  >
    <div class="import-container">
      <!-- 文件上传区域 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :auto-upload="false"
          :on-change="handleFileChange"
          :file-list="fileList"
          accept=".xlsx,.xls"
          :limit="1"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 xlsx/xls 文件，且不超过 10MB
            </div>
          </template>
        </el-upload>
      </div>

      <!-- 数据预览区域 -->
      <div v-if="previewData.length > 0" class="preview-section">
        <el-divider content-position="left">数据预览</el-divider>
        <div class="preview-info">
          <span>共 {{ previewData.length }} 条数据</span>
          <el-button type="text" @click="downloadTemplate">下载模板</el-button>
        </div>
        
        <el-table
          :data="previewData.slice(0, 5)"
          border
          size="small"
          max-height="300"
        >
          <el-table-column prop="material_id" label="物料编号" width="120" />
          <el-table-column prop="material_name" label="物料名称" width="150" />
          <el-table-column prop="category_id" label="分类ID" width="100" />
          <el-table-column prop="specification" label="规格型号" width="120" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="current_price" label="当前价格" width="100" />
          <el-table-column prop="description" label="描述" min-width="150" />
        </el-table>
        
        <div v-if="previewData.length > 5" class="more-data-tip">
          还有 {{ previewData.length - 5 }} 条数据未显示...
        </div>
      </div>

      <!-- 导入结果区域 -->
      <div v-if="importResult" class="result-section">
        <el-divider content-position="left">导入结果</el-divider>
        <el-alert
          :title="importResult.message"
          :type="importResult.successCount > 0 ? 'success' : 'error'"
          :closable="false"
          show-icon
        >
          <template #default>
            <div>
              <p>成功导入：{{ importResult.successCount }} 条</p>
              <p>导入失败：{{ importResult.failCount }} 条</p>
              <div v-if="importResult.errors && importResult.errors.length > 0">
                <p>错误详情：</p>
                <ul class="error-list">
                  <li v-for="(error, index) in importResult.errors" :key="index">
                    {{ error }}
                  </li>
                </ul>
              </div>
            </div>
          </template>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :disabled="previewData.length === 0"
          :loading="importing"
          @click="handleImport"
        >
          开始导入
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage, type UploadInstance, type UploadUserFile } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import { importMaterialJsonData } from '@/api/material'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const importing = ref(false)
const uploadRef = ref<UploadInstance>()
const fileList = ref<UploadUserFile[]>([])
const previewData = ref<any[]>([])
const importResult = ref<any>(null)

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val
  }
)

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 处理文件变化
const handleFileChange = (file: UploadUserFile) => {
  if (!file.raw) return

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const data = new Uint8Array(e.target?.result as ArrayBuffer)
      const workbook = XLSX.read(data, { type: 'array' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet)

      // 转换数据格式
      const processedData = jsonData.map((row: any) => ({
        material_id: row['物料编号'] || row.material_id || '',
        material_name: row['物料名称'] || row.material_name || '',
        category_id: row['分类ID'] || row.category_id || '',
        specification: row['规格型号'] || row.specification || '',
        unit: row['单位'] || row.unit || '',
        current_price: parseFloat(row['当前价格'] || row.current_price || 0),
        description: row['描述'] || row.description || '',
      }))

      previewData.value = processedData
      importResult.value = null
      ElMessage.success(`成功解析 ${processedData.length} 条数据`)
    } catch (error) {
      console.error('文件解析失败:', error)
      ElMessage.error('文件解析失败，请检查文件格式')
    }
  }
  reader.readAsArrayBuffer(file.raw)
}

// 开始导入
const handleImport = async () => {
  if (previewData.value.length === 0) {
    ElMessage.warning('请先选择要导入的文件')
    return
  }

  try {
    importing.value = true
    
    const importData = {
      sheetName: '物料信息',
      data: previewData.value,
      fileName: fileList.value[0]?.name || 'unknown',
      totalRecords: previewData.value.length,
    }

    const response = await importMaterialJsonData(importData)
    
    if (response && response.data) {
      importResult.value = response.data
      
      if ((response.data as any).successCount > 0) {
        ElMessage.success('导入完成')
        emit('success')
      } else {
        ElMessage.error('导入失败')
      }
    }
  } catch (error: any) {
    console.error('导入失败:', error)
    ElMessage.error(error.response?.data?.message || '导入失败')
  } finally {
    importing.value = false
  }
}

// 下载模板
const downloadTemplate = () => {
  const templateData = [
    {
      物料编号: 'WL001',
      物料名称: '示例物料',
      分类ID: 'CAT001',
      规格型号: '规格示例',
      单位: '米',
      当前价格: 10.5,
      描述: '物料描述示例',
    },
  ]

  const ws = XLSX.utils.json_to_sheet(templateData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, '物料信息模板')
  XLSX.writeFile(wb, '物料信息导入模板.xlsx')
  
  ElMessage.success('模板下载成功')
}

// 重置对话框
const resetDialog = () => {
  fileList.value = []
  previewData.value = []
  importResult.value = null
  importing.value = false
  
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}
</script>

<style scoped>
.import-container {
  padding: 0 10px;
}

.upload-section {
  margin-bottom: 20px;
}

.preview-section {
  margin-bottom: 20px;
}

.preview-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
  color: #666;
}

.more-data-tip {
  text-align: center;
  color: #999;
  font-size: 12px;
  margin-top: 10px;
}

.result-section {
  margin-bottom: 20px;
}

.error-list {
  max-height: 150px;
  overflow-y: auto;
  margin: 0;
  padding-left: 20px;
}

.error-list li {
  margin-bottom: 5px;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}
</style>
