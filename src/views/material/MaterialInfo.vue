<template>
  <PageTemplate
    :queryParams="queryParams"
    :tableData="tableData"
    :total="total"
    :loading="loading"
    :tableHeight="650"
    @query="handleQuery"
    @reset="handleReset"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  >
    <!-- 查询区域左侧 -->
    <template #query-form-left>
      <el-form-item label="物料编号" prop="material_id">
        <el-input
          v-model="queryParams.material_id"
          placeholder="输入物料编号"
          clearable
          style="width: 150px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料名称" prop="material_name">
        <el-input
          v-model="queryParams.material_name"
          placeholder="输入物料名称"
          clearable
          style="width: 150px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分类" prop="category_id">
        <el-select
          v-model="queryParams.category_id"
          placeholder="选择分类"
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="category in categoryOptions"
            :key="category.category_id"
            :label="category.category_name"
            :value="category.category_id"
          />
        </el-select>
      </el-form-item>
    </template>

    <!-- 查询区域右侧 -->
    <template #query-form-right>
      <el-button type="primary" @click="handleAdd">新增物料</el-button>
      <el-button type="success" @click="handleImport">导入物料</el-button>
      <el-button type="info" @click="handleExport">导出数据</el-button>
    </template>

    <!-- 表格列 -->
    <template #table-columns>
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="material_id" label="物料编号" width="120" align="center" />
      <el-table-column prop="material_name" label="物料名称" width="200" align="center" />
      <el-table-column prop="category_name" label="分类" width="150" align="center" />
      <el-table-column prop="specification" label="规格型号" width="150" align="center" />
      <el-table-column prop="unit" label="单位" width="80" align="center" />
      <el-table-column prop="current_price" label="当前价格" width="100" align="center">
        <template #default="{ row }">
          <span>{{ row.current_price ? `¥${row.current_price}` : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" min-width="150" align="center" />
      <el-table-column label="操作" width="180" align="center" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </template>
  </PageTemplate>

  <!-- 物料对话框 -->
  <MaterialDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :material="currentMaterial"
    @success="handleDialogSuccess"
  />

  <!-- 导入对话框 -->
  <MaterialImport
    v-model="importDialogVisible"
    @success="handleImportSuccess"
  />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 导入通用页面模板
import PageTemplate from '@/components/common/PageTemplate.vue'
// 导入物料对话框组件
import MaterialDialog from './components/MaterialDialog.vue'
// 导入物料导入组件
import MaterialImport from './components/MaterialImport.vue'
// 导入API
import { getMaterialList, deleteMaterial } from '@/api/material'
import { getMaterialCategoryOptions } from '@/api/materialCategory'
// 引入xlsx库
import * as XLSX from 'xlsx'
import type { Material, MaterialCategory } from '@/types/bom'

// 定义本地类型
interface QueryMaterialParams {
  material_id?: string
  material_name?: string
  category_id?: string
  years?: string[]
  page?: number
  limit?: number
}

// 加载状态
const loading = ref(false)

// 表格数据
const tableData = ref<Material[]>([])

// 总数
const total = ref(0)

// 查询参数
const queryParams = reactive<QueryMaterialParams>({
  material_id: '',
  material_name: '',
  category_id: '',
  years: [],
  page: 1,
  limit: 10,
})

// 分类选项
const categoryOptions = ref<MaterialCategory[]>([])

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('新增物料')
const currentMaterial = ref<Material | null>(null)

// 导入相关
const importDialogVisible = ref(false)

// 页面加载时获取数据
onMounted(async () => {
  await Promise.all([fetchMaterialList(), loadCategoryOptions()])
})

// 获取物料列表
const fetchMaterialList = async () => {
  try {
    loading.value = true
    const res = await getMaterialList(queryParams)

    // 基于标准API响应格式 {code: 200, data: {materialList, total, page, limit}, message} 进行处理
    if (res && res.data && (res.data as any).materialList) {
      const materialList = (res.data as any).materialList

      // 为每个物料项添加分类名称
      const enrichedMaterialList = materialList.map((material: any) => {
        const category = categoryOptions.value.find(cat => cat.category_id === material.category_id)
        return {
          ...material,
          category_name: category ? category.category_name : material.category_id
        }
      })

      tableData.value = enrichedMaterialList
      total.value = (res.data as any).total || 0
    } else {
      console.error('API 返回数据格式不正确:', res)
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取物料列表失败:', error)
    ElMessage.error('获取物料列表失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 加载分类选项
const loadCategoryOptions = async () => {
  try {
    const res = await getMaterialCategoryOptions()
    if (res && res.data && Array.isArray(res.data)) {
      categoryOptions.value = res.data
    }
  } catch (error) {
    console.error('加载分类选项失败:', error)
  }
}

// 查询
const handleQuery = () => {
  queryParams.page = 1
  fetchMaterialList()
}

// 重置
const handleReset = () => {
  queryParams.material_id = ''
  queryParams.material_name = ''
  queryParams.category_id = ''
  queryParams.years = []
  queryParams.page = 1
  fetchMaterialList()
}

// 每页数量变化
const handleSizeChange = (val: number) => {
  queryParams.limit = val
  fetchMaterialList()
}

// 页码变化
const handleCurrentChange = (val: number) => {
  queryParams.page = val
  fetchMaterialList()
}

// 新增物料
const handleAdd = () => {
  currentMaterial.value = null
  dialogTitle.value = '新增物料'
  dialogVisible.value = true
}

// 编辑物料
const handleEdit = (row: Material) => {
  currentMaterial.value = { ...row }
  dialogTitle.value = '编辑物料'
  dialogVisible.value = true
}

// 删除物料
const handleDelete = async (row: Material) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除物料 "${row.material_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    if (row._id) {
      await deleteMaterial(row._id)
      ElMessage.success('删除成功')
      fetchMaterialList()
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除物料失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 导入物料
const handleImport = () => {
  importDialogVisible.value = true
}

// 导出数据
const handleExport = () => {
  if (tableData.value.length === 0) {
    ElMessage.warning('暂无数据可导出')
    return
  }

  // 准备导出数据
  const exportData = tableData.value.map(item => ({
    物料编号: item.material_id,
    物料名称: item.material_name,
    分类: item.category_name || '',
    规格型号: item.specification || '',
    单位: item.unit,
    当前价格: item.current_price || 0,
    描述: item.description || '',
  }))

  // 创建工作簿
  const ws = XLSX.utils.json_to_sheet(exportData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, '物料信息')

  // 导出文件
  const fileName = `物料信息_${new Date().toISOString().slice(0, 10)}.xlsx`
  XLSX.writeFile(wb, fileName)

  ElMessage.success('导出成功')
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchMaterialList()
}

// 导入成功回调
const handleImportSuccess = () => {
  fetchMaterialList()
}
</script>

<style scoped>
/* 页面样式 */
</style>
