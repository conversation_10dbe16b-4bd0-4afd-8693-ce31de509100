<template>
  <PageTemplate
    :queryParams="queryParams"
    :tableData="tableData"
    :total="total"
    :loading="loading"
    :tableHeight="650"
    @query="handleQuery"
    @reset="handleReset"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  >
    <!-- 查询区域左侧 -->
    <template #query-form-left>
      <el-form-item label="所属年份" prop="years">
        <el-select
          v-model="queryParams.years"
          placeholder="选择年份"
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          style="width: 180px"
          @change="handleYearChange"
        >
          <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
        </el-select>
      </el-form-item>
      <el-form-item label="供应商" prop="suppliers">
        <el-select
          v-model="queryParams.suppliers"
          placeholder="选择供应商"
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          filterable
          style="width: 220px"
          @change="handleSupplierChange"
        >
          <el-option
            v-for="supplier in supplierOptions"
            :key="supplier"
            :label="supplier"
            :value="supplier"
          />
        </el-select>
      </el-form-item>
    </template>

    <!-- 查询区域右侧 -->
    <template #query-form-right>
      <el-button type="primary" @click="handleAdd">新增入库</el-button>
    </template>

    <!-- 表格列 -->
    <template #table-columns>
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="fabric_warehouse_year" label="所属年份" width="100" align="center" />
      <el-table-column label="入库日期" width="120" align="center">
        <template #default="scope">
          {{ scope.row.date_in ? new Date(scope.row.date_in).toISOString().split('T')[0] : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="supplier" label="供应商" width="150" align="center" />
      <el-table-column prop="remark" label="备注" show-overflow-tooltip align="center" />
      <el-table-column fixed="right" label="操作" width="150" align="center">
        <template #default="{ row }">
          <el-button link type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button link type="danger" size="small" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </template>

    <!-- 额外内容插槽 -->
    <template #extra-content>
      <!-- 入库表单对话框 -->
      <el-dialog
        v-model="dialogVisible"
        width="50%"
        top="10vh"
        @closed="handleClose"
        :close-on-click-modal="false"
      >
        <el-form ref="formRef" :model="form" :rules="formRules" label-width="100px">
          <div class="form-row">
            <el-form-item label="入库日期" prop="stockInDate" class="form-item">
              <el-date-picker
                v-model="form.stockInDate"
                type="date"
                placeholder="请选择入库日期"
                value-format="x"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="所属年份" prop="year" class="form-item">
              <el-input-number
                v-model="yearValue"
                controls-position="right"
                @change="handleChangeYear"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="供应商" prop="supplier" class="form-item">
              <el-select
                v-model="form.supplier"
                placeholder="供应商"
                filterable
                allow-create
                style="width: 100%"
              >
                <el-option
                  v-for="supplier in supplierOptions"
                  :key="supplier"
                  :label="supplier"
                  :value="supplier"
                />
              </el-select>
            </el-form-item>
          </div>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" placeholder="请输入备注" />
          </el-form-item>
          <el-form-item label="入库编码" prop="code">
            <el-input disabled v-model="form.code" />
          </el-form-item>
        </el-form>
        <div class="button-row">
          <el-upload
            class="upload-demo"
            action=""
            :show-file-list="false"
            :auto-upload="false"
            :on-change="uploadChange"
          >
            <el-button type="primary">点击上传</el-button>
          </el-upload>
          <el-button type="primary" @click="getFabricInfo">补齐信息</el-button>
        </div>
        <el-table
          class="detail-table"
          @cell-dblclick="tableEdit"
          :data="form.details"
          table-layout="auto"
          size="default"
          style="font-size: 16px; margin-top: 20px"
          max-height="400px"
          border
          :header-cell-style="{ 'text-align': 'center' }"
          :cell-style="{ 'text-align': 'center' }"
          show-summary
          sum-text="合计"
        >
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="fabricId" label="布料编码" width="150" />
          <el-table-column prop="fabricName" label="布料名称" width="150" />
          <el-table-column prop="category" label="布料分类" width="210" />
          <el-table-column prop="quantity" label="米数" width="150" />
        </el-table>
        <template #footer>
          <el-button type="danger" v-if="dialogType === 'edit'" @dblclick="handleDelete"
            >双击删除</el-button
          >
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </template>
      </el-dialog>
    </template>
  </PageTemplate>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import * as XLSX from 'xlsx'
// 导入通用页面模板
import PageTemplate from '@/components/common/PageTemplate.vue'
import {
  getFabricStockInList,
  createFabricStockIn,
  updateFabricStockIn,
  deleteFabricStockIn,
  getFabricStockInYearOptions,
  getFabricStockInSupplierOptions,
} from '@/api/fabricStockIn'
import { getFabricList, updateFabricQuantityBatch, getFabricSupplierOptions } from '@/api/fabric'
import {
  getFabricWarehouseDetailsByWarehouseId,
  createFabricWarehouseDetailBatch,
  deleteFabricWarehouseDetail,
} from '@/api/fabricWarehouseDetail'
// 导入类型定义
import type {
  FabricWarehouse,
  FabricWarehouseDetail,
  FabricStockInDetail,
  QueryParams,
  FormData,
  SubmitDetailData,
} from '@/types/fabricWarehouse'

// 加载状态
const loading = ref(false)

// 表格数据
const tableData = ref<FabricWarehouse[]>([])
const total = ref(0)

// 年份值
const yearValue = ref(new Date().getFullYear())

// 查询参数
const queryParams = reactive<QueryParams>({
  years: [],
  suppliers: [],
  page: 1,
  limit: 10,
})

// 查询表单
const queryFormRef = ref<FormInstance>()

// 添加/编辑表单
const formRef = ref<FormInstance>()
const form = reactive<FormData>({
  code: '',
  year: '',
  stockInDate: new Date().getTime(),
  supplier: '',
  remark: '',
  details: [],
})

// 对话框状态
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const currentId = ref<string | null>(null)

// 下拉选项
const yearOptions = ref<string[]>([])
const supplierOptions = ref<string[]>([])

// 初始化加载方法

/**
 * 加载初始选项数据
 */
const loadInitialOptions = async (): Promise<void> => {
  try {
    // 获取年份选项
    const yearResponse = await getFabricStockInYearOptions(queryParams.suppliers.join(','))
    // 将年份选项按照倒序排列
    const years = yearResponse.data || []
    yearOptions.value = years.sort((a: string, b: string) => {
      // 提取年份数字进行比较
      const yearA = parseInt(a.match(/\d+/)?.[0] || '0')
      const yearB = parseInt(b.match(/\d+/)?.[0] || '0')
      return yearB - yearA // 倒序排列
    })

    // 获取供应商选项
    const supplierResponse = await getFabricStockInSupplierOptions(queryParams.years.join(','))
    supplierOptions.value = supplierResponse.data || []
  } catch (error) {
    console.error('加载选项数据失败', error)
    const errorMessage = error instanceof Error ? error.message : '加载选项数据失败'
    ElMessage.error(errorMessage)
  }
}

/**
 * 年份选择器值变化时的处理函数
 */
const handleYearChange = async (): Promise<void> => {
  try {
    // 根据选中的年份获取对应的供应商选项
    const response = await getFabricStockInSupplierOptions(queryParams.years.join(','))
    supplierOptions.value = response.data || []
  } catch (error) {
    console.error('获取供应商选项失败:', error)
    const errorMessage = error instanceof Error ? error.message : '获取供应商选项失败'
    ElMessage.error(errorMessage)
  } finally {
    // 无论成功失败都执行查询
    handleQuery()
  }
}

/**
 * 供应商选择器值变化时的处理函数
 */
const handleSupplierChange = async (): Promise<void> => {
  try {
    // 根据选中的供应商获取对应的年份选项
    const response = await getFabricStockInYearOptions(queryParams.suppliers.join(','))
    // 将年份选项按照倒序排列
    const years = response.data || []
    yearOptions.value = years.sort((a: string, b: string) => {
      // 提取年份数字进行比较
      const yearA = parseInt(a.match(/\d+/)?.[0] || '0')
      const yearB = parseInt(b.match(/\d+/)?.[0] || '0')
      return yearB - yearA // 倒序排列
    })
  } catch (error) {
    console.error('获取年份选项失败:', error)
    const errorMessage = error instanceof Error ? error.message : '获取年份选项失败'
    ElMessage.error(errorMessage)
  } finally {
    // 无论成功失败都执行查询
    handleQuery()
  }
}

// 初始化
onMounted(async () => {
  // 加载所有选项数据
  await loadInitialOptions()

  // 加载初始数据和布料选项
  // 使用handleQuery函数来确保数据按照正确的格式请求
  handleQuery()
})

/**
 * 加载数据
 * @param params 查询参数
 */
const loadData = async (params: Record<string, any> = {}): Promise<void> => {
  loading.value = true
  try {
    const response = await getFabricStockInList(params)
    if (response?.data) {
      tableData.value = response.data.data || []
      total.value = response.data.total || 0
    } else {
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('加载数据失败', error)
    // 使用更安全的错误处理
    const errorMessage = error instanceof Error ? error.message : '加载数据失败'
    ElMessage.error(errorMessage)
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

/**
 * 构建查询参数
 * @returns 格式化后的查询参数
 */
const buildQueryParams = (): Record<string, any> => {
  const params: Record<string, any> = {
    page: queryParams.page,
    limit: queryParams.limit,
    // 后端已经默认按date_in(入库日期)倒序排列
  }

  // 处理年份参数
  if (queryParams.years.length > 0) {
    if (queryParams.years.length === 1) {
      params.fabric_warehouse_year = queryParams.years[0]
    } else {
      params.fabric_warehouse_years = queryParams.years
    }
  }

  // 处理供应商参数
  if (queryParams.suppliers.length > 0) {
    if (queryParams.suppliers.length === 1) {
      params.supplier = queryParams.suppliers[0]
    } else {
      params.suppliers = queryParams.suppliers
    }
  }

  return params
}

/**
 * 执行查询
 */
const handleQuery = (): void => {
  queryParams.page = 1 // 重置到第一页
  const params = buildQueryParams()
  loadData(params)
}

/**
 * 重置查询条件
 */
const handleReset = async () => {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields()
  }

  // 重置查询参数
  queryParams.page = 1
  queryParams.years = []
  queryParams.suppliers = []

  // 重新加载所有选项
  await loadInitialOptions()

  // 重新加载数据
  handleQuery()
}

/**
 * 分页大小变化处理
 * @param val 新的每页数量
 */
const handleSizeChange = (val: number): void => {
  queryParams.limit = val
  queryParams.page = 1 // 重置页码
  const params = buildQueryParams()
  loadData(params)
}

/**
 * 分页页码变化处理
 * @param val 新的页码
 */
const handleCurrentChange = (val: number): void => {
  queryParams.page = val
  const params = buildQueryParams()
  loadData(params)
}

/**
 * 根据年份加载供应商选项
 * @param year 年份
 */
const loadSuppliersByYear = async (year: string): Promise<void> => {
  try {
    // 调用API获取供应商选项
    const response = await getFabricSupplierOptions(year)
    if (response && response.data) {
      supplierOptions.value = response.data || []
      console.log(`根据年份 ${year} 加载了 ${supplierOptions.value.length} 个供应商选项`)
    } else {
      supplierOptions.value = []
    }
  } catch (error) {
    console.error('加载供应商选项失败', error)
    ElMessage.error('加载供应商选项失败')
    supplierOptions.value = []
  }
}

/**
 * 打开新增对话框
 */
const handleAdd = async (): Promise<void> => {
  dialogType.value = 'add'
  resetForm()
  // 生成入库编码
  form.code = 'BLRK' + new Date().getTime()
  form.year = `${yearValue.value}年`
  form.stockInDate = new Date().getTime()

  // 根据选择的年份加载供应商选项
  await loadSuppliersByYear(form.year)

  dialogVisible.value = true
}

/**
 * 打开编辑对话框
 * @param row 要编辑的行数据
 */
const handleEdit = async (row: FabricWarehouse): Promise<void> => {
  dialogType.value = 'edit'
  currentId.value = row._id

  // 重置表单字段
  if (formRef.value) {
    formRef.value.resetFields()
  }

  // 设置表单值
  form.code = row.fabric_warehouse_id
  form.year = row.fabric_warehouse_year
  form.stockInDate = new Date(row.date_in).getTime()
  form.supplier = row.supplier
  form.remark = row.remark || ''
  form.details = []

  // 设置年份值
  const yearMatch = form.year.match(/\d+/)
  if (yearMatch) {
    yearValue.value = parseInt(yearMatch[0])
  }

  // 根据年份加载供应商选项
  await loadSuppliersByYear(form.year)

  dialogVisible.value = true

  // 从fabric-warehouse-detail API获取入库明细数据
  if (currentId.value) {
    try {
      // 使用封装的 API 获取入库明细数据
      const response = await getFabricWarehouseDetailsByWarehouseId(currentId.value)
      console.log('获取入库明细响应:', response)

      // 处理响应数据，现在已经确保是数组
      const detailData = response

      if (detailData && Array.isArray(detailData)) {
        console.log('入库明细数据详情:', JSON.stringify(detailData))
        // 将后端明细数据转换为前端表单所需的格式
        form.details = detailData.map((item: FabricWarehouseDetail): FabricStockInDetail => {
          console.log('处理明细项:', item)
          return {
            fabricId: item.fabric_id,
            fabricName: item.fabric_name,
            category: item.classification || '',
            quantity: item.meter,
            remark: item.remark || '',
            fabricCode: item.fabric_id, // 使用fabric_id作为fabricCode
          }
        })
      } else {
        form.details = []
        ElMessage.warning('未找到入库明细数据')
      }
    } catch (error) {
      console.error('获取入库明细数据失败', error)
      const errorMessage =
        error instanceof Error ? error.message : '获取入库明细数据失败，请检查网络连接'
      ElMessage.warning(errorMessage)
    }
  }
}

/**
 * 重置表单
 */
const resetForm = (): void => {
  // 重置表单验证
  if (formRef.value) {
    formRef.value.resetFields()
  }

  // 重置表单数据到初始状态
  form.code = ''
  form.year = ''
  form.stockInDate = new Date().getTime()
  form.supplier = ''
  form.remark = ''
  form.details = []

  // 重置年份值为当前年份
  yearValue.value = new Date().getFullYear()

  // 清除当前编辑的ID
  currentId.value = null
}

/**
 * 年份输入框值变化时更新表单年份和供应商选项
 */
const handleChangeYear = async (): Promise<void> => {
  // 将年份数字转换为带年字的格式
  form.year = `${yearValue.value}年`

  // 根据新的年份加载供应商选项
  await loadSuppliersByYear(form.year)

  // 清空当前选中的供应商
  form.supplier = ''
}

/**
 * 表格单元格编辑
 * @param row 行数据
 * @param column 列定义
 * @param cell 单元格元素
 * @param event 事件对象
 */
const tableEdit = (
  row: FabricStockInDetail,
  column: { label: string; property: string },
  cell: HTMLElement,
  event: MouseEvent
): void => {
  // 如果列有标签，允许编辑
  // 可以根据需要限制只能编辑特定列：如 column.label === '米数'
  if (column.label) {
    // 获取当前单元格的值
    const target = event.target as HTMLElement
    const beforeVal = target.textContent || ''

    // 清空单元格内容
    target.innerHTML = ''

    // 创建输入框
    const inputHtml = `<div class='cell'>
      <div class='el-input'>
        <input type='text' placeholder='请输入内容' class='el-input__inner'>
      </div>
    </div>`

    cell.innerHTML = inputHtml

    // 获取输入框元素
    const cellInput = cell.querySelector('input.el-input__inner') as HTMLInputElement
    if (cellInput) {
      cellInput.value = beforeVal
      cellInput.focus() // 自动聚焦

      // 失去焦点时更新数据
      cellInput.onblur = () => {
        const newValue = cellInput.value
        cell.innerHTML = `<div class='cell'>${newValue}</div>`

        // 更新行数据
        if (column.property in row) {
          switch (column.property) {
            case 'quantity':
              row[column.property] = Number(newValue) || 0
              break
            case 'fabricId':
            case 'fabricName':
            case 'category':
            case 'remark':
            case 'fabricCode':
              row[column.property] = String(newValue)
              break
          }
        }
      }
    }
  }
}

/**
 * 处理文件上传变化
 * @param file 上传的文件对象
 */
interface UploadFile {
  raw: File
}

const uploadChange = async (file: UploadFile): Promise<void> => {
  form.details = []
  try {
    const reader = new FileReader()

    reader.onload = (e: ProgressEvent<FileReader>) => {
      try {
        if (!e.target?.result) {
          throw new Error('文件读取失败')
        }

        // 解析Excel数据
        const data = new Uint8Array(e.target.result as ArrayBuffer)
        const workBook = XLSX.read(data, { type: 'array', cellDates: true })

        // 获取工作表
        let sheetName = workBook.SheetNames[0]
        if (workBook.SheetNames.indexOf('布料入库') !== -1) {
          sheetName = '布料入库'
        }

        const workSheet = workBook.Sheets[sheetName]
        const excelData = XLSX.utils.sheet_to_json(workSheet) as Record<string, any>[]

        // 转换数据结构
        form.details = excelData.map(
          (item): FabricStockInDetail => ({
            fabricId: item.fabric_id || item.fabricId || '',
            fabricName: item.fabric_name || item.fabricName || '',
            category: item.classification || item.category || '',
            quantity: Number(item.meter || item.quantity || 0),
            remark: item.remark || '',
          })
        )

        ElMessage.success('Excel解析成功')
      } catch (error) {
        console.error('解析Excel失败', error)
        const errorMessage =
          error instanceof Error ? error.message : '解析Excel文件失败，请检查文件格式'
        ElMessage.error(errorMessage)
      }
    }

    reader.onerror = () => {
      ElMessage.error('读取文件失败')
    }

    // 读取文件
    reader.readAsArrayBuffer(file.raw)
  } catch (error) {
    console.error('上传Excel失败', error)
    const errorMessage = error instanceof Error ? error.message : '上传Excel失败'
    ElMessage.error(errorMessage)
  }
}

/**
 * 补齐布料信息
 * 根据当前选择的年份和供应商获取布料信息，并填充到表格中
 */
const getFabricInfo = async (): Promise<void> => {
  // 检查年份和供应商是否已填写
  if (!form.year) {
    ElMessage.error('请先选择所属年份')
    return
  }
  if (!form.supplier) {
    ElMessage.error('请先选择供应商')
    return
  }

  try {
    // 构建查询参数
    const params = {
      supplier: form.supplier,
      fabric_year: form.year,
      page: 1,
      limit: 1000, // 设置较大的限制数，确保获取到所有数据
    }

    // 获取布料列表
    const response = await getFabricList(params)
    const fabrics = response?.data?.fabricList || []
    console.log('获取到的布料信息:', fabrics)

    if (!fabrics || fabrics.length === 0) {
      ElMessage.warning('未找到符合条件的布料信息')
      return
    }

    // 更新布料信息
    const notFoundFabrics: string[] = [] // 存储未找到的布料名称

    form.details.forEach((item: FabricStockInDetail) => {
      // 查找匹配的布料
      const matchFabric = fabrics.find((fabric: any) => fabric.fabric_name === item.fabricName)

      if (matchFabric) {
        item.fabricId = matchFabric.fabric_id // 填充布料ID
        item.fabricCode = matchFabric.fabric_id // 填充布料编码
        item.category = matchFabric.classification // 使用classification作为分类
      } else {
        // 记录未找到的布料名称
        notFoundFabrics.push(item.fabricName)
      }
    })

    // 一次性提示未找到的布料
    if (notFoundFabrics.length > 0) {
      if (notFoundFabrics.length <= 2) {
        // 如果未找到的布料较少，显示具体名称
        ElMessage.warning(`未找到布料: ${notFoundFabrics.join(', ')} 的信息`)
      } else {
        // 如果未找到的布料较多，只显示数量
        ElMessage.warning(`有 ${notFoundFabrics.length} 个布料未找到匹配信息`)
      }
    }

    // 生成备注信息
    let remarkText = ''
    // 创建一个Map来统计每个分类的数量
    const categoryMap = new Map<string, number>()
    form.details.forEach((detail: FabricStockInDetail) => {
      if (detail.category) {
        const currentQuantity = categoryMap.get(detail.category) || 0
        categoryMap.set(detail.category, currentQuantity + (detail.quantity || 0))
      }
    })

    // 生成备注文本
    categoryMap.forEach((quantity, category) => {
      if (remarkText) {
        remarkText += ' | '
      }
      remarkText += `${category}: ${Math.floor(quantity)}`
    })

    // 更新备注字段
    form.remark = remarkText

    // 如果没有入库编码，生成一个
    if (!form.code) {
      form.code = 'FSIN' + new Date().getTime()
    }

    ElMessage.success('布料信息补齐完成')
  } catch (error) {
    console.error('补齐布料信息失败', error)
    ElMessage.error('补齐信息失败，请检查网络连接')
  }
}

/**
 * 弹窗关闭处理
 */
const handleClose = (): void => {
  dialogVisible.value = false
  resetForm()
}

/**
 * 表单验证规则
 */
const formRules = {
  stockInDate: [{ required: true, message: '请选择入库日期', trigger: 'change' }],
  year: [{ required: true, message: '请输入所属年份', trigger: 'blur' }],
  supplier: [{ required: true, message: '请输入供应商', trigger: 'blur' }],
  code: [{ required: true, message: '请输入入库编码', trigger: 'blur' }],
}

/**
 * 提交表单
 */
const handleSubmit = async (): Promise<void> => {
  if (!formRef.value) return

  try {
    // 表单验证
    await formRef.value.validate()

    // 验证明细数据
    if (!form.details || form.details.length === 0) {
      ElMessage.error('请上传或添加入库明细')
      return
    }

    // 验证入库明细数据是否完整
    const incompleteDetails = form.details.filter(
      (detail: FabricStockInDetail) =>
        !detail.fabricId ||
        !detail.fabricCode ||
        !detail.fabricName ||
        !detail.category ||
        !detail.quantity
    )

    if (incompleteDetails.length > 0) {
      // 找出哪些字段缺失
      const missingFields = new Set<string>()
      incompleteDetails.forEach((detail: FabricStockInDetail) => {
        if (!detail.fabricId) missingFields.add('布料ID')
        if (!detail.fabricCode) missingFields.add('布料编码')
        if (!detail.fabricName) missingFields.add('布料名称')
        if (!detail.category) missingFields.add('布料分类')
        if (!detail.quantity) missingFields.add('米数')
      })

      // 构建提示信息
      const missingFieldsText = Array.from(missingFields).join('、')

      ElMessage.error(
        `入库明细数据不完整，缺少${missingFieldsText}信息，请点击"补齐信息"按钮完善数据`
      )
      return
    }

    // form.stockInDate 只有日期，加上本地的时间
    // 获取当前时间的时分秒
    const now = new Date()
    const hours = now.getHours()
    const minutes = now.getMinutes()
    const seconds = now.getSeconds()

    // 将时分秒添加到 form.stockInDate
    form.stockInDate = new Date(form.stockInDate).setHours(hours, minutes, seconds)

    // 准备提交数据
    const submitData = {
      fabric_warehouse_id: form.code,
      fabric_warehouse_year: form.year,
      date_in: new Date(form.stockInDate), // 使用Date对象
      supplier: form.supplier,
      remark: form.remark,
      // 不再在主表中包含明细数据，明细数据将在创建成功后单独提交
    }

    try {
      let warehouseId = currentId.value
      let originalDetails: any[] = []

      // 如果是编辑模式，先获取原有的明细数据
      if (dialogType.value === 'edit' && currentId.value) {
        // 获取原有明细数据
        const response = await getFabricWarehouseDetailsByWarehouseId(currentId.value)
        // 处理响应数据，现在已经确保是数组
        originalDetails = response
      }

      // 创建或更新主表记录
      if (dialogType.value === 'add') {
        const response = await createFabricStockIn(submitData)
        // 检查response是否存在并包含_id
        if (response && typeof response === 'object') {
          // 处理不同的响应结构
          if ('_id' in response) {
            warehouseId = response._id as string
          } else if (
            'data' in response &&
            response.data &&
            typeof response.data === 'object' &&
            '_id' in response.data
          ) {
            warehouseId = response.data._id as string
          } else {
            console.error('响应数据结构:', response)
            throw new Error('新创建的入库记录没有返回有效的ID')
          }
        } else {
          console.error('无效的响应:', response)
          throw new Error('新创建入库记录失败')
        }
        ElMessage.success('新增入库成功')
      } else if (dialogType.value === 'edit' && currentId.value) {
        await updateFabricStockIn(currentId.value, submitData)
        ElMessage.success('更新入库成功')
      }

      // 如果有仓库ID，则提交明细数据
      if (warehouseId) {
        // 将明细数据转换为后端需要的格式
        const detailsData = form.details.map(
          (detail: FabricStockInDetail): SubmitDetailData => ({
            fabric_id: detail.fabricId,
            fabric_name: detail.fabricName,
            classification: detail.category,
            meter: Number(detail.quantity),
            remark: detail.remark || '',
          })
        )

        // 先删除原有的明细数据（如果是编辑模式）
        if (dialogType.value === 'edit') {
          // 删除原有明细数据
          for (const detail of originalDetails) {
            await deleteFabricWarehouseDetail(detail._id)
          }
        }

        // 批量创建新的明细数据
        await createFabricWarehouseDetailBatch(warehouseId, detailsData)

        ElMessage.success('入库明细数据保存成功')

        // 使用新的 API 更新布料入库数量
        // 收集需要更新的布料ID
        const fabricIdsToUpdate = new Set<string>()

        // 收集所有需要更新的布料ID
        // 1. 如果是编辑模式，添加原有明细中的布料ID
        if (dialogType.value === 'edit' && originalDetails.length > 0) {
          for (const detail of originalDetails) {
            if (detail.fabric_id) {
              fabricIdsToUpdate.add(detail.fabric_id)
            }
          }
        }

        // 2. 添加新的明细中的布料ID
        for (const detail of detailsData) {
          if (detail.fabric_id) {
            fabricIdsToUpdate.add(detail.fabric_id)
          }
        }

        // 如果有需要更新的布料，调用批量更新API
        if (fabricIdsToUpdate.size > 0) {
          try {
            const fabricIds = Array.from(fabricIdsToUpdate)
            console.log(`将更新 ${fabricIds.length} 个布料的入库数量`)

            // 调用批量更新API
            const response = await updateFabricQuantityBatch(fabricIds)

            // 检查响应结构
            let updateResult
            if (response && typeof response === 'object') {
              // 处理不同的响应结构
              if ('success' in response) {
                updateResult = response
              } else if ('data' in response && response.data && typeof response.data === 'object') {
                updateResult = response.data
              }
            }

            // 记录响应结构以便调试
            console.log('更新布料入库数量响应:', updateResult)

            if (updateResult && updateResult.success) {
              console.log(`成功更新了 ${updateResult.totalUpdated} 个布料的入库数量`)
              ElMessage.success(`成功更新了 ${updateResult.totalUpdated} 个布料的入库数量`)
            } else if (updateResult && updateResult.errors && updateResult.errors.length > 0) {
              // 如果有错误信息，显示详细错误
              console.warn(`部分布料入库数量更新失败，失败数量: ${updateResult.totalFailed}`)
              console.warn('错误详情:', updateResult.errors)

              // 收集常见错误类型
              const errorTypes = new Set()
              updateResult.errors.forEach((err: any) => {
                if (err.error) {
                  // 提取错误消息中的关键部分
                  if (err.error.includes('不存在')) {
                    errorTypes.add('布料不存在')
                  } else {
                    errorTypes.add('其他错误')
                  }
                }
              })

              // 构建错误提示
              let errorMessage = `布料入库数量更新: 成功 ${updateResult.totalUpdated} 个，失败 ${updateResult.totalFailed} 个`
              if (errorTypes.size > 0) {
                errorMessage += `\n错误原因: ${Array.from(errorTypes).join(', ')}`
              }

              // 显示成功和失败的数量
              ElMessage.warning(errorMessage)
            } else {
              console.warn('布料入库数量更新失败，响应结构:', updateResult)
              ElMessage.warning('部分布料入库数量更新失败')
            }
          } catch (error) {
            console.error('更新布料入库数量失败', error)
            ElMessage.error('更新布料入库数量失败，请检查网络连接')
          }
        }
      }
    } catch (error) {
      console.error('提交数据失败', error)
      ElMessage.error('提交数据失败，请检查网络连接')
      return // 出错时不关闭对话框
    }

    dialogVisible.value = false
    // 重新加载数据和选项
    await Promise.all([handleQuery(), loadInitialOptions()])
  } catch (error) {
    console.error('提交失败', error)

    // 使用更安全的错误处理
    let errorMessage = '表单验证失败，请检查输入'

    if (error instanceof Error) {
      errorMessage = error.message
    } else if (typeof error === 'object' && error !== null) {
      // 尝试提取错误消息
      const anyError = error as any
      if (anyError.response?.data?.message) {
        errorMessage = anyError.response.data.message
      }
    }

    ElMessage.error(errorMessage)
  }
}

/**
 * 删除入库记录
 * @param row 要删除的行数据，如果从对话框中调用可以为空
 */
const handleDelete = (row?: FabricWarehouse): void => {
  let targetRow: { _id: string } | null = null

  // 如果是从对话框中双击删除按钮调用的
  if (dialogType.value === 'edit' && currentId.value) {
    targetRow = { _id: currentId.value }
  } else if (row?._id) {
    targetRow = { _id: row._id }
  }

  // 如果没有ID，则显示错误信息
  if (!targetRow) {
    ElMessage.error('未获取到入库单ID')
    return
  }

  ElMessageBox.confirm('确定要删除该入库记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await deleteFabricStockIn(targetRow!._id)
      ElMessage.success('删除成功')
      // 如果是从对话框中删除的，关闭对话框
      if (dialogVisible.value) {
        dialogVisible.value = false
      }
      // 重新加载数据和选项
      await Promise.all([handleQuery(), loadInitialOptions()])
    } catch (error) {
      console.error('删除失败', error)

      // 使用更安全的错误处理
      let errorMessage = '删除失败'

      if (error instanceof Error) {
        errorMessage = error.message
      } else if (typeof error === 'object' && error !== null) {
        const anyError = error as any
        if (anyError.response?.data?.message) {
          errorMessage = anyError.response.data.message
        }
      }

      ElMessage.error(errorMessage)
    }
  })
}
</script>

<style scoped>
/* 页面特定样式 */

/* 新增的详情表格样式 */
.detail-table {
  margin-top: 20px;
  width: 100%;
  height: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  border: 1px solid #e8e8e8;
}

.upload-demo {
  margin-right: 10px;
}

/* 响应式表单布局 */
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}

.form-item {
  padding: 0 10px;
  box-sizing: border-box;
  margin-bottom: 20px;
  width: 33.33%;
}

.button-row {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

/* 响应式布局 */
@media screen and (max-width: 1200px) {
  .form-item {
    width: 50%;
  }
}

@media screen and (max-width: 768px) {
  .form-item {
    width: 100%;
  }

  .el-dialog {
    width: 95% !important;
  }
}
</style>
