<template>
  <!-- 打印按钮 -->
  <el-button type="primary" plain icon="Printer" @click="handlePrint"> 打印 </el-button>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import type { Clothing } from '@/types/clothing'
import type { Fabric } from '@/api/fabric'

// 定义组件属性
const props = defineProps({
  fabricGroup: {
    type: Object,
    required: true,
  },
  clothingList: {
    type: Array as () => Clothing[],
    required: true,
  },
  fabricDetails: {
    type: Array as () => Fabric[],
    required: true,
  },
  colorPcsMap: {
    type: Object as () => Record<string, string>,
    required: true,
  },
})

// 处理打印按钮点击
const handlePrint = () => {
  // 直接打印，不显示预览
  const printWindow = window.open('', '_blank')
  if (!printWindow) {
    ElMessage.error('无法创建打印窗口，请检查浏览器是否阻止了弹出窗口')
    return
  }

  // 准备打印内容
  const printContent = preparePrintContent()

  // 创建完整的HTML内容
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>布料组服装用料信息</title>
      <meta charset="utf-8">
      <style>
        @page {
          margin-top: 300px; /* 上方留白 */
          margin-bottom: 20px;
          margin-left: 20px;
          margin-right: 20px;
        }
        body {
          font-family: Arial, sans-serif;
          color: #333;
        }
        .group-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
          font-size: 16px;
          border-bottom: 1px solid #ddd;
          padding-bottom: 10px;
          max-width: 900px;
        }
        .info-item {
          display: flex;
          align-items: center;
          flex: 1;
        }
        .info-label {
          font-weight: bold;
          margin-right: 8px;
          width: 90px;
          text-align: right;
          font-size: 16px;
        }
        .info-value {
          min-width: 100px;
          text-align: left;
          font-size: 16px;
        }
        .print-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
          font-size: 14px;
        }
        .print-table th, .print-table td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: center;
        }
        .print-table th {
          background-color: #f2f2f2;
          font-weight: bold;
        }
        .fabric-name {
          font-weight: bold;
          font-size: 15px;
        }
        .fabric-id {
          color: #666;
          font-size: 12px;
        }
        .clothing-info {
          text-align: center;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          padding: 5px;
        }
        .clothing-name {
          font-weight: bold;
          font-size: 17px;
          margin-bottom: 8px;
          text-align: center;
          width: 100%;
        }
        .clothing-details-container {
          display: flex;
          width: 100%;
          justify-content: space-between;
        }
        .clothing-left-details, .clothing-right-details {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 48%;
        }
        .clothing-id {
          color: #666;
          font-size: 12px;
          margin-bottom: 4px;
        }
        .clothing-size, .clothing-style, .clothing-pocket {
          font-size: 12px;
          color: #666;
        }
        .pcs-value {
          font-weight: bold;
          font-size: 19px;
        }
        .clipping-value {
          color: #f56c6c;
        }
        .order-value {
          color: #67c23a;
        }
        .print-footer {
          margin-top: 30px;
          font-size: 14px;
          color: #999;
          text-align: center;
        }
      </style>
    </head>
    <body>
      ${printContent}
    </body>
    </html>
  `

  // 使用blob和URL.createObjectURL创建一个临时URL
  const blob = new Blob([htmlContent], { type: 'text/html' })
  const url = URL.createObjectURL(blob)

  // 关闭之前的窗口
  printWindow.close()

  // 打开新窗口并加载内容
  const newWindow = window.open(url, '_blank')
  if (!newWindow) {
    ElMessage.error('无法创建打印窗口，请检查浏览器是否阻止了弹出窗口')
    return
  }

  // 等待内容加载完成后自动打印
  newWindow.onload = () => {
    setTimeout(() => {
      newWindow.print()
      // 打印完成后关闭窗口
      newWindow.onafterprint = () => {
        newWindow.close()
      }
    }, 500)
  }

  // 释放URL对象
  setTimeout(() => {
    URL.revokeObjectURL(url)
  }, 1000)
}

// 准备打印内容
const preparePrintContent = () => {
  // 获取布料组基本信息
  const groupInfo = {
    id: props.fabricGroup.fabric_group_id || '',
    supplier: props.fabricGroup.supplier || '',
    classification: Array.isArray(props.fabricGroup.group_classification)
      ? props.fabricGroup.group_classification.join(', ')
      : '',
  }

  // 对布料详情进行排序
  const sortedFabricDetails = [...props.fabricDetails].sort((a, b) => {
    return a.fabric_id.localeCompare(b.fabric_id)
  })

  // 对服装列表进行排序
  const sortedClothingList = [...props.clothingList].sort((a, b) => {
    // 如果a没有裁剪数量且订单数量为0，而b有，则a排在后面
    const aHasQuantity =
      (a.clipping_pcs && a.clipping_pcs > 0) || (a.order_quantity && a.order_quantity > 0)
    const bHasQuantity =
      (b.clipping_pcs && b.clipping_pcs > 0) || (b.order_quantity && b.order_quantity > 0)

    if (!aHasQuantity && bHasQuantity) return 1
    if (aHasQuantity && !bHasQuantity) return -1

    // 如果两者都有或都没有，则按照服装编码排序
    return a.clothing_id.localeCompare(b.clothing_id)
  })

  // 构建HTML内容 - 只包含表格内容，不包含完整的HTML文档
  let html = `
    <div class="group-info">
      <div class="info-item">
        <div class="info-label">布料组编码:</div>
        <div class="info-value">${groupInfo.id}</div>
      </div>
      <div class="info-item">
        <div class="info-label">供应商:</div>
        <div class="info-value">${groupInfo.supplier}</div>
      </div>
      <div class="info-item">
        <div class="info-label">布料分类:</div>
        <div class="info-value">${groupInfo.classification}</div>
      </div>
      <div class="info-item">
        <div class="info-value">${new Date().toLocaleDateString()}</div>
      </div>
    </div>

    <table class="print-table">
      <thead>
        <tr>
          <th style="width: 200px;">服装信息</th>
          ${sortedFabricDetails
            .map(
              (fabric) => `
            <th>
              <div class="fabric-name">${fabric.fabric_name || '未命名布料'}</div>
              <div class="fabric-id">${fabric.fabric_id}</div>
            </th>
          `
            )
            .join('')}
          <th>裁剪总数</th>
        </tr>
      </thead>
      <tbody>
        ${sortedClothingList
          .map(
            (clothing) => `
          <tr>
            <td class="clothing-info">
              <div class="clothing-name">${clothing.clothing_name || '未命名服装'}</div>
              <div class="clothing-details-container">
                <div class="clothing-left-details">
                  <div class="clothing-id">${clothing.clothing_id}</div>
                  <div class="clothing-size">${clothing.size || '-'}</div>
                </div>
                <div class="clothing-right-details">
                  <div class="clothing-pocket">${clothing.pocket_type || '-'}</div>
                  <div class="clothing-style">${clothing.style || '-'}</div>
                </div>
              </div>
            </td>
            ${sortedFabricDetails
              .map((fabric) => {
                const key = `${clothing.clothing_id}-${fabric.fabric_id}`
                const pcs = props.colorPcsMap[key] || ''
                return `<td class="pcs-value">${pcs}</td>`
              })
              .join('')}
            <td class="pcs-value ${clothing.clipping_pcs ? 'clipping-value' : 'order-value'}">
              ${clothing.clipping_pcs || clothing.order_quantity || ''}
            </td>
          </tr>
        `
          )
          .join('')}
      </tbody>
    </table>
  `

  return html
}
</script>
