<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogType === 'add' ? '新增布料组' : '编辑布料组'"
    :width="activeTab === 'basic' ? '40%' : '70%'"
    destroy-on-close
    class="fabric-group-dialog"
    align-center
    center
    top="5vh"
    @closed="handleDialogClosed"
  >
    <el-tabs v-model="activeTab" class="dialog-tabs">
      <el-tab-pane label="基础信息" name="basic">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="90px"
          :validate-on-rule-change="false"
          class="fabric-group-form"
        >
          <div class="form-section">
            <div class="form-card">
              <div class="form-grid">
                <div class="form-column">
                  <el-form-item label="所属年份" prop="fabric_group_year">
                    <el-input
                      v-model="form.fabric_group_year"
                      placeholder="请输入所属年份"
                      class="custom-input"
                    />
                  </el-form-item>
                  <el-form-item label="供应商" prop="supplier">
                    <el-input
                      v-model="form.supplier"
                      placeholder="请选择布料后自动获取"
                      class="custom-input"
                      disabled
                    />
                  </el-form-item>
                  <el-form-item label="结算状态" prop="settlement_state">
                    <el-select
                      v-model="form.settlement_state"
                      placeholder="请选择结算状态"
                      class="custom-input"
                    >
                      <el-option label="未结算" value="0" />
                      <el-option label="已结算" value="1" />
                    </el-select>
                  </el-form-item>
                </div>
                <div class="form-column">
                  <el-form-item label="布组编码" prop="fabric_group_id">
                    <el-input
                      v-model="form.fabric_group_id"
                      placeholder="请输入布料组编码"
                      class="custom-input"
                      :disabled="dialogType === 'edit'"
                    />
                  </el-form-item>
                  <el-form-item label="状态" prop="state">
                    <el-select v-model="form.state" placeholder="请选择状态" class="custom-input">
                      <el-option label="待排单" value="待排单" />
                      <el-option label="排单完成" value="排单完成" />
                      <el-option label="裁剪完成" value="裁剪完成" />
                      <el-option label="归档" value="归档" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="分类" prop="group_classification">
                    <div class="classification-display custom-input">
                      <el-tag
                        v-for="(classification, index) in form.group_classification"
                        :key="index"
                        class="classification-tag"
                        size="small"
                        type="info"
                      >
                        {{ classification }}
                      </el-tag>
                      <div
                        v-if="!form.group_classification || form.group_classification.length === 0"
                        class="empty-text"
                      >
                        请选择布料后自动获取
                      </div>
                    </div>
                  </el-form-item>
                </div>
              </div>

              <div class="form-divider"></div>

              <!-- 布料筛选和列表区域 -->
              <div class="fabric-filter-list-section">
                <div class="filter-section">
                  <div class="filter-row">
                    <div class="filter-item">
                      <el-select
                        v-model="fabricFilter.fabric_year"
                        placeholder="选择年份"
                        clearable
                        class="filter-select"
                        @change="handleFabricYearChange"
                      >
                        <el-option
                          v-for="year in fabricYearOptions"
                          :key="year"
                          :label="year"
                          :value="year"
                        />
                      </el-select>
                    </div>
                    <div class="filter-item">
                      <el-select
                        v-model="fabricFilter.supplier"
                        placeholder="选择供应商"
                        clearable
                        class="filter-select"
                        @change="handleFabricSupplierChange"
                      >
                        <el-option
                          v-for="supplier in fabricSupplierOptions"
                          :key="supplier"
                          :label="supplier"
                          :value="supplier"
                        />
                      </el-select>
                    </div>
                    <div class="filter-item">
                      <el-select
                        v-model="fabricFilter.classification"
                        placeholder="选择分类"
                        clearable
                        class="filter-select"
                        @change="handleFabricClassificationChange"
                      >
                        <el-option
                          v-for="classification in fabricClassificationOptions"
                          :key="classification"
                          :label="classification"
                          :value="classification"
                        />
                      </el-select>
                    </div>
                  </div>
                </div>

                <el-form-item label="布料列表" prop="fabrics" class="fabric-list-item">
                  <el-select
                    v-model="form.fabrics"
                    placeholder="请选择布料"
                    multiple
                    filterable
                    class="custom-input"
                    @change="handleFabricsChange"
                  >
                    <el-option
                      v-for="fabric in fabricOptions"
                      :key="fabric.fabric_id"
                      :label="fabric.fabric_name"
                      :value="fabric.fabric_id"
                    />
                  </el-select>
                </el-form-item>
              </div>

              <el-form-item label="调度信息" prop="scheduling">
                <el-input
                  v-model="form.scheduling"
                  placeholder="请输入调度信息"
                  type="textarea"
                  :rows="3"
                  class="custom-input"
                />
              </el-form-item>
            </div>
          </div>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="服装用料信息" name="matrix" :disabled="!canShowMatrix">
        <!-- 二维表区域 - 仅在有服装和布料数据时显示 -->
        <div v-if="clothingList.length > 0 && fabricDetails.length > 0" class="matrix-container">
          <div v-loading="loading" class="matrix-table">
            <!-- 表头 - 布料信息卡片 -->
            <div class="matrix-header">
              <div class="matrix-corner-cell">
                <div class="supplier-info">
                  <div class="supplier-value">{{ form.supplier }}</div>
                </div>
                <div class="classification-info">
                  <div class="classification-value">
                    {{ form?.group_classification?.join(', ') ?? '' }}
                  </div>
                </div>
              </div>
              <div
                v-for="(fabric, index) in sortedFabricDetails"
                :key="fabric.fabric_id"
                class="matrix-header-cell"
              >
                <div class="fabric-card">
                  <div class="fabric-index">{{ index + 1 }}#</div>
                  <div class="fabric-details">
                    <div class="fabric-name">{{ fabric.fabric_name }}</div>
                    <div class="fabric-id">{{ fabric.fabric_id }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 表格内容 -->
            <div class="matrix-body">
              <div
                v-for="clothing in sortedClothingList"
                :key="clothing.clothing_id"
                class="matrix-row"
              >
                <!-- 服装信息卡片 -->
                <div class="matrix-row-header">
                  <div class="clothing-card">
                    <!-- 上层：分为左右两块 -->
                    <div class="clothing-top-section">
                      <!-- 左侧：服装名称和编码 -->
                      <div class="clothing-info-top-left">
                        <div class="clothing-name">{{ clothing.clothing_name }}</div>
                        <div class="clothing-id">{{ clothing.clothing_id }}</div>
                      </div>

                      <!-- 右侧：裁剪件数或订单数量 -->
                      <div class="clothing-info-top-right">
                        <div class="clipping-pcs">
                          <template v-if="clothing.clipping_pcs && clothing.clipping_pcs > 0">
                            <span class="pcs-value clipping-value">{{
                              clothing.clipping_pcs
                            }}</span>
                          </template>
                          <template
                            v-else-if="clothing.order_quantity && clothing.order_quantity > 0"
                          >
                            <span class="pcs-value order-value">{{ clothing.order_quantity }}</span>
                          </template>
                          <!-- 当裁剪数量和订单数量都为0或不存在时，显示空白 -->
                          <template v-else>
                            <span class="pcs-value empty-value"></span>
                          </template>
                        </div>
                      </div>
                    </div>

                    <!-- 中层：分为左右两块 -->
                    <div class="clothing-middle-section">
                      <!-- 左侧：袖长和尺码 -->
                      <div class="clothing-info-left">
                        <div v-if="clothing.long_or_short_sleeve">
                          {{ clothing.long_or_short_sleeve }}
                        </div>
                        <div v-if="clothing.size">{{ clothing.size }}</div>
                      </div>

                      <!-- 右侧：款式和口袋 -->
                      <div class="clothing-info-right">
                        <div v-if="clothing.style">{{ clothing.style }}</div>
                        <div v-if="clothing.pocket_type">{{ clothing.pocket_type }}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 数据单元格 - 显示colorPcs数据，可编辑 -->
                <div
                  v-for="fabric in sortedFabricDetails"
                  :key="clothing.clothing_id + '-' + fabric.fabric_id"
                  class="matrix-cell"
                >
                  <el-input
                    v-model="colorPcsMap[clothing.clothing_id + '-' + fabric.fabric_id]"
                    type="text"
                    size="small"
                    :placeholder="''"
                    @input="handleColorPcsChange(clothing, fabric.fabric_id, $event)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="empty-matrix-message">
          <el-empty description="暂无服装用料数据" />
          <p v-if="dialogType === 'add'">请先保存基础信息，然后再添加服装用料数据</p>
          <p v-else-if="!form.fabrics || form.fabrics.length === 0">请先在基础信息中选择布料</p>
          <p v-else>未找到与该布料组关联的服装数据</p>
        </div>
      </el-tab-pane>
    </el-tabs>

    <div class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        v-if="dialogType === 'edit' && form.state && activeTab === 'matrix'"
        :type="form.state === '待排单' ? 'success' : form.state === '排单完成' ? 'warning' : 'info'"
        :disabled="form.state === '裁剪完成'"
        @click="handleStateAction"
      >
        {{ form.state === '待排单' ? '排单' : form.state === '排单完成' ? '裁剪完成' : '完成' }}
      </el-button>
      <FabricGroupPrint
        v-if="activeTab === 'matrix' && dialogType === 'edit'"
        :fabricGroup="form"
        :clothingList="clothingList"
        :fabricDetails="fabricDetails"
        :colorPcsMap="colorPcsMap"
      />
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits, watch, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import FabricGroupPrint from './FabricGroupPrint.vue'
import {
  getFabricList,
  getFabricDetail,
  getFabricYearOptions,
  getFabricSupplierOptions,
  getFabricCategoryOptions,
} from '@/api/fabric'
import { getClothingList, updateClothing } from '@/api/clothing'
import { getFabricGroupList, updateFabricGroup } from '@/api/fabricGroup'
import type { FabricGroupParams } from '@/api/fabricGroup'
import type { FabricGroup } from '@/types/fabricGroup'
import type { Clothing, ColorPcs } from '@/types/clothing'
import type { Fabric } from '@/api/fabric'

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String as () => 'add' | 'edit',
    default: 'add',
  },
  editData: {
    type: Object as () => FabricGroup | null,
    default: null,
  },
  classificationOptions: {
    type: Array as () => string[],
    default: () => [],
  },
})

// 定义组件事件
const emit = defineEmits(['update:visible', 'submit', 'cancel'])

// 对话框可见性
const dialogVisible = ref(props.visible)

// 对话框类型
const dialogType = ref<'add' | 'edit'>(props.type)

// 表单引用
const formRef = ref<FormInstance>()

// 布料选项
const fabricOptions = ref<Fabric[]>([])

// 布料筛选条件
const fabricFilter = reactive({
  fabric_year: '',
  supplier: '',
  classification: '',
})

// 布料筛选选项
const fabricYearOptions = ref<string[]>([])
const fabricSupplierOptions = ref<string[]>([])
const fabricClassificationOptions = ref<string[]>([])

// 当前激活的选项卡
const activeTab = ref('basic')

// 是否可以显示矩阵选项卡
const canShowMatrix = computed(() => {
  if (dialogType.value === 'add') {
    return false // 新增模式下不显示矩阵
  }
  return form.fabrics && form.fabrics.length > 0
})

// 表单数据
const form = reactive<FabricGroupParams>({
  fabric_group_id: '',
  fabric_group_year: '',
  supplier: '',
  group_classification: [],
  fabrics: [],
  settlement_state: '0',
  state: '待排单', // 默认为待排单
  scheduling: '',
})

// 服装列表数据
const clothingList = ref<Clothing[]>([])

// 布料详情数据
const fabricDetails = ref<Fabric[]>([])

// 加载状态
const loading = ref(false)

// 最大布料组编码
const maxFabricGroupId = ref('')

// 颜色件数映射表 - 用于编辑功能
const colorPcsMap = ref<Record<string, string>>({})

// 按布料编码排序的布料详情
const sortedFabricDetails = computed(() => {
  return [...fabricDetails.value].sort((a, b) => {
    return a.fabric_id.localeCompare(b.fabric_id)
  })
})

// 排序后的服装列表，将没有裁剪数量且订单数量为0的服装放在最下面
const sortedClothingList = computed(() => {
  return [...clothingList.value].sort((a, b) => {
    // 如果a没有裁剪数量且订单数量为0，而b有，则a排在后面
    const aHasQuantity =
      (a.clipping_pcs && a.clipping_pcs > 0) || (a.order_quantity && a.order_quantity > 0)
    const bHasQuantity =
      (b.clipping_pcs && b.clipping_pcs > 0) || (b.order_quantity && b.order_quantity > 0)

    if (!aHasQuantity && bHasQuantity) return 1
    if (aHasQuantity && !bHasQuantity) return -1

    // 如果两者都有或都没有，则按照服装编码排序
    return a.clothing_id.localeCompare(b.clothing_id)
  })
})

// 这里不再需要tableData计算属性，因为我们使用colorPcsMap来管理数据

// 表单验证规则
const rules = {
  fabric_group_id: [{ required: true, message: '请输入布料组编码', trigger: 'blur' }],
  fabric_group_year: [{ required: true, message: '请输入所属年份', trigger: 'blur' }],
  supplier: [{ required: true, message: '请输入供应商', trigger: 'blur' }],
}

// 监听 visible 属性变化
watch(
  () => props.visible,
  (newValue) => {
    dialogVisible.value = newValue
  }
)

// 监听 dialogVisible 变化，同步回父组件
watch(dialogVisible, async (newValue) => {
  emit('update:visible', newValue)

  // 当对话框打开时
  if (newValue) {
    // 加载布料筛选选项
    await loadFabricYearOptions()
    await loadFabricSupplierOptions()
    await loadFabricClassificationOptions()

    // 如果是新增模式，获取最大布料组编码
    if (dialogType.value === 'add') {
      getMaxFabricGroupId()
    }
  }
})

// 监听选项卡变化，确保弹窗位置保持稳定
watch(activeTab, () => {
  // 使用nextTick确保DOM更新后再调整位置
  nextTick(() => {
    // 获取弹窗元素
    const dialogEl = document.querySelector('.fabric-group-dialog .el-dialog')
    if (dialogEl) {
      // 记录当前滚动位置
      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
      // 保持滚动位置不变
      window.scrollTo(0, scrollTop)
    }
  })
})

// 监听状态变化，当状态为"排单完成"时，自动切换到服装用料信息选项卡
watch(
  () => form.state,
  (newValue) => {
    if (newValue === '排单完成' && canShowMatrix.value) {
      console.log('状态变为排单完成，自动切换到服装用料信息选项卡')
      activeTab.value = 'matrix'
    }
  }
)

// 获取最大布料组编码
const getMaxFabricGroupId = async () => {
  try {
    // 查询所有布料组，按编码倒序排序，只取第一条
    const response = await getFabricGroupList({
      limit: 1,
      page: 1,
    })
    console.log('获取到的布料组数据：', response)

    if (response?.data && Array.isArray(response.data) && response.data.length > 0) {
      const latestGroup = response.data[0]
      const latestId = latestGroup.fabric_group_id
      form.fabric_group_year = latestGroup.fabric_group_year

      // 尝试提取编码中的数字部分
      const match = latestId.match(/(\d+)$/)
      if (match) {
        const numPart = parseInt(match[1])
        const prefix = latestId.substring(0, latestId.length - match[1].length)

        // 生成新编码：前缀 + (数字部分 + 1)，保持相同的位数
        const newNumPart = (numPart + 1).toString().padStart(match[1].length, '0')
        maxFabricGroupId.value = prefix + newNumPart

        // 自动填充到表单
        if (dialogType.value === 'add') {
          form.fabric_group_id = maxFabricGroupId.value
        }
      } else {
        // 如果无法提取数字部分，则简单地在末尾加1
        maxFabricGroupId.value = latestId + '1'

        // 自动填充到表单
        if (dialogType.value === 'add') {
          form.fabric_group_id = maxFabricGroupId.value
        }
      }

      console.log('生成的新布料组编码:', maxFabricGroupId.value)
    }
  } catch (error) {
    console.error('获取最大布料组编码失败', error)
  }
}

// 监听 type 属性变化
watch(
  () => props.type,
  (newValue) => {
    dialogType.value = newValue
  }
)

// 处理布料列表变更
const handleFabricsChange = async (newFabrics: string[]) => {
  console.log('布料列表变更:', newFabrics)

  // 根据选择的布料自动更新供应商和分类
  if (newFabrics && newFabrics.length > 0) {
    try {
      // 从API获取最新的布料详情，确保数据准确
      const fabricDetails = await Promise.all(
        newFabrics.map(async (fabricId) => {
          try {
            const response = await getFabricDetail(fabricId)
            console.log(`获取布料 ${fabricId} 详情成功`, response)
            return response
          } catch (error) {
            console.error(`获取布料 ${fabricId} 详情失败:`, error)
            return null
          }
        })
      )

      // 过滤掉获取失败的布料
      const validFabricDetails = fabricDetails.filter((detail) => detail !== null)

      if (validFabricDetails.length > 0) {
        // 提取供应商信息
        const suppliers = new Set<string>()
        validFabricDetails.forEach((fabric) => {
          if ((fabric as any).supplier) {
            suppliers.add((fabric as any).supplier)
          }
        })

        // 提取分类信息
        const classifications = new Set<string>()
        validFabricDetails.forEach((fabric) => {
          if ((fabric as any).classification) {
            // 分类可能是字符串或数组，需要分别处理
            if (Array.isArray((fabric as any).classification)) {
              ;(fabric as any).classification.forEach((cls: string) => {
                if (cls) classifications.add(cls)
              })
            } else {
              classifications.add((fabric as any).classification)
            }
          }
        })

        console.log('收集到的分类:', Array.from(classifications))

        // 更新表单中的供应商和分类
        // 无论是否有值，都更新供应商和分类，确保它们始终反映当前选择的布料
        form.supplier = suppliers.size > 0 ? Array.from(suppliers).join(', ') : ''

        // 确保分类是数组格式
        form.group_classification = Array.from(classifications)

        console.log('更新后的分类:', form.group_classification)
      }
    } catch (error) {
      console.error('处理布料变更时出错:', error)
      ElMessage.error('处理布料变更时出错')
    }
  } else {
    // 如果没有选择布料，清空供应商和分类
    form.supplier = ''
    form.group_classification = []
  }

  // 如果是编辑模式，更新布料详情和服装用料信息
  if (dialogType.value === 'edit') {
    // 加载布料详情
    if (newFabrics && newFabrics.length > 0) {
      await loadFabricDetails(newFabrics)

      // 初始化颜色件数映射表
      initColorPcsMap()
    } else {
      // 清空布料详情
      fabricDetails.value = []
      // 清空颜色件数映射表
      colorPcsMap.value = {}
    }
  }
}

// 监听 editData 属性变化
watch(
  () => props.editData,
  async (newValue) => {
    console.log('editData 变化:', newValue)
    if (newValue) {
      // 先重置表单，确保没有旧数据残留
      resetForm()

      // 填充表单数据
      Object.assign(form, {
        fabric_group_id: newValue.fabric_group_id,
        fabric_group_year: newValue.fabric_group_year,
        supplier: newValue.supplier,
        group_classification: Array.isArray(newValue.group_classification)
          ? newValue.group_classification
          : [],
        fabrics: newValue.fabrics || [],
        settlement_state: newValue.settlement_state || '0',
        state: newValue.state || '待排单', // 使用新的默认状态
        scheduling: newValue.scheduling || '',
      })
      // 如果是编辑模式，加载服装数据和布料详情
      if (dialogType.value === 'edit' && newValue.fabric_group_id) {
        // 加载服装数据
        await loadClothingData(newValue.fabric_group_id)

        // 加载布料详情
        if (newValue.fabrics && newValue.fabrics.length > 0) {
          await loadFabricDetails(newValue.fabrics)

          // 初始化颜色件数映射表
          initColorPcsMap()

          // 如果状态为"排单完成"，自动切换到服装用料信息选项卡
          if (form.state === '排单完成') {
            console.log('编辑状态为排单完成的布料组，自动切换到服装用料信息选项卡')
            nextTick(() => {
              activeTab.value = 'matrix'
            })
          }
        }
      }
    } else if (dialogType.value === 'add') {
      // 重置表单
      resetForm()
      // 清空服装和布料数据
      clothingList.value = []
      fabricDetails.value = []
    }
  }
)

// 加载布料年份选项
const loadFabricYearOptions = async (supplier?: string, classification?: string) => {
  try {
    const response = await getFabricYearOptions(supplier, classification)
    if (response?.data) {
      fabricYearOptions.value = response.data
    }
  } catch (error) {
    console.error('加载布料年份选项失败', error)
    ElMessage.error('加载布料年份选项失败')
  }
}

// 加载布料供应商选项
const loadFabricSupplierOptions = async (year?: string, classification?: string) => {
  try {
    const response = await getFabricSupplierOptions(year, classification)
    if (response?.data) {
      fabricSupplierOptions.value = response.data
    }
  } catch (error) {
    console.error('加载布料供应商选项失败', error)
    ElMessage.error('加载布料供应商选项失败')
  }
}

// 加载布料分类选项
const loadFabricClassificationOptions = async (year?: string, supplier?: string) => {
  try {
    const response = await getFabricCategoryOptions(year, supplier)
    if (response?.data) {
      fabricClassificationOptions.value = response.data
    }
  } catch (error) {
    console.error('加载布料分类选项失败', error)
    ElMessage.error('加载布料分类选项失败')
  }
}

// 处理布料年份变化
const handleFabricYearChange = async () => {
  // 更新供应商和分类选项
  await loadFabricSupplierOptions(fabricFilter.fabric_year, fabricFilter.classification)
  await loadFabricClassificationOptions(fabricFilter.fabric_year, fabricFilter.supplier)
}

// 处理布料供应商变化
const handleFabricSupplierChange = async () => {
  // 更新年份和分类选项
  await loadFabricYearOptions(fabricFilter.supplier, fabricFilter.classification)
  await loadFabricClassificationOptions(fabricFilter.fabric_year, fabricFilter.supplier)

  // 加载布料选项
  await loadFabricOptions()
}

// 处理布料分类变化
const handleFabricClassificationChange = async () => {
  // 更新年份和供应商选项
  await loadFabricYearOptions(fabricFilter.supplier, fabricFilter.classification)
  await loadFabricSupplierOptions(fabricFilter.fabric_year, fabricFilter.classification)

  // 加载布料选项
  await loadFabricOptions()
}

// 加载布料选项
const loadFabricOptions = async (year?: string, supplier?: string) => {
  try {
    const params = {
      fabric_year: year || fabricFilter.fabric_year,
      supplier: supplier || fabricFilter.supplier,
      classification: fabricFilter.classification,
      limit: 1000,
    }
    const response = await getFabricList(params)
    if (response?.data?.fabricList) {
      fabricOptions.value = response.data.fabricList
    }
    console.log('加载到的布料选项6666:', fabricOptions.value)
  } catch (error) {
    console.error('加载布料选项失败', error)
    ElMessage.error('加载布料选项失败')
  }
}

// 加载服装数据
const loadClothingData = async (fabricGroupId: string) => {
  if (!fabricGroupId) return

  loading.value = true
  try {
    const params = {
      fabric_group_id: fabricGroupId,
      limit: 1000,
    }
    const response = await getClothingList(params)
    console.log('获取到的服装数据:', response)
    if (response?.data) {
      clothingList.value = (response.data as any).clothingList || []
      console.log('加载到的服装数据:', clothingList.value)

      if (clothingList.value.length === 0) {
        ElMessage.info(`未找到与布料组 ${fabricGroupId} 关联的服装`)
      } else {
        ElMessage.success(`成功加载 ${clothingList.value.length} 件服装数据`)
      }
    } else {
      clothingList.value = []
      ElMessage.info(`未找到与布料组 ${fabricGroupId} 关联的服装`)
    }
  } catch (error) {
    console.error('加载服装数据失败', error)
    ElMessage.error('加载服装数据失败，请检查网络连接或联系管理员')
    clothingList.value = []
  } finally {
    loading.value = false
  }
}

// 加载布料详情数据
const loadFabricDetails = async (fabricIds: string[]) => {
  if (!fabricIds || fabricIds.length === 0) return

  loading.value = true
  fabricDetails.value = []

  try {
    // 创建一个简单的布料信息缓存，避免重复请求
    const fabricCache: Record<string, Fabric> = {}

    // 使用 Promise.allSettled 替代 Promise.all，这样即使部分请求失败也能继续处理
    const promises = fabricIds.map(async (fabricId) => {
      // 如果缓存中已有该布料信息，直接返回
      if (fabricCache[fabricId]) {
        return fabricCache[fabricId]
      }

      try {
        // 尝试从布料列表中查找布料信息
        const fabricList = await getFabricList({
          fabric_id: fabricId,
          limit: 1,
        })

        if (fabricList?.data?.fabricList && fabricList.data.fabricList.length > 0) {
          const fabric = fabricList.data.fabricList[0]
          fabricCache[fabricId] = fabric
          return fabric
        }

        // 如果列表中找不到，再尝试直接获取详情
        const response = await getFabricDetail(fabricId)
        if (response && response.data) {
          fabricCache[fabricId] = response.data
          return response.data
        }

        console.warn(`未找到布料 ${fabricId} 的详情`)
        // 创建一个基本的布料对象，避免UI显示问题
        return {
          _id: '',
          fabric_id: fabricId,
          fabric_name: `未知布料(${fabricId})`,
          fabric_year: '',
          supplier: '未知',
          classification: '未知',
          in_quantity: 0,
          order_quantity: 0,
        } as Fabric
      } catch (error) {
        console.error(`加载布料 ${fabricId} 详情失败`, error)
        // 创建一个基本的布料对象，避免UI显示问题
        return {
          _id: '',
          fabric_id: fabricId,
          fabric_name: `加载失败(${fabricId})`,
          fabric_year: '',
          supplier: '未知',
          classification: '未知',
          in_quantity: 0,
          order_quantity: 0,
        } as Fabric
      }
    })

    const results = await Promise.allSettled(promises)
    // 只保留成功的结果
    fabricDetails.value = results
      .filter((result): result is PromiseFulfilledResult<Fabric> => result.status === 'fulfilled')
      .map((result) => result.value)

    console.log('加载到的布料详情:', fabricDetails.value)

    if (fabricDetails.value.length === 0) {
      ElMessage.warning('未能加载任何布料详情')
    } else if (fabricDetails.value.length < fabricIds.length) {
      ElMessage.warning(
        `部分布料详情加载失败，已加载 ${fabricDetails.value.length}/${fabricIds.length}`
      )
    }
  } catch (error) {
    console.error('加载布料详情失败', error)
    ElMessage.error('加载布料详情失败')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    fabric_group_id: '',
    fabric_group_year: '',
    supplier: '',
    group_classification: [],
    fabrics: [],
    settlement_state: '0',
    state: '待排单', // 默认为待排单
    scheduling: '',
  })

  // 重置选项卡
  activeTab.value = 'basic'
}

// 处理对话框关闭
const handleDialogClosed = () => {
  // 重置表单验证
  formRef.value?.clearValidate()

  // 不再重置表单数据，让父组件控制数据的重置
  // 只清空布料筛选条件
  Object.assign(fabricFilter, {
    fabric_year: '',
    supplier: '',
    classification: '',
  })

  // 重置选项卡到基础信息
  activeTab.value = 'basic'
}

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
  emit('cancel')
}

// 初始化颜色件数映射表
const initColorPcsMap = () => {
  // 清空映射表
  colorPcsMap.value = {}

  // 遍历所有服装和布料，初始化映射表
  clothingList.value.forEach((clothing) => {
    sortedFabricDetails.value.forEach((fabric) => {
      const key = `${clothing.clothing_id}-${fabric.fabric_id}`
      let value = '0'

      // 如果有colorPcs数据，则使用已有的值
      if (clothing.colorPcs && clothing.colorPcs.length > 0) {
        const colorPcsItem = clothing.colorPcs.find((item) => item.fabric_id === fabric.fabric_id)
        if (colorPcsItem) {
          // 如果pcs是空字符串、null或'0'，则使用空字符串
          value = colorPcsItem.pcs === '0' ? '' : colorPcsItem.pcs || ''
        }
      } else {
        // 默认值设为空字符串而不是'0'
        value = ''
      }

      colorPcsMap.value[key] = value
    })
  })
}

// 处理颜色件数变更
const handleColorPcsChange = (clothing: Clothing, fabricId: string, value: string) => {
  // 如果输入的是0，立即转换为空字符串
  if (value === '0') {
    const key = `${clothing.clothing_id}-${fabricId}`
    colorPcsMap.value[key] = ''
    value = ''
  }

  // 确保服装有colorPcs数组
  if (!clothing.colorPcs) {
    clothing.colorPcs = []
  }

  // 查找是否已有该布料的记录
  const existingIndex = clothing.colorPcs.findIndex((item) => item.fabric_id === fabricId)

  // 处理空值和0值，确保UI显示一致
  const trimmedValue = value.trim()
  const processedValue = trimmedValue === '' || trimmedValue === '0' ? '0' : trimmedValue

  if (existingIndex >= 0) {
    // 如果已有记录，则更新
    clothing.colorPcs[existingIndex].pcs = processedValue
  } else {
    // 如果没有记录，则添加新记录
    const newColorPcs: ColorPcs = {
      fabric_id: fabricId,
      pcs: processedValue,
    }
    clothing.colorPcs.push(newColorPcs)
  }

  console.log(`更新服装 ${clothing.clothing_id} 的布料 ${fabricId} 件数为 ${processedValue}`)

  // 同步更新裁剪数量
  updateClippingPcs(clothing)
}

// 处理状态操作按钮点击
const handleStateAction = async () => {
  if (!props.editData?._id) {
    ElMessage.error('无法获取布料组ID')
    return
  }

  try {
    if (form.state === '待排单') {
      // 确认是否要排单
      await ElMessageBox.confirm('确定要将状态改为"排单完成"吗？', '确认操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })

      // 更新状态为"排单完成"
      form.state = '排单完成'

      // 如果服装信息有订单数量且布料信息有入库数量，按比例分配
      if (clothingList.value.length > 0 && fabricDetails.value.length > 0) {
        // 计算分配比例
        distributeOrderQuantityByRatio()
      }

      // 保存状态变更
      await updateFabricGroup(props.editData._id, { state: form.state })
      ElMessage.success('状态已更新为"排单完成"')

      // 自动切换到服装用料信息选项卡
      activeTab.value = 'matrix'
    } else if (form.state === '排单完成') {
      // 确认是否要完成裁剪
      await ElMessageBox.confirm('确定要将状态改为"裁剪完成"吗？', '确认操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })

      // 更新状态为"裁剪完成"
      form.state = '裁剪完成'

      // 保存状态变更
      await updateFabricGroup(props.editData._id, { state: form.state })
      ElMessage.success('状态已更新为"裁剪完成"')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('状态更新失败', error)
      ElMessage.error('状态更新失败')
    }
  }
}

// 按比例分配订单数量到colorPcs
const distributeOrderQuantityByRatio = () => {
  // 检查是否有服装和布料数据
  if (clothingList.value.length === 0 || fabricDetails.value.length === 0) {
    console.log('没有服装或布料数据，无法分配')
    return
  }

  // 计算所有布料的总入库数量
  const totalInQuantity = fabricDetails.value.reduce((sum, fabric) => {
    return sum + (fabric.in_quantity || 0)
  }, 0)

  if (totalInQuantity <= 0) {
    console.log('所有布料的总入库数量为0，无法按比例分配')
    ElMessage.warning('所有布料的总入库数量为0，无法按比例分配')
    return
  }

  console.log(`所有布料的总入库数量: ${totalInQuantity}`)

  // 遍历所有服装
  clothingList.value.forEach((clothing) => {
    // 只处理有订单数量的服装
    if (!clothing.order_quantity || clothing.order_quantity <= 0) {
      console.log(`服装 ${clothing.clothing_id} 没有订单数量，跳过`)
      return
    }

    const orderQuantity = clothing.order_quantity

    // 确保服装有colorPcs数组
    if (!clothing.colorPcs) {
      clothing.colorPcs = []
    }

    // 创建一个本地引用，确保TypeScript知道它已初始化
    const colorPcsArray = clothing.colorPcs as ColorPcs[]

    // 确定分配规则：根据尺码确定需要被整除的数
    const sizeInfo = clothing.size || ''
    const divisor = sizeInfo === '11XL-13XL' ? 3 : 10
    console.log(`服装 ${clothing.clothing_id} 尺码为 ${sizeInfo}，分配数量需要被 ${divisor} 整除`)

    // 遍历所有布料
    fabricDetails.value.forEach((fabric) => {
      // 只处理有入库数量的布料
      if (!fabric.in_quantity || fabric.in_quantity <= 0) {
        console.log(`布料 ${fabric.fabric_id} 没有入库数量，跳过`)
        return
      }

      // 计算该布料占总入库数量的比例
      const ratio = fabric.in_quantity / totalInQuantity

      // 按比例分配订单数量
      let allocatedQuantity = Math.round(orderQuantity * ratio)

      // 调整分配数量，使其能被divisor整除
      if (allocatedQuantity > 0) {
        // 四舍五入到最接近的能被divisor整除的数
        allocatedQuantity = Math.round(allocatedQuantity / divisor) * divisor
      }

      // 查找是否已有该布料的记录
      const existingIndex = colorPcsArray.findIndex((item) => item.fabric_id === fabric.fabric_id)

      if (existingIndex >= 0) {
        // 如果已有记录，则更新
        colorPcsArray[existingIndex].pcs = allocatedQuantity > 0 ? allocatedQuantity.toString() : ''
      } else {
        // 如果没有记录，则添加新记录
        const newColorPcs: ColorPcs = {
          fabric_id: fabric.fabric_id,
          pcs: allocatedQuantity > 0 ? allocatedQuantity.toString() : '',
        }
        colorPcsArray.push(newColorPcs)
      }

      // 更新UI显示
      const key = `${clothing.clothing_id}-${fabric.fabric_id}`
      colorPcsMap.value[key] = allocatedQuantity > 0 ? allocatedQuantity.toString() : ''

      console.log(
        `分配服装 ${clothing.clothing_id} 的布料 ${fabric.fabric_id} 件数为 ${allocatedQuantity}，比例: ${(ratio * 100).toFixed(2)}%`
      )
    })

    // 更新服装的裁剪数量为所有布料分配数量之和
    updateClippingPcs(clothing)
  })

  ElMessage.success('已根据入库数量比例自动分配订单数量')
}

// 更新服装的裁剪数量为所有布料分配数量之和
const updateClippingPcs = (clothing: Clothing) => {
  if (!clothing.colorPcs) {
    clothing.colorPcs = []
  }

  // 计算所有布料分配数量之和
  let totalPcs = 0
  clothing.colorPcs.forEach((item) => {
    if (item.pcs && item.pcs !== '') {
      totalPcs += parseInt(item.pcs, 10) || 0
    }
  })

  // 无论总数是否大于0，都更新裁剪数量
  // 这样当删除所有数据时，裁剪数量会正确更新为0
  clothing.clipping_pcs = totalPcs > 0 ? totalPcs : 0
  console.log(`更新服装 ${clothing.clothing_id} 的裁剪数量为 ${clothing.clipping_pcs}`)
}

// 处理提交
const handleSubmit = async () => {
  // 无论在哪个选项卡，都验证并提交保存布料组信息
  if (!formRef.value) return

  try {
    loading.value = true

    // 验证布料组基础信息
    const valid = await formRef.value.validate()
    if (!valid) {
      ElMessage.error('请填写必填项')
      loading.value = false
      return
    }

    // 创建一个Promise数组，用于收集所有需要执行的操作
    const savePromises = []

    // 1. 保存布料组信息
    if (dialogType.value === 'add') {
      // 新增模式 - 通过父组件提交
      savePromises.push(
        new Promise((resolve) => {
          emit('submit', {
            type: 'add',
            data: { ...form },
          })
          resolve(true)
        })
      )
    } else if (dialogType.value === 'edit' && props.editData?._id) {
      // 编辑模式 - 直接调用API更新
      savePromises.push(updateFabricGroup(props.editData._id, { ...form }))
    }

    // 2. 如果在服装用料信息选项卡，额外保存服装用量数据
    if (activeTab.value === 'matrix') {
      // 收集所有服装的colorPcs数据
      let hasValidData = false

      // 更新所有服装的裁剪数量
      for (const clothing of clothingList.value) {
        // 确保服装有colorPcs数组
        if (!clothing.colorPcs) {
          clothing.colorPcs = []
        }

        // 更新裁剪数量
        updateClippingPcs(clothing)

        // 只有当colorPcs有数据时才更新服装
        if (clothing.colorPcs && clothing.colorPcs.length > 0 && clothing._id) {
          hasValidData = true
          //clothing.colorPcs里的 pcs 转为 string
          clothing.colorPcs = clothing.colorPcs.map((item) => {
            return {
              ...item,
              pcs: item.pcs.toString(),
            }
          })

          // 创建更新数据对象
          const updateData = {
            colorPcs: clothing.colorPcs,
            clipping_pcs: clothing.clipping_pcs,
          }

          // 添加到更新队列
          savePromises.push(updateClothing(clothing._id, updateData))
        }
      }

      if (!hasValidData) {
        ElMessage.warning('没有服装用料数据需要保存，仅保存布料组信息')
      }
    }

    // 执行所有保存操作
    await Promise.all(savePromises)

    // 显示成功消息
    if (activeTab.value === 'matrix') {
      ElMessage.success('布料组信息和服装用料数据保存成功')
    } else {
      ElMessage.success('布料组信息保存成功')
    }

    // 关闭对话框
    dialogVisible.value = false

    // 通知父组件提交成功
    if (dialogType.value === 'edit') {
      emit('submit', {
        type: 'edit',
        data: { ...form },
      })
    }
  } catch (error) {
    console.error('保存数据失败', error)
    ElMessage.error('保存数据失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.fabric-group-dialog {
  /* 确保弹窗在切换选项卡时保持位置 */
  transition: width 0.3s ease;
}

.fabric-group-dialog :deep(.el-dialog__body) {
  padding: 20px;
  overflow-y: auto;
  max-height: 80vh;
}

.fabric-group-dialog :deep(.el-dialog__header) {
  padding-bottom: 10px;
  margin-bottom: 0;
  border-bottom: 1px solid #ebeef5;
}

.dialog-tabs {
  margin-bottom: 15px;
}

.dialog-tabs :deep(.el-tabs__header) {
  margin-bottom: 15px;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-width: 780px;
  margin: 0 auto;
}

.form-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;
}

.form-column {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-divider {
  height: 1px;
  background-color: #ebeef5;
  margin: 15px 0;
}

.fabric-filter-list-section {
  background-color: #f5f7fa;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
  border: 1px solid #ebeef5;
}

.filter-section {
  margin-bottom: 15px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 15px;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
}

.filter-select {
  flex: 1;
}

.fabric-list-item {
  margin-bottom: 0;
}

.custom-input {
  width: 100%;
}

.classification-display {
  min-height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 4px 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  align-items: center;
  background-color: #f5f7fa;
}

.classification-tag {
  margin-right: 0;
}

.empty-text {
  color: #909399;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  margin-top: 25px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.fabric-group-form :deep(.el-form-item__label) {
  font-weight: bold;
  color: #606266;
}

.fabric-group-form :deep(.el-form-item) {
  margin-bottom: 15px;
}

.fabric-group-form :deep(.el-form-item:last-child) {
  margin-bottom: 0;
}

.empty-matrix-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #909399;
  text-align: center;
}

.empty-matrix-message p {
  margin-top: 10px;
  font-size: 14px;
}

/* 二维表样式 */
.matrix-container {
  margin-top: 20px;
  padding: 0;
  display: flex;
  justify-content: center; /* 居中显示表格 */
}

.matrix-table {
  width: fit-content;
  /* 移除 min-width: 100%，让表格宽度由内容决定 */
  overflow: auto;
  max-height: 500px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
}

.matrix-header {
  display: flex;
  position: sticky;
  top: 0;
  background-color: #f5f7fa;
  z-index: 1;
  border-bottom: 1px solid #dcdfe6;
}

.matrix-corner-cell {
  width: 220px;
  min-width: 220px;
  max-width: 220px;
  padding: 8px;
  font-weight: bold;
  border-right: 1px solid #dcdfe6;
  background-color: #f0f2f5;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box; /* 确保padding不会增加元素宽度 */
}

.supplier-info,
.classification-info {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.supplier-label,
.classification-label {
  font-weight: bold;
  margin-right: 8px;
  color: #606266;
  width: 60px;
  text-align: right;
}

.supplier-value,
.classification-value {
  color: #303133;
  flex: 1;
}

.matrix-header-cell {
  width: 120px;
  min-width: 120px;
  max-width: 120px;
  padding: 8px;
  border-right: 1px solid #dcdfe6;
  flex-shrink: 0;
  text-align: center;
  box-sizing: border-box; /* 确保padding不会增加元素宽度 */
}

.matrix-body {
  display: flex;
  flex-direction: column;
}

.matrix-row {
  display: flex;
  border-bottom: 1px solid #dcdfe6;
  align-items: stretch;
}

.matrix-row:last-child {
  border-bottom: none;
}

.matrix-row-header {
  width: 220px;
  min-width: 220px;
  max-width: 220px;
  padding: 8px;
  border-right: 1px solid #dcdfe6;
  background-color: #f5f7fa;
  box-sizing: border-box; /* 确保padding不会增加元素宽度 */
}

.matrix-cell {
  width: 120px;
  min-width: 120px;
  max-width: 120px;
  padding: 8px;
  border-right: 1px solid #dcdfe6;
  text-align: center;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box; /* 确保padding不会增加元素宽度 */
}

.matrix-cell:last-child {
  border-right: none;
}

.matrix-cell .el-input {
  width: 100px; /* 增加输入框宽度，使其更接近单元格宽度 */
}

/* 布料卡片样式 */
.fabric-card {
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: center;
  width: 100%;
}

.fabric-index {
  font-weight: bold;
  color: #909399;
  font-size: 14px;
}

.fabric-details {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.fabric-name {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 2px;
}

.fabric-id {
  font-size: 12px;
  color: #409eff;
}

/* 服装卡片样式 */
.clothing-card {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

/* 上层：分为左右两块 */
.clothing-top-section {
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px dashed #dcdfe6;
  display: flex;
  flex-direction: row;
}

.clothing-name {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.clothing-id {
  font-size: 12px;
  color: #67c23a;
}

/* 中层：分为左右两块 */
.clothing-middle-section {
  display: flex;
  font-size: 12px;
  margin-bottom: 0;
  padding-bottom: 0;
}

.clothing-info-left {
  flex: 1;
  color: #606266;
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding-right: 8px;
  /* 居中对齐 */
  align-items: center;
}

.clothing-info-right {
  flex: 1;
  color: #606266;
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding-left: 8px;
  border-left: 1px solid #ebeef5;
  /* 居中对齐 */
  align-items: center;
}

.clothing-info-top-left {
  flex: 1;
  color: #606266;
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding-right: 8px;
  /* 居中对齐 */
  align-items: center;
}

.clothing-info-top-right {
  flex: 1;
  color: #606266;
  display: flex;
  flex-direction: row;
  gap: 2px;
  padding-left: 8px;
  border-left: 1px solid #ebeef5;
  /* 居中对齐 */
  align-items: center;
  justify-content: center;
}

/* 裁剪件数样式 */

.clipping-pcs {
  font-weight: bold;
  color: #e6a23c;
  text-align: right;
}

.pcs-value {
  font-size: 14px;
  margin-left: 4px;
}

.clipping-value {
  color: #f56c6c; /* 红色 - 裁剪数量 */
}

.order-value {
  color: #67c23a; /* 绿色 - 订单数量 */
}

.empty-input {
  color: #909399;
}

.empty-input :deep(input) {
  color: #909399;
}
</style>
