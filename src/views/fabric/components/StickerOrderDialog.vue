<template>
  <el-dialog
    v-model="dialogVisible"
    align-center
    width="50%"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <div v-loading="loading" class="sticker-order-container">
      <div v-if="tableData.length === 0 && !loading" class="empty-data">
        <el-empty description="没有找到裁剪完成的布料组数据" />
      </div>
      <el-table
        v-else
        :data="tableData"
        border
        style="width: 100%"
        max-height="500px"
        :header-cell-style="{ textAlign: 'center' }"
        :cell-style="{ textAlign: 'center' }"
      >
        <!-- 服装名称列 -->
        <el-table-column prop="clothing_name" label="服装名称" width="120" fixed="left" />

        <!-- 动态生成布料列 -->
        <el-table-column
          v-for="fabricId in allFabricIds"
          :key="fabricId"
          :label="fabricIdToNameMap[fabricId] || fabricId"
          min-width="80px"
        >
          <template #default="{ row }">
            <span class="fabric-pcs">{{ row.fabric_usages[fabricId] || '' }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleComplete"
          :disabled="clippingCompletedFabricGroupIds.length === 0"
        >
          完成
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getFinishedCuttingGroupsWithClothing, updateFabricGroup } from '@/api/fabricGroup'
import type { FinishedCuttingGroup, ClothingInfo, ColorPcs } from '@/api/fabricGroup'

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'success'])

// 对话框可见性
const dialogVisible = ref(props.visible)
// 监听visible属性变化

watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
  }
)
// 监听dialogVisible变化
watch(
  () => dialogVisible.value,
  (val) => {
    emit('update:visible', val)
  }
)

// 加载状态
const loading = ref(false)

// 裁剪完成的布料组ID列表
const clippingCompletedFabricGroupIds = ref<string[]>([])

// 布料ID到布料名称的映射
const fabricIdToNameMap = ref<Record<string, string>>({})

// 服装数据表格
const tableData = ref<
  {
    clothing_name: string
    fabric_usages: Record<string, string>
  }[]
>([])

// 所有布料ID列表（用于表头）
const allFabricIds = ref<string[]>([])

// 监听对话框可见性变化
watch(
  () => dialogVisible.value,
  (val) => {
    if (val) {
      // 对话框打开时加载数据
      loadData()
    }
  }
)

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const response = await getFinishedCuttingGroupsWithClothing()
    console.log('获取到的裁剪完成的布料组数据:', response)
    if (response?.data) {
      const finishedGroups = response.data as unknown as FinishedCuttingGroup[]

      // 重置数据
      tableData.value = []
      allFabricIds.value = []
      fabricIdToNameMap.value = {}
      clippingCompletedFabricGroupIds.value = []

      if (finishedGroups.length === 0) {
        ElMessage.info('没有找到裁剪完成的布料组数据')
        return
      }

      // 收集所有布料组ID
      clippingCompletedFabricGroupIds.value = finishedGroups.map(
        (group: FinishedCuttingGroup) => group._id
      )

      // 收集所有服装数据和它们的colorPcs
      const clothingDataMap = new Map<string, { clothing: ClothingInfo; colorPcs: ColorPcs[] }>()

      finishedGroups.forEach((group: FinishedCuttingGroup) => {
        if (group.clothingList && group.clothingList.length > 0) {
          group.clothingList.forEach((clothing: ClothingInfo) => {
            if (clothing.colorPcs && clothing.colorPcs.length > 0) {
              // 为每个服装创建一个条目，包含服装信息和它的colorPcs
              clothingDataMap.set(clothing.clothing_id, {
                clothing,
                colorPcs: [...clothing.colorPcs],
              })
            }
          })
        }
      })

      // 确定最大的colorPcs数量
      let maxColorPcsCount = 0
      clothingDataMap.forEach((data) => {
        maxColorPcsCount = Math.max(maxColorPcsCount, data.colorPcs.length)
      })

      // 创建序号列（1#, 2#, 3#...）
      allFabricIds.value = []
      for (let i = 0; i < maxColorPcsCount; i++) {
        allFabricIds.value.push(`column_${i}`)
        fabricIdToNameMap.value[`column_${i}`] = `${i + 1}#`
      }

      // 为每个服装创建表格行
      clothingDataMap.forEach((data) => {
        const rowData = {
          clothing_name: data.clothing.clothing_name,
          fabric_usages: {} as Record<string, string>,
        }

        // 按顺序填充colorPcs数据
        data.colorPcs.forEach((colorPcs, index) => {
          rowData.fabric_usages[`column_${index}`] = colorPcs.pcs
        })

        // 添加到表格数据
        tableData.value.push(rowData)
      })
    }
  } catch (error) {
    console.error('加载数据失败', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 完成
const handleComplete = async () => {
  if (clippingCompletedFabricGroupIds.value.length === 0) {
    ElMessage.warning('没有可归档的布料组')
    return
  }

  try {
    // 确认是否归档
    await ElMessageBox.confirm('确定要将所有裁剪完成的布料组归档吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    loading.value = true

    // 更新所有布料组状态为归档
    const updatePromises = clippingCompletedFabricGroupIds.value.map((id) => {
      return updateFabricGroup(id, { state: '归档' })
    })

    await Promise.all(updatePromises)

    ElMessage.success('布料组归档成功')
    emit('success')
    dialogVisible.value = false
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('归档布料组失败', error)
      ElMessage.error(error.response?.data?.message || '归档布料组失败')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.sticker-order-container {
  min-height: 200px;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.fabric-usage-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.fabric-usage-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 4px 8px;
}

.fabric-id {
  font-size: 12px;
  color: #606266;
}

.fabric-pcs {
  font-size: 14px;
  font-weight: bold;
  color: #409eff;
}

.clipping-pcs {
  font-weight: bold;
  color: #f56c6c;
  font-size: 15px;
}
</style>
