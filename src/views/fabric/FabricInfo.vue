<template>
  <PageTemplate
    :queryParams="queryParams"
    :tableData="tableData"
    :total="total"
    :loading="loading"
    :tableHeight="650"
    @query="handleQuery"
    @reset="handleReset"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  >
    <!-- 查询区域左侧 -->
    <template #query-form-left>
      <el-form-item label="所属年份" prop="fabric_years">
        <el-select
          v-model="queryParams.fabric_years"
          placeholder="选择年份"
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          style="width: 120px"
          @change="handleYearChange"
        >
          <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
        </el-select>
      </el-form-item>
      <el-form-item label="供应商" prop="suppliers">
        <el-select
          v-model="queryParams.suppliers"
          placeholder="选择供应商"
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          filterable
          style="width: 150px"
          @change="handleSupplierChange"
        >
          <el-option
            v-for="supplier in supplierOptions"
            :key="supplier"
            :label="supplier"
            :value="supplier"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="布料分类" prop="classifications">
        <el-select
          v-model="queryParams.classifications"
          placeholder="选择布料分类"
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          filterable
          style="width: 200px"
          @change="handleCategoryChange"
        >
          <el-option
            v-for="category in categoryOptions"
            :key="category"
            :label="category"
            :value="category"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="布料名称" prop="fabric_name">
        <el-input
          v-model="queryParams.fabric_name"
          placeholder="输入布料名称搜索"
          clearable
          style="width: 150px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
    </template>

    <!-- 查询区域右侧 -->
    <template #query-form-right>
      <el-button type="primary" @click="handleAdd">新增布料</el-button>
    </template>

    <!-- 分页区域左侧 -->
    <template #pagination-left>
      <div class="table-tools">
        <el-upload
          class="upload-button"
          :action="null"
          :auto-upload="false"
          :on-change="handleFileChange"
          accept=".xlsx,.xls"
          :show-file-list="false"
        >
          <el-button type="success" plain size="small">Excel导入</el-button>
        </el-upload>

        <el-button type="warning" plain size="small" @click="handleExport">Excel导出</el-button>
      </div>
    </template>

    <!-- 表格列 -->
    <template #table-columns>
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="fabric_year" label="所属年份" width="100" align="center" />
      <el-table-column prop="fabric_id" label="布料编码" width="120" align="center" />
      <el-table-column prop="fabric_name" label="布料名称" width="150" align="center" />
      <el-table-column prop="supplier" label="供应商" width="150" align="center" />
      <el-table-column prop="classification" label="布料分类" width="180" align="center" />
      <el-table-column prop="in_quantity" label="入库数量" width="100" align="center">
        <template #default="{ row }">
          <el-popover
            placement="top"
            :width="200"
            trigger="click"
            popper-class="in-quantity-popover"
          >
            <template #reference>
              <span class="clickable-cell" @click="handleViewInQuantityDetail(row)">
                {{ row.in_quantity }}
              </span>
            </template>
            <template #default>
              <div v-loading="detailLoading">
                <el-table
                  :data="inQuantityDetails"
                  stripe
                  size="small"
                  style="width: 100%"
                
                >
                  <el-table-column prop="date" label="入库日期" width="100" />
                  <el-table-column prop="quantity" label="数量" width="60" align="right" />
                </el-table>
              </div>
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="out_quantity" label="出库数量" width="100" align="center">
        <template #default="{ row }">
          <el-popover
            placement="top"
            :width="200"
            trigger="click"
            popper-class="in-quantity-popover"
          >
            <template #reference>
              <span class="clickable-cell" @click="handleViewOutQuantityDetail(row)">
                {{ row.out_quantity }}
              </span>
            </template>
            <template #default>
              <div v-loading="outDetailLoading">
                <el-table
                  :data="outQuantityDetails"
                  stripe
                  size="small"
                  style="width: 100%"
                  
                >
                  <el-table-column prop="date" label="出库日期" width="100" />
                  <el-table-column prop="quantity" label="数量" width="80" align="right" />
                </el-table>
              </div>
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="order_quantity" label="订购数量" width="100" align="center" />
      <el-table-column prop="remark" label="备注" show-overflow-tooltip />
      <el-table-column fixed="right" label="操作" width="150" align="center">
        <template #default="{ row }">
          <el-button link type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button link type="danger" size="small" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </template>

    <!-- 额外内容插槽 -->
    <template #extra-content>
      <!-- 新增/编辑布料对话框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="dialogType === 'add' ? '新增布料' : '编辑布料'"
        width="700px"
        destroy-on-close
        class="fabric-dialog"
        align-center
        center
      >
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
          :validate-on-rule-change="false"
          class="fabric-form"
        >
          <div class="form-section">
            <div class="form-row">
              <el-form-item label="所属年份" prop="fabric_year">
                <el-input
                  v-model="form.fabric_year"
                  placeholder="请输入所属年份"
                  class="custom-input"
                />
              </el-form-item>
              <el-form-item label="布料编码" prop="fabric_id">
                <el-input
                  v-model="form.fabric_id"
                  placeholder="请输入布料编码"
                  class="custom-input"
                />
              </el-form-item>
            </div>

            <div class="form-row">
              <el-form-item label="布料名称" prop="fabric_name">
                <el-input
                  v-model="form.fabric_name"
                  placeholder="请输入布料名称"
                  class="custom-input"
                />
              </el-form-item>
              <el-form-item label="布料分类" prop="classification">
                <el-input
                  v-model="form.classification"
                  placeholder="请输入布料分类"
                  class="custom-input"
                />
              </el-form-item>
            </div>

            <div class="form-row">
              <el-form-item label="供应商" prop="supplier" class="full-width">
                <el-input v-model="form.supplier" placeholder="请输入供应商" class="custom-input" />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="入库数量" prop="in_quantity">
                <el-input
                  v-model="form.in_quantity"
                  placeholder="请输入入库数量"
                  class="custom-input"
                />
              </el-form-item>
              <el-form-item label="订购数量" prop="order_quantity">
                <el-input
                  v-model="form.order_quantity"
                  placeholder="请输入订购数量"
                  class="custom-input"
                />
              </el-form-item>
            </div>
            <div>
              <el-form-item label="备注" prop="remark" class="full-width">
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  placeholder="请输入备注信息"
                  :rows="2"
                  class="custom-textarea"
                />
              </el-form-item>
            </div>
          </div>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogVisible = false" class="cancel-btn">取消</el-button>
            <el-button
              type="primary"
              @click="handleSubmit"
              :loading="submitLoading"
              class="submit-btn"
            >
              {{ dialogType === 'add' ? '新增' : '保存' }}
            </el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 使用新的导入组件 -->
      <FabricImport
        v-model:visible="importDialogVisible"
        :file="importFile"
        @import-success="handleImportSuccess"
      />
    </template>
  </PageTemplate>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 导入布料导入组件
import FabricImport from './components/FabricImport.vue'
import type { FormInstance } from 'element-plus'
// 导入通用页面模板
import PageTemplate from '@/components/common/PageTemplate.vue'
import {
  getFabricList,
  createFabric,
  updateFabric,
  deleteFabric,
  getFabricCategoryOptions,
  getFabricYearOptions,
  getFabricSupplierOptions,
} from '@/api/fabric'
import { getFabricWarehouseDetailsByFabricId } from '@/api/fabricWarehouseDetail'
import type { FabricParams, Fabric } from '@/api/fabric'
// 引入xlsx库
import * as XLSX from 'xlsx'
// import { Lunar } from 'lunar-typescript'
// 加载状态
const loading = ref(false)
const submitLoading = ref(false)

// 表格数据
const tableData = ref<Fabric[]>([])
const total = ref(0)

// 不再需要农历年份
// const LunarYearValue = ref(Lunar.fromDate(new Date()).getYear())

// 查询参数
const queryParams = reactive({
  fabric_years: [] as string[],
  suppliers: [] as string[],
  classifications: [] as string[],
  fabric_name: '',
  page: 1,
  limit: 10,
})

// 下拉选项
const yearOptions = ref<string[]>([])
const supplierOptions = ref<string[]>([])
const categoryOptions = ref<string[]>([])

// 库存入库明细
const detailLoading = ref(false)
const inQuantityDetails = ref<any[]>([])

// 库存出库明细
const outDetailLoading = ref(false)
const outQuantityDetails = ref<any[]>([])

// 初始化选项值
const loadInitialOptions = async () => {
  const supplierResponse = await getFabricSupplierOptions(
    queryParams.fabric_years.join(','),
    queryParams.classifications.join(',')
  )
  console.log('供应商选项：', supplierResponse)
  supplierOptions.value = supplierResponse.data
  const categoryResponse = await getFabricCategoryOptions(
    queryParams.fabric_years.join(','),
    queryParams.suppliers.join(',')
  )
  console.log('分类选项：', categoryResponse)
  categoryOptions.value = categoryResponse.data
  const yearResponse = await getFabricYearOptions(
    queryParams.suppliers.join(','),
    queryParams.classifications.join(',')
  )
  // 将年份选项按照倒序排列
  const years = yearResponse.data || []
  yearOptions.value = years.sort((a: string, b: string) => {
    // 提取年份数字进行比较
    const yearA = parseInt(a.match(/\d+/)?.[0] || '0')
    const yearB = parseInt(b.match(/\d+/)?.[0] || '0')
    return yearB - yearA // 倒序排列
  })
}

// 查询表单
const queryFormRef = ref<FormInstance>()

// 添加/编辑表单
const formRef = ref<FormInstance>()
const form = reactive<FabricParams>({
  fabric_year: '',
  fabric_id: '',
  fabric_name: '',
  supplier: '',
  classification: '',
  in_quantity: 0,
  order_quantity: 0,
  remark: '',
})

// 对话框状态
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const currentId = ref<string | null>(null)

// 导入相关
const importDialogVisible = ref(false)
const importFile = ref<File | null>(null)
// 表单校验规则
const rules = {
  fabric_year: [{ required: true, message: '请选择所属年份', trigger: 'change' }],
  fabric_id: [{ required: true, message: '请输入布料编码', trigger: 'blur' }],
  fabric_name: [{ required: true, message: '请输入布料名称', trigger: 'blur' }],
  supplier: [{ required: true, message: '请输入供应商', trigger: 'blur' }],
  classification: [{ required: true, message: '请输入布料分类', trigger: 'blur' }],
  in_quantity: [{ required: false, message: '请输入入库数量', trigger: 'blur' }],
  order_quantity: [{ required: false, message: '请输入订购数量', trigger: 'blur' }],
}

// 选择器值变化时的处理函数
const handleYearChange = async () => {
  try {
    console.log('年份选择器值变化：', queryParams.fabric_years)

    // 根据选中的年份获取对应的供应商选项
    const supplierResponse = await getFabricSupplierOptions(
      queryParams.fabric_years.join(','),
      queryParams.classifications.join(',')
    )
    console.log('供应商选项1111：', supplierResponse)
    supplierOptions.value = supplierResponse.data
    console.log('供应商选项2222：', supplierOptions.value)
    const categoryResponse = await getFabricCategoryOptions(
      queryParams.fabric_years.join(','),
      queryParams.suppliers.join(',')
    )
    console.log('分类选项：', categoryResponse)
    categoryOptions.value = categoryResponse.data
  } catch (error) {
    console.error('获取供应商选项失败:', error)
  }

  // 执行查询
  handleQuery()
}

const handleSupplierChange = async () => {
  try {
    // 根据选中的供应商获取对应的年份选项
    const yearResponse = await getFabricYearOptions(
      queryParams.suppliers.join(','),
      queryParams.classifications.join(',')
    )
    // 将年份选项按照倒序排列
    const years = yearResponse.data || []
    yearOptions.value = years.sort((a: string, b: string) => {
      // 提取年份数字进行比较
      const yearA = parseInt(a.match(/\d+/)?.[0] || '0')
      const yearB = parseInt(b.match(/\d+/)?.[0] || '0')
      return yearB - yearA // 倒序排列
    })

    const categoryResponse = await getFabricCategoryOptions(
      queryParams.fabric_years.join(','),
      queryParams.suppliers.join(',')
    )
    categoryOptions.value = categoryResponse.data
  } catch (error) {
    console.error('获取年份选项失败:', error)
  }

  // 执行查询
  handleQuery()
}

const handleCategoryChange = async () => {
  try {
    // 根据选中的分类获取对应的年份选项
    const yearResponse = await getFabricYearOptions(
      queryParams.suppliers.join(','),
      queryParams.classifications.join(',')
    )
    // 将年份选项按照倒序排列
    const years = yearResponse.data || []
    yearOptions.value = years.sort((a: string, b: string) => {
      // 提取年份数字进行比较
      const yearA = parseInt(a.match(/\d+/)?.[0] || '0')
      const yearB = parseInt(b.match(/\d+/)?.[0] || '0')
      return yearB - yearA // 倒序排列
    })
    // 根据选中的分类获取对应的供应商选项
    const supplierResponse = await getFabricSupplierOptions(
      queryParams.fabric_years.join(','),
      queryParams.classifications.join(',')
    )
    supplierOptions.value = supplierResponse.data
  } catch (error) {
    console.error('获取年份和供应商选项失败:', error)
  }

  // 执行查询
  handleQuery()
}

// 初始化
onMounted(async () => {
  // 加载所有选项数据
  await loadInitialOptions()

  // 加载数据
  await loadData()
})

// 加载数据
const loadData = async (params: any = queryParams) => {
  loading.value = true
  try {
    console.log('开始加载布料数据，查询参数：', params)
    const response = await getFabricList(params)
    console.log('获取到的布料数据：', response)
    tableData.value = response.data.fabricList
    total.value = response.data.total

    // 如果没有数据，提示用户
    if (response.data.total === 0) {
      ElMessage.info('未查询到布料数据，请尝试其他筛选条件或添加新数据')
    }
  } catch (error: any) {
    console.error('加载布料数据失败，详细错误：', error)
    ElMessage.error(error.response?.data?.message || '加载布料数据失败')
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  // 构造符合后端API期望的查询参数
  const params: any = {
    page: queryParams.page,
    limit: queryParams.limit,
  }

  // 处理年份参数 - 如果只有一个值，使用字符串，否则使用数组
  if (queryParams.fabric_years && queryParams.fabric_years.length > 0) {
    if (queryParams.fabric_years.length === 1) {
      params.fabric_year = queryParams.fabric_years[0]
    } else {
      params.fabric_years = queryParams.fabric_years
    }
  }

  // 处理供应商参数
  if (queryParams.suppliers && queryParams.suppliers.length > 0) {
    if (queryParams.suppliers.length === 1) {
      params.supplier = queryParams.suppliers[0]
    } else {
      params.suppliers = queryParams.suppliers
    }
  }

  // 处理分类参数
  if (queryParams.classifications && queryParams.classifications.length > 0) {
    if (queryParams.classifications.length === 1) {
      params.classification = queryParams.classifications[0]
    } else {
      params.classifications = queryParams.classifications
    }
  }

  // 处理布料名称参数
  if (queryParams.fabric_name && queryParams.fabric_name.trim() !== '') {
    params.fabric_name = queryParams.fabric_name.trim()
  }

  console.log('发送的查询参数：', params)
  loadData(params)
}

// 重置
const handleReset = async () => {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields()
  }
  queryParams.page = 1
  queryParams.fabric_years = []
  queryParams.suppliers = []
  queryParams.classifications = []
  queryParams.fabric_name = ''

  // 重新加载所有选项
  await loadInitialOptions()

  // 重新加载数据
  loadData({
    page: 1,
    limit: queryParams.limit,
  })
}

// 打开新增对话框
const handleAdd = () => {
  dialogType.value = 'add'
  resetForm()
  dialogVisible.value = true
}

// 打开编辑对话框
const handleEdit = (row: any) => {
  console.log('准备编辑布料，原始数据：', row)
  dialogType.value = 'edit'
  currentId.value = row.id || row._id

  // 不使用resetForm，直接设置表单值
  if (formRef.value) {
    formRef.value.resetFields()
  }

  // 明确设置每个字段值
  form.fabric_year = row.fabric_year
  form.fabric_id = row.fabric_id
  form.fabric_name = row.fabric_name
  form.supplier = row.supplier
  form.classification = row.classification
  form.in_quantity = row.in_quantity
  form.order_quantity = row.order_quantity
  form.remark = row.remark || row.auxiliary_cloth || ''

  console.log('表单数据设置完成：', form)
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.fabric_year = ''
  form.fabric_id = ''
  form.fabric_name = ''
  form.supplier = ''
  form.classification = ''
  form.in_quantity = 0
  form.order_quantity = 0
  form.remark = ''
  currentId.value = null
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (!valid) return

    submitLoading.value = true
    try {
      if (dialogType.value === 'add') {
        console.log('准备新增布料：', form)
        // 创建普通对象，确保数据类型正确
        const createData = {
          fabric_year: form.fabric_year,
          fabric_id: form.fabric_id,
          fabric_name: form.fabric_name,
          supplier: form.supplier,
          classification: form.classification,
          in_quantity: form.in_quantity ? Number(form.in_quantity) : 0,
          order_quantity: form.order_quantity ? Number(form.order_quantity) : 0,
          remark: form.remark,
        }
        await createFabric(createData)
        ElMessage.success('新增布料成功')
        dialogVisible.value = false
        // 刷新
        handleQuery()
        loadInitialOptions()
      } else if (dialogType.value === 'edit' && currentId.value) {
        console.log('准备更新布料，ID：', currentId.value)

        // 创建普通对象，确保数据类型正确
        const updateData = {
          fabric_year: form.fabric_year,
          fabric_id: form.fabric_id,
          fabric_name: form.fabric_name,
          supplier: form.supplier,
          classification: form.classification,
          in_quantity: form.in_quantity ? Number(form.in_quantity) : 0,
          order_quantity: form.order_quantity ? Number(form.order_quantity) : 0,
          remark: form.remark,
        }

        console.log('发送的更新数据对象：', updateData)

        await updateFabric(currentId.value, updateData)
        ElMessage.success('更新布料成功')
        dialogVisible.value = false

        // 刷新
        handleQuery()
        loadInitialOptions()
      }
    } catch (error: any) {
      console.error('提交失败', error)
      ElMessage.error(error.response?.data?.message || '操作失败')
    } finally {
      submitLoading.value = false
    }
  })
}

// 删除
const handleDelete = (row: any) => {
  // 检查布料ID是否存在
  const fabricId = row._id || row.id
  if (!fabricId) {
    console.error('删除失败: 未找到布料ID', row)
    ElMessage.error('删除失败: 未找到布料ID')
    return
  }

  console.log('准备删除布料，ID:', fabricId)

  ElMessageBox.confirm('确定要删除该布料吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await deleteFabric(fabricId)
      ElMessage.success('删除成功')
      // 刷新数据
      handleQuery()
      loadInitialOptions()
    } catch (error: any) {
      console.error('删除失败', error)
      // 显示更详细的错误信息
      const errorMessage = error.response?.data?.message || '删除失败'
      ElMessage.error(errorMessage)
    }
  })
}

// Excel导出 - 直接在前端实现
const handleExport = () => {
  // 构建确认信息
  let confirmMessage = '确定要导出布料数据吗？'

  // 根据查询条件添加提示信息
  const conditions = []

  if (queryParams.fabric_years && queryParams.fabric_years.length > 0) {
    conditions.push(`年份: ${queryParams.fabric_years.join(', ')}`)
  }

  if (queryParams.suppliers && queryParams.suppliers.length > 0) {
    conditions.push(`供应商: ${queryParams.suppliers.join(', ')}`)
  }

  if (queryParams.classifications && queryParams.classifications.length > 0) {
    conditions.push(`分类: ${queryParams.classifications.join(', ')}`)
  }

  if (queryParams.fabric_name) {
    conditions.push(`布料名称: ${queryParams.fabric_name}`)
  }

  if (conditions.length > 0) {
    confirmMessage += '\n\n当前查询条件:\n' + conditions.join('\n')
  } else {
    confirmMessage += '\n\n当前没有设置查询条件，将导出所有数据。'
  }

  // 显示确认对话框
  ElMessageBox.confirm(confirmMessage, '导出确认', {
    confirmButtonText: '确认导出',
    cancelButtonText: '取消',
    type: 'info',
    dangerouslyUseHTMLString: false,
  })
    .then(async () => {
      // 用户确认导出，执行导出操作
      try {
        // 显示加载中
        loading.value = true
        ElMessage.info('正在准备导出数据，请稍候...')

        // 构建查询参数，获取所有数据
        const params: Record<string, any> = {}

        // 添加年份过滤条件
        if (queryParams.fabric_years && queryParams.fabric_years.length > 0) {
          if (queryParams.fabric_years.length === 1) {
            params.fabric_year = queryParams.fabric_years[0]
          } else {
            params.fabric_years = queryParams.fabric_years
          }
        }

        // 添加供应商过滤条件
        if (queryParams.suppliers && queryParams.suppliers.length > 0) {
          if (queryParams.suppliers.length === 1) {
            params.supplier = queryParams.suppliers[0]
          } else {
            params.suppliers = queryParams.suppliers
          }
        }

        // 添加布料分类过滤条件
        if (queryParams.classifications && queryParams.classifications.length > 0) {
          if (queryParams.classifications.length === 1) {
            params.classification = queryParams.classifications[0]
          } else {
            params.classifications = queryParams.classifications
          }
        }

        // 添加布料名称过滤条件
        if (queryParams.fabric_name) {
          params.fabric_name = queryParams.fabric_name
        }

        // 设置分页参数，获取尽可能多的数据
        params.limit = 5000 // 设置较大的限制数，确保获取到所有数据
        params.page = 1

        console.log('导出查询参数:', params)

        // 使用 API 获取数据
        const response = await getFabricList(params)
        const fabricData = response?.data?.fabricList || []

        if (fabricData.length === 0) {
          ElMessage.warning('没有数据可导出')
          return
        }

        console.log(`已获取 ${fabricData.length} 条数据用于导出`)

        // 准备导出数据
        const exportData = fabricData.map((item) => ({
          所属年份: item.fabric_year,
          布料编码: item.fabric_id,
          布料名称: item.fabric_name,
          供应商: item.supplier,
          布料分类: item.classification,
          入库数量: item.in_quantity,
          订购数量: item.order_quantity,
          备注: item.remark || '',
        }))

        // 创建Excel工作表
        const worksheet = XLSX.utils.json_to_sheet(exportData)

        // 设置列宽
        const columnWidths = [
          { wch: 10 }, // 所属年份
          { wch: 15 }, // 布料编码
          { wch: 20 }, // 布料名称
          { wch: 20 }, // 供应商
          { wch: 20 }, // 布料分类
          { wch: 10 }, // 入库数量
          { wch: 10 }, // 订购数量
          { wch: 30 }, // 备注
        ]
        worksheet['!cols'] = columnWidths

        // 创建Excel工作簿
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, '布料列表')

        // 生成Excel文件
        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
        const blob = new Blob([excelBuffer], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        })

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        // 生成文件名
        const now = new Date()
        const year = now.getFullYear()
        const month = String(now.getMonth() + 1).padStart(2, '0')
        const day = String(now.getDate()).padStart(2, '0')
        const hours = String(now.getHours()).padStart(2, '0')
        const minutes = String(now.getMinutes()).padStart(2, '0')

        // 构建文件名，包含查询条件
        let fileName = `布料列表_${year}-${month}-${day}_${hours}-${minutes}`

        // 添加查询条件到文件名
        if (queryParams.fabric_years && queryParams.fabric_years.length > 0) {
          fileName += `_年份${queryParams.fabric_years.join('-')}`
        }
        if (
          queryParams.suppliers &&
          queryParams.suppliers.length > 0 &&
          queryParams.suppliers.length <= 2
        ) {
          fileName += `_供应商${queryParams.suppliers.join('-')}`
        }
        if (queryParams.fabric_name) {
          fileName += `_名称${queryParams.fabric_name}`
        }

        link.setAttribute('download', `${fileName}.xlsx`)
        document.body.appendChild(link)
        link.click()

        // 清理
        window.URL.revokeObjectURL(url)
        document.body.removeChild(link)
        ElMessage.success(`导出成功，共 ${fabricData.length} 条数据`)
      } catch (error) {
        console.error('导出失败', error)
        const errorMessage = error instanceof Error ? error.message : '导出Excel失败'
        ElMessage.error(`导出失败: ${errorMessage}`)
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      // 用户取消导出
      ElMessage.info('已取消导出操作')
    })
}

// 导入成功处理函数
const handleImportSuccess = () => {
  // 刷新数据列表
  handleQuery()
  // 重新加载选项
  loadInitialOptions()
  // 显示成功提示
  ElMessage.success('导入数据成功')
}

// 文件变更
const handleFileChange = async (file: any) => {
  if (file.raw) {
    // 设置文件
    importFile.value = file.raw
    // 显示导入对话框
    importDialogVisible.value = true
    console.log('文件已设置:', importFile.value)
  }
}

// 分页处理
const handleSizeChange = (val: number) => {
  queryParams.limit = val
  queryParams.page = 1 // 当每页数量变化时，重置为第一页
  handleQuery()
}

const handleCurrentChange = (val: number) => {
  queryParams.page = val
  handleQuery()
}

// 查看入库明细
const handleViewInQuantityDetail = async (row: any) => {
  if (!row || !row.fabric_id) {
    ElMessage.warning('无法获取布料信息')
    return
  }

  detailLoading.value = true

  try {
    // 使用真实 API 获取布料入库明细数据
    const response = await getFabricWarehouseDetailsByFabricId(row.fabric_id)

    // 处理响应数据
    if (response && Array.isArray(response) && response.length > 0) {
      // 将后端数据转换为前端显示所需的格式
      inQuantityDetails.value = response.map((item) => ({
        date: item.date ? new Date(item.date).toISOString().split('T')[0] : '-',
        quantity: item.meter || 0,
        supplier: item.supplier || '-',
        remark: item.remark || '-',
      }))
    } else {
      inQuantityDetails.value = []
      ElMessage.info('没有找到入库明细数据')
    }
  } catch (error) {
    console.error('获取入库明细数据失败', error)
    ElMessage.error('获取入库明细数据失败')
    inQuantityDetails.value = []
  } finally {
    detailLoading.value = false
  }
}

// 查看出库明细
const handleViewOutQuantityDetail = (row: any) => {
  // 注意：这里使用了 row 参数，避免 IDE 警告
  console.log('查看出库明细，布料ID:', row?.fabric_id)
  outDetailLoading.value = true

  // 模拟从API获取数据，实际项目中应调用后端API
  setTimeout(() => {
    // 这里是示例数据，实际应从后端获取
    outQuantityDetails.value = [
      {
        date: '2023-06-15',
        quantity: 20,
        operator: '李四',
        remark: '生产领用',
      },
      {
        date: '2023-07-22',
        quantity: 15,
        operator: '王五',
        remark: '样衣制作',
      },
      {
        date: '2023-09-05',
        quantity: 10,
        operator: '赵六',
        remark: '返修用料',
      },
    ]
    outDetailLoading.value = false
  }, 500)
}
</script>

<style scoped>
/* 页面特定样式 */
.el-table {
  --el-table-border-color: var(--el-border-color-lighter);
  --el-table-header-bg-color: var(--el-fill-color-light);
  --el-table-row-hover-bg-color: var(--el-fill-color);
}

/* 可点击单元格样式 */
.clickable-cell {
  color: #333;
  cursor: pointer;
}

/* 入库明细弹窗样式 */
:deep(.in-quantity-popover) {
  padding: 0;

}

.popover-title {
  font-size: 16px;
  font-weight: bold;
  padding: 8px 12px;
  background-color: var(--el-color-primary-light-9);
  border-bottom: 1px solid var(--el-border-color-lighter);
}

/* 布料编辑弹窗样式 */
.fabric-dialog {
  :deep(.el-dialog) {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  :deep(.el-dialog__header) {
    margin: 0;
    padding: 20px 24px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    background-color: var(--el-color-primary-light-9);
    text-align: center;
  }

  :deep(.el-dialog__title) {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-color-primary);
    display: inline-block;
    text-align: center;
    width: 100%;
  }

  :deep(.el-dialog__body) {
    padding: 14px 20px 6px;
  }

  :deep(.el-dialog__footer) {
    padding: 8px 24px 16px;
    border-top: none;
    text-align: center;
  }
}

.fabric-form {
  .form-section {
    padding: 10px 16px;
    background-color: var(--el-fill-color-light);
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  /* 已删除 section-title 样式 */

  .form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 4px;
    align-items: center;
    justify-content: space-between;

    :deep(.el-form-item) {
      flex: 1;
      margin-bottom: 8px;

      &.full-width {
        flex: 0 0 100%;
      }
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  :deep(.el-form-item__content) {
    display: flex;
    align-items: center;
  }

  .custom-input,
  .custom-input-number,
  .custom-textarea {
    width: 100%;
  }

  :deep(.el-input__wrapper),
  :deep(.el-textarea__inner) {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
    padding: 0 12px;

    &:hover,
    &:focus {
      box-shadow: 0 0 0 1px var(--el-color-primary-light-5);
    }
  }

  :deep(.el-textarea__inner) {
    padding: 8px 12px;
    min-height: 60px;
    resize: vertical;
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 10px;

  .cancel-btn,
  .submit-btn {
    min-width: 90px;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.3s;
    font-size: 14px;
  }

  .cancel-btn {
    border-color: var(--el-border-color);

    &:hover {
      color: var(--el-color-primary);
      border-color: var(--el-color-primary-light-5);
      background-color: var(--el-color-primary-light-9);
    }
  }

  .submit-btn {
    font-weight: 500;

    &:hover {
      background-color: var(--el-color-primary-dark-1);
    }
  }
}

.empty-data {
  text-align: center;
  color: var(--el-text-color-secondary);
  padding: 16px;
  font-size: 14px;
}

/* 导入对话框样式 */
.import-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }

  .import-container {
    display: flex;
    gap: 20px;
    min-height: 500px;
  }

  .import-sidebar {
    width: 300px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .preview-section {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-color-primary);
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .upload-section {
    .excel-uploader {
      width: 100%;
    }

    :deep(.el-upload-dragger) {
      width: 100%;
      height: auto;
      padding: 20px;
    }

    .upload-icon {
      font-size: 40px;
      color: var(--el-color-primary);
      margin-bottom: 10px;
    }

    .upload-text {
      font-size: 14px;
      margin-bottom: 5px;
    }

    .upload-tip {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }

    .file-info {
      margin-top: 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .sheet-section {
    .sheet-selector {
      display: flex;
      flex-direction: column;
      gap: 8px;

      :deep(.el-radio) {
        margin-right: 0;
        margin-bottom: 8px;
      }
    }
  }

  .import-guide {
    .guide-list {
      padding-left: 20px;
      margin: 0;

      li {
        margin-bottom: 8px;
        color: var(--el-text-color-regular);
      }
    }
  }

  .preview-table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .preview-info {
    margin-top: 12px;

    .preview-stats {
      display: flex;
      justify-content: space-between;
    }
  }
}

.table-tools {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-button {
  margin: 0;
  display: inline-block;
}
</style>
