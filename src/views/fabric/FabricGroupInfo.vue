<template>
  <PageTemplate
    :queryParams="queryParams"
    :tableData="tableData"
    :total="total"
    :loading="loading"
    :tableHeight="650"
    @query="handleQuery"
    @reset="handleReset"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  >
    <!-- 查询区域左侧 -->
    <template #query-form-left>
      <el-form-item label="所属年份" prop="fabric_group_years">
        <el-select
          v-model="queryParams.fabric_group_years"
          placeholder="选择年份"
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          style="width: 160px"
          @change="handleYearChange"
        >
          <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
        </el-select>
      </el-form-item>
      <el-form-item label="供应商" prop="suppliers">
        <el-select
          v-model="queryParams.suppliers"
          placeholder="选择供应商"
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          style="width: 180px"
          @change="handleSupplierChange"
        >
          <el-option
            v-for="supplier in supplierOptions"
            :key="supplier"
            :label="supplier"
            :value="supplier"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="分类" prop="classifications">
        <el-select
          v-model="queryParams.classifications"
          placeholder="选择分类"
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          style="width: 200px"
          @change="handleClassificationChange"
        >
          <el-option
            v-for="classification in classificationOptions"
            :key="classification"
            :label="classification"
            :value="classification"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="编码" prop="fabric_group_id">
        <el-input
          v-model="queryParams.fabric_group_id"
          placeholder="输入编码"
          clearable
          style="width: 120px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="state">
        <el-select
          v-model="queryParams.state"
          placeholder="选择状态"
          clearable
          style="width: 120px"
          @change="handleQuery"
        >
          <el-option v-for="state in stateOptions" :key="state" :label="state" :value="state" />
        </el-select>
      </el-form-item>
    </template>

    <!-- 查询区域右侧 -->
    <template #query-form-right>
      <el-button type="primary" @click="handleAdd">新增布料组</el-button>
    </template>

    <!-- 表格列 -->
    <template #table-columns>
      <el-table-column prop="fabric_group_id" label="布料组编码" width="120" align="center" />
      <el-table-column prop="fabric_group_year" label="所属年份" width="100" align="center" />
      <el-table-column prop="supplier" label="供应商" width="120" align="center" />
      <el-table-column label="分类" width="150" align="center">
        <template #default="{ row }">
          <el-tag
            v-for="(classification, index) in row.group_classification"
            :key="index"
            class="mx-1"
            size="small"
            style="margin-right: 5px"
          >
            {{ classification }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="布料列表" min-width="200" align="center">
        <template #default="{ row }">
          <div class="fabric-cards-container">
            <div
              v-for="(fabric, index) in row.fabricsWithDetails || []"
              :key="fabric.fabric_id || index"
              class="fabric-card"
            >
              <div class="fabric-name">{{ fabric.fabric_name || '未知布料' }}</div>
              <div class="in-quantity" v-if="fabric.in_quantity">{{ fabric.in_quantity }}</div>
            </div>
            <!-- 如果没有详细信息，则显示原始的布料ID -->
            <template v-if="!row.fabricsWithDetails || row.fabricsWithDetails.length === 0">
              <el-tag
                v-for="(fabric, index) in row.fabrics"
                :key="index"
                class="mx-1"
                size="small"
                style="margin-right: 5px; margin-bottom: 5px"
              >
                {{ fabric }}
              </el-tag>
            </template>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="state" label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getStateTagType(row.state)">
            {{ row.state }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="settlement_state" label="结算状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="String(row.settlement_state) === '1' ? 'success' : 'warning'">
            {{ String(row.settlement_state) === '1' ? '已结算' : '未结算' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </template>

    <!-- 分页区域左侧按钮 -->
    <template #pagination-left>
      <el-button type="primary" @click="handleStickerOrder">不干胶下单</el-button>
    </template>

    <!-- 额外内容插槽 -->
    <template #extra-content>
      <!-- 新增/编辑布料组对话框 -->
      <FabricGroupDialog
        v-model:visible="dialogVisible"
        :type="dialogType"
        :edit-data="currentEditRow"
        :classification-options="classificationOptions"
        @submit="handleDialogSubmit"
        @cancel="handleDialogCancel"
      />

      <!-- 不干胶下单对话框 -->
      <StickerOrderDialog
        v-model:visible="stickerOrderDialogVisible"
        @success="handleStickerOrderSuccess"
      />
    </template>
  </PageTemplate>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 导入通用页面模板
import PageTemplate from '@/components/common/PageTemplate.vue'
// 导入布料组对话框组件
import FabricGroupDialog from '@/views/fabric/components/FabricGroupDialog.vue'
// 导入不干胶下单对话框组件
import StickerOrderDialog from '@/views/fabric/components/StickerOrderDialog.vue'
import { extractData } from '@/utils/apiHelper'
import {
  getFabricGroupList,
  createFabricGroup,
  updateFabricGroup,
  deleteFabricGroup,
  getFabricGroupYearOptions,
  getFabricGroupSupplierOptions,
  getFabricGroupClassificationOptions,
} from '@/api/fabricGroup'
import type { FabricGroupParams } from '@/api/fabricGroup'
import type { FabricGroup } from '@/types/fabricGroup'

// 加载状态
const loading = ref(false)

// 表格数据
const tableData = ref<FabricGroup[]>([])
const total = ref(0)

// 查询参数
const queryParams = reactive({
  fabric_group_years: [] as string[],
  suppliers: [] as string[],
  classifications: [] as string[],
  fabric_group_id: '',
  state: '',
  page: 1,
  limit: 10,
})

// 选项数据
const yearOptions = ref<string[]>([])
const supplierOptions = ref<string[]>([])
const classificationOptions = ref<string[]>([])
const stateOptions = ref<string[]>(['待排单', '排单完成', '裁剪完成', '归档'])

// 对话框相关
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const currentEditRow = ref<FabricGroup | null>(null)

// 不干胶下单对话框相关
const stickerOrderDialogVisible = ref(false)

// 初始化
onMounted(async () => {
  // 确保 tableData 有初始值
  tableData.value = []
  total.value = 0

  // 加载所有选项数据
  await loadInitialOptions()

  // 加载数据
  await loadData()
})

// 加载初始选项
const loadInitialOptions = async () => {
  // 清空所有选择
  queryParams.fabric_group_years = []
  queryParams.suppliers = []
  queryParams.classifications = []

  try {
    // 直接加载所有选项，不依赖 updateOptions 方法
    const yearsResponse = await getFabricGroupYearOptions()
    yearOptions.value = extractData<string[]>(yearsResponse, [])

    const suppliersResponse = await getFabricGroupSupplierOptions()
    supplierOptions.value = extractData<string[]>(suppliersResponse, [])

    const classificationsResponse = await getFabricGroupClassificationOptions()
    classificationOptions.value = extractData<string[]>(classificationsResponse, [])
  } catch (error) {
    console.error('加载选项数据失败', error)
    ElMessage.error('加载选项数据失败')
  }
}

// 处理对话框提交
const handleDialogSubmit = async (data: { type: 'add' | 'edit'; data: FabricGroupParams }) => {
  try {
    if (data.type === 'add') {
      // 新增
      await createFabricGroup(data.data)
      ElMessage.success('新增成功')
    } else {
      // 编辑
      const editRow = tableData.value.find(
        (item) => item.fabric_group_id === data.data.fabric_group_id
      )
      if (editRow) {
        await updateFabricGroup(editRow._id, data.data)
        ElMessage.success('更新成功')
      } else {
        ElMessage.error('未找到要编辑的布料组')
        return
      }
    }

    // 关闭对话框
    dialogVisible.value = false

    // 重新加载数据
    loadData()
  } catch (error: any) {
    console.error('保存布料组失败', error)
    ElMessage.error(error.response?.data?.message || '保存布料组失败')
  }
}

// 处理对话框取消
const handleDialogCancel = () => {
  dialogVisible.value = false
}

// 加载数据
const loadData = async (params = queryParams) => {
  loading.value = true
  try {
    // 构建查询参数，确保正确传递给后端
    const queryParams = {
      fabric_group_years: params.fabric_group_years,
      suppliers: params.suppliers,
      classifications: params.classifications,
      fabric_group_id: params.fabric_group_id,
      state: params.state,
      page: params.page,
      limit: params.limit,
    }

    const response = await getFabricGroupList(queryParams)

    // 确保 tableData 始终是数组
    if (response && typeof response === 'object') {
      if (response.data && typeof response.data === 'object') {
        // 如果响应格式是 { data: { data: [...], total: ... } }
        if ('data' in response.data && Array.isArray(response.data.data)) {
          tableData.value = response.data.data
          total.value = 'total' in response.data ? (response.data.total as number) : 0
        }
        // 如果响应格式是 { data: [...] }
        else if (Array.isArray(response.data)) {
          tableData.value = response.data
          total.value = 'total' in response ? (response.total as number) : response.data.length
        } else {
          tableData.value = []
          total.value = 0
        }
      } else {
        tableData.value = []
        total.value = 0
      }
    } else {
      tableData.value = []
      total.value = 0
    }

    // 如果没有数据，提示用户
    if (total.value === 0) {
      ElMessage.info('未查询到布料组数据，请尝试其他筛选条件或添加新数据')
    }
  } catch (error: any) {
    console.error('加载布料组数据失败', error)
    ElMessage.error(error.response?.data?.message || '加载布料组数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 通用的更新选项方法
const updateOptions = async (changedSelector: 'year' | 'supplier' | 'classification') => {
  // 准备参数
  const yearsStr = queryParams.fabric_group_years?.length
    ? queryParams.fabric_group_years.join(',')
    : undefined
  const suppliersStr = queryParams.suppliers?.length ? queryParams.suppliers.join(',') : undefined
  const classificationsStr = queryParams.classifications?.length
    ? queryParams.classifications.join(',')
    : undefined

  try {
    // 更新年份选项
    const yearsResponse = await getFabricGroupYearOptions(suppliersStr, classificationsStr)
    const newYearOptions = extractData<string[]>(yearsResponse, [])

    // 更新年份选项
    yearOptions.value = newYearOptions

    // 如果当前选择的年份不在新的选项中，且不是年份变化触发的，清空选择
    if (
      changedSelector !== 'year' &&
      queryParams.fabric_group_years?.length &&
      !queryParams.fabric_group_years.every((year) => newYearOptions.includes(year))
    ) {
      queryParams.fabric_group_years = []
    }

    // 更新供应商选项
    const suppliersResponse = await getFabricGroupSupplierOptions(yearsStr, classificationsStr)
    const newSupplierOptions = extractData<string[]>(suppliersResponse, [])

    // 更新供应商选项
    supplierOptions.value = newSupplierOptions

    // 如果当前选择的供应商不在新的选项中，且不是供应商变化触发的，清空选择
    if (
      changedSelector !== 'supplier' &&
      queryParams.suppliers?.length &&
      !queryParams.suppliers.every((supplier) => newSupplierOptions.includes(supplier))
    ) {
      queryParams.suppliers = []
    }

    // 更新分类选项
    const classificationsResponse = await getFabricGroupClassificationOptions(
      yearsStr,
      suppliersStr
    )
    const newClassificationOptions = extractData<string[]>(classificationsResponse, [])

    // 更新分类选项
    classificationOptions.value = newClassificationOptions

    // 如果当前选择的分类不在新的选项中，且不是分类变化触发的，清空选择
    if (
      changedSelector !== 'classification' &&
      queryParams.classifications?.length &&
      !queryParams.classifications.every((classification) =>
        newClassificationOptions.includes(classification)
      )
    ) {
      queryParams.classifications = []
    }
  } catch (error) {
    console.error('更新选项失败', error)
    ElMessage.error('更新选项失败')
  }
}

// 年份变化
const handleYearChange = async () => {
  // 更新其他选择器的选项
  await updateOptions('year')

  // 触发查询
  queryParams.page = 1
  loadData()
}

// 供应商变化
const handleSupplierChange = async () => {
  // 更新其他选择器的选项
  await updateOptions('supplier')

  // 触发查询
  queryParams.page = 1
  loadData()
}

// 分类变化
const handleClassificationChange = async () => {
  // 更新其他选择器的选项
  await updateOptions('classification')

  // 触发查询
  queryParams.page = 1
  loadData()
}

// 查询
const handleQuery = () => {
  queryParams.page = 1
  loadData()
}

// 重置查询
const handleReset = async () => {
  // 清空查询参数
  queryParams.fabric_group_id = ''
  queryParams.state = ''
  queryParams.page = 1

  // 加载初始选项（会清空所有选择并加载所有选项）
  await loadInitialOptions()

  // 加载数据
  loadData()
}

// 分页大小变化
const handleSizeChange = (val: number) => {
  queryParams.limit = val
  loadData()
}

// 当前页变化
const handleCurrentChange = (val: number) => {
  queryParams.page = val
  loadData()
}

// 新增
const handleAdd = () => {
  dialogType.value = 'add'
  currentEditRow.value = null
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: FabricGroup) => {
  dialogType.value = 'edit'
  // 创建一个深拷贝，确保每次编辑时都使用新的对象引用
  currentEditRow.value = JSON.parse(JSON.stringify(row))
  // 先设置数据，再打开弹窗
  setTimeout(() => {
    dialogVisible.value = true
  }, 0)
}

// 删除
const handleDelete = (row: FabricGroup) => {
  ElMessageBox.confirm(`确定要删除布料组 ${row.fabric_group_id} 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        await deleteFabricGroup(row._id)
        ElMessage.success('删除成功')
        loadData()
      } catch (error: any) {
        console.error('删除布料组失败', error)
        ElMessage.error(error.response?.data?.message || '删除布料组失败')
      }
    })
    .catch(() => {
      // 取消删除
    })
}

// 获取状态标签类型
const getStateTagType = (state: string): '' | 'success' | 'warning' | 'danger' | 'info' => {
  switch (state) {
    case '待排单':
      return 'info'
    case '排单完成':
      return 'warning'
    case '裁剪完成':
      return 'danger'
    case '归档':
      return 'success'
    default:
      return ''
  }
}

// 处理不干胶下单
const handleStickerOrder = () => {
  // 打开不干胶下单对话框
  stickerOrderDialogVisible.value = true
}

// 处理不干胶下单成功
const handleStickerOrderSuccess = () => {
  // 重新加载数据
  loadData()
}
</script>

<style scoped>
.form-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.form-row {
  display: flex;
  gap: 20px;
}

.form-row .el-form-item {
  flex: 1;
}

.form-row .full-width {
  flex: 1;
  width: 100%;
}

.custom-input {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.fabric-group-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.fabric-group-form :deep(.el-form-item__label) {
  font-weight: bold;
}

/* 布料卡片样式 */
.fabric-cards-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  padding: 2px;
}

.fabric-card {
  position: relative;
  width: 90px;
  height: 35px;
  background-color: #f8f9fa;
  border-radius: 3px;
  border: 1px solid #e9ecef;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.03);
}

.fabric-name {
  font-size: 12px;
  font-weight: 500;
  color: #495057;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 90%;
  padding: 0 2px;
  line-height: 1.2;
}

.in-quantity {
  font-size: 12px;
  font-weight: 600;
  color: #2b8a3e;
  text-align: center;
  padding: 0 2px;
  line-height: 1.2;
  margin-top: 1px;
}
</style>
