<template>
  <PageTemplate
    :queryParams="queryParams"
    :tableData="tableData"
    :total="total"
    :loading="loading"
    :tableHeight="650"
    :row-class-name="tableRowClassName"
    @query="handleQuery"
    @reset="handleReset"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  >
    <!-- 查询区域左侧 -->
    <template #query-form-left>
      <el-form-item label="所属年份" prop="clothing_years">
        <el-select
          v-model="queryParams.clothing_years"
          placeholder="选择年份"
          size="small"
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          style="width: 120px"
          @change="handleYearChange"
        >
          <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
        </el-select>
      </el-form-item>
      <el-form-item label="供应商" prop="suppliers">
        <el-select
          v-model="queryParams.suppliers"
          placeholder="选择供应商"
          size="small"
          filterable
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          style="width: 160px"
          @change="handleSupplierChange"
        >
          <el-option v-for="item in supplierOptions" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="分类" prop="classifications">
        <el-select
          v-model="queryParams.classifications"
          placeholder="选择分类"
          size="small"
          filterable
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          style="width: 220px"
          @change="handleClassificationChange"
        >
          <el-option
            v-for="item in classificationOptions"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="服装名称" prop="clothing_name">
        <el-input
          v-model="queryParams.clothing_name"
          placeholder="输入名称"
          size="small"
          clearable
          style="width: 100px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="state">
        <el-select
          v-model="queryParams.state"
          placeholder="选择状态"
          size="small"
          clearable
          style="width: 100px"
        >
          <el-option label="未完成" value="0" />
          <el-option label="已完成" value="1" />
        </el-select>
      </el-form-item>
    </template>

    <!-- 查询区域右侧 -->
    <template #query-form-right>
      <el-button type="warning" @click="handleDataCalibration">数据校准</el-button>
      <el-button type="primary" @click="handleAdd">新增</el-button>
    </template>

    <!-- 表格列 -->
    <template #pagination-left>
      <div class="table-tools">
        <el-popover placement="bottom" :width="300" trigger="click">
          <template #reference>
            <el-button type="primary" plain size="small">列设置</el-button>
          </template>
          <el-checkbox-group v-model="visibleColumns">
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px">
              <el-checkbox v-for="col in allColumns" :key="col.value" :value="col.value">
                {{ col.label }}
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </el-popover>

        <el-upload
          class="upload-button"
          :action="null"
          :auto-upload="false"
          :on-change="handleFileChange"
          accept=".xlsx,.xls"
          :show-file-list="false"
        >
          <el-button type="success" plain size="small">Excel导入</el-button>
        </el-upload>

        <el-button type="warning" plain size="small" @click="handleExport">Excel导出</el-button>
      </div>
    </template>

    <template #table-columns>
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column
        prop="clothing_year"
        label="所属年份"
        width="100"
        align="center"
        v-if="visibleColumns.includes('clothing_year')"
      />
      <el-table-column
        prop="clipping_year"
        label="裁剪年份"
        width="100"
        align="center"
        v-if="visibleColumns.includes('clipping_year')"
      />
      <el-table-column
        prop="make_year"
        label="缝制年份"
        width="100"
        align="center"
        v-if="visibleColumns.includes('make_year')"
      />
      <el-table-column
        prop="clothing_id"
        label="服装编号"
        width="120"
        align="center"
        v-if="visibleColumns.includes('clothing_id')"
      />
      <el-table-column
        prop="clothing_name"
        label="服装名称"
        width="100"
        align="center"
        v-if="visibleColumns.includes('clothing_name')"
      />
      <el-table-column
        prop="supplier"
        label="供应商"
        width="120"
        align="center"
        v-if="visibleColumns.includes('supplier')"
      />
      <el-table-column
        label="分类"
        width="150"
        align="center"
        v-if="visibleColumns.includes('group_classification')"
      >
        <template #default="scope">
          <span>{{
            scope.row.group_classification ? scope.row.group_classification.join(', ') : ''
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="long_or_short_sleeve"
        label="袖长"
        width="80"
        align="center"
        v-if="visibleColumns.includes('long_or_short_sleeve')"
      />
      <el-table-column
        prop="size"
        label="尺码"
        width="100"
        align="center"
        v-if="visibleColumns.includes('size')"
      />
      <el-table-column
        prop="style"
        label="款式"
        width="130"
        align="center"
        v-if="visibleColumns.includes('style')"
      />
      <el-table-column
        prop="pocket_type"
        label="口袋类型"
        width="150"
        align="center"
        v-if="visibleColumns.includes('pocket_type')"
      />
      <el-table-column
        prop="fabric_group_id"
        label="布料组"
        width="120"
        align="center"
        v-if="visibleColumns.includes('fabric_group_id')"
      />
      <el-table-column
        prop="order_quantity"
        label="订单数量"
        width="100"
        align="center"
        v-if="visibleColumns.includes('order_quantity')"
      />
      <el-table-column
        prop="clipping_pcs"
        label="裁剪数"
        width="100"
        align="center"
        v-if="visibleColumns.includes('clipping_pcs')"
      />
      <el-table-column
        prop="fabric_usage_per_clothing"
        label="单件用料"
        width="100"
        align="center"
        v-if="visibleColumns.includes('fabric_usage_per_clothing')"
      />
      <el-table-column
        prop="total_material_cost"
        label="物料成本"
        width="100"
        align="center"
        v-if="visibleColumns.includes('total_material_cost')"
      >
        <template #default="{ row }">
          <span>{{ row.total_material_cost ? `¥${row.total_material_cost.toFixed(2)}` : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="printed"
        label="印花"
        width="80"
        align="center"
        v-if="visibleColumns.includes('printed')"
      />
      <el-table-column
        prop="shipments"
        label="出货数"
        width="80"
        align="center"
        v-if="visibleColumns.includes('shipments')"
      >
        <template #default="{ row }">
          <el-popover
            placement="top"
            :width="200"
            trigger="click"
            popper-class="out-quantity-popover"
          >
            <template #reference>
              <span class="clickable-cell" @click="handleViewOutQuantityDetail(row)">
                {{ row.shipments }}
              </span>
            </template>
            <template #default>
              <div v-loading="outDetailLoading">
                <el-table :data="outQuantityDetails" stripe size="small" style="width: 100%">
                  <el-table-column prop="date" label="出库日期" width="100" />
                  <el-table-column prop="quantity" label="数量" width="60" align="right" />
                </el-table>
              </div>
            </template>
          </el-popover>
        </template>
      </el-table-column>

      <el-table-column
        prop="state"
        label="状态"
        width="90"
        align="center"
        v-if="visibleColumns.includes('state')"
      >
        <template #default="scope">
          <el-tag :type="scope.row.state === '1' ? 'success' : 'info'">
            {{ scope.row.state === '1' ? '已完成' : '未完成' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="memo"
        label="备注"
        min-width="150"
        align="center"
        v-if="visibleColumns.includes('memo')"
      />
      <el-table-column label="操作" width="150" align="center" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </template>
  </PageTemplate>

  <!-- 服装编辑对话框 -->
  <ClothingDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :clothing="currentClothing"
    @success="handleDialogSuccess"
  />

  <!-- 导入对话框 -->
  <ClothingImport
    v-model:visible="importDialogVisible"
    :file="importFile"
    @import-success="handleImportSuccess"
  />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
// 导入通用页面模板
import PageTemplate from '@/components/common/PageTemplate.vue'
// 导入服装对话框组件
import ClothingDialog from './components/ClothingDialog.vue'
// 导入服装导入组件
import ClothingImport from './components/ClothingImport.vue'
// 导入API
import {
  getClothingList,
  deleteClothing,
  getClothingYearOptions,
  getSupplierOptions,
  getClassificationOptions,
  calibrateClothingData,
} from '@/api/clothing'
import { getTransportationDetailsByClothingId } from '@/api/transportation'
// 导入类型定义
import type { Clothing, QueryClothingParams, ClothingListResponse } from '@/types/clothing'
// 引入xlsx库
import * as XLSX from 'xlsx'

// 加载状态
const loading = ref(false)

// 表格数据
const tableData = ref<Clothing[]>([])

// 总数
const total = ref(0)

// 出货明细相关
const outDetailLoading = ref(false)
const outQuantityDetails = ref<any[]>([])

// 查询参数
const queryParams = reactive<QueryClothingParams>({
  page: 1,
  limit: 10,
  clothing_years: [],
  clothing_name: '',
  supplier: '',
  suppliers: [],
  classification: '',
  classifications: [],
  state: '',
})

// 年份选项
const yearOptions = ref<string[]>([])
// 供应商选项
const supplierOptions = ref<string[]>([])
// 分类选项
const classificationOptions = ref<string[]>([])

// 列设置相关
const allColumns = [
  { label: '所属年份', value: 'clothing_year' },
  { label: '裁剪年份', value: 'clipping_year' },
  { label: '缝制年份', value: 'make_year' },
  { label: '服装编号', value: 'clothing_id' },
  { label: '服装名称', value: 'clothing_name' },
  { label: '供应商', value: 'supplier' },
  { label: '分类', value: 'group_classification' },
  { label: '袖长', value: 'long_or_short_sleeve' },
  { label: '尺码', value: 'size' },
  { label: '款式', value: 'style' },
  { label: '口袋类型', value: 'pocket_type' },
  { label: '布料组', value: 'fabric_group_id' },
  { label: '订单数量', value: 'order_quantity' },
  { label: '裁剪件数', value: 'clipping_pcs' },
  { label: '单件用料', value: 'fabric_usage_per_clothing' },
  { label: '物料成本', value: 'total_material_cost' },
  { label: '印花', value: 'printed' },
  { label: '出货数量', value: 'shipments' },
  { label: '状态', value: 'state' },
  { label: '备注', value: 'memo' },
]
const visibleColumns = ref([
  'clothing_year',
  'clothing_id',
  'clothing_name',
  'supplier',
  'group_classification',
  'long_or_short_sleeve',
  'size',
  'style',
  'pocket_type',
  'fabric_group_id',
  'clipping_pcs',
  'shipments',
])

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('新增服装')
const currentClothing = ref<Partial<Clothing>>({})

// 导入对话框相关
const importDialogVisible = ref(false)
const importFile = ref<File | null>(null)

// 初始化
onMounted(async () => {
  // 加载年份选项
  await loadYearOptions()

  // 加载供应商选项
  await loadSupplierOptions()

  // 加载分类选项
  await loadClassificationOptions()

  // 加载数据
  await loadData()
})

// 加载年份选项
const loadYearOptions = async (suppliers?: string[], classifications?: string[]) => {
  try {
    const params: any = {}

    if (suppliers && suppliers.length > 0) {
      params.supplier = suppliers.join(',')
    }

    if (classifications && classifications.length > 0) {
      params.classification = classifications.join(',')
    }

    console.log('加载年份选项的参数：', params)

    const response = await getClothingYearOptions(params)
    // 检查响应格式
    if (response && response.data && Array.isArray(response.data)) {
      yearOptions.value = response.data || []
    } else {
      console.error('响应格式不正确', response)
      yearOptions.value = []
    }
  } catch (error: any) {
    console.error('加载年份选项失败', error)
    ElMessage.error(error.response?.data?.message || '加载年份选项失败')
    yearOptions.value = []
  }
}

// 加载数据
const loadData = async (params: any = queryParams) => {
  loading.value = true
  try {
    // 处理多选参数
    const processedParams = { ...params }

    // 如果有suppliers多选参数，将其转换为supplier字符串
    if (processedParams.suppliers && processedParams.suppliers.length > 0) {
      processedParams.supplier = processedParams.suppliers.join(',')
      delete processedParams.suppliers
    }

    // 如果有classifications多选参数，将其转换为classification字符串
    if (processedParams.classifications && processedParams.classifications.length > 0) {
      processedParams.classification = processedParams.classifications.join(',')
      delete processedParams.classifications
    }

    // 处理clothing_years多选参数
    if (processedParams.clothing_years && processedParams.clothing_years.length > 0) {
      // 保留clothing_years数组，确保它作为数组传递
      console.log('使用clothing_years筛选:', processedParams.clothing_years)

      // 确保clothing_years作为数组传递，而不是clothing_years[]
      const yearsArray = [...processedParams.clothing_years]
      delete processedParams.clothing_years
      processedParams.clothing_years = yearsArray
    } else {
      // 如果clothing_years为空数组，删除它以避免后端处理错误
      delete processedParams.clothing_years
    }

    console.log('处理后的查询参数：', processedParams)

    const response = await getClothingList(processedParams)
    console.log('获取到的服装数据：', response)
    // 检查响应格式
    if (response && response.data) {
      const listResponse = response.data as unknown as ClothingListResponse
      tableData.value = listResponse.clothingList || []
      total.value = listResponse.total || 0

      // 如果没有数据，提示用户
      if (listResponse.total === 0) {
        ElMessage.info('未查询到服装数据，请尝试其他筛选条件或添加新数据')
      }
    } else {
      console.error('响应格式不正确', response)
      tableData.value = []
      total.value = 0
    }
  } catch (error: any) {
    console.error('加载服装数据失败', error)
    ElMessage.error(error.response?.data?.message || '加载服装数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  queryParams.page = 1
  loadData()
}

// 重置
const handleReset = () => {
  queryParams.page = 1
  queryParams.clothing_years = []
  queryParams.clothing_name = ''
  queryParams.supplier = ''
  queryParams.suppliers = []
  queryParams.classification = ''
  queryParams.classifications = []
  queryParams.state = ''
  loadData()
}

// 加载供应商选项
const loadSupplierOptions = async (year?: string, classifications?: string[]) => {
  try {
    const params: any = {}

    if (year) {
      params.year = year
    } else if (queryParams.clothing_years && queryParams.clothing_years.length > 0) {
      params.year = queryParams.clothing_years.join(',')
    }

    if (classifications && classifications.length > 0) {
      params.classification = classifications.join(',')
    } else if (queryParams.classifications && queryParams.classifications.length > 0) {
      params.classification = queryParams.classifications.join(',')
    }

    const response = await getSupplierOptions(params)
    if (response && response.data && Array.isArray(response.data)) {
      supplierOptions.value = response.data
    } else {
      console.error('供应商选项响应格式不正确', response)
      supplierOptions.value = []
    }
  } catch (error: any) {
    console.error('加载供应商选项失败', error)
    ElMessage.error(error.response?.data?.message || '加载供应商选项失败')
    supplierOptions.value = []
  }
}

// 加载分类选项
const loadClassificationOptions = async (year?: string, suppliers?: string[]) => {
  try {
    const params: any = {}

    if (year) {
      params.year = year
    } else if (queryParams.clothing_years && queryParams.clothing_years.length > 0) {
      params.year = queryParams.clothing_years.join(',')
    }

    if (suppliers && suppliers.length > 0) {
      params.supplier = suppliers.join(',')
    } else if (queryParams.suppliers && queryParams.suppliers.length > 0) {
      params.supplier = queryParams.suppliers.join(',')
    }

    const response = await getClassificationOptions(params)
    if (response && response.data && Array.isArray(response.data)) {
      classificationOptions.value = response.data
    } else {
      console.error('分类选项响应格式不正确', response)
      classificationOptions.value = []
    }
  } catch (error: any) {
    console.error('加载分类选项失败', error)
    ElMessage.error(error.response?.data?.message || '加载分类选项失败')
    classificationOptions.value = []
  }
}

// 加载年份选项（根据供应商和分类）
const loadYearOptionsByFilters = async (suppliers?: string[], classifications?: string[]) => {
  try {
    console.log('根据筛选条件加载年份选项', { suppliers, classifications })
    // 直接调用 loadYearOptions 并传入供应商和分类参数
    await loadYearOptions(suppliers, classifications)
  } catch (error: any) {
    console.error('根据筛选条件加载年份选项失败', error)
  }
}

// 年份变化
const handleYearChange = () => {
  // 根据年份加载供应商和分类选项
  loadSupplierOptions()
  loadClassificationOptions()

  // 清空供应商和分类选择
  queryParams.suppliers = []
  queryParams.classifications = []

  // 自动查询
  handleQuery()
}

// 供应商变化
const handleSupplierChange = () => {
  // 根据供应商加载分类选项
  loadClassificationOptions(undefined, queryParams.suppliers)

  // 根据供应商加载年份选项
  loadYearOptionsByFilters(queryParams.suppliers, queryParams.classifications)

  // 清空分类选择
  queryParams.classifications = []

  // 自动查询
  handleQuery()
}

// 分类变化
const handleClassificationChange = () => {
  // 根据分类加载年份选项
  loadYearOptionsByFilters(queryParams.suppliers, queryParams.classifications)
  loadSupplierOptions()
  // 自动查询
  handleQuery()
}

// 分页大小变化
const handleSizeChange = (val: number) => {
  queryParams.limit = val
  loadData()
}

// 当前页变化
const handleCurrentChange = (val: number) => {
  queryParams.page = val
  loadData()
}

// 数据校准
const handleDataCalibration = async () => {
  ElMessageBox.confirm(
    '数据校准将重新统计当前页面显示的本厂服装的入库数和出库数，这可能需要一些时间。确认继续吗？',
    '数据校准确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      const loadingInstance = ElLoading.service({
        lock: true,
        text: '正在校准数据，请稍候...',
        background: 'rgba(0, 0, 0, 0.7)',
      })

      try {
        // 准备校准数据，传递当前查询参数
        const calibrateData = {
          queryParams: { ...queryParams }
        }

        console.log('本厂服装数据校准参数:', calibrateData)

        const response = await calibrateClothingData(calibrateData)
        console.log('数据校准响应:', response)

        ElMessage.success(`数据校准完成！已更新 ${(response as any).data?.updated || 0} 条记录`)

        // 刷新当前页面数据
        await loadData()
      } catch (error) {
        console.error('数据校准失败:', error)
        ElMessage.error('数据校准失败，请重试')
      } finally {
        loadingInstance.close()
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增服装'
  currentClothing.value = {
    clothing_year: yearOptions.value[0] || '',
    state: '0',
  }
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: Clothing) => {
  dialogTitle.value = '编辑服装'
  currentClothing.value = { ...row }
  dialogVisible.value = true
}

// 删除
const handleDelete = (row: Clothing) => {
  ElMessageBox.confirm(`确定要删除服装 ${row.clothing_name} 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const response = await deleteClothing(row._id || '')
        if (response && response.data) {
          const deleteResponse = response.data as unknown as { success: boolean; message: string }
          if (deleteResponse.success) {
            ElMessage.success('删除成功')
            loadData()
          } else {
            ElMessage.error(deleteResponse.message || '删除服装失败')
          }
        } else {
          ElMessage.error('删除服装失败')
        }
      } catch (error: any) {
        console.error('删除服装失败', error)
        ElMessage.error(error.response?.data?.message || '删除服装失败')
      }
    })
    .catch(() => {
      // 取消删除
    })
}

// 对话框成功回调
const handleDialogSuccess = () => {
  loadData()
}

// 导出
const handleExport = () => {
  ElMessageBox.confirm('确定要导出当前筛选条件下的服装数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info',
  })
    .then(async () => {
      try {
        loading.value = true
        // 获取所有数据（不分页）
        const exportParams = { ...queryParams, page: 1, limit: 10000 }
        console.log('导出查询参数:', exportParams)
        const response = await getClothingList(exportParams)

        if (response && response.data) {
          const listResponse = response.data as unknown as ClothingListResponse
          const data = listResponse.clothingList || []

          if (data.length === 0) {
            ElMessage.warning('没有数据可导出')
            return
          }

          // 准备Excel数据
          const exportData = data.map((item: any) => ({
            所属年份: item.clothing_year,
            裁剪年份: item.clipping_year || '',
            制作年份: item.make_year || '',
            服装编号: item.clothing_id,
            服装名称: item.clothing_name,
            供应商: item.supplier || '',
            分类: item.group_classification?.join(',') || '',
            袖长: item.long_or_short_sleeve || '',
            尺码: item.size || '',
            款式: item.style || '',
            口袋类型: item.pocket_type || '',
            布料组: item.fabric_group_id || '',
            订单数量: item.order_quantity || 0,
            裁剪件数: item.clipping_pcs || 0,
            单件用料: item.fabric_usage_per_clothing || 0,
            物料成本: item.total_material_cost || 0,
            印花: item.printed || '',
            出货数量: item.shipments || 0,
            状态: item.state === '1' ? '已完成' : '未完成',
            备注: item.memo || '',
          }))

          // 创建工作簿
          const worksheet = XLSX.utils.json_to_sheet(exportData)
          const workbook = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(workbook, worksheet, '服装信息')

          // 导出Excel
          XLSX.writeFile(workbook, `服装信息_${new Date().toISOString().split('T')[0]}.xlsx`)
          ElMessage.success('导出成功')
        } else {
          console.error('响应格式不正确', response)
          ElMessage.error('导出服装数据失败')
        }
      } catch (error: any) {
        console.error('导出服装数据失败', error)
        ElMessage.error(error.response?.data?.message || '导出服装数据失败')
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      // 取消导出
    })
}

// 文件变更
const handleFileChange = async (file: any) => {
  if (file.raw) {
    // 设置文件
    importFile.value = file.raw
    // 显示导入对话框
    importDialogVisible.value = true
    console.log('文件已设置:', importFile.value)
  }
}

// 导入成功回调
const handleImportSuccess = () => {
  // 重新加载数据
  loadData()
}

// 表格行类名
const tableRowClassName = ({ row }: { row: Clothing }) => {
  // 如果行数据有图片，返回 has-image 类名
  if (row.img && row.img.length > 0) {
    return 'has-image'
  }
  return ''
}

// 查看出货明细
const handleViewOutQuantityDetail = async (row: Clothing) => {
  if (!row || !row.clothing_id) {
    ElMessage.warning('无法获取服装信息')
    return
  }

  outDetailLoading.value = true

  try {
    // 使用真实 API 获取服装出货明细数据
    const response = await getTransportationDetailsByClothingId(row.clothing_id)
    console.log('获取出货明细响应:', response)

    // 处理响应数据
    if (response && Array.isArray(response) && response.length > 0) {
      // 过滤出普通服装的出货记录（非OEM服装）
      const clothingDetails = response.filter((item: any) => item.oem !== '是')

      // 将后端数据转换为前端显示所需的格式
      outQuantityDetails.value = clothingDetails.map((item: any) => ({
        date: item.transportation.date_out
          ? new Date(item.transportation.date_out).toISOString().split('T')[0]
          : '-',
        quantity: item.out_pcs || 0,
      }))

      if (clothingDetails.length === 0) {
        ElMessage.info('没有找到出货明细数据')
      }
    } else {
      outQuantityDetails.value = []
      ElMessage.info('没有找到出货明细数据')
    }
  } catch (error) {
    ElMessage.error('获取出货明细数据失败')
    outQuantityDetails.value = []
  } finally {
    outDetailLoading.value = false
  }
}
</script>

<style scoped>
.preview-container {
  margin-top: 20px;
  max-height: 400px;
}

.table-tools {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-button {
  margin: 0;
  display: inline-block;
}

/* 为有图片的行添加浅绿色背景 */
:deep(.has-image) {
  background-color: #d5f1c6 !important;
}

/* 可点击单元格样式 */
.clickable-cell {
  color: #333;
  cursor: pointer;
}

/* 出货明细弹窗样式 */
:deep(.out-quantity-popover) {
  padding: 0;
}
</style>
