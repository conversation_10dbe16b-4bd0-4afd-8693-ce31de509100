<template>
  <!-- 导入对话框 - 简化版本 -->
  <el-dialog
    v-model="dialogVisible"
    title="Excel导入"
    width="80%"
    destroy-on-close
    class="import-dialog"
  >
    <div class="import-container">
      <!-- 预览区 -->
      <div class="preview-section">
        <div class="file-info" v-if="importFile">
          <el-tag type="success" effect="plain" class="file-tag">
            <el-icon><Document /></el-icon>
            {{ importFile.name }}
          </el-tag>
          <span v-if="selectedSheet" class="sheet-info">
            工作表: <el-tag size="small" type="info">{{ selectedSheet }}</el-tag>
          </span>
          <el-button type="danger" link @click="clearImportFile" class="clear-btn">清除</el-button>
        </div>

        <!-- 预览表格 -->
        <div class="preview-table-container">
          <el-empty v-if="!importFile || previewData.length === 0" description="请选择Excel文件">
            <template #extra>
              <el-upload
                class="excel-uploader"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handleFileChange"
                accept=".xlsx,.xls"
              >
                <el-button type="primary">
                  <el-icon><UploadFilled /></el-icon>
                  选择文件
                </el-button>
              </el-upload>
            </template>
          </el-empty>

          <!-- 手动选择文件按钮 -->
          <div v-if="importFile && previewData.length === 0" class="manual-upload">
            <el-alert type="warning" :closable="false">
              <template #default>
                <div>无法读取文件数据，请尝试重新选择文件</div>
              </template>
            </el-alert>
            <el-upload
              class="excel-uploader"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleFileChange"
              accept=".xlsx,.xls"
            >
              <el-button type="primary" style="margin-top: 10px">
                <el-icon><UploadFilled /></el-icon>
                重新选择文件
              </el-button>
            </el-upload>
          </div>
          <el-table
            v-else
            v-loading="previewLoading"
            :data="previewData"
            border
            stripe
            style="width: 100%"
            max-height="500px"
            size="small"
          >
            <el-table-column
              v-for="column in previewColumns"
              :key="column"
              :prop="column"
              :label="column"
              show-overflow-tooltip
              min-width="120"
            />
          </el-table>

          <div v-if="previewData.length > 0" class="preview-info">
            <el-alert type="info" :closable="false">
              <template #default>
                <div class="preview-stats">
                  <span>当前显示: 前 {{ Math.min(previewData.length, 100) }} 条数据</span>
                  <span>总记录数: {{ totalRecords }}</span>
                </div>
              </template>
            </el-alert>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button
          type="primary"
          @click="handleImport"
          :loading="importLoading"
          :disabled="!importFile || previewLoading || previewData.length === 0"
        >
          确认导入
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, UploadFilled } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import { importJsonData } from '@/api/clothing'
import type { ImportResponse } from '@/types/clothing'

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  file: {
    type: [File, Object, null],
    default: null,
    required: false,
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'import-success'])

// 对话框可见性
const dialogVisible = ref(props.visible)

// 监听 visible 属性变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
  }
)

// 监听对话框可见性变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
  // 如果对话框关闭，清除导入文件和相关数据
  if (!newVal) {
    clearImportFile()
  }
})

// 监听文件属性变化
watch(
  () => props.file,
  (newFile) => {
    if (newFile && newFile instanceof File) {
      // 清除之前的数据
      clearImportFile()
      // 设置新文件
      importFile.value = newFile
      // 读取Excel文件的工作表列表并自动选择服装信息sheet
      readExcelSheets(newFile)
    }
  },
  { immediate: true }
)

// 导入相关状态
const importFile = ref<File | null>(null)
const previewData = ref<any[]>([])
const previewColumns = ref<string[]>([])
const previewLoading = ref(false)
const importLoading = ref(false)
const sheetNames = ref<string[]>([])
const selectedSheet = ref('')
const totalRecords = ref(0)

// 清除导入文件
const clearImportFile = () => {
  importFile.value = null
  previewData.value = []
  previewColumns.value = []
  sheetNames.value = []
  selectedSheet.value = ''
  totalRecords.value = 0
}

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false
}

// 文件变更
const handleFileChange = async (file: any) => {
  if (file.raw) {
    // 清除之前的数据
    clearImportFile()

    // 设置新文件
    importFile.value = file.raw

    // 读取Excel文件的工作表列表并自动选择服装信息sheet
    await readExcelSheets(file.raw)
  }
}

// 读取Excel工作表列表
const readExcelSheets = async (file: File) => {
  previewLoading.value = true
  try {
    // 使用FileReader读取文件
    const reader = new FileReader()

    reader.onload = (e: any) => {
      try {
        // 解析Excel数据
        const data = new Uint8Array(e.target.result)
        const workbook = XLSX.read(data, { type: 'array' })

        // 获取所有工作表名称
        sheetNames.value = workbook.SheetNames

        // 如果有工作表，自动查找并选择"服装信息"工作表
        if (sheetNames.value.length > 0) {
          // 尝试查找名为"服装信息"的工作表
          const clothingInfoSheet = sheetNames.value.find((name) => name.includes('服装信息'))

          if (clothingInfoSheet) {
            // 如果找到了服装信息工作表，选择它
            selectedSheet.value = clothingInfoSheet
          } else {
            // 否则选择第一个工作表
            selectedSheet.value = sheetNames.value[0]
          }

          // 预览选中的工作表
          handlePreviewExcel(file, selectedSheet.value)
        } else {
          ElMessage.warning('Excel文件中没有工作表')
          previewLoading.value = false
        }
      } catch (error) {
        console.error('解析Excel工作表失败', error)
        ElMessage.error('解析Excel文件失败，请检查文件格式')
        previewLoading.value = false
      }
    }

    reader.onerror = () => {
      ElMessage.error('读取文件失败')
      previewLoading.value = false
    }

    // 读取文件
    reader.readAsArrayBuffer(file)
  } catch (error: any) {
    console.error('读取Excel工作表失败', error)
    ElMessage.error(error.message || '读取Excel工作表失败')
    previewLoading.value = false
  }
}

// 预览Excel
const handlePreviewExcel = async (file: File, sheetName: string) => {
  previewLoading.value = true
  try {
    // 使用FileReader读取文件
    const reader = new FileReader()

    reader.onload = (e: any) => {
      try {
        // 解析Excel数据
        const data = new Uint8Array(e.target.result)
        const workbook = XLSX.read(data, { type: 'array' })

        // 获取指定的工作表
        const worksheet = workbook.Sheets[sheetName]
        // 转换为JSON
        let jsonData = XLSX.utils.sheet_to_json(worksheet) as Record<string, any>[]
        // 过滤空白行
        jsonData = jsonData.filter((row) => {
          // 检查行是否为空白行（所有字段都为空或只有空格）
          const isEmpty = Object.values(row).every((value) => {
            if (value === undefined || value === null) return true
            if (typeof value === 'string' && value.trim() === '') return true
            return false
          })
          return !isEmpty // 返回非空白行
        })

        // 标准化数据字段名称
        jsonData = jsonData.map((item) => {
          // 将中文字段名映射到英文字段名
          const standardizedItem: Record<string, any> = {}

          // 遍历原始数据的所有字段
          Object.entries(item).forEach(([key, value]) => {
            // 根据字段名称进行映射
            if (key.includes('所属年份') || key.includes('clothing_year')) {
              standardizedItem.clothing_year = value
            } else if (key.includes('裁剪年份') || key.includes('clipping_year')) {
              standardizedItem.clipping_year = value
            } else if (key.includes('制作年份') || key.includes('make_year')) {
              standardizedItem.make_year = value
            } else if (key.includes('服装编号') || key.includes('clothing_id')) {
              standardizedItem.clothing_id = value
            } else if (key.includes('服装名称') || key.includes('clothing_name')) {
              standardizedItem.clothing_name = value
            } else if (key.includes('袖长') || key.includes('long_or_short_sleeve')) {
              standardizedItem.long_or_short_sleeve = value
            } else if (key.includes('尺码') || key.includes('size')) {
              standardizedItem.size = value
            } else if (key.includes('款式') || key.includes('style')) {
              standardizedItem.style = value
            } else if (key.includes('口袋类型') || key.includes('pocket_type')) {
              standardizedItem.pocket_type = value
            } else if (key.includes('布料组') || key.includes('fabric_group_id')) {
              standardizedItem.fabric_group_id = value
            } else if (key.includes('订单数量') || key.includes('order_quantity')) {
              standardizedItem.order_quantity = Number(value) || 0
            } else if (key.includes('裁剪件数') || key.includes('clipping_pcs')) {
              standardizedItem.clipping_pcs = Number(value) || 0
            } else if (key.includes('单件用料') || key.includes('fabric_usage_per_clothing')) {
              standardizedItem.fabric_usage_per_clothing = Number(value) || 0
            } else if (key.includes('印花') || key.includes('printed')) {
              standardizedItem.printed = value
            } else if (key.includes('出货数') || key.includes('shipments')) {
              standardizedItem.shipments = Number(value) || 0
            } else if (key.includes('状态') || key.includes('state')) {
              standardizedItem.state = value === '已完成' ? '1' : '0'
            } else if (key.includes('备注') || key.includes('memo')) {
              standardizedItem.memo = value
            } else {
              // 其他字段保持原样
              standardizedItem[key] = value
            }
          })

          return standardizedItem
        })

        // 获取列名
        const columns = jsonData.length > 0 ? Object.keys(jsonData[0] as object) : []

        // 设置总记录数
        totalRecords.value = jsonData.length

        // 显示前100条数据
        previewData.value = jsonData.slice(0, 100)
        previewColumns.value = columns

        previewLoading.value = false
      } catch (error) {
        console.error('解析Excel失败', error)
        ElMessage.error('解析Excel文件失败，请检查文件格式')
        previewLoading.value = false
        previewData.value = []
        previewColumns.value = []
        totalRecords.value = 0
      }
    }

    reader.onerror = () => {
      ElMessage.error('读取文件失败')
      previewLoading.value = false
      previewData.value = []
      previewColumns.value = []
      totalRecords.value = 0
    }

    // 读取文件
    reader.readAsArrayBuffer(file)
  } catch (error: any) {
    console.error('预览Excel失败', error)
    ElMessage.error(error.message || '预览Excel失败')
    previewData.value = []
    previewColumns.value = []
    totalRecords.value = 0
    previewLoading.value = false
  }
}

// 导入Excel
const handleImport = async () => {
  if (!importFile.value || !selectedSheet.value) return

  // 显示确认对话框
  ElMessageBox.confirm(
    `确定要导入工作表 "${selectedSheet.value}" 中的数据吗？共 ${totalRecords.value} 条记录。`,
    '导入确认',
    {
      confirmButtonText: '确认导入',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      // 用户确认导入
      importLoading.value = true
      try {
        // 检查文件和预览数据是否存在
        if (!importFile.value || !selectedSheet.value || previewData.value.length === 0) {
          throw new Error('文件、工作表或预览数据不存在')
        }

        // 准备要发送给后端的数据
        const importData = {
          sheetName: selectedSheet.value,
          data: previewData.value, // 使用已经转换好的JSON数据
          fileName: importFile.value.name,
          totalRecords: totalRecords.value,
        }

        // 检查必填字段
        for (const item of importData.data) {
          const missingFields = []
          if (!item.clothing_year) missingFields.push('所属年份')
          if (!item.clothing_id) missingFields.push('服装编号')
          if (!item.clothing_name) missingFields.push('服装名称')

          if (missingFields.length > 0) {
            throw new Error(`数据缺少必填字段: ${missingFields.join(', ')}`)
          }
        }

        // 确保数据中不包含空白行
        if (importData.data.length === 0) {
          throw new Error('没有有效的数据可以导入')
        }

        // 调用API函数，将JSON数据发送给后端
        const response = await importJsonData(importData)
        // 从响应中提取导入结果
        const importResult = (response?.data?.data as ImportResponse) || { success: true, count: 0 }

        // 导入成功
        ElMessage.success(
          `导入成功${importResult.count ? `: 已导入${importResult.count}条数据` : ''}`
        )

        // 关闭对话框
        dialogVisible.value = false

        // 触发导入成功事件
        emit('import-success')
      } catch (error: any) {
        console.error('导入失败', error)
        ElMessage.error(error.response?.data?.message || error.message || '导入失败')
      } finally {
        importLoading.value = false
      }
    })
    .catch(() => {
      // 用户取消导入
      ElMessage.info('已取消导入操作')
    })
}
</script>

<style scoped>
/* 导入对话框样式 */
.import-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }

  .import-container {
    min-height: 500px;
    display: flex;
    flex-direction: column;
  }

  .preview-section {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .file-info {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    gap: 12px;
  }

  .file-tag {
    font-size: 14px;
  }

  .sheet-info {
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }

  .clear-btn {
    margin-left: auto;
  }

  .preview-table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .preview-info {
    margin-top: 12px;

    .preview-stats {
      display: flex;
      justify-content: space-between;
    }
  }

  .excel-uploader {
    margin: 16px 0;
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
}
</style>
