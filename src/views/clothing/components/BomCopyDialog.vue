<template>
  <el-dialog
    v-model="dialogVisible"
    title="复制物料清单"
    width="600px"
    :close-on-click-modal="false"
    @closed="resetDialog"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <!-- 年份选择器 -->
      <el-form-item label="年份" prop="year">
        <el-select
          v-model="form.year"
          placeholder="选择年份"
          style="width: 100%"
          @change="handleYearChange"
        >
          <el-option
            v-for="year in yearOptions"
            :key="year"
            :label="year"
            :value="year"
          />
        </el-select>
      </el-form-item>

      <!-- 供应商选择器 -->
      <el-form-item label="供应商" prop="supplier">
        <el-select
          v-model="form.supplier"
          placeholder="选择供应商"
          style="width: 100%"
          @change="handleSupplierChange"
        >
          <el-option
            v-for="supplier in supplierOptions"
            :key="supplier"
            :label="supplier"
            :value="supplier"
          />
        </el-select>
      </el-form-item>

      <!-- 分类选择器 -->
      <el-form-item label="分类" prop="classification">
        <el-select
          v-model="form.classification"
          placeholder="选择分类"
          style="width: 100%"
          @change="handleClassificationChange"
        >
          <el-option
            v-for="classification in classificationOptions"
            :key="classification"
            :label="classification"
            :value="classification"
          />
        </el-select>
      </el-form-item>

      <!-- 服装多选器 -->
      <el-form-item label="目标服装" prop="targetClothingIds">
        <el-select
          v-model="form.targetClothingIds"
          placeholder="选择目标服装（可多选）"
          multiple
          filterable
          style="width: 100%"
          :loading="loadingClothings"
        >
          <el-option
            v-for="clothing in filteredClothingOptions"
            :key="clothing.clothing_id"
            :label="`${clothing.clothing_id} - ${clothing.clothing_name}`"
            :value="clothing.clothing_id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="复制说明">
        <el-alert
          title="复制说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>• 根据筛选条件显示没有物料清单的服装</p>
            <p>• 将复制当前物料清单的所有物料项到所选服装</p>
            <p>• 复制后的物料清单状态为草稿，需要重新保存</p>
          </template>
        </el-alert>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="copying"
          @click="handleCopy"
          :disabled="!form.targetClothingIds || form.targetClothingIds.length === 0"
        >
          确定复制
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { copyBom } from '@/api/bom'
import { getClothingList, getClothingYearOptions, getSupplierOptions, getClassificationOptions } from '@/api/clothing'
import type { Clothing } from '@/types/clothing'

// Props
interface Props {
  modelValue: boolean
  sourceBomId: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const copying = ref(false)
const loadingClothings = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  year: '',
  supplier: '',
  classification: '',
  targetClothingIds: [] as string[],
})

// 选项数据
const yearOptions = ref<string[]>([])
const supplierOptions = ref<string[]>([])
const classificationOptions = ref<string[]>([])
const clothingOptions = ref<Clothing[]>([])

// 根据筛选条件过滤的服装选项（没有物料清单的服装）
const filteredClothingOptions = computed(() => {
  return clothingOptions.value.filter(() => {
    // 这里应该检查服装是否已有物料清单，暂时返回所有服装
    return true
  })
})

// 表单验证规则
const rules: FormRules = {
  year: [
    { required: true, message: '请选择年份', trigger: 'change' },
  ],
  supplier: [
    { required: true, message: '请选择供应商', trigger: 'change' },
  ],
  classification: [
    { required: true, message: '请选择分类', trigger: 'change' },
  ],
  targetClothingIds: [
    { required: true, message: '请选择目标服装', trigger: 'change' },
  ],
}

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val
    if (val) {
      loadYearOptions()
    }
  }
)

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 初始化
onMounted(() => {
  loadYearOptions()
})

// 加载服装选项（根据筛选条件）
const loadClothingOptions = async () => {
  if (!form.year || !form.supplier || !form.classification) return

  try {
    loadingClothings.value = true
    const res = await getClothingList({
      clothing_year: form.year,
      supplier: form.supplier,
      classification: form.classification,
      limit: 1000
    })
    if (res && res.data) {
      const responseData = res.data as any
      if (responseData.clothingList) {
        clothingOptions.value = responseData.clothingList
      } else if (Array.isArray(responseData)) {
        clothingOptions.value = responseData
      }
    }
  } catch (error) {
    console.error('加载服装选项失败:', error)
  } finally {
    loadingClothings.value = false
  }
}

// 加载年份选项
const loadYearOptions = async () => {
  try {
    const res = await getClothingYearOptions()
    if (res && res.data) {
      const responseData = res.data as any
      // 处理不同的响应格式
      if (responseData.data) {
        yearOptions.value = responseData.data
      } else if (Array.isArray(responseData)) {
        yearOptions.value = responseData
      }
    }
  } catch (error) {
    console.error('加载年份选项失败:', error)
  }
}

// 年份变化处理
const handleYearChange = () => {
  form.supplier = ''
  form.classification = ''
  form.targetClothingIds = []
  supplierOptions.value = []
  classificationOptions.value = []
  clothingOptions.value = []
  if (form.year) {
    loadSupplierOptions()
  }
}

// 供应商变化处理
const handleSupplierChange = () => {
  form.classification = ''
  form.targetClothingIds = []
  classificationOptions.value = []
  clothingOptions.value = []
  if (form.supplier) {
    loadClassificationOptions()
  }
}

// 分类变化处理
const handleClassificationChange = () => {
  form.targetClothingIds = []
  clothingOptions.value = []
  if (form.classification) {
    loadClothingOptions()
  }
}

// 加载供应商选项
const loadSupplierOptions = async () => {
  try {
    const res = await getSupplierOptions({ year: form.year })
    if (res && res.data && Array.isArray(res.data)) {
      supplierOptions.value = res.data
    }
  } catch (error) {
    console.error('加载供应商选项失败:', error)
  }
}

// 加载分类选项
const loadClassificationOptions = async () => {
  try {
    const res = await getClassificationOptions({ year: form.year, supplier: form.supplier })
    if (res && res.data && Array.isArray(res.data)) {
      classificationOptions.value = res.data
    }
  } catch (error) {
    console.error('加载分类选项失败:', error)
  }
}

// 执行复制
const handleCopy = async () => {
  if (!formRef.value) return

  const valid = await formRef.value.validate()
  if (!valid) return

  if (form.targetClothingIds.length === 0) {
    ElMessage.warning('请选择目标服装')
    return
  }

  try {
    copying.value = true

    console.log('开始复制物料清单，源BOM ID:', props.sourceBomId)
    console.log('目标服装列表:', form.targetClothingIds)
    console.log('目标年份:', form.year)

    // 批量复制到多个服装
    const promises = form.targetClothingIds.map(clothingId =>
      copyBom(props.sourceBomId, clothingId, form.year)
    )

    const results = await Promise.allSettled(promises)

    const successCount = results.filter(result => result.status === 'fulfilled').length
    const failCount = results.length - successCount

    // 记录失败的详细信息
    const failedResults = results.filter(result => result.status === 'rejected')
    if (failedResults.length > 0) {
      console.error('复制失败的详细信息:', failedResults.map(result => (result as PromiseRejectedResult).reason))
    }

    if (successCount > 0) {
      if (failCount > 0) {
        ElMessage.warning(`复制完成：成功 ${successCount} 个，失败 ${failCount} 个`)
      } else {
        ElMessage.success(`复制成功 ${successCount} 个`)
      }
      emit('success')
      dialogVisible.value = false
    } else {
      ElMessage.error('复制失败，请检查源物料清单是否存在')
    }
  } catch (error: any) {
    console.error('复制物料清单失败:', error)
    const errorMessage = error.response?.data?.message || error.message || '复制失败'
    ElMessage.error(errorMessage)
  } finally {
    copying.value = false
  }
}

// 重置对话框
const resetDialog = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }

  form.year = ''
  form.supplier = ''
  form.classification = ''
  form.targetClothingIds = []

  yearOptions.value = []
  supplierOptions.value = []
  classificationOptions.value = []
  clothingOptions.value = []

  copying.value = false
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-alert__content) {
  font-size: 12px;
}

:deep(.el-alert__content p) {
  margin: 2px 0;
}
</style>
