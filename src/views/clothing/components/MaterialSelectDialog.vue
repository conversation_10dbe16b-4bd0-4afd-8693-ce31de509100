<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择物料"
    width="800px"
    :close-on-click-modal="false"
    @closed="resetDialog"
  >
    <!-- 查询区域 -->
    <div class="query-section">
      <el-form :model="queryParams" inline size="small">
        <el-form-item label="物料编号">
          <el-input
            v-model="queryParams.material_id"
            placeholder="输入物料编号"
            clearable
            style="width: 150px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物料名称">
          <el-input
            v-model="queryParams.material_name"
            placeholder="输入物料名称"
            clearable
            style="width: 150px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="分类">
          <el-select
            v-model="queryParams.category_id"
            placeholder="选择分类"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="category in categoryOptions"
              :key="category.category_id"
              :label="category.category_name"
              :value="category.category_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 物料表格 -->
    <div class="table-section">
      <el-table
        ref="tableRef"
        :data="tableData"
        border
        size="small"
        max-height="400"
        @selection-change="handleSelectionChange"
        v-loading="loading"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="material_id" label="物料编号" width="120" align="center" />
        <el-table-column prop="material_name" label="物料名称" width="150" align="center" />
        <el-table-column prop="category_name" label="分类" width="120" align="center" />
        <el-table-column prop="specification" label="规格型号" width="120" align="center" />
        <el-table-column prop="unit" label="单位" width="80" align="center" />
        <el-table-column prop="current_price" label="当前价格" width="100" align="center">
          <template #default="{ row }">
            <span>{{ row.current_price ? `¥${row.current_price}` : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="150" align="center" />
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="queryParams.page"
        v-model:page-size="queryParams.limit"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <span class="selected-info">已选择 {{ selectedMaterials.length }} 个物料</span>
        <div>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :disabled="selectedMaterials.length === 0"
            @click="handleConfirm"
          >
            确定
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getMaterialList } from '@/api/material'
import { getMaterialCategoryOptions } from '@/api/materialCategory'
import type { Material, MaterialCategory } from '@/types/bom'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  confirm: [materials: Material[]]
}>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const tableRef = ref()

// 查询参数
const queryParams = reactive({
  material_id: '',
  material_name: '',
  category_id: '',
  page: 1,
  limit: 10,
})

// 表格数据
const tableData = ref<Material[]>([])
const total = ref(0)
const selectedMaterials = ref<Material[]>([])

// 分类选项
const categoryOptions = ref<MaterialCategory[]>([])

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val
    if (val) {
      fetchMaterialList()
    }
  }
)

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 初始化
onMounted(async () => {
  await loadCategoryOptions()
})

// 加载分类选项
const loadCategoryOptions = async () => {
  try {
    const res = await getMaterialCategoryOptions()
    if (res && res.data) {
      const responseData = res.data as any
      if (Array.isArray(responseData)) {
        categoryOptions.value = responseData
      } else if (responseData.categoryList) {
        categoryOptions.value = responseData.categoryList
      }
    }
  } catch (error) {
    console.error('加载分类选项失败:', error)
  }
}

// 获取物料列表
const fetchMaterialList = async () => {
  try {
    loading.value = true
    const res = await getMaterialList(queryParams)
    
    if (res && res.data) {
      const responseData = res.data as any
      // 检查是否有嵌套的data结构
      const actualData = responseData.data || responseData

      if (actualData.materialList) {
        tableData.value = actualData.materialList || []
        total.value = actualData.total || 0
      } else if (Array.isArray(actualData)) {
        tableData.value = actualData
        total.value = actualData.length
      } else {
        tableData.value = []
        total.value = 0
      }
    } else {
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取物料列表失败:', error)
    ElMessage.error('获取物料列表失败')
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  queryParams.page = 1
  fetchMaterialList()
}

// 重置
const handleReset = () => {
  queryParams.material_id = ''
  queryParams.material_name = ''
  queryParams.category_id = ''
  queryParams.page = 1
  fetchMaterialList()
}

// 每页数量变化
const handleSizeChange = (val: number) => {
  queryParams.limit = val
  fetchMaterialList()
}

// 页码变化
const handleCurrentChange = (val: number) => {
  queryParams.page = val
  fetchMaterialList()
}

// 选择变化
const handleSelectionChange = (selection: Material[]) => {
  selectedMaterials.value = selection
}

// 确认选择
const handleConfirm = () => {
  emit('confirm', selectedMaterials.value)
  dialogVisible.value = false
}

// 重置对话框
const resetDialog = () => {
  selectedMaterials.value = []
  queryParams.material_id = ''
  queryParams.material_name = ''
  queryParams.category_id = ''
  queryParams.page = 1
  
  if (tableRef.value) {
    tableRef.value.clearSelection()
  }
}
</script>

<style scoped>
.query-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.table-section {
  margin-bottom: 20px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-info {
  color: #909399;
  font-size: 14px;
}
</style>
