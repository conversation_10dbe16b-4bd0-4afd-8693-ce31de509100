<template>
  <!-- 导入对话框 -->
  <el-dialog
    v-model="dialogVisible"
    title="Excel导入"
    width="80%"
    destroy-on-close
    class="import-dialog"
  >
    <div class="import-container">
      <!-- 预览区 -->
      <div class="preview-section">
        <div class="file-info" v-if="importFile">
          <el-tag type="success" effect="plain" class="file-tag">
            <el-icon><Document /></el-icon>
            {{ importFile.name }}
          </el-tag>
          <span v-if="selectedSheet" class="sheet-info">
            工作表: <el-tag size="small" type="info">{{ selectedSheet }}</el-tag>
          </span>
          <el-button type="danger" link @click="clearImportFile" class="clear-btn">清除</el-button>
        </div>

        <div v-if="!importFile" class="upload-area">
          <el-upload
            class="excel-uploader"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleFileChange"
            accept=".xlsx,.xls"
          >
            <div class="upload-content">
              <el-icon class="upload-icon"><UploadFilled /></el-icon>
              <div class="upload-text">
                <span>点击或拖拽文件到此处上传</span>
                <p class="upload-hint">支持 .xlsx, .xls 格式</p>
              </div>
            </div>
          </el-upload>
        </div>

        <div v-else class="preview-area">
          <!-- 工作表选择 -->
          <div v-if="sheetNames.length > 1" class="sheet-selector">
            <span class="sheet-label" style="width: 100px">选择工作表:</span>
            <el-select v-model="selectedSheet" @change="handleSheetChange" size="small">
              <el-option v-for="sheet in sheetNames" :key="sheet" :label="sheet" :value="sheet" />
            </el-select>
          </div>

          <!-- 手动选择文件按钮 -->
          <div v-if="importFile && previewData.length === 0" class="manual-upload">
            <el-alert type="warning" :closable="false">
              <template #default>
                <div>无法读取文件数据，请尝试重新选择文件</div>
              </template>
            </el-alert>
            <el-upload
              class="excel-uploader"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleFileChange"
              accept=".xlsx,.xls"
            >
              <el-button type="primary" style="margin-top: 10px">
                <el-icon><UploadFilled /></el-icon>
                重新选择文件
              </el-button>
            </el-upload>
          </div>
          <el-table
            v-else
            v-loading="previewLoading"
            :data="previewData"
            border
            stripe
            style="width: 100%"
            max-height="500px"
            size="small"
          >
            <el-table-column
              v-for="column in previewColumns"
              :key="column"
              :prop="column"
              :label="column"
              show-overflow-tooltip
              min-width="120"
            />
          </el-table>

          <!-- 数据统计 -->
          <div v-if="totalRecords > 0" class="data-stats">
            <el-alert type="info" :closable="false">
              <template #default>
                <div>
                  共 <strong>{{ totalRecords }}</strong> 条记录
                  <span v-if="totalRecords > 100">（预览显示前100条）</span>
                </div>
              </template>
            </el-alert>

            <!-- 中文字段名提示 -->
            <el-alert v-if="hasChinese" type="success" :closable="false" style="margin-top: 10px">
              <template #default>
                <div>
                  <strong>检测到中文字段名</strong>，导入时将自动转换为英文字段名。
                  <div style="margin-top: 5px; font-size: 12px; color: #666">
                    支持的中文字段名：服装编码、服装名称、款式、单价、数量、金额等
                  </div>
                </div>
              </template>
            </el-alert>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button
          type="primary"
          @click="handleImport"
          :disabled="!importFile || previewData.length === 0"
          :loading="importLoading"
        >
          导入数据
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, UploadFilled } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'

// 字段映射：中文标题 -> 英文字段
const fieldMapping = {
  服装编码: 'oem_clothing_id',
  服装名称: 'oem_clothing_name',
  款式: 'style',
  单价: 'price',
  数量: 'in_pcs',
  金额: 'money',
  备注: 'remark',
}

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  file: {
    type: [File, Object, null],
    default: null,
    required: false,
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'import-success'])

// 对话框可见性
const dialogVisible = ref(props.visible)

// 监听 visible 属性变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
  }
)

// 监听对话框可见性变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
  // 如果对话框关闭，清除导入文件和相关数据
  if (!newVal) {
    clearImportFile()
  }
})

// 监听文件属性变化
watch(
  () => props.file,
  (newFile) => {
    if (newFile && newFile instanceof File) {
      // 清除之前的数据
      clearImportFile()
      // 设置新文件
      importFile.value = newFile
      // 读取Excel文件的工作表列表并自动选择OEM服装入库sheet
      readExcelSheets(newFile)
    }
  },
  { immediate: true }
)

// 导入相关状态
const importFile = ref<File | null>(null)
const previewData = ref<any[]>([])
const previewColumns = ref<string[]>([])
const previewLoading = ref(false)
const importLoading = ref(false)
const sheetNames = ref<string[]>([])
const selectedSheet = ref('')
const totalRecords = ref(0)
const hasChinese = ref(false)

// 清除导入文件
const clearImportFile = () => {
  importFile.value = null
  previewData.value = []
  previewColumns.value = []
  sheetNames.value = []
  selectedSheet.value = ''
  totalRecords.value = 0
  hasChinese.value = false
}

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false
}

// 文件变更
const handleFileChange = async (file: any) => {
  if (file.raw) {
    // 清除之前的数据
    clearImportFile()

    // 设置新文件
    importFile.value = file.raw

    // 读取Excel文件的工作表列表并自动选择OEM服装入库sheet
    await readExcelSheets(file.raw)
  }
}

// 工作表变更
const handleSheetChange = (sheetName: string) => {
  if (importFile.value && sheetName) {
    handlePreviewExcel(importFile.value, sheetName)
  }
}

// 读取Excel工作表列表
const readExcelSheets = async (file: File) => {
  previewLoading.value = true
  try {
    const reader = new FileReader()

    reader.onload = (e: any) => {
      try {
        if (!e.target || !e.target.result) {
          throw new Error('读取文件内容失败')
        }

        // 解析Excel数据
        const data = new Uint8Array(e.target.result)
        let workbook

        try {
          workbook = XLSX.read(data, { type: 'array' })
        } catch (xlsxError) {
          console.error('解析Excel文件失败:', xlsxError)
          throw new Error('Excel文件格式错误，请确保文件未损坏且格式正确')
        }

        // 检查工作表是否存在
        if (
          !workbook.SheetNames ||
          !Array.isArray(workbook.SheetNames) ||
          workbook.SheetNames.length === 0
        ) {
          throw new Error('Excel文件中没有工作表')
        }

        // 获取所有工作表名称
        sheetNames.value = workbook.SheetNames

        // 如果有工作表，自动查找并选择"OEM服装入库"工作表
        if (sheetNames.value.length > 0) {
          // 尝试查找名为"OEM服装入库"的工作表
          const oemClothingIncomingSheet = sheetNames.value.find(
            (name) =>
              name.includes('OEM服装入库') ||
              name.includes('oem服装入库')
          )

          if (oemClothingIncomingSheet) {
            // 如果找到了OEM服装入库工作表，选择它
            selectedSheet.value = oemClothingIncomingSheet
          } else {
            // 否则选择第一个工作表
            selectedSheet.value = sheetNames.value[0]
          }

          // 预览选中的工作表
          try {
            handlePreviewExcel(file, selectedSheet.value)
          } catch (previewError) {
            console.error('预览工作表失败:', previewError)
            ElMessage.error('预览工作表内容失败，请检查Excel文件格式')
            previewLoading.value = false
          }
        } else {
          ElMessage.warning('Excel文件中没有工作表')
          previewLoading.value = false
        }
      } catch (error: any) {
        console.error('解析Excel工作表失败', error)
        ElMessage.error(error.message || '解析Excel文件失败，请检查文件格式')
        previewLoading.value = false
      }
    }

    reader.onerror = (event) => {
      console.error('文件读取错误:', event)
      ElMessage.error('读取文件失败，请检查文件是否可访问')
      previewLoading.value = false
    }

    // 读取文件
    try {
      reader.readAsArrayBuffer(file)
    } catch (readError) {
      console.error('启动文件读取失败:', readError)
      ElMessage.error('无法读取文件，请重新选择文件')
      previewLoading.value = false
    }
  } catch (error: any) {
    console.error('读取Excel工作表失败', error)
    ElMessage.error(error.message || '读取Excel工作表失败')
    previewLoading.value = false
  }
}

// 预览Excel
const handlePreviewExcel = (file: File, sheetName: string) => {
  previewLoading.value = true
  try {
    const reader = new FileReader()

    reader.onload = (e: any) => {
      try {
        if (!e.target || !e.target.result) {
          throw new Error('读取文件内容失败')
        }

        // 解析Excel数据
        const data = new Uint8Array(e.target.result)
        let workbook

        try {
          workbook = XLSX.read(data, { type: 'array' })
        } catch (xlsxError) {
          console.error('解析Excel文件失败:', xlsxError)
          throw new Error('Excel文件格式错误，请确保文件未损坏且格式正确')
        }

        // 检查工作表是否存在
        if (!workbook.Sheets || !workbook.Sheets[sheetName]) {
          throw new Error(`找不到工作表 "${sheetName}"`)
        }

        // 获取选中的工作表
        const worksheet = workbook.Sheets[sheetName]

        // 将工作表转换为JSON
        let jsonData
        try {
          jsonData = XLSX.utils.sheet_to_json(worksheet)
        } catch (jsonError) {
          console.error('转换工作表为JSON失败:', jsonError)
          throw new Error('无法读取工作表数据，请检查Excel文件格式')
        }

        // 检查数据是否为数组
        if (!Array.isArray(jsonData)) {
          throw new Error('读取到的数据格式不正确')
        }

        // 过滤掉空行
        const filteredData = jsonData.filter((row: any) => {
          if (!row || typeof row !== 'object') return false
          // 检查行是否有至少一个非空值
          return Object.values(row).some(
            (value) => value !== null && value !== undefined && value !== ''
          )
        })

        // 获取列名
        const columns = filteredData.length > 0 ? Object.keys(filteredData[0] as object) : []

        // 设置总记录数
        totalRecords.value = filteredData.length

        // 显示前100条数据
        previewData.value = filteredData.slice(0, 100)
        previewColumns.value = columns

        // 检查是否有中文字段名，如果有，在控制台输出提示
        hasChinese.value = columns.some((col) => col in fieldMapping)
        if (hasChinese.value) {
          console.log('检测到中文字段名，导入时将自动转换为英文字段名')
        }

        // 如果没有数据，显示警告
        if (filteredData.length === 0) {
          ElMessage.warning('Excel文件中没有有效数据')
        } else if (columns.length === 0) {
          ElMessage.warning('Excel文件中没有有效列')
        }

        previewLoading.value = false
      } catch (error: any) {
        console.error('解析Excel失败', error)
        ElMessage.error(error.message || '解析Excel文件失败，请检查文件格式')
        previewLoading.value = false
        previewData.value = []
        previewColumns.value = []
        totalRecords.value = 0
      }
    }

    reader.onerror = (event) => {
      console.error('文件读取错误:', event)
      ElMessage.error('读取文件失败，请检查文件是否可访问')
      previewLoading.value = false
      previewData.value = []
      previewColumns.value = []
      totalRecords.value = 0
    }

    // 读取文件
    try {
      reader.readAsArrayBuffer(file)
    } catch (readError) {
      console.error('启动文件读取失败:', readError)
      ElMessage.error('无法读取文件，请重新选择文件')
      previewLoading.value = false
      previewData.value = []
      previewColumns.value = []
      totalRecords.value = 0
    }
  } catch (error: any) {
    console.error('预览Excel失败', error)
    ElMessage.error(error.message || '预览Excel失败')
    previewData.value = []
    previewColumns.value = []
    totalRecords.value = 0
    previewLoading.value = false
  }
}

// 导入Excel
const handleImport = async () => {
  if (!importFile.value || !selectedSheet.value) return

  // 显示确认对话框
  ElMessageBox.confirm(
    `确定要导入工作表 "${selectedSheet.value}" 中的数据吗？共 ${totalRecords.value} 条记录。`,
    '导入确认',
    {
      confirmButtonText: '确认导入',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      // 用户确认导入
      importLoading.value = true
      try {
        // 读取完整的Excel数据
        const reader = new FileReader()

        reader.onload = async (e: any) => {
          try {
            if (!e.target || !e.target.result) {
              throw new Error('读取文件内容失败')
            }

            // 解析Excel数据
            const data = new Uint8Array(e.target.result)
            let workbook
            try {
              workbook = XLSX.read(data, { type: 'array' })
            } catch (xlsxError) {
              console.error('解析Excel文件失败:', xlsxError)
              throw new Error('Excel文件格式错误，请确保文件未损坏且格式正确')
            }

            // 检查工作表是否存在
            if (!workbook.Sheets || !workbook.Sheets[selectedSheet.value]) {
              throw new Error(`找不到工作表 "${selectedSheet.value}"`)
            }

            // 获取选中的工作表
            const worksheet = workbook.Sheets[selectedSheet.value]

            // 将工作表转换为JSON
            let jsonData
            try {
              jsonData = XLSX.utils.sheet_to_json(worksheet)
            } catch (jsonError) {
              console.error('转换工作表为JSON失败:', jsonError)
              throw new Error('无法读取工作表数据，请检查Excel文件格式')
            }

            // 过滤掉空行
            const filteredData = jsonData.filter((row: any) => {
              if (!row || typeof row !== 'object') return false
              // 检查行是否有至少一个非空值
              return Object.values(row).some(
                (value) => value !== null && value !== undefined && value !== ''
              )
            })

            // 转换字段名（中文 -> 英文）
            const convertedData = filteredData.map((item: any) => {
              const convertedItem: Record<string, any> = {}

              // 遍历原始数据的所有字段
              Object.entries(item).forEach(([key, value]) => {
                // 如果是中文字段名，转换为英文字段名
                if (key in fieldMapping) {
                  convertedItem[fieldMapping[key as keyof typeof fieldMapping]] = value
                } else {
                  // 否则保持原样
                  convertedItem[key] = value
                }
              })

              return convertedItem
            })

            // 准备导入数据
            const importData = {
              details: convertedData,
              fileName: importFile.value?.name || '',
              totalRecords: convertedData.length,
            }

            // 确保数据中不包含空白行
            if (importData.details.length === 0) {
              throw new Error('没有有效的数据可以导入')
            }

            // 导入成功
            ElMessage.success(`导入成功: 共 ${importData.details.length} 条数据`)

            // 关闭对话框
            dialogVisible.value = false

            // 触发导入成功事件，并传递导入的数据
            emit('import-success', importData.details)
          } catch (error: any) {
            console.error('导入处理失败:', error)
            ElMessage.error(error.message || '导入失败')
          } finally {
            importLoading.value = false
          }
        }

        reader.onerror = (event) => {
          console.error('文件读取错误:', event)
          ElMessage.error('读取文件失败，请检查文件是否可访问')
          importLoading.value = false
        }

        // 读取文件
        try {
          // 确保文件存在
          if (!importFile.value) {
            throw new Error('导入文件不存在')
          }
          reader.readAsArrayBuffer(importFile.value)
        } catch (readError) {
          console.error('启动文件读取失败:', readError)
          ElMessage.error('无法读取文件，请重新选择文件')
          importLoading.value = false
        }
      } catch (error: any) {
        console.error('导入操作失败:', error)
        ElMessage.error(error.message || '导入失败')
        importLoading.value = false
      }
    })
    .catch(() => {
      // 用户取消导入
      ElMessage.info('已取消导入操作')
    })
}
</script>

<style scoped>
.import-dialog :deep(.el-dialog__body) {
  padding: 10px 20px;
}

.import-container {
  display: flex;
  flex-direction: column;
}

.file-info {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.file-tag {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.sheet-info {
  margin-right: 10px;
}

.upload-area {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  padding: 40px 0;
  text-align: center;
  margin-bottom: 15px;
}

.upload-area:hover {
  border-color: #409eff;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 48px;
  color: #8c939d;
  margin-bottom: 10px;
}

.upload-text {
  color: #606266;
}

.upload-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.preview-area {
  margin-bottom: 15px;
}

.sheet-selector {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.sheet-label {
  margin-right: 10px;
}

.data-stats {
  margin-top: 15px;
}

.manual-upload {
  margin: 15px 0;
}

.clear-btn {
  margin-left: auto;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
}
</style>
