<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="650px"
    :close-on-click-modal="false"
    @closed="resetForm"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="oem-clothing-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属年份" prop="oem_clothing_year">
            <el-select v-model="form.oem_clothing_year" placeholder="选择年份" style="width: 100%">
              <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服装编号" prop="oem_clothing_id">
            <el-input v-model="form.oem_clothing_id" placeholder="请输入服装编号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="服装名称" prop="oem_clothing_name">
            <el-input v-model="form.oem_clothing_name" placeholder="请输入服装名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="供应商" prop="oem_supplier">
            <el-input v-model="form.oem_supplier" placeholder="请输入供应商" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="分类" prop="classification">
            <el-input v-model="form.classification" placeholder="请输入分类" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="款式" prop="style">
            <el-select v-model="form.style" placeholder="选择款式" style="width: 100%">
              <el-option v-for="style in styleOptions" :key="style" :label="style" :value="style" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="尺码" prop="size">
            <el-select v-model="form.size" placeholder="选择尺码" style="width: 100%">
              <el-option v-for="size in sizeOptions" :key="size" :label="size" :value="size" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="价格" prop="price">
            <el-input v-model.number="form.price" placeholder="请输入价格" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="入库数量" prop="in_pcs">
            <el-input v-model.number="form.in_pcs" placeholder="请输入入库数量" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="订单数量" prop="order_quantity">
            <el-input v-model.number="form.order_quantity" placeholder="请输入订单数量" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="出货数" prop="shipments">
            <el-input v-model.number="form.shipments" placeholder="请输入出货数" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="印花" prop="printed">
            <el-input v-model="form.printed" placeholder="请输入印花" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" :rows="2" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="图片" prop="img">
            <div class="image-container">
              <!-- 左侧：图片预览组件 -->
              <div class="image-preview-section">
                <div v-if="fileList.length > 0" class="image-preview-container">
                  <el-image
                    v-for="(file, index) in fileList"
                    :key="index"
                    :src="file.url"
                    fit="contain"
                    :preview-src-list="getPreviewSrcList()"
                    :initial-index="index"
                    class="preview-image-item"
                    :z-index="3000"
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                        <div class="error-text">加载失败</div>
                      </div>
                    </template>
                  </el-image>
                </div>
              </div>

              <!-- 右侧：上传组件 - 只显示文件名 -->
              <div class="upload-section">
                <el-upload
                  class="file-upload"
                  action="#"
                  :auto-upload="false"
                  :file-list="fileList"
                  :on-change="handleFileChange"
                  :on-remove="handleFileRemove"
                  :show-file-list="true"
                  list-type="text"
                >
                  <el-button type="primary">
                    <el-icon><Plus /></el-icon>
                    选择图片
                  </el-button>
                </el-upload>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Picture } from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadFile, UploadUserFile } from 'element-plus'
import {
  createOemClothing,
  updateOemClothing,
  getOemClothingYearOptions,
  getTwoOptions,
} from '@/api/oemClothing'
import type { OemClothing, OemClothingImage } from '@/types/oemClothing'
import { uploadImage, deleteImages, UploadType } from '@/utils/upload'

// 定义属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  title: {
    type: String,
    default: '新增OEM服装',
  },
  oemClothing: {
    type: Object as () => Partial<OemClothing>,
    default: () => ({}),
  },
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'success'])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

// 表单引用
const formRef = ref<FormInstance>()

// 选项数据
const yearOptions = ref<string[]>([])
const sizeOptions = ref<string[]>([])
const styleOptions = ref<string[]>([])

// 表单数据
const form = reactive<Partial<OemClothing>>({
  oem_clothing_year: '',
  oem_clothing_id: '',
  oem_clothing_name: '',
  oem_supplier: '',
  classification: '',
  style: '',
  size: '',
  price: undefined,
  in_pcs: undefined,
  order_quantity: undefined,
  shipments: undefined,
  printed: '',
  remark: '',
  state: '0',
  img: [],
})

// 文件列表
const fileList = ref<UploadUserFile[]>([])

// 待上传的文件列表
const pendingUploadFiles = ref<File[]>([])

// 需要从服务器删除的图片
const imagesToDelete = ref<{ Key: string }[]>([])

// 表单验证规则
const rules = reactive<FormRules>({
  oem_clothing_year: [{ required: true, message: '请选择所属年份', trigger: 'change' }],
  oem_clothing_id: [{ required: true, message: '请输入服装编号', trigger: 'blur' }],
  oem_clothing_name: [{ required: true, message: '请输入服装名称', trigger: 'blur' }],
})

// 监听服装数据变化
watch(
  () => props.oemClothing,
  (newVal) => {
    if (newVal) {
      // 复制对象，避免直接修改props
      Object.keys(form).forEach((key) => {
        // @ts-ignore
        form[key] = newVal[key] !== undefined ? newVal[key] : form[key]
      })

      // 处理图片
      fileList.value = []
      if (newVal.img && newVal.img.length > 0) {
        fileList.value = newVal.img.map(
          (img) =>
            ({
              name: img.Key.split('/').pop() || 'image.jpg',
              url: img.url,
              // 使用自定义属性存储原始图片信息
              uid: Math.random(), // 添加必要的uid属性
              customData: img, // 使用自定义属性存储原始数据
            }) as UploadUserFile
        )
      }
    }
  },
  { deep: true, immediate: true }
)

// 初始化
onMounted(async () => {
  await Promise.all([loadYearOptions(), loadTwoOptions()])
})

// 加载年份选项
const loadYearOptions = async () => {
  try {
    // 使用当前年份和前几年作为默认选项，确保即使API失败也有可用选项
    const currentYear = new Date().getFullYear()
    const defaultYears = [
      `${currentYear}年`,
      `${currentYear - 1}年`,
      `${currentYear - 2}年`,
      `${currentYear - 3}年`,
      `${currentYear - 4}年`,
    ]

    // 先设置默认年份，确保界面有可用选项
    yearOptions.value = defaultYears

    try {
      // 尝试从API获取年份选项
      const response = await getOemClothingYearOptions()
      console.log('加载到的年份选项：', response)
      if (response && response.data && Array.isArray(response.data)) {
        // 确保返回的数据是数组
        const years = response.data as unknown as string[]
        if (years.length > 0) {
          yearOptions.value = years
        }
      } else {
        console.warn('使用默认年份选项，API返回格式不正确', response)
      }
    } catch (error) {
      console.warn('从API加载年份选项失败，使用默认年份选项', error)
      // 出错时保留默认年份选项，不需要额外处理
    }
  } catch (error) {
    console.error('初始化年份选项失败', error)
    // 确保至少有当前年份可选
    yearOptions.value = [`${new Date().getFullYear()}年`]
  }
}

// 加载尺码、款式选项
const loadTwoOptions = async () => {
  try {
    // 设置默认尺码和款式选项
    const defaultSizes = ['S', 'M', 'L', 'XL', 'XXL', 'XXXL', '均码']
    const defaultStyles = ['短袖', '长袖', '无袖', '背心', '外套', '裤子', '裙子']

    // 先设置默认选项，确保界面有可用选项
    sizeOptions.value = defaultSizes
    styleOptions.value = defaultStyles

    try {
      // 尝试从API获取尺码、款式选项
      const response = await getTwoOptions()
      console.log('加载到的尺码、款式选项：', response)
      if (response && response.data) {
        const data = response.data as unknown as { sizes: string[]; styles: string[] }

        // 只有当API返回的数据不为空时才更新
        if (data.sizes && data.sizes.length > 0) {
          sizeOptions.value = data.sizes
        }

        if (data.styles && data.styles.length > 0) {
          styleOptions.value = data.styles
        }
      } else {
        console.warn('使用默认尺码、款式选项，API返回格式不正确', response)
      }
    } catch (error) {
      console.warn('从API加载尺码、款式选项失败，使用默认选项', error)
      // 出错时保留默认选项，不需要额外处理
    }
  } catch (error) {
    console.error('初始化尺码、款式选项失败', error)
    // 确保至少有基本选项可用
    sizeOptions.value = ['S', 'M', 'L', 'XL']
    styleOptions.value = ['短袖', '长袖']
  }
}

// 文件变更
const handleFileChange = (uploadFile: UploadFile) => {
  console.log('文件变更：', uploadFile)

  if (uploadFile.raw) {
    // 将文件添加到待上传列表
    pendingUploadFiles.value.push(uploadFile.raw)

    // 创建临时预览
    const reader = new FileReader()
    reader.onload = (e) => {
      const newFile = {
        name: uploadFile.name,
        url: e.target?.result as string,
        uid: uploadFile.uid,
        status: 'ready',
        raw: uploadFile.raw,
      } as UploadUserFile

      // 更新文件列表
      const index = fileList.value.findIndex((file) => file.uid === uploadFile.uid)
      if (index !== -1) {
        fileList.value[index] = newFile
      } else {
        fileList.value.push(newFile)
      }
    }
    reader.readAsDataURL(uploadFile.raw)
  }
}

// 文件移除
const handleFileRemove = (uploadFile: UploadFile) => {
  console.log('文件移除：', uploadFile)

  // 如果是已有的图片（有customData），标记为需要删除
  if ((uploadFile as any).customData) {
    // 将图片添加到待删除列表，在提交时处理
    const customData = (uploadFile as any).customData
    imagesToDelete.value.push({ Key: customData.Key })
    console.log('标记图片需要删除:', customData.Key)
  } else if (uploadFile.raw) {
    // 如果是待上传的图片，从待上传列表中移除
    const index = pendingUploadFiles.value.findIndex((file) => file === uploadFile.raw)
    if (index !== -1) {
      pendingUploadFiles.value.splice(index, 1)
    }
  }

  // 从文件列表中移除
  const index = fileList.value.findIndex((file) => file.uid === uploadFile.uid)
  if (index !== -1) {
    fileList.value.splice(index, 1)
  }
}

// 获取所有图片的URL列表，用于预览
const getPreviewSrcList = () => {
  return fileList.value.map((file) => file.url || '').filter((url) => url)
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    // 使用 await 等待表单验证完成
    const valid = await formRef.value.validate()

    if (!valid) {
      console.error('表单验证失败')
      return
    }

    // 处理图片数据

    // 1. 删除标记为需要删除的图片
    if (imagesToDelete.value.length > 0) {
      try {
        console.log('删除服务器上的图片:', imagesToDelete.value)
        await deleteImages(imagesToDelete.value)
        ElMessage.success('已删除标记的图片')
      } catch (error) {
        console.error('删除图片失败:', error)
        ElMessage.error('部分图片删除失败，但会继续保存表单')
      }
    }

    // 2. 获取已有的图片（从服务器加载的，且未被标记删除）
    const existingImages = fileList.value
      .filter((file) => (file as any).customData)
      .map((file) => (file as any).customData)

    // 3. 上传新选择的图片
    const newImages: OemClothingImage[] = []
    if (pendingUploadFiles.value.length > 0) {
      ElMessage.info('正在上传图片，请稍候...')

      // 逐个上传图片
      for (const file of pendingUploadFiles.value) {
        try {
          const imageInfo = await uploadImage(file, UploadType.OEM_CLOTHING)
          if (imageInfo) {
            newImages.push(imageInfo as OemClothingImage)
          }
        } catch (error) {
          console.error('上传图片失败:', error)
          ElMessage.error(`图片 ${file.name} 上传失败`)
          // 继续上传其他图片
        }
      }
    }

    // 4. 合并已有图片和新上传的图片
    const imgData: OemClothingImage[] = [...existingImages, ...newImages]

    // 确保必填字段存在
    if (!form.oem_clothing_year || !form.oem_clothing_id || !form.oem_clothing_name) {
      ElMessage.error('请填写必填字段')
      return
    }

    // 构建提交数据
    const submitData = {
      oem_clothing_year: form.oem_clothing_year as string,
      oem_clothing_id: form.oem_clothing_id as string,
      oem_clothing_name: form.oem_clothing_name as string,
      oem_supplier: form.oem_supplier as string,
      classification: form.classification as string,
      style: form.style as string,
      size: form.size as string,
      price: form.price || undefined,
      in_pcs: form.in_pcs || undefined,
      order_quantity: form.order_quantity || undefined,
      shipments: form.shipments || undefined,
      printed: form.printed as string,
      remark: form.remark as string,
      state: form.state as string,
      img: imgData.length > 0 ? imgData : undefined,
    }

    let response
    if (props.oemClothing._id) {
      // 更新
      response = await updateOemClothing(props.oemClothing._id as string, submitData)
    } else {
      // 创建
      response = await createOemClothing(submitData)
    }

    if (response && response.data) {
      ElMessage.success(props.oemClothing._id ? '更新成功' : '创建成功')
      dialogVisible.value = false
      emit('success')
    } else {
      ElMessage.error(props.oemClothing._id ? '更新失败' : '创建失败')
    }
  } catch (error: any) {
    console.error(props.oemClothing._id ? '更新OEM服装失败' : '创建OEM服装失败', error)
    ElMessage.error(
      error.response?.data?.message ||
        (props.oemClothing._id ? '更新OEM服装失败' : '创建OEM服装失败')
    )
  }
}

// 处理取消按钮点击
const handleCancel = () => {
  // 直接关闭弹窗，resetForm会在@closed事件中被调用
  dialogVisible.value = false
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  // 清空表单数据
  Object.keys(form).forEach((key) => {
    if (key === 'state') {
      form[key] = '0'
    } else if (key === 'img') {
      form[key] = []
    } else if (['price', 'in_pcs', 'order_quantity', 'shipments'].includes(key)) {
      // @ts-ignore
      form[key] = undefined
    } else {
      // @ts-ignore
      form[key] = ''
    }
  })
  fileList.value = []

  // 清空待上传文件列表
  pendingUploadFiles.value = []

  // 清空待删除图片列表
  imagesToDelete.value = []
}
</script>

<style scoped>
.oem-clothing-form {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 10px;
}

.image-container {
  display: flex;
  gap: 20px;
}

.image-preview-section {
  flex: 3;
}

.upload-section {
  flex: 2;
  min-width: 200px;
}

.image-preview-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.preview-image-item {
  width: 100px;
  height: 100px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  cursor: pointer;
  object-fit: cover;
}

.image-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
  color: #909399;
}

.error-text {
  margin-top: 8px;
  font-size: 12px;
}

/* 覆盖 Element Plus 的图片预览样式 */
:deep(.el-image-viewer__wrapper) {
  z-index: 3000 !important;
}

:deep(.el-image-viewer__close) {
  color: #fff;
}

:deep(.el-image-viewer__actions) {
  opacity: 1;
}

:deep(.el-image-viewer__canvas) {
  user-select: none;
}
</style>
