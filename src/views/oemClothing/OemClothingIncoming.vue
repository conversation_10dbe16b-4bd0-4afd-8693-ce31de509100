<template>
  <PageTemplate
    :queryParams="queryParams"
    :tableData="tableData"
    :total="total"
    :loading="loading"
    :tableHeight="650"
    @query="handleQuery"
    @reset="handleReset"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  >
    <!-- 查询区域左侧 -->
    <template #query-form-left>
      <el-form-item label="所属年份" prop="years">
        <el-select
          v-model="queryParams.years"
          placeholder="选择年份"
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          style="width: 180px"
          @change="handleYearChange"
        >
          <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
        </el-select>
      </el-form-item>
      <el-form-item label="供应商" prop="suppliers">
        <el-select
          v-model="queryParams.suppliers"
          placeholder="选择供应商"
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          style="width: 180px"
          @change="handleSupplierChange"
        >
          <el-option
            v-for="supplier in supplierOptions"
            :key="supplier"
            :label="supplier"
            :value="supplier"
          />
        </el-select>
      </el-form-item>
    </template>

    <!-- 查询区域右侧 -->
    <template #query-form-right>
      <el-button type="primary" @click="handleAdd">新增入库</el-button>
    </template>

    <!-- 表格列 -->
    <template #table-columns>
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column
        prop="oem_clothing_incoming_year"
        label="所属年份"
        width="100"
        align="center"
      />
      <el-table-column label="入库日期" width="120" align="center">
        <template #default="scope">
          {{ scope.row.date_in ? new Date(scope.row.date_in).toISOString().split('T')[0] : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="supplier" label="供应商" width="150" align="center" />
      <el-table-column prop="remark" label="备注" show-overflow-tooltip align="center" />
      <el-table-column fixed="right" label="操作" width="150" align="center">
        <template #default="{ row }">
          <el-button link type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
        </template>
      </el-table-column>
    </template>

    <!-- 分页区域左侧 -->
    <template #pagination-left>
      <div class="table-tools">
        <el-upload
          class="upload-button"
          :action="null"
          :auto-upload="false"
          :on-change="handleFileChange"
          accept=".xlsx,.xls"
          :show-file-list="false"
        >
          <el-button type="success" plain size="small">Excel导入</el-button>
        </el-upload>

        <el-button type="warning" plain size="small" @click="handleExport">Excel导出</el-button>
      </div>
    </template>

    <!-- 额外内容插槽 -->
    <template #extra-content>
      <!-- 入库表单对话框 -->
      <OemClothingIncomingDialog
        v-model:visible="dialogVisible"
        :type="dialogType"
        :current-id="currentId"
        ref="dialogRef"
        @submit="handleSubmit"
        @delete="handleDelete"
      />

      <!-- 使用导入组件 -->
      <OemClothingIncomingImport
        v-model:visible="importDialogVisible"
        :file="importFile"
        @import-success="handleImportSuccess"
      />
    </template>
  </PageTemplate>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as XLSX from 'xlsx'
// 导入通用页面模板
import PageTemplate from '@/components/common/PageTemplate.vue'
// 导入对话框组件
import OemClothingIncomingDialog from './components/OemClothingIncomingDialog.vue'
// 导入导入组件
import OemClothingIncomingImport from './components/OemClothingIncomingImport.vue'
import {
  getOemClothingIncomingList,
  createOemClothingIncoming,
  updateOemClothingIncoming,
  deleteOemClothingIncoming,
  getOemClothingIncomingYearOptions,
  getOemClothingIncomingSupplierOptions,
  getOemClothingIncomingDetailsByIncomingId,
  createOemClothingIncomingDetailBatch,
} from '@/api/oemClothingIncoming'
// 定义本地类型
interface OemClothingIncoming {
  _id: string
  oem_clothing_incoming_id: string
  oem_clothing_incoming_year: string
  date_in: string | Date
  supplier: string
  remark?: string
  createTime?: string | Date
}

interface QueryParams {
  years: string[]
  suppliers: string[]
  page: number
  limit: number
}

interface SubmitData {
  oem_clothing_incoming_id: string
  oem_clothing_incoming_year: string
  date_in: Date
  supplier: string
  remark?: string
}

interface SubmitDetailData {
  oem_clothing_id: string
  oem_clothing_name: string
  style?: string
  price?: number
  in_pcs: number
  money?: string
  remark?: string
}

// 加载状态
const loading = ref(false)

// 表格数据
const tableData = ref<OemClothingIncoming[]>([])
const total = ref(0)

// 查询参数
const queryParams = reactive<QueryParams>({
  years: [],
  suppliers: [],
  page: 1,
  limit: 10,
})

// 年份和供应商选项
const yearOptions = ref<string[]>([])
const supplierOptions = ref<string[]>([])

// 对话框状态
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const dialogRef = ref<InstanceType<typeof OemClothingIncomingDialog> | null>(null)
const currentId = ref<string>('')

// 导入相关
const importDialogVisible = ref(false)
const importFile = ref<File | null>(null)

// 初始化
onMounted(async () => {
  // 加载所有选项数据
  await loadInitialOptions()

  // 加载初始数据
  handleQuery()
})

/**
 * 加载初始选项数据
 */
const loadInitialOptions = async (): Promise<void> => {
  try {
    // 加载年份选项
    const yearResponse = await getOemClothingIncomingYearOptions()
    console.log('获取到的OEM服装入库年份数据：', yearResponse)
    if (yearResponse?.data?.years) {
      yearOptions.value = yearResponse.data.years
    }

    // 加载供应商选项
    const supplierResponse = await getOemClothingIncomingSupplierOptions()
    console.log('获取到的OEM服装入库供应商数据：', supplierResponse)
    if (supplierResponse?.data?.suppliers) {
      supplierOptions.value = supplierResponse.data.suppliers
    }
  } catch (error) {
    console.error('加载选项数据失败', error)
    ElMessage.error('加载选项数据失败，请刷新页面重试')
  }
}

/**
 * 年份变化时更新供应商选项并刷新数据
 */
const handleYearChange = async (): Promise<void> => {
  try {
    // 根据选中的年份加载供应商选项
    const response = await getOemClothingIncomingSupplierOptions(
      queryParams.years.length > 0 ? queryParams.years.join(',') : undefined
    )

    // 使用类型断言处理响应
    const responseData = response as any
    if (responseData?.data?.suppliers) {
      supplierOptions.value = responseData.data.suppliers
    } else {
      supplierOptions.value = []
    }

    // 自动刷新数据
    handleQuery()
  } catch (error) {
    console.error('加载供应商选项失败', error)
    ElMessage.error('加载供应商选项失败')
  }
}

/**
 * 供应商变化时更新年份选项并刷新数据
 */
const handleSupplierChange = async (): Promise<void> => {
  try {
    // 根据选中的供应商加载年份选项
    const response = await getOemClothingIncomingYearOptions(
      queryParams.suppliers.length > 0 ? queryParams.suppliers.join(',') : undefined
    )

    // 使用类型断言处理响应
    const responseData = response as any
    if (responseData?.data?.years) {
      yearOptions.value = responseData.data.years
    } else {
      yearOptions.value = []
    }

    // 自动刷新数据
    handleQuery()
  } catch (error) {
    console.error('加载年份选项失败', error)
    ElMessage.error('加载年份选项失败')
  }
}

/**
 * 加载数据
 * @param params 查询参数
 */
const loadData = async (params: Record<string, any> = {}): Promise<void> => {
  loading.value = true
  try {
    const response = await getOemClothingIncomingList(params)

    // 根据后端返回的实际数据结构进行处理
    if (response) {
      // 使用类型断言来处理响应
      const responseData = response as any
      tableData.value = responseData.data || []
      total.value = responseData.total || 0
    } else {
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('加载数据失败', error)
    // 使用更安全的错误处理
    const errorMessage = error instanceof Error ? error.message : '加载数据失败'
    ElMessage.error(errorMessage)
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

/**
 * 查询数据
 */
const handleQuery = (): void => {
  // 构建查询参数
  const params: Record<string, any> = {
    page: queryParams.page,
    limit: queryParams.limit,
  }

  // 添加年份过滤
  if (queryParams.years && queryParams.years.length > 0) {
    // 将数组转换为逗号分隔的字符串
    params.oem_clothing_incoming_years = queryParams.years
    console.log('发送的年份参数:', params.oem_clothing_incoming_years)
  }

  // 添加供应商过滤
  if (queryParams.suppliers && queryParams.suppliers.length > 0) {
    // 将数组转换为逗号分隔的字符串
    params.suppliers = queryParams.suppliers
    console.log('发送的供应商参数:', params.suppliers)
  }

  // 加载数据
  loadData(params)
}

/**
 * 重置查询
 */
const handleReset = (): void => {
  queryParams.years = []
  queryParams.suppliers = []
  queryParams.page = 1
  handleQuery()
}

/**
 * 分页大小变化
 * @param val 每页条数
 */
const handleSizeChange = (val: number): void => {
  queryParams.limit = val
  queryParams.page = 1 // 当每页数量变化时，重置为第一页
  handleQuery()
}

/**
 * 当前页变化
 * @param val 当前页码
 */
const handleCurrentChange = (val: number): void => {
  queryParams.page = val
  handleQuery()
}

/**
 * 打开新增对话框
 */
const handleAdd = (): void => {
  dialogType.value = 'add'
  currentId.value = ''
  dialogVisible.value = true

  // 重置表单
  if (dialogRef.value) {
    dialogRef.value.resetForm()
  }
}

/**
 * 打开编辑对话框
 * @param row 要编辑的行数据
 */
const handleEdit = async (row: OemClothingIncoming): Promise<void> => {
  dialogType.value = 'edit'
  currentId.value = row._id

  // 重置表单
  if (dialogRef.value) {
    dialogRef.value.resetForm()
  }

  try {
    // 获取入库明细
    const detailResponse = await getOemClothingIncomingDetailsByIncomingId(row._id)
    console.log('获取到的OEM服装入库明细数据：', detailResponse)
    const details = (detailResponse as any) || []

    // 设置表单数据
    if (dialogRef.value) {
      dialogRef.value.setFormData(row, details)
    }

    // 显示对话框
    dialogVisible.value = true
  } catch (error) {
    console.error('获取入库明细失败', error)
    ElMessage.error('获取入库明细失败，请重试')
  }
}

/**
 * 提交表单
 * @param data 表单数据
 */
const handleSubmit = async (data: {
  formData: SubmitData
  details: SubmitDetailData[]
}): Promise<void> => {
  try {
    if (dialogType.value === 'add') {
      // 创建新入库记录
      const response = await createOemClothingIncoming(data.formData)
      const incomingId = (response as any)._id
      console.log('创建入库记录响应数据：', response)
      if (incomingId) {
        // 创建入库明细
        await createOemClothingIncomingDetailBatch(incomingId, data.details)

        ElMessage.success('新增入库记录成功')
      } else {
        throw new Error('创建入库记录失败')
      }
    } else {
      // 更新入库记录
      if (!currentId.value) {
        throw new Error('缺少入库记录ID')
      }

      await updateOemClothingIncoming(currentId.value, data.formData)

      // 更新入库明细（先删除再创建）
      await createOemClothingIncomingDetailBatch(currentId.value, data.details)

      ElMessage.success('更新入库记录成功')
    }

    // 关闭对话框
    dialogVisible.value = false

    // 刷新数据
    handleQuery()
  } catch (error) {
    console.error('提交表单失败', error)
    ElMessage.error('提交失败，请重试')
  }
}

/**
 * 删除入库记录
 */
const handleDelete = async (): Promise<void> => {
  if (!currentId.value) {
    ElMessage.warning('缺少入库记录ID')
    return
  }

  try {
    await deleteOemClothingIncoming(currentId.value)

    ElMessage.success('删除入库记录成功')

    // 关闭对话框
    dialogVisible.value = false

    // 刷新数据
    handleQuery()
  } catch (error) {
    console.error('删除入库记录失败', error)
    ElMessage.error('删除失败，请重试')
  }
}

/**
 * 文件变更
 * @param file 上传的文件
 */
const handleFileChange = (file: any): void => {
  if (file.raw) {
    // 设置文件
    importFile.value = file.raw
    // 显示导入对话框
    importDialogVisible.value = true
  }
}

/**
 * 导入成功处理函数
 * @param importedData 导入的数据
 */
const handleImportSuccess = (importedData: any[]): void => {
  // 显示导入成功提示
  ElMessage.success(`成功导入 ${importedData.length} 条数据`)

  // 刷新数据
  handleQuery()

  // 重新加载选项
  loadInitialOptions()
}

/**
 * 导出Excel
 */
const handleExport = (): void => {
  if (tableData.value.length === 0) {
    ElMessage.warning('没有数据可导出')
    return
  }

  try {
    // 创建工作簿
    const wb = XLSX.utils.book_new()

    // 准备导出数据
    const exportData = tableData.value.map((item: OemClothingIncoming) => ({
      所属年份: item.oem_clothing_incoming_year,
      入库日期: item.date_in ? new Date(item.date_in).toISOString().split('T')[0] : '-',
      供应商: item.supplier,
      入库编号: item.oem_clothing_incoming_id,
      备注: item.remark || '',
    }))

    // 创建工作表
    const ws = XLSX.utils.json_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 10 }, // 所属年份
      { wch: 12 }, // 入库日期
      { wch: 20 }, // 供应商
      { wch: 15 }, // 入库编号
      { wch: 30 }, // 备注
    ]
    ws['!cols'] = colWidths

    // 将工作表添加到工作簿
    XLSX.utils.book_append_sheet(wb, ws, 'OEM服装入库记录')

    // 导出文件
    XLSX.writeFile(wb, `OEM服装入库记录_${new Date().toISOString().split('T')[0]}.xlsx`)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出Excel失败', error)
    ElMessage.error('导出失败，请重试')
  }
}
</script>

<style scoped>
/* 页面特定样式 */
.detail-table {
  margin-top: 20px;
  width: 100%;
  height: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  border: 1px solid #e8e8e8;
}

.table-tools {
  display: flex;
  gap: 10px;
}

.upload-button {
  margin-right: 10px;
}
</style>
