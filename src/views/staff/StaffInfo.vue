<template>
  <PageTemplate
    :queryParams="queryParams"
    :tableData="tableData"
    :total="total"
    :loading="loading"
    :tableHeight="650"
    @query="handleQuery"
    @reset="resetQuery"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  >
    <!-- 查询区域左侧 -->
    <template #query-form-left>
      <el-form-item label="拼音简写" prop="pinyin">
        <el-input
          v-model="queryParams.pinyin"
          placeholder="输入拼音简写"
          clearable
          style="width: 120px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="floors">
        <el-select
          v-model="queryParams.floors"
          placeholder="选择部门"
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          filterable
          style="width: 180px"
          @change="handleFloorChange"
        >
          <el-option v-for="floor in floorOptions" :key="floor" :label="floor" :value="floor" />
        </el-select>
      </el-form-item>
      <el-form-item label="职位" prop="posts">
        <el-select
          v-model="queryParams.posts"
          placeholder="选择职位"
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          filterable
          style="width: 180px"
          @change="handlePostQuery"
        >
          <el-option v-for="post in postOptions" :key="post" :label="post" :value="post" />
        </el-select>
      </el-form-item>
    </template>

    <!-- 查询区域右侧 -->
    <template #query-form-right>
      <el-button type="primary" @click="handleAdd">新增员工</el-button>
    </template>

    <!-- 分页区域左侧 -->
    <template #pagination-left>
      <div class="table-tools">
        <el-button type="success" plain size="small" @click="showImportDialog">Excel导入</el-button>
        <el-button type="warning" plain size="small" @click="handleExport">Excel导出</el-button>
        <el-button type="primary" plain size="small" @click="handleExportAllSalary">
          导出所有员工工资
        </el-button>
      </div>
    </template>

    <!-- 表格列 -->
    <template #table-columns>
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="staff_id" label="员工编号" width="100" />
      <el-table-column prop="name" label="员工姓名" width="120" />
      <el-table-column prop="pinyin" label="拼音简写" width="100" />
      <el-table-column prop="gender" label="性别" width="80" />
      <el-table-column prop="floor" label="部门" width="120" />
      <el-table-column prop="post" label="职位" width="120" />
      <el-table-column prop="tel" label="联系电话" width="150" />
      <el-table-column prop="add" label="地址" min-width="200" show-overflow-tooltip />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </template>

    <!-- 额外内容插槽 -->
    <template #extra-content>
      <!-- 新增/编辑员工对话框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="dialogType === 'add' ? '新增员工' : '编辑员工'"
        width="720px"
        destroy-on-close
        class="staff-dialog"
        center
        :close-on-click-modal="false"
      >
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="90px"
          :validate-on-rule-change="false"
          class="staff-form"
          size="default"
        >
          <div class="form-tabs">
            <div class="form-tab" :class="{ active: activeTabIndex === 0 }" @click="switchTab(0)">
              基本信息
            </div>
            <div class="form-tab" :class="{ active: activeTabIndex === 1 }" @click="switchTab(1)">
              工作信息
            </div>
            <div class="form-tab" :class="{ active: activeTabIndex === 2 }" @click="switchTab(2)">
              财务信息
            </div>
          </div>

          <div class="form-section">
            <!-- 基本信息区域 -->
            <div v-show="activeTabIndex === 0">
              <div class="form-row">
                <el-form-item label="员工编号" prop="staff_id">
                  <el-input
                    v-model="form.staff_id"
                    placeholder="请输入员工编号"
                    class="custom-input"
                  />
                </el-form-item>
                <el-form-item label="员工姓名" prop="name">
                  <el-input v-model="form.name" placeholder="请输入员工姓名" class="custom-input" />
                </el-form-item>
              </div>
              <div class="form-row">
                <el-form-item label="拼音简写" prop="pinyin">
                  <el-input
                    v-model="form.pinyin"
                    placeholder="请输入拼音简写"
                    class="custom-input"
                  />
                </el-form-item>
                <el-form-item label="性别" prop="gender">
                  <el-select v-model="form.gender" placeholder="请选择性别" class="custom-input">
                    <el-option label="男" value="男" />
                    <el-option label="女" value="女" />
                  </el-select>
                </el-form-item>
              </div>
              <div class="form-row">
                <el-form-item label="身份证号" prop="id_number">
                  <el-input
                    v-model="form.id_number"
                    placeholder="请输入身份证号"
                    class="custom-input"
                  />
                </el-form-item>
                <el-form-item label="联系电话" prop="tel">
                  <el-input v-model="form.tel" placeholder="请输入联系电话" class="custom-input" />
                </el-form-item>
              </div>
              <div class="form-row full-width">
                <el-form-item label="地址" prop="add">
                  <el-input v-model="form.add" placeholder="请输入地址" class="custom-input" />
                </el-form-item>
              </div>
            </div>

            <!-- 工作信息区域 -->
            <div v-show="activeTabIndex === 1">
              <div class="form-row">
                <el-form-item label="部门" prop="floor">
                  <el-select
                    v-model="form.floor"
                    placeholder="请选择部门"
                    filterable
                    allow-create
                    default-first-option
                    class="custom-input"
                  >
                    <el-option
                      v-for="floor in floorOptions"
                      :key="floor"
                      :label="floor"
                      :value="floor"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="职位" prop="post">
                  <el-select
                    v-model="form.post"
                    placeholder="请选择职位"
                    filterable
                    allow-create
                    default-first-option
                    class="custom-input"
                  >
                    <el-option
                      v-for="post in postOptions"
                      :key="post"
                      :label="post"
                      :value="post"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </div>

            <!-- 财务信息区域 -->
            <div v-show="activeTabIndex === 2">
              <div class="form-row">
                <el-form-item label="银行卡号" prop="bank_card_number">
                  <el-input
                    v-model="form.bank_card_number"
                    placeholder="请输入银行卡号"
                    class="custom-input"
                  />
                </el-form-item>
                <el-form-item label="工资" prop="salary">
                  <el-input
                    v-model.number="form.salary"
                    placeholder="请输入工资"
                    class="custom-input"
                  />
                </el-form-item>
              </div>
              <div class="form-row">
                <el-form-item label="是否结算" prop="clearing">
                  <el-switch
                    v-model="form.clearing"
                    active-text="已结算"
                    inactive-text="未结算"
                    inline-prompt
                  />
                </el-form-item>
              </div>
              <div class="form-row">
                <el-form-item label="年份选择" prop="selectedYear">
                  <el-select
                    v-model="selectedYear"
                    placeholder="请选择年份"
                    clearable
                    class="custom-input"
                  >
                    <el-option
                      v-for="year in yearOptions"
                      :key="year"
                      :label="year"
                      :value="year"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    :disabled="!selectedYear"
                    @click="handleExportSalary"
                  >
                    导出工资
                  </el-button>
                  <el-text type="info" size="small" class="ml-2">
                    将导出当前员工的工资数据
                  </el-text>
                </el-form-item>
              </div>
            </div>
          </div>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogVisible = false" class="cancel-btn">取消</el-button>
            <el-button
              type="primary"
              @click="handleSubmit"
              :loading="submitLoading"
              class="submit-btn"
              >确定</el-button
            >
          </div>
        </template>
      </el-dialog>

      <!-- 导入对话框 -->
      <StaffImport
        v-if="importDialogVisible"
        v-model:visible="importDialogVisible"
        @import-success="handleImportSuccess"
      />
    </template>
  </PageTemplate>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
// 导入员工导入组件
import StaffImport from './components/StaffImport.vue'
import type { FormInstance } from 'element-plus'
// 导入通用页面模板
import PageTemplate from '@/components/common/PageTemplate.vue'
import {
  getStaffList,
  createStaff,
  updateStaff,
  deleteStaff,
  getFloorOptions,
  getPostOptions,
  getGenderOptions,
  exportStaffSalary,
  exportStaffSalaryZip,
} from '@/api/staff'
import type { Staff } from '@/api/staff'
// 引入xlsx库
import * as XLSX from 'xlsx'
// 导入分工年份选项API
import { getDivisionWorkYearOptions } from '@/api/divisionWork'

// 加载状态
const loading = ref(false)
const submitLoading = ref(false)

// 表格数据
const tableData = ref<Staff[]>([])
const total = ref(0)

// 查询参数
const queryParams = reactive({
  pinyin: '',
  floors: [] as string[],
  posts: [] as string[],
  page: 1,
  limit: 10,
})

// 下拉选项
const floorOptions = ref<string[]>([])
const postOptions = ref<string[]>([])
const genderOptions = ref<string[]>([])
const yearOptions = ref<string[]>([])
const selectedYear = ref<string>('')

// 对话框相关
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const importDialogVisible = ref(false)
const activeTabIndex = ref(0) // 当前激活的标签页索引

// 切换标签页
const switchTab = (index: number) => {
  activeTabIndex.value = index
}

// 表单对象
const form = reactive({
  _id: '',
  staff_id: '',
  name: '',
  pinyin: '',
  gender: '',
  id_number: '',
  tel: '',
  add: '',
  bank_card_number: '',
  post: '',
  floor: '',
  clearing: false,
  salary: 0,
})

// 表单校验规则
const rules = {
  staff_id: [{ required: true, message: '请输入员工编号', trigger: 'blur' }],
  name: [{ required: true, message: '请输入员工姓名', trigger: 'blur' }],
}

// 表单引用
const formRef = ref<FormInstance>()
const queryFormRef = ref<FormInstance>()

// 初始化
onMounted(async () => {
  // 加载所有选项数据
  await loadInitialOptions()

  // 加载年份选项
  await loadYearOptions()

  // 加载数据
  await loadData()
})

// 加载数据
const loadData = async (params: any = queryParams) => {
  loading.value = true
  try {
    console.log('开始加载员工数据，查询参数：', params)
    const response = await getStaffList(params)
    console.log('获取到的员工数据：', response)
    tableData.value = response.data.staffList
    total.value = response.data.total

    // 如果没有数据，提示用户
    if (response.data.total === 0) {
      ElMessage.info('未查询到员工数据，请尝试其他筛选条件或添加新数据')
    }
  } catch (error) {
    console.error('加载员工数据失败，详细错误：', error)
    const errorMessage = error instanceof Error ? error.message : '加载员工数据失败'
    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}

// 加载初始选项
const loadInitialOptions = async () => {
  try {
    // 加载部门选项
    const floorResponse = await getFloorOptions()
    floorOptions.value = floorResponse.data || []

    // 如果有部门选择，加载职位选项
    if (queryParams.floors.length > 0) {
      await loadPostOptions(queryParams.floors.join(','))
    } else {
      // 否则加载所有职位
      const postResponse = await getPostOptions()
      postOptions.value = postResponse.data || []
    }

    // 加载性别选项（用于表单编辑）
    const genderResponse = await getGenderOptions()
    genderOptions.value = genderResponse.data || []
  } catch (error) {
    console.error('加载选项数据失败', error)
    ElMessage.error('加载选项数据失败')
  }
}

// 加载职位选项
const loadPostOptions = async (floors: string) => {
  try {
    const response = await getPostOptions(floors)
    postOptions.value = response.data || []
  } catch (error) {
    console.error('加载职位选项失败', error)
    ElMessage.error('加载职位选项失败')
  }
}

// 加载部门选项
const loadFloorOptions = async (posts: string) => {
  try {
    const response = await getFloorOptions(posts)
    floorOptions.value = response.data || []
  } catch (error) {
    console.error('加载部门选项失败', error)
    ElMessage.error('加载部门选项失败')
  }
}

// 部门变更
const handleFloorChange = async () => {
  if (queryParams.floors.length > 0) {
    await loadPostOptions(queryParams.floors.join(','))
  } else {
    // 如果没有选择部门，加载所有职位
    const response = await getPostOptions()
    postOptions.value = response.data || []
  }
  handleQuery()
}

// 职位变更
const handlePostQuery = async () => {
  if (queryParams.posts.length > 0) {
    await loadFloorOptions(queryParams.posts.join(','))
  } else {
    // 如果没有选择职位，加载所有部门
    const response = await getFloorOptions()
    floorOptions.value = response.data || []
  }
  // 直接触发查询
  handleQuery()
}

// 查询
const handleQuery = () => {
  queryParams.page = 1
  loadData()
}

// 重置查询
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryParams.page = 1
  loadData()
}

// 处理分页大小变化
const handleSizeChange = (val: number) => {
  queryParams.limit = val
  loadData()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  queryParams.page = val
  loadData()
}

// 新增员工
const handleAdd = () => {
  dialogType.value = 'add'
  resetForm()
  activeTabIndex.value = 0 // 重置标签页索引
  dialogVisible.value = true
}

// 编辑员工
const handleEdit = (row: Staff) => {
  dialogType.value = 'edit'
  resetForm()

  // 填充表单数据 - 使用类型安全的方式
  form._id = row._id
  form.staff_id = row.staff_id
  form.name = row.name
  form.pinyin = row.pinyin || ''
  form.gender = row.gender || ''
  form.id_number = row.id_number || ''
  form.tel = row.tel || ''
  form.add = row.add || ''
  form.bank_card_number = row.bank_card_number || ''
  form.post = row.post || ''
  form.floor = row.floor || ''
  form.clearing = row.clearing || false
  form.salary = row.salary || 0

  activeTabIndex.value = 0 // 重置标签页索引
  dialogVisible.value = true
  loadYearOptions()
}

// 删除员工
const handleDelete = (row: Staff) => {
  ElMessageBox.confirm(`确定要删除员工 ${row.name} 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        await deleteStaff(row._id)
        ElMessage.success('删除成功')
        loadData()
      } catch (error) {
        console.error('删除员工失败', error)
        const errorMessage = error instanceof Error ? error.message : '删除员工失败'
        ElMessage.error(errorMessage)
      }
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 重置表单
const resetForm = () => {
  // 重置表单数据 - 使用类型安全的方式
  form._id = ''
  form.staff_id = ''
  form.name = ''
  form.pinyin = ''
  form.gender = ''
  form.id_number = ''
  form.tel = ''
  form.add = ''
  form.bank_card_number = ''
  form.post = ''
  form.floor = ''
  form.clearing = false
  form.salary = 0

  // 重置表单验证
  formRef.value?.resetFields()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.error('请填写必填项')
      return
    }

    submitLoading.value = true
    try {
      if (dialogType.value === 'add') {
        // 创建员工
        await createStaff({
          staff_id: form.staff_id,
          name: form.name,
          pinyin: form.pinyin,
          gender: form.gender,
          id_number: form.id_number,
          tel: form.tel,
          add: form.add,
          bank_card_number: form.bank_card_number,
          post: form.post,
          floor: form.floor,
          clearing: form.clearing,
          salary: form.salary,
        })
        ElMessage.success('创建成功')
      } else {
        // 更新员工
        await updateStaff(form._id, {
          staff_id: form.staff_id,
          name: form.name,
          pinyin: form.pinyin,
          gender: form.gender,
          id_number: form.id_number,
          tel: form.tel,
          add: form.add,
          bank_card_number: form.bank_card_number,
          post: form.post,
          floor: form.floor,
          clearing: form.clearing,
          salary: form.salary,
        })
        ElMessage.success('更新成功')
      }

      dialogVisible.value = false
      loadData()
    } catch (error) {
      console.error('保存员工失败', error)
      const errorMessage = error instanceof Error ? error.message : '保存员工失败'
      ElMessage.error(errorMessage)
    } finally {
      submitLoading.value = false
    }
  })
}

// 导出Excel
const handleExport = async () => {
  ElMessageBox.confirm('确定要导出当前筛选条件下的员工数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info',
  })
    .then(async () => {
      try {
        // 获取所有数据（不分页）
        const exportParams = { ...queryParams, page: 1, limit: 10000 }
        const response = await getStaffList(exportParams)
        const data = response.data.staffList || []

        if (data.length === 0) {
          ElMessage.warning('没有数据可导出')
          return
        }

        // 准备Excel数据
        const exportData = data.map((item) => ({
          员工编号: item.staff_id,
          员工姓名: item.name,
          拼音简写: item.pinyin,
          性别: item.gender,
          身份证号: item.id_number,
          联系电话: item.tel,
          地址: item.add,
          银行卡号: item.bank_card_number,
          职位: item.post,
          部门: item.floor,
          是否结算: item.clearing ? '是' : '否',
          工资: item.salary,
        }))

        // 创建工作簿和工作表
        const wb = XLSX.utils.book_new()
        const ws = XLSX.utils.json_to_sheet(exportData)

        // 添加工作表到工作簿
        XLSX.utils.book_append_sheet(wb, ws, '员工信息')

        // 导出Excel文件
        XLSX.writeFile(wb, `员工信息_${new Date().toISOString().split('T')[0]}.xlsx`)

        ElMessage.success('导出成功')
      } catch (error) {
        console.error('导出失败', error)
        ElMessage.error('导出失败')
      }
    })
    .catch(() => {
      // 用户取消导出
    })
}

// 显示导入对话框
const showImportDialog = () => {
  importDialogVisible.value = true
}

// 导入成功回调
const handleImportSuccess = () => {
  // 重新加载选项数据
  loadInitialOptions()
  // 重新加载员工数据
  loadData()
  ElMessage.success('数据导入成功，已更新列表')
}

// 加载年份选项
const loadYearOptions = async () => {
  try {
    const response = await getDivisionWorkYearOptions() as any
    console.log('获取到的年份选项数据：', response)
    if (response && response.data && response.data.years) {
      yearOptions.value = response.data.years
    }
  } catch (error) {
    console.error('加载年份选项失败', error)
    ElMessage.error('加载年份选项失败')
  }
}





// 导出所有员工工资为ZIP
const handleExportAllSalary = async () => {
  // 先加载年份选项，确保有数据
  if (yearOptions.value.length === 0) {
    await loadYearOptions()
  }

  if (yearOptions.value.length === 0) {
    ElMessage.warning('没有可用的年份数据')
    return
  }

  // 使用确认对话框代替
  ElMessageBox.confirm(`确定要导出 ${yearOptions.value[0]} 年所有员工工资数据吗？`, '导出确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    // 使用第一个年份作为默认值
    const year = yearOptions.value[0]

    ElMessage.info({
      message: '正在准备下载，请稍候...',
      duration: 3000
    })

    // 调用API下载ZIP文件
    exportStaffSalaryZip(year)
  }).catch(() => {
    // 用户取消操作
  })
}

// 导出员工工资
const handleExportSalary = async () => {
  if (!selectedYear.value) {
    ElMessage.warning('请先选择年份')
    return
  }

  if (!form.staff_id) {
    ElMessage.warning('当前员工信息不完整，无法导出工资')
    return
  }

  const loadingInstance = ElLoading.service({
    text: '正在导出员工工资数据，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)',
  })

  try {
    // 调用后端API导出员工工资，使用当前编辑的员工ID
    const response = await exportStaffSalary(selectedYear.value, form.staff_id)

    if (response && response.data) {
      const { staffSalaryData, totalData } = response.data

      // 创建工作簿
      const wb = XLSX.utils.book_new()

      // 创建明细工作表
      if (staffSalaryData && staffSalaryData.length > 0) {
        const detailWs = XLSX.utils.json_to_sheet(staffSalaryData)
        XLSX.utils.book_append_sheet(wb, detailWs, '明细')
      } else {
        // 如果没有明细数据，创建一个空的工作表
        const emptyDetailWs = XLSX.utils.aoa_to_sheet([['没有找到相关的工资明细数据']])
        XLSX.utils.book_append_sheet(wb, emptyDetailWs, '明细')
      }

      // 创建总计工作表
      if (totalData && totalData.length > 0) {
        const totalWs = XLSX.utils.json_to_sheet(totalData)
        XLSX.utils.book_append_sheet(wb, totalWs, '总计')
      } else {
        // 如果没有总计数据，创建一个空的工作表
        const emptyTotalWs = XLSX.utils.aoa_to_sheet([['没有找到相关的工资汇总数据']])
        XLSX.utils.book_append_sheet(wb, emptyTotalWs, '总计')
      }

      // 生成文件名
      let fileName = `员工工资_${selectedYear.value}`

      // 添加当前员工信息到文件名
      fileName += `_${form.name}(${form.staff_id})`

      fileName += `_${new Date().toISOString().split('T')[0]}.xlsx`

      // 导出Excel文件
      XLSX.writeFile(wb, fileName)

      ElMessage.success('导出成功')
    } else {
      ElMessage.warning('没有找到相关的工资数据')
    }
  } catch (error) {
    console.error('导出员工工资失败', error)
    ElMessage.error('导出员工工资失败，请重试')
  } finally {
    loadingInstance.close()
  }
}
</script>

<style scoped>
/* 页面特定样式 */

/* 员工对话框样式 */
.staff-dialog {
  :deep(.el-dialog) {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  :deep(.el-dialog__header) {
    margin: 0;
    padding: 16px 20px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    background-color: var(--el-color-primary-light-9);
    text-align: center; /* 标题居中 */
    position: relative;
  }

  :deep(.el-dialog__headerbtn) {
    top: 16px;
    right: 16px;
    width: 24px;
    height: 24px;

    .el-dialog__close {
      color: var(--el-color-primary);
      font-weight: bold;
      transition: all 0.3s;

      &:hover {
        transform: rotate(90deg);
        color: var(--el-color-danger);
      }
    }
  }

  :deep(.el-dialog__title) {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-color-primary);
    position: relative;
    display: inline-block;
    padding: 0 15px;

    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      width: 20px;
      height: 1px;
      background-color: var(--el-color-primary-light-5);
    }

    &::before {
      left: -15px;
    }

    &::after {
      right: -15px;
    }
  }

  :deep(.el-dialog__body) {
    padding: 20px;
  }

  :deep(.el-dialog__footer) {
    padding: 10px 20px 16px;
    border-top: 1px solid var(--el-border-color-lighter);
    background-color: var(--el-fill-color-light);
  }
}

/* 表单样式 */
.staff-form {
  max-height: 60vh;
  overflow-y: auto;
}

.form-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.form-tab {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: var(--el-text-color-secondary);
  border-bottom: 2px solid transparent;
  transition: all 0.3s;

  &.active {
    color: var(--el-color-primary);
    border-bottom-color: var(--el-color-primary);
    font-weight: 500;
  }

  &:hover {
    color: var(--el-color-primary-light-3);
  }
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: flex;
  gap: 24px;
  margin-bottom: 4px;

  :deep(.el-form-item) {
    margin-bottom: 16px;
    flex: 1;

    .el-form-item__label {
      font-weight: 500;
      color: var(--el-text-color-regular);
    }

    .el-form-item__content {
      display: flex;
      align-items: center;
    }
  }
}

.form-row.full-width {
  flex-direction: column;
}

.custom-input {
  width: 170px;

  :deep(.el-input__wrapper) {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;

    &:hover,
    &:focus {
      box-shadow: 0 0 0 1px var(--el-color-primary-light-5);
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 20px;

  .cancel-btn,
  .submit-btn {
    min-width: 90px;
    padding: 8px 20px;
    border-radius: 4px;
    transition: all 0.3s;
  }

  .cancel-btn {
    border-color: var(--el-border-color);

    &:hover {
      color: var(--el-color-primary);
      border-color: var(--el-color-primary-light-5);
      background-color: var(--el-color-primary-light-9);
    }
  }

  .submit-btn {
    font-weight: 500;

    &:hover {
      background-color: var(--el-color-primary-dark-1);
    }
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form-right {
    margin-top: 10px;
  }
}

/* 表格工具栏 */
.table-tools {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
