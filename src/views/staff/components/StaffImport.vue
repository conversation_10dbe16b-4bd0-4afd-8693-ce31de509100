<template>
  <!-- 导入对话框 - 简化版本 -->
  <el-dialog
    v-model="dialogVisible"
    title="Excel导入"
    width="80%"
    destroy-on-close
    class="import-dialog"
  >
    <div class="import-container">
      <!-- 预览区 -->
      <div class="preview-section">
        <div class="file-info" v-if="importFile">
          <el-tag type="success" effect="plain" class="file-tag">
            <el-icon><Document /></el-icon>
            {{ importFile.name }}
          </el-tag>
          <span v-if="selectedSheet" class="sheet-info">
            工作表: <el-tag size="small" type="info">{{ selectedSheet }}</el-tag>
          </span>
          <el-button type="danger" link @click="clearImportFile" class="clear-btn">清除</el-button>
        </div>

        <div v-if="!importFile" class="upload-area">
          <el-upload
            class="excel-uploader"
            :auto-upload="false"
            :show-file-list="false"
            accept=".xlsx,.xls"
            :on-change="handleFileChange"
          >
            <el-button type="primary">
              <el-icon><UploadFilled /></el-icon>
              选择Excel文件
            </el-button>
            <div class="el-upload__tip">只支持 .xlsx, .xls 格式文件</div>
          </el-upload>
        </div>

        <div v-if="previewData.length > 0" class="preview-table-container">
          <el-table
            :data="previewData"
            border
            stripe
            height="400"
            v-loading="previewLoading"
            :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          >
            <el-table-column
              v-for="column in previewColumns"
              :key="column"
              :prop="column"
              :label="column"
              :min-width="120"
            />
          </el-table>

          <div class="preview-info">
            <div class="preview-stats">
              <span
                >总记录数: <strong>{{ totalRecords }}</strong></span
              >
              <span
                >预览记录数: <strong>{{ previewData.length }}</strong></span
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button
          type="primary"
          @click="handleImport"
          :disabled="!importFile || previewData.length === 0"
          :loading="importLoading"
        >
          导入数据
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, UploadFilled } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import { importJsonData } from '@/api/staff'

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  file: {
    type: [File, Object, null],
    default: null,
    required: false,
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'import-success'])

// 对话框可见性
const dialogVisible = ref(props.visible)

// 监听 visible 属性变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
  }
)

// 监听对话框可见性变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
  // 如果对话框关闭，清除导入文件和相关数据
  if (!newVal) {
    clearImportFile()
  }
})

// 监听文件属性变化
watch(
  () => props.file,
  (newFile) => {
    if (newFile && newFile instanceof File) {
      // 清除之前的数据
      clearImportFile()
      // 设置新文件
      importFile.value = newFile
      // 读取Excel文件的工作表列表并自动选择员工信息sheet
      readExcelSheets(newFile)
    }
  },
  { immediate: true }
)

// 导入相关状态
const importFile = ref<File | null>(null)
const previewData = ref<any[]>([])
const previewColumns = ref<string[]>([])
const previewLoading = ref(false)
const importLoading = ref(false)
const sheetNames = ref<string[]>([])
const selectedSheet = ref('')
const totalRecords = ref(0)

// 清除导入文件
const clearImportFile = () => {
  importFile.value = null
  previewData.value = []
  previewColumns.value = []
  sheetNames.value = []
  selectedSheet.value = ''
  totalRecords.value = 0
}

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false
}

// 文件变更
const handleFileChange = async (file: any) => {
  if (file.raw) {
    // 清除之前的数据
    clearImportFile()

    // 设置新文件
    importFile.value = file.raw

    // 读取Excel文件的工作表列表并自动选择员工信息sheet
    await readExcelSheets(file.raw)
  }
}

// 读取Excel工作表列表
const readExcelSheets = async (file: File) => {
  previewLoading.value = true
  try {
    const data = await file.arrayBuffer()
    const workbook = XLSX.read(data)

    // 获取所有工作表名称
    sheetNames.value = workbook.SheetNames

    // 自动选择名为"员工信息"的工作表，如果没有则选择第一个
    const targetSheetName =
      workbook.SheetNames.find((name) => name.includes('员工信息')) || workbook.SheetNames[0]

    if (targetSheetName) {
      selectedSheet.value = targetSheetName
      await previewExcelData(workbook, targetSheetName)
    }
  } catch (error) {
    console.error('读取Excel文件失败', error)
    ElMessage.error('读取Excel文件失败')
  } finally {
    previewLoading.value = false
  }
}

// 预览Excel数据
const previewExcelData = async (workbook: XLSX.WorkBook, sheetName: string) => {
  previewLoading.value = true
  try {
    // 获取指定工作表
    const worksheet = workbook.Sheets[sheetName]

    // 将工作表转换为JSON
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { defval: '' })

    // 过滤掉空行（所有字段都为空的行）
    const filteredData = jsonData.filter((row: any) => {
      return Object.values(row).some((value) => value !== '')
    })

    totalRecords.value = filteredData.length

    // 获取列名
    if (filteredData.length > 0) {
      // 使用类型断言确保类型安全
      const firstRow = filteredData[0] as Record<string, unknown>
      previewColumns.value = Object.keys(firstRow)
    }

    // 设置预览数据（最多显示100条）
    previewData.value = filteredData.slice(0, 100)

    // 如果没有数据，显示提示
    if (filteredData.length === 0) {
      ElMessage.warning('Excel文件中没有有效数据')
    }
  } catch (error) {
    console.error('预览Excel数据失败', error)
    ElMessage.error('预览Excel数据失败')
  } finally {
    previewLoading.value = false
  }
}

// 处理导入
const handleImport = async () => {
  if (!importFile.value || previewData.value.length === 0) {
    ElMessage.warning('没有可导入的数据')
    return
  }

  // 确认导入
  try {
    await ElMessageBox.confirm(`确定要导入 ${totalRecords.value} 条员工数据吗？`, '确认导入', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
  } catch (error) {
    // 用户取消导入
    return
  }

  importLoading.value = true
  try {
    // 读取完整数据
    const data = await importFile.value.arrayBuffer()
    const workbook = XLSX.read(data)
    const worksheet = workbook.Sheets[selectedSheet.value]
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { defval: '' })

    // 过滤掉空行
    const filteredData = jsonData.filter((row: any) => {
      return Object.values(row).some((value) => value !== '')
    })

    // 转换字段名称为后端需要的格式
    const mappedData = filteredData.map((item: any) => {
      const mappedItem: any = {}

      // 映射字段名称
      Object.keys(item).forEach((key) => {
        if (key === '员工编号') mappedItem.staff_id = item[key]
        else if (key === '员工姓名') mappedItem.name = item[key]
        else if (key === '拼音简写') mappedItem.pinyin = item[key]
        else if (key === '性别') mappedItem.gender = item[key]
        else if (key === '身份证号') mappedItem.id_number = item[key]
        else if (key === '联系电话') mappedItem.tel = item[key]
        else if (key === '地址') mappedItem.add = item[key]
        else if (key === '银行卡号') mappedItem.bank_card_number = item[key]
        else if (key === '职位') mappedItem.post = item[key]
        else if (key === '部门') mappedItem.floor = item[key]
        else if (key === '是否结算') mappedItem.clearing = item[key] === '是' || false
        else if (key === '工资') mappedItem.salary = Number(item[key]) || 0
        else mappedItem[key] = item[key] // 保留其他字段
      })

      return mappedItem
    })

    // 发送数据到后端
    console.log('准备发送数据到后端，数据长度:', mappedData.length)

    try {
      const importResult = await importJsonData({
        sheetName: selectedSheet.value,
        data: mappedData,
        fileName: importFile.value.name,
        totalRecords: mappedData.length,
      })

      // 详细记录响应数据
      console.log('导入结果:', importResult)
      console.log('结果类型:', typeof importResult)
      if (importResult) {
        console.log('结果属性:', Object.keys(importResult))
      }

      if (importResult && typeof importResult === 'object') {
        if ('success' in importResult && importResult.success === true) {
          console.log('导入成功，显示成功消息')
          ElMessage.success(importResult.message || '导入成功')
          emit('import-success')
          closeDialog()
        } else if ('message' in importResult) {
          console.log('导入失败，显示错误消息:', importResult.message)
          ElMessage.error(importResult.message)
        } else {
          console.log('导入失败，无错误消息')
          ElMessage.error('导入失败')
        }
      } else {
        // 如果返回的不是预期的对象格式
        console.log('返回结果不是预期的对象格式，默认显示成功')
        ElMessage.success('导入成功')
        emit('import-success')
        closeDialog()
      }
    } catch (innerError) {
      console.error('导入过程中发生异常:', innerError)
      ElMessage.error('导入过程中发生异常')
    }
  } catch (error) {
    console.error('导入数据失败', error)
    const errorMessage = error instanceof Error ? error.message : '导入数据失败'
    ElMessage.error(errorMessage)
  } finally {
    importLoading.value = false
  }
}
</script>

<style scoped>
/* 导入对话框样式 */
.import-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }

  .import-container {
    min-height: 500px;
    display: flex;
    flex-direction: column;
  }

  .preview-section {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .file-info {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    gap: 12px;
  }

  .file-tag {
    font-size: 14px;
  }

  .sheet-info {
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }

  .clear-btn {
    margin-left: auto;
  }

  .preview-table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .preview-info {
    margin-top: 12px;

    .preview-stats {
      display: flex;
      justify-content: space-between;
    }
  }

  .excel-uploader {
    margin: 16px 0;
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  border: 1px dashed var(--el-border-color);
  border-radius: 4px;
  background-color: var(--el-fill-color-light);
}
</style>
