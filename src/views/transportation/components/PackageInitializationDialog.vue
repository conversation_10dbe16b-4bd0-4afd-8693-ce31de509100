<template>
  <el-dialog
    v-model="dialogVisible"
    title="导入库存包裹"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="package-initialization-dialog">
      <!-- 步骤式选项卡 -->
      <el-tabs v-model="activeTab" type="card" class="package-tabs">
        <!-- 第一步：下载模板 -->
        <el-tab-pane label="第一步：下载模板" name="template" :disabled="false">
          <div class="template-section">
            <div class="step-content">
              <div class="step-icon">
                <el-icon size="24"><Download /></el-icon>
              </div>
              <h4 class="step-title">下载Excel模板</h4>
              <p class="step-description">请先下载标准模板，按照格式填写包裹数据</p>
              <div class="step-actions">
                <el-button type="primary" @click="downloadTemplate" :icon="Download" size="large">
                  下载Excel模板
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 第二步：上传文件 -->
        <el-tab-pane label="第二步：上传文件" name="upload" :disabled="false">
          <div class="upload-section">
            <div class="step-content">
              <div class="step-icon">
                <el-icon size="24"><UploadFilled /></el-icon>
              </div>
              <h4 class="step-title">上传Excel文件</h4>
              <p class="step-description">请上传按照模板格式填写的包裹数据文件</p>
              <div class="step-actions">
                <el-upload
                  ref="uploadRef"
                  class="upload-demo"
                  drag
                  :auto-upload="false"
                  :limit="1"
                  :on-change="handleFileChange"
                  :on-exceed="handleExceed"
                  accept=".xlsx,.xls"
                >
                  <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                  <div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>
                  <template #tip>
                    <div class="el-upload__tip">只能上传 .xlsx/.xls 文件，且不超过 10MB</div>
                  </template>
                </el-upload>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 第三步：预览数据 -->
        <el-tab-pane label="第三步：预览数据" name="preview" :disabled="previewData.length === 0">
          <div class="preview-section">
            <div class="step-content">
              <div class="step-icon">
                <el-icon size="24"><View /></el-icon>
              </div>
              <h4 class="step-title">预览数据</h4>
              <p class="step-description">检查上传的数据是否正确，确认后可进行导入</p>

              <div v-if="previewData.length > 0" class="preview-data">
                <div class="data-summary mb-4">
                  <el-tag type="info" size="large">
                    共 {{ previewData.length }} 条Excel行数据
                  </el-tag>
                  <el-tag type="success" size="large" class="ml-2">
                    预计生成：{{ dataSummary.independentPackages }} 个独立包裹
                  </el-tag>
                  <el-tag type="warning" size="large" class="ml-2">
                    {{ dataSummary.mixedPackageGroups }} 个混合包裹
                  </el-tag>
                </div>

                <el-table :data="previewData.slice(0, 10)" border size="small" max-height="400">
                  <el-table-column prop="clothing_name" label="服装名称" width="150" />
                  <el-table-column prop="original_quantity" label="初始数量" width="100" />
                  <el-table-column prop="current_quantity" label="当前数量" width="100" />
                  <el-table-column prop="clothing_id" label="服装ID" width="100" />
                  <el-table-column prop="oem_clothing_id" label="OEM服装ID" width="120" />
                  <el-table-column prop="warehouse_name" label="仓库名称" width="120" />
                  <el-table-column prop="package_count" label="包裹数" width="80" />
                  <el-table-column prop="package_type" label="package_type" width="120" />
                </el-table>

                <div class="mt-3 text-center">
                  <el-button
                    type="primary"
                    size="large"
                    @click="activeTab = 'import'"
                    :disabled="importing"
                  >
                    确认数据，开始导入
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 第四步：执行导入 -->
        <el-tab-pane label="第四步：执行导入" name="import" :disabled="previewData.length === 0">
          <div class="import-section">
            <div class="step-content">
              <div class="step-icon">
                <el-icon size="24"><Upload /></el-icon>
              </div>
              <h4 class="step-title">执行导入</h4>
              <p class="step-description">开始导入包裹数据到系统中</p>

              <div class="import-actions mb-6">
                <el-button
                  type="primary"
                  size="large"
                  @click="handleImport"
                  :disabled="importing || previewData.length === 0"
                  :loading="importing"
                >
                  {{ importing ? '导入中...' : '开始导入' }}
                </el-button>
              </div>

              <!-- 进度条 -->
              <div v-if="importing" class="progress-section mb-6">
                <el-progress :percentage="importProgress" :status="importStatus" />
                <div class="mt-2 text-center text-sm text-gray-600">
                  {{ importMessage }}
                </div>
              </div>

              <!-- 导入结果 -->
              <div v-if="importResult" class="result-section">
                <el-alert
                  :title="importResult.message || '操作完成'"
                  :type="importResult.code === 200 ? 'success' : 'error'"
                  :closable="false"
                  show-icon
                >
                  <template #default>
                    <div class="text-sm">
                      <div class="result-stats mb-4">
                        <el-row :gutter="16">
                          <el-col :span="6">
                            <el-statistic
                              title="总计请求"
                              :value="importResult.data?.total_requested || 0"
                              suffix="个包裹"
                            />
                          </el-col>
                          <el-col :span="6">
                            <el-statistic
                              title="成功创建"
                              :value="importResult.data?.successful_created || 0"
                              suffix="个包裹"
                            />
                          </el-col>
                          <el-col :span="6">
                            <el-statistic
                              title="创建失败"
                              :value="importResult.data?.failed_created || 0"
                              suffix="个包裹"
                            />
                          </el-col>
                          <el-col :span="6">
                            <div class="batch-code">
                              <div class="text-xs text-gray-500">批次号</div>
                              <div class="text-sm font-mono">
                                {{ importResult.data?.batch_code || '无' }}
                              </div>
                            </div>
                          </el-col>
                        </el-row>
                      </div>

                      <div
                        v-if="
                          importResult.data?.created_packages &&
                          importResult.data.created_packages.length > 0
                        "
                        class="mt-4"
                      >
                        <p class="font-medium text-green-600 mb-2">
                          成功创建的包裹 ({{ importResult.data.created_packages.length }}个):
                        </p>
                        <div class="max-h-40 overflow-y-auto border rounded p-2 bg-green-50">
                          <div class="grid grid-cols-4 gap-2">
                            <span
                              v-for="packageCode in importResult.data.created_packages"
                              :key="packageCode"
                              class="text-xs text-green-700 font-mono bg-white px-2 py-1 rounded"
                            >
                              {{ packageCode }}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div
                        v-if="
                          importResult.data?.failed_packages &&
                          importResult.data.failed_packages.length > 0
                        "
                        class="mt-4"
                      >
                        <p class="font-medium text-red-600 mb-2">失败详情:</p>
                        <div class="max-h-32 overflow-y-auto border rounded p-2 bg-red-50">
                          <ul class="list-disc list-inside">
                            <li
                              v-for="failed in importResult.data.failed_packages"
                              :key="failed.package_code"
                              class="text-red-700 text-sm"
                            >
                              {{ failed.package_code }}: {{ failed.reason }}
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </template>
                </el-alert>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="importing">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, UploadFilled, View, Upload } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import { initializePackages } from '@/api/transportation'
import {
  validatePackageInitializationData,
  sanitizePackageInitializationData,
  formatValidationResult,
  generatePackageInitializationStats,
} from '@/utils/packageInitializationValidator'

// Props
interface Props {
  visible: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const uploadRef = ref()
const activeTab = ref('template') // 当前激活的选项卡
const previewData = ref<any[]>([])
const importing = ref(false)
const importProgress = ref(0)
const importStatus = ref<'success' | 'exception' | undefined>(undefined)
const importMessage = ref('')
const importResult = ref<any>(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

// 格式化Excel日期为标准字符串格式
function formatExcelDate(dateInput: any): string {
  if (!dateInput) return ''

  // 如果是数字，可能是Excel日期序列号
  if (typeof dateInput === 'number') {
    // Excel日期序列号从1900年1月1日开始计算
    const excelEpoch = new Date(1900, 0, 1)
    const daysSinceEpoch = dateInput - 1 // Excel从1开始计数
    const date = new Date(excelEpoch.getTime() + daysSinceEpoch * 24 * 60 * 60 * 1000)

    // 验证日期是否合理
    if (date.getFullYear() >= 1900 && date.getFullYear() <= 2100) {
      return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`
    }
  }

  // 转换为字符串进行格式解析
  const dateStr = String(dateInput).trim()
  if (!dateStr) return ''

  // 支持的日期格式模式
  const patterns = [
    // YYYY/M/D 或 YYYY/MM/DD 格式 - 直接返回
    /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,
    // YYYY-M-D 或 YYYY-MM-DD 格式 - 转换为YYYY/M/D
    /^(\d{4})-(\d{1,2})-(\d{1,2})$/,
    // M/D/YYYY 或 MM/DD/YYYY 格式 - 转换为YYYY/M/D
    /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
    // M-D-YYYY 或 MM-DD-YYYY 格式 - 转换为YYYY/M/D
    /^(\d{1,2})-(\d{1,2})-(\d{4})$/,
  ]

  for (let i = 0; i < patterns.length; i++) {
    const match = dateStr.match(patterns[i])
    if (match) {
      let year: number, month: number, day: number

      if (i === 0) {
        // YYYY/M/D 格式，直接返回
        return dateStr
      } else if (i === 1) {
        // YYYY-M-D 格式，转换为YYYY/M/D
        year = parseInt(match[1])
        month = parseInt(match[2])
        day = parseInt(match[3])
        return `${year}/${month}/${day}`
      } else {
        // M/D/YYYY 或 M-D-YYYY 格式，转换为YYYY/M/D
        month = parseInt(match[1])
        day = parseInt(match[2])
        year = parseInt(match[3])
        return `${year}/${month}/${day}`
      }
    }
  }

  // 尝试使用JavaScript原生Date解析
  const nativeDate = new Date(dateStr)
  if (!isNaN(nativeDate.getTime())) {
    return `${nativeDate.getFullYear()}/${nativeDate.getMonth() + 1}/${nativeDate.getDate()}`
  }

  // 如果都无法解析，返回原始字符串
  return dateStr
}

// 数据统计信息
const dataSummary = computed(() => {
  if (previewData.value.length === 0) {
    return { totalRows: 0, mixedPackageGroups: 0, independentPackages: 0 }
  }

  let mixedPackageGroups = 0
  let independentPackages = 0
  const packageTypeSet = new Set<string>()

  for (const row of previewData.value) {
    if (row.package_type && row.package_type.trim() !== '') {
      packageTypeSet.add(row.package_type.trim())
    } else {
      const packageCount = row.package_count || 1
      independentPackages += packageCount
    }
  }

  mixedPackageGroups = packageTypeSet.size

  return {
    totalRows: previewData.value.length,
    mixedPackageGroups,
    independentPackages,
  }
})

// 下载Excel模板
const downloadTemplate = () => {
  // 创建模板数据（新业务逻辑）
  const templateData = [
    // 单一包裹示例 - 生成5个独立包裹
    {
      服装名称: '示例服装1',
      初始数量: 100,
      当前数量: 100,
      服装ID: 'CLT001',
      OEM服装ID: '',
      仓库名称: '示例仓库1',
      包裹数: 5,
      入库日期: '2025/1/15', // 支持多种格式
      package_type: '',
    },
    // 小数包裹示例 - 生成3个完整包裹+1个70%的部分包裹
    {
      服装名称: '示例服装2',
      初始数量: 100,
      当前数量: '', // 空值，自动设置为等于初始数量
      服装ID: 'CLT002',
      OEM服装ID: '',
      仓库名称: '示例仓库1',
      包裹数: 3.7,
      入库日期: '2025-01-12', // ISO格式示例
      package_type: '',
    },
    // 部分出货包裹示例 - 包裹数为空，当前数量小于初始数量
    {
      服装名称: '示例服装3',
      初始数量: 200,
      当前数量: 150,
      服装ID: 'CLT003',
      OEM服装ID: '',
      仓库名称: '示例仓库1',
      包裹数: '',
      入库日期: '01/10/2025', // 美式格式示例
      package_type: '',
    },
    // 混合包裹示例 - 第一个产品
    {
      服装名称: '混合包裹服装1',
      初始数量: 50,
      当前数量: 50,
      服装ID: 'CLT004',
      OEM服装ID: '',
      仓库名称: '示例仓库2',
      包裹数: '',
      入库日期: '2025/1/20',
      package_type: 'MIX001',
    },
    // 混合包裹示例 - 第二个产品（相同package_type）
    {
      服装名称: '混合包裹服装2',
      初始数量: 30,
      当前数量: 30,
      服装ID: '',
      OEM服装ID: 'OEM001',
      仓库名称: '示例仓库2',
      包裹数: '',
      入库日期: '2025/1/20',
      package_type: 'MIX001',
    },
  ]

  // 创建说明数据
  const instructionData = [
    { 字段名称: '服装名称', 是否必填: '是', 说明: '产品的显示名称' },
    { 字段名称: '初始数量', 是否必填: '是', 说明: '包裹中该产品的原始数量' },
    {
      字段名称: '当前数量',
      是否必填: '否',
      说明: '包裹中该产品的剩余数量，为空时自动等于初始数量',
    },
    { 字段名称: '服装ID', 是否必填: '二选一', 说明: '普通服装的标识符，与OEM服装ID二选一' },
    { 字段名称: 'OEM服装ID', 是否必填: '二选一', 说明: 'OEM服装的标识符，与服装ID二选一' },
    { 字段名称: '仓库名称', 是否必填: '是', 说明: '仓库的中文名称，系统会自动转换为仓库ID' },
    {
      字段名称: '包裹数',
      是否必填: '否',
      说明: '生成包裹的数量，支持小数（如3.7），为空时表示已部分出货',
    },
    {
      字段名称: '入库日期',
      是否必填: '是',
      说明: '支持多种格式：2025/7/27、2025-07-27、07/27/2025、Excel日期等',
    },
    { 字段名称: 'package_type', 是否必填: '否', 说明: '包裹分组标识，相同值的行会合并为混合包裹' },
    { 字段名称: '', 是否必填: '', 说明: '' },
    { 字段名称: '系统自动生成', 是否必填: '', 说明: '以下字段由系统自动生成，无需填写' },
    { 字段名称: '包裹编码', 是否必填: '自动', 说明: '格式：INIT2025xxxx（4位递增数字）' },
    { 字段名称: 'SKU', 是否必填: '自动', 说明: 'CLO_服装ID 或 OEM_OEM服装ID' },
    { 字段名称: '供应商', 是否必填: '自动', 说明: '统一设置为"初始"' },
    { 字段名称: '货运单ID', 是否必填: '自动', 说明: '统一设置为"FH2025999"' },
    { 字段名称: '系列号', 是否必填: '自动', 说明: '递增数字（从1开始）' },
    { 字段名称: '', 是否必填: '', 说明: '' },
    { 字段名称: '业务规则说明', 是否必填: '', 说明: '' },
    { 字段名称: '当前数量空值', 是否必填: '', 说明: '当前数量为空时，自动设置为等于初始数量' },
    { 字段名称: '包裹数小数', 是否必填: '', 说明: '包裹数3.7=3个完整包裹+1个70%的部分包裹' },
    { 字段名称: '独立包裹', 是否必填: '', 说明: '包裹数为整数时，生成对应数量的独立包裹' },
    {
      字段名称: '部分出货包裹',
      是否必填: '',
      说明: '包裹数为空且当前数量<初始数量时，生成partially_shipped状态包裹',
    },
    { 字段名称: '混合包裹', 是否必填: '', 说明: 'package_type相同的多行数据合并为一个混合包裹' },
  ]

  // 创建工作簿
  const wb = XLSX.utils.book_new()

  // 创建数据模板工作表
  const ws = XLSX.utils.json_to_sheet(templateData)

  // 设置列宽
  const colWidths = [
    { wch: 20 }, // 服装名称
    { wch: 10 }, // 初始数量
    { wch: 10 }, // 当前数量
    { wch: 12 }, // 服装ID
    { wch: 15 }, // OEM服装ID
    { wch: 15 }, // 仓库名称
    { wch: 8 }, // 包裹数
    { wch: 12 }, // 入库日期
    { wch: 15 }, // package_type
  ]
  ws['!cols'] = colWidths

  // 创建说明工作表
  const instructionWs = XLSX.utils.json_to_sheet(instructionData)
  const instructionColWidths = [
    { wch: 15 }, // 字段名称
    { wch: 10 }, // 是否必填
    { wch: 50 }, // 说明
  ]
  instructionWs['!cols'] = instructionColWidths

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, instructionWs, '使用说明')
  XLSX.utils.book_append_sheet(wb, ws, '数据模板')

  // 导出文件
  XLSX.writeFile(wb, '包裹初始化模板（新业务逻辑）.xlsx')
  ElMessage.success('模板下载成功')
}

// 处理文件变化
const handleFileChange = (file: any) => {
  if (file.raw) {
    parseExcelFile(file.raw)
  }
}

// 处理文件超出限制
const handleExceed = () => {
  ElMessage.warning('只能上传一个文件')
}

// 解析Excel文件
const parseExcelFile = (file: File) => {
  const reader = new FileReader()

  reader.onload = (e: any) => {
    try {
      // 解析Excel数据
      const data = new Uint8Array(e.target.result)
      const workbook = XLSX.read(data, { type: 'array' })

      // 获取第一个工作表
      const firstSheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[firstSheetName]

      // 转换为JSON
      let jsonData = XLSX.utils.sheet_to_json(worksheet) as Record<string, any>[]

      // 过滤空白行
      jsonData = jsonData.filter((row) => {
        return Object.values(row).some((value) => {
          if (value === undefined || value === null) return false
          if (typeof value === 'string' && value.trim() === '') return false
          return true
        })
      })

      // 标准化字段名称（新业务逻辑）
      const rawData = jsonData.map((item) => {
        const originalQuantity = Number(item['初始数量'] || item['original_quantity'] || 0)

        // 处理当前数量空值：如果为空则设置为等于初始数量
        let currentQuantity = item['当前数量'] || item['current_quantity']
        if (currentQuantity === undefined || currentQuantity === null || currentQuantity === '') {
          currentQuantity = originalQuantity
        } else {
          currentQuantity = Number(currentQuantity)
        }

        // 处理入库日期，支持Excel的各种日期格式
        let inboundDate = item['入库日期'] || item['inbound_date'] || ''
        if (inboundDate) {
          inboundDate = formatExcelDate(inboundDate)
        }

        return {
          clothing_name: item['服装名称'] || item['clothing_name'] || '',
          original_quantity: originalQuantity,
          current_quantity: currentQuantity,
          clothing_id: item['服装ID'] || item['clothing_id'] || '',
          oem_clothing_id: item['OEM服装ID'] || item['oem_clothing_id'] || '',
          warehouse_name: item['仓库名称'] || item['warehouse_name'] || '',
          package_count:
            item['包裹数'] !== undefined && item['包裹数'] !== '' ? Number(item['包裹数']) : null,
          inbound_date: inboundDate,
          package_type: item['package_type'] || '',
        }
      })

      // 清理和标准化数据
      const standardizedData = sanitizePackageInitializationData(rawData)

      // 验证数据
      const validationResult = validatePackageInitializationData(standardizedData)

      previewData.value = standardizedData
      importResult.value = null

      // 显示验证结果
      if (validationResult.errors.length > 0) {
        ElMessage.error(`数据验证失败：发现 ${validationResult.errors.length} 个错误`)
        console.error('数据验证错误:', formatValidationResult(validationResult))
      } else {
        const stats = generatePackageInitializationStats(standardizedData)
        ElMessage.success(
          `成功解析 ${standardizedData.length} 条数据，涉及 ${stats.warehouseCount} 个仓库，预计生成 ${stats.totalEstimatedPackages} 个包裹`
        )

        if (validationResult.warnings.length > 0) {
          ElMessage.warning(`发现 ${validationResult.warnings.length} 个警告，请检查数据`)
          console.warn('数据验证警告:', formatValidationResult(validationResult))
        }

        // 自动切换到预览选项卡
        activeTab.value = 'preview'
      }
    } catch (error) {
      console.error('解析Excel文件失败:', error)
      ElMessage.error('解析Excel文件失败，请检查文件格式')
    }
  }

  reader.readAsArrayBuffer(file)
}

// 处理导入
const handleImport = async () => {
  if (previewData.value.length === 0) {
    ElMessage.warning('请先上传Excel文件')
    return
  }

  // 数据验证
  const validationResult = validatePackageInitializationData(previewData.value)

  if (!validationResult.isValid) {
    ElMessage.error(`数据验证失败：发现 ${validationResult.errors.length} 个错误`)
    console.error('验证错误详情:', formatValidationResult(validationResult))
    return
  }

  // 显示警告（如果有）
  if (validationResult.warnings.length > 0) {
    console.warn('验证警告详情:', formatValidationResult(validationResult))
  }

  let progressInterval: any = null

  try {
    await ElMessageBox.confirm(
      `确定要导入 ${previewData.value.length} 条Excel数据吗？\n` +
        `预计生成：${dataSummary.value.independentPackages} 个独立包裹，${dataSummary.value.mixedPackageGroups} 个混合包裹\n` +
        `此操作不可撤销。`,
      '确认导入',
      {
        confirmButtonText: '确定导入',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    importing.value = true
    importProgress.value = 0
    importStatus.value = undefined
    importMessage.value = '正在导入包裹数据...'

    // 模拟进度更新
    progressInterval = setInterval(() => {
      if (importProgress.value < 90) {
        importProgress.value += 10
      }
    }, 200)

    // 调用API - 使用Excel行数据格式
    const response = await initializePackages({
      excel_rows: previewData.value, // 发送Excel行数据，后端会自动处理分组
      operator: 'admin', // 可以从用户信息中获取
      notes: '批量包裹初始化（支持混合包裹）',
    })

    if (progressInterval) {
      clearInterval(progressInterval)
      progressInterval = null
    }
    importProgress.value = 100

    // 安全地处理API响应
    console.log('API响应数据:', response) // 调试日志

    if (response && response.data) {
      console.log('响应数据结构:', response.data) // 调试日志
      const responseData = response as any
      importStatus.value = responseData.code === 200 ? 'success' : 'exception'
      importMessage.value = responseData.message || '操作完成'
      importResult.value = response.data

      if (responseData.code === 200) {
        ElMessage.success('包裹初始化成功')
        // 自动切换到导入结果选项卡
        activeTab.value = 'import'
        emit('success')
      } else {
        ElMessage.error(responseData.message || '包裹初始化失败')
        // 切换到导入选项卡显示错误
        activeTab.value = 'import'
      }
    } else {
      // 处理响应格式异常的情况
      console.error('响应格式异常:', response) // 调试日志
      importStatus.value = 'exception'
      importMessage.value = '响应数据格式异常'
      importResult.value = {
        code: 500,
        message: '响应数据格式异常',
        data: {
          total_requested: 0,
          successful_created: 0,
          failed_created: 0,
          batch_code: '无',
          created_packages: [],
          failed_packages: [],
        },
      }
      activeTab.value = 'import'
      ElMessage.error('包裹初始化失败：响应数据格式异常')
    }
  } catch (error: any) {
    if (progressInterval) {
      clearInterval(progressInterval)
      progressInterval = null
    }
    importing.value = false
    importProgress.value = 0
    importStatus.value = 'exception'

    if (error === 'cancel') {
      importMessage.value = '用户取消导入'
      importResult.value = {
        code: 400,
        message: '用户取消导入',
        data: {
          total_requested: 0,
          successful_created: 0,
          failed_created: 0,
          batch_code: '无',
          created_packages: [],
          failed_packages: [],
        },
      }
    } else {
      console.error('包裹初始化失败:', error)
      const errorMessage = error.response?.data?.message || error.message || '包裹初始化失败'
      importMessage.value = errorMessage
      importResult.value = {
        code: error.response?.status || 500,
        message: errorMessage,
        data: {
          total_requested: previewData.value.length || 0,
          successful_created: 0,
          failed_created: previewData.value.length || 0,
          batch_code: '无',
          created_packages: [],
          failed_packages: [
            {
              package_code: '未知',
              reason: errorMessage,
            },
          ],
        },
      }
      ElMessage.error(errorMessage)
    }
  } finally {
    importing.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  if (importing.value) {
    ElMessage.warning('正在导入中，请稍候...')
    return
  }

  // 重置数据
  activeTab.value = 'template'
  previewData.value = []
  importResult.value = null
  importProgress.value = 0
  importStatus.value = undefined
  importMessage.value = ''

  // 清空上传组件
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }

  emit('update:visible', false)
}
</script>

<style scoped>
.package-initialization-dialog {
  padding: 0 10px;
}

.template-section,
.upload-section,
.preview-section,
.progress-section,
.result-section {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
}

.upload-demo {
  width: 100%;
}

.dialog-footer {
  text-align: right;
}

.mb-3 {
  margin-bottom: 12px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-6 {
  margin-bottom: 24px;
}

.mt-2 {
  margin-top: 8px;
}

.mt-3 {
  margin-top: 12px;
}

.ml-3 {
  margin-left: 12px;
}

.text-sm {
  font-size: 14px;
}

.text-lg {
  font-size: 18px;
}

.font-medium {
  font-weight: 500;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-600 {
  color: #4b5563;
}

.text-red-600 {
  color: #dc2626;
}

.text-center {
  text-align: center;
}

.list-disc {
  list-style-type: disc;
}

.list-inside {
  list-style-position: inside;
}

/* 选项卡样式 */
.package-tabs {
  margin-top: 20px;
}

.package-tabs .el-tabs__content {
  padding: 20px 0;
}

/* 步骤内容样式 */
.step-content {
  text-align: center;
  padding: 40px 20px;
}

.step-icon {
  margin-bottom: 16px;
  color: #409eff;
}

.step-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #303133;
}

.step-description {
  font-size: 14px;
  color: #606266;
  margin-bottom: 24px;
}

.step-actions {
  margin-bottom: 20px;
}

/* 预览数据样式 */
.preview-data {
  text-align: left;
}

.data-summary {
  text-align: center;
}

/* 导入结果样式 */
.result-stats .el-statistic {
  text-align: center;
}

.batch-code {
  text-align: center;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

.grid {
  display: grid;
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.gap-2 {
  gap: 8px;
}

.bg-green-50 {
  background-color: #f0f9ff;
}

.bg-red-50 {
  background-color: #fef2f2;
}

.bg-white {
  background-color: white;
}

.border {
  border: 1px solid #e5e7eb;
}

.rounded {
  border-radius: 4px;
}

.font-mono {
  font-family:
    ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
}
</style>
