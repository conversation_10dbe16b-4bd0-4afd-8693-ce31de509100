<template>
  <el-dialog
    v-model="dialogVisible"
    title="物料分类树形视图"
    width="800px"
    :close-on-click-modal="false"
    @closed="resetDialog"
  >
    <div class="tree-container">
      <div v-if="!apiAvailable" class="api-status-alert">
        <el-alert
          title="开发模式"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>分类树形视图功能正在开发中，当前显示示例数据。</p>
          </template>
        </el-alert>
      </div>

      <el-tree
        :data="treeData"
        :props="treeProps"
        :expand-on-click-node="false"
        default-expand-all
        node-key="category_id"
        class="category-tree"
        v-loading="loading"
      >
        <template #default="{ data }">
          <div class="tree-node">
            <div class="node-content">
              <el-tag
                :type="getLevelTagType(data.level)"
                size="small"
                class="level-tag"
              >
                {{ getLevelText(data.level) }}
              </el-tag>
              <span class="node-label">{{ data.category_name }}</span>
              <span class="node-id">({{ data.category_id }})</span>
            </div>
            <div class="node-actions">
              <el-button
                type="primary"
                size="small"
                @click="handleEdit(data)"
              >
                编辑
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="handleAddChild(data)"
                v-if="data.level < 3"
              >
                添加子分类
              </el-button>
            </div>
          </div>
        </template>
      </el-tree>

      <div v-if="treeData.length === 0 && !loading" class="empty-tree">
        <el-empty description="暂无分类数据">
          <el-button type="primary" @click="handleAddRoot">
            创建根分类
          </el-button>
        </el-empty>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleRefresh" :loading="loading">
          刷新数据
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { MaterialCategory } from '@/types/bom'

// Mock数据
const mockTreeData = [
  {
    category_id: 'CAT001',
    category_name: '面料',
    level: 1,
    children: [
      {
        category_id: 'CAT001001',
        category_name: '棉布',
        level: 2,
        parent_category_id: 'CAT001',
        children: [
          {
            category_id: 'CAT001001001',
            category_name: '40支纯棉布',
            level: 3,
            parent_category_id: 'CAT001001'
          }
        ]
      },
      {
        category_id: 'CAT001002',
        category_name: '化纤布',
        level: 2,
        parent_category_id: 'CAT001'
      }
    ]
  },
  {
    category_id: 'CAT002',
    category_name: '辅料',
    level: 1,
    children: [
      {
        category_id: 'CAT002001',
        category_name: '拉链',
        level: 2,
        parent_category_id: 'CAT002'
      },
      {
        category_id: 'CAT002002',
        category_name: '纽扣',
        level: 2,
        parent_category_id: 'CAT002'
      }
    ]
  },
  {
    category_id: 'CAT003',
    category_name: '包装材料',
    level: 1
  }
]

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  edit: [category: MaterialCategory]
  addChild: [parentCategory: MaterialCategory]
  addRoot: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const apiAvailable = ref(false)
const treeData = ref<MaterialCategory[]>([])

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'category_name',
}

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val
    if (val) {
      loadTreeData()
    }
  },
  { immediate: true }
)

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 初始化
onMounted(() => {
  if (dialogVisible.value) {
    loadTreeData()
  }
})

// 获取API或Mock
const getApiOrMock = async () => {
  try {
    const categoryApi = await import('@/api/materialCategory')
    apiAvailable.value = true
    return categoryApi
  } catch (error) {
    console.warn('MaterialCategory API不可用，使用Mock数据:', error)
    apiAvailable.value = false
    return {
      getMaterialCategoryTree: () => Promise.resolve({ data: mockTreeData })
    }
  }
}

// 加载树形数据
const loadTreeData = async () => {
  try {
    loading.value = true
    const api = await getApiOrMock()
    const response = await api.getMaterialCategoryTree()

    // 处理不同的响应格式
    if (response && Array.isArray(response)) {
      // 直接返回数组的情况
      treeData.value = response
    } else if (response && response.data && Array.isArray(response.data)) {
      // 标准ApiResponse格式
      treeData.value = response.data
    } else {
      treeData.value = []
    }
  } catch (error) {
    console.error('加载分类树失败:', error)
    ElMessage.error('加载分类树失败')
    treeData.value = []
  } finally {
    loading.value = false
  }
}

// 获取级别标签类型
const getLevelTagType = (level: number) => {
  switch (level) {
    case 1: return 'primary'
    case 2: return 'success'
    case 3: return 'warning'
    default: return 'info'
  }
}

// 获取级别文本
const getLevelText = (level: number) => {
  switch (level) {
    case 1: return '主类别'
    case 2: return '子类别'
    case 3: return '具体物料项'
    default: return '未知'
  }
}

// 编辑分类
const handleEdit = (category: MaterialCategory) => {
  emit('edit', category)
}

// 添加子分类
const handleAddChild = (parentCategory: MaterialCategory) => {
  emit('addChild', parentCategory)
}

// 添加根分类
const handleAddRoot = () => {
  emit('addRoot')
}

// 刷新数据
const handleRefresh = () => {
  loadTreeData()
}

// 重置对话框
const resetDialog = () => {
  treeData.value = []
  loading.value = false
}
</script>

<style scoped>
.tree-container {
  padding: 10px 0;
  max-height: 500px;
  overflow-y: auto;
}

.api-status-alert {
  margin-bottom: 20px;
}

.api-status-alert :deep(.el-alert__content) {
  font-size: 13px;
}

.category-tree {
  background: transparent;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 5px 0;
}

.node-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.level-tag {
  margin-right: 8px;
}

.node-label {
  font-weight: 500;
  margin-right: 8px;
}

.node-id {
  color: #909399;
  font-size: 12px;
}

.node-actions {
  display: flex;
  gap: 5px;
}

.empty-tree {
  text-align: center;
  padding: 40px 20px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-tree-node__content) {
  height: auto;
  padding: 8px 0;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}
</style>
