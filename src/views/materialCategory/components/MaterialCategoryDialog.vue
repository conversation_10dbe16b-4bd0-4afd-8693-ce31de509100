<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="600px"
    :close-on-click-modal="false"
    @closed="resetForm"
    center
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="category-form">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="分类编号" prop="category_id">
            <el-input
              v-model="form.category_id"
              placeholder="自动生成"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分类名称" prop="category_name">
            <el-input
              v-model="form.category_name"
              placeholder="请输入分类名称"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="分类级别" prop="level">
            <el-select
              v-model="form.level"
              placeholder="选择分类级别"
              style="width: 100%"
              @change="handleLevelChange"
            >
              <el-option label="主类别" :value="1" />
              <el-option label="子类别" :value="2" />
              <el-option label="具体物料项" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="父分类" prop="parent_category_id" v-if="(form.level || 1) > 1">
            <el-select
              v-model="form.parent_category_id"
              placeholder="选择父分类"
              style="width: 100%"
              filterable
              @change="handleParentCategoryChange"
            >
              <el-option
                v-for="parentCategory in parentCategoryOptions"
                :key="parentCategory.category_id"
                :label="parentCategory.category_name"
                :value="parentCategory.category_id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排序顺序" prop="sort_order">
            <el-input-number
              v-model="form.sort_order"
              :min="0"
              placeholder="排序顺序"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="state">
            <el-select
              v-model="form.state"
              placeholder="选择状态"
              style="width: 100%"
            >
              <el-option label="启用" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="3"
              placeholder="请输入描述"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import type { MaterialCategory } from '@/types/bom'
import {
  createMaterialCategory,
  updateMaterialCategory,
  getMaterialCategoryOptions,
  generateCategoryId as apiGenerateCategoryId
} from '@/api/materialCategory'



// Props
interface Props {
  modelValue: boolean
  title: string
  category?: MaterialCategory | null
}

const props = withDefaults(defineProps<Props>(), {
  category: null,
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

// 父分类选项
const parentCategoryOptions = ref<MaterialCategory[]>([])

// 表单数据
const form = reactive<Partial<MaterialCategory>>({
  category_id: '',
  category_name: '',
  parent_category_id: '',
  level: 1,
  sort_order: 0,
  description: '',
  state: '1',
})

// 重置表单数据（不清除验证状态）
const resetFormData = () => {
  Object.keys(form).forEach((key) => {
    if (key === 'level') {
      // @ts-ignore
      form[key] = 1
    } else if (key === 'sort_order') {
      // @ts-ignore
      form[key] = 0
    } else if (key === 'state') {
      // @ts-ignore
      form[key] = '1'
    } else {
      // @ts-ignore
      form[key] = ''
    }
  })
}

// 表单验证规则
const rules: FormRules = {
  category_id: [
    {
      required: true,
      message: '分类编号不能为空',
      trigger: 'blur',
      validator: (_rule, value, callback) => {
        if (!value || value.trim() === '') {
          callback(new Error('分类编号不能为空'))
        } else {
          callback()
        }
      }
    },
  ],
  category_name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
  ],
  level: [
    { required: true, message: '请选择分类级别', trigger: 'change' },
  ],
  parent_category_id: [
    {
      required: false,
      message: '请选择父分类',
      trigger: 'change',
      validator: (_rule, value, callback) => {
        if ((form.level || 1) > 1 && !value) {
          callback(new Error('请选择父分类'))
        } else {
          callback()
        }
      }
    },
  ],
}

// 监听对话框显示状态
watch(
  () => props.modelValue,
  async (val) => {
    dialogVisible.value = val
    if (val) {
      await loadParentCategoryOptions()
      // 判断是否需要自动生成分类编号
      const isEditMode = props.category && props.category._id
      if (!isEditMode) {
        // 新增模式（包括新增根分类和新增子分类）下自动生成分类编号
        await generateCategoryId()
      }
    }
  },
  { immediate: true }
)

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 监听分类数据变化
watch(
  () => props.category,
  async (newVal) => {
    if (newVal) {
      // 判断是编辑模式还是新增子分类模式
      const isEditMode = newVal._id

      if (isEditMode) {
        // 编辑模式：复制所有字段
        Object.keys(form).forEach((key) => {
          // @ts-ignore
          form[key] = newVal[key] !== undefined ? newVal[key] : form[key]
        })
      } else {
        // 新增子分类模式：只复制必要的字段，其他字段重置
        resetFormData()
        form.parent_category_id = newVal.parent_category_id || ''
        form.level = newVal.level || 1

        // 如果对话框已经打开，立即生成分类编号
        if (dialogVisible.value) {
          await generateCategoryId()
        }
      }
    } else {
      // 新增根分类模式，重置表单数据
      resetFormData()
    }
  },
  { deep: true, immediate: true }
)

// 初始化
onMounted(async () => {
  await loadParentCategoryOptions()
})

// 加载父分类选项
const loadParentCategoryOptions = async () => {
  try {
    const res = await getMaterialCategoryOptions()
    // 处理不同的响应格式
    let categories: MaterialCategory[] = []
    if (res && Array.isArray(res)) {
      // 直接返回数组的情况
      categories = res
    } else if (res && res.data && Array.isArray(res.data)) {
      // 标准ApiResponse格式
      categories = res.data
    }
    parentCategoryOptions.value = categories.filter((cat: MaterialCategory) => cat.level < 3)
  } catch (error) {
    console.error('加载父分类选项失败:', error)
  }
}



// 级别变化处理
const handleLevelChange = async (level: number) => {
  if (level === 1) {
    form.parent_category_id = ''
  }
  // 级别变化时重新生成分类编号（只在新增模式下）
  const isEditMode = props.category && props.category._id
  if (!isEditMode) {
    await generateCategoryId()
  }
}

// 父分类变化处理
const handleParentCategoryChange = async () => {
  // 父分类变化时重新生成分类编号（只在新增模式下）
  const isEditMode = props.category && props.category._id
  if (!isEditMode) {
    await generateCategoryId()
  }
}

// 自动生成分类编号
const generateCategoryId = async () => {
  try {
    const level = form.level || 1
    const parentId = form.parent_category_id

    // 使用后端API生成分类编号
    const params: any = { level }
    if (parentId) {
      params.parentCategoryId = parentId
    }

    const res = await apiGenerateCategoryId(params)
    if (res && res.data) {
      const data = res.data as any
      const categoryId = data.category_id
      if (categoryId) {
        form.category_id = categoryId
        return
      }
    }

    // 如果API调用失败，使用时间戳作为后备方案
    const timestamp = Date.now().toString().slice(-6)
    form.category_id = `CAT${timestamp}`
  } catch (error) {
    console.error('生成分类编号失败:', error)
    // 如果生成失败，使用时间戳作为后备方案
    const timestamp = Date.now().toString().slice(-6)
    form.category_id = `CAT${timestamp}`
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetFormData()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  const valid = await formRef.value.validate()
  if (valid) {
    try {
      loading.value = true
      const submitData = { ...form }

      let response: any
      if (props.category?._id) {
        response = await updateMaterialCategory(props.category._id, submitData as any)
        // 检查响应格式：如果有code字段说明是标准ApiResponse格式，否则是直接返回的数据
        if (response && ((response.code >= 200 && response.code < 300) || response.data || response._id)) {
          ElMessage.success((response as any).message || '更新分类成功')
          dialogVisible.value = false
          emit('success')
        } else {
          ElMessage.error('更新分类失败')
        }
      } else {
        response = await createMaterialCategory(submitData as any)
        // 检查响应格式：如果有code字段说明是标准ApiResponse格式，否则是直接返回的数据
        if (response && ((response.code >= 200 && response.code < 300) || response.data || response._id)) {
          ElMessage.success((response as any).message || '新增分类成功')
          dialogVisible.value = false
          emit('success')
        } else {
          ElMessage.error('新增分类失败')
        }
      }
    } catch (error: any) {
      console.error('保存分类失败', error)
      ElMessage.error(error.response?.data?.message || '保存分类失败')
    } finally {
      loading.value = false
    }
  } else {
    ElMessage.warning('请填写必填项')
  }
}
</script>

<style scoped>
.category-form {
  padding: 0 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
