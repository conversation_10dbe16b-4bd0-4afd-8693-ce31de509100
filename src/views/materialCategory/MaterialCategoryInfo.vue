<template>
  <PageTemplate
    :queryParams="queryParams"
    :tableData="tableData"
    :total="total"
    :loading="loading"
    :tableHeight="650"
    @query="handleQuery"
    @reset="handleReset"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  >
    <!-- 查询区域左侧 -->
    <template #query-form-left>
      <el-form-item label="分类名称" prop="category_name">
        <el-input
          v-model="queryParams.category_name"
          placeholder="输入分类名称"
          clearable
          style="width: 150px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分类级别" prop="level">
        <el-select
          v-model="queryParams.level"
          placeholder="选择级别"
          clearable
          style="width: 120px"
        >
          <el-option label="主类别" :value="1" />
          <el-option label="子类别" :value="2" />
          <el-option label="具体物料项" :value="3" />
        </el-select>
      </el-form-item>
    </template>

    <!-- 查询区域右侧 -->
    <template #query-form-right>
      <el-button type="primary" @click="handleAdd">新增分类</el-button>
      <el-button type="info" @click="handleViewTree">树形视图</el-button>
    </template>

    <!-- 表格列 -->
    <template #table-columns>
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="category_id" label="分类编号" width="120" align="center" />
      <el-table-column prop="category_name" label="分类名称" width="200" align="center" />
      <el-table-column prop="level" label="级别" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getLevelTagType(row.level)">
            {{ getLevelText(row.level) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="parent_category_id" label="父分类" width="120" align="center" />
      <el-table-column prop="sort_order" label="排序" width="80" align="center" />
      <el-table-column prop="description" label="描述" min-width="150" align="center" />
      <el-table-column prop="state" label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.state === '1' ? 'success' : 'danger'">
            {{ row.state === '1' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" align="center" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </template>
  </PageTemplate>

  <!-- 分类对话框 -->
  <MaterialCategoryDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :category="currentCategory"
    @success="handleDialogSuccess"
  />

  <!-- 树形视图对话框 -->
  <MaterialCategoryTreeDialog
    v-model="treeDialogVisible"
    @edit="handleTreeEdit"
    @add-child="handleTreeAddChild"
    @add-root="handleTreeAddRoot"
  />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 导入通用页面模板
import PageTemplate from '@/components/common/PageTemplate.vue'
// 导入分类对话框组件
import MaterialCategoryDialog from './components/MaterialCategoryDialog.vue'
import MaterialCategoryTreeDialog from './components/MaterialCategoryTreeDialog.vue'
// 导入API
import { getMaterialCategoryList, deleteMaterialCategory } from '@/api/materialCategory'
import type { MaterialCategory } from '@/types/bom'

// 定义本地类型
interface QueryMaterialCategoryParams {
  category_name?: string
  level?: number
  parent_category_id?: string
  page?: number
  limit?: number
}

// 加载状态
const loading = ref(false)

// 表格数据
const tableData = ref<MaterialCategory[]>([])

// 总数
const total = ref(0)

// 查询参数
const queryParams = reactive<QueryMaterialCategoryParams>({
  category_name: '',
  level: undefined,
  parent_category_id: '',
  page: 1,
  limit: 10,
})

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('新增分类')
const currentCategory = ref<MaterialCategory | null>(null)

// 树形视图对话框
const treeDialogVisible = ref(false)

// 页面加载时获取数据
onMounted(async () => {
  await fetchCategoryList()
})

// 获取分类列表
const fetchCategoryList = async () => {
  try {
    loading.value = true
    const res = await getMaterialCategoryList(queryParams)
    console.log('分类列表 API 返回数据:', res)

    // 处理不同的响应格式
    if (res && res.data) {
      const responseData = res.data as any
      // 检查是否有嵌套的data结构：{code, data: {data: {categoryList, total}}, message}
      const actualData = responseData.data || responseData

      if (actualData.categoryList) {
        tableData.value = actualData.categoryList || []
        total.value = actualData.total || 0
      } else if (Array.isArray(actualData)) {
        // 直接返回数组的情况
        tableData.value = actualData
        total.value = actualData.length
      } else {
        console.error('API 返回数据格式不正确:', res)
        tableData.value = []
        total.value = 0
      }
    } else {
      console.error('API 返回数据格式不正确:', res)
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

// 获取级别标签类型
const getLevelTagType = (level: number) => {
  switch (level) {
    case 1: return 'primary'
    case 2: return 'success'
    case 3: return 'warning'
    default: return 'info'
  }
}

// 获取级别文本
const getLevelText = (level: number) => {
  switch (level) {
    case 1: return '主类别'
    case 2: return '子类别'
    case 3: return '具体物料项'
    default: return '未知'
  }
}

// 查询
const handleQuery = () => {
  queryParams.page = 1
  fetchCategoryList()
}

// 重置
const handleReset = () => {
  queryParams.category_name = ''
  queryParams.level = undefined
  queryParams.parent_category_id = ''
  queryParams.page = 1
  fetchCategoryList()
}

// 每页数量变化
const handleSizeChange = (val: number) => {
  queryParams.limit = val
  fetchCategoryList()
}

// 页码变化
const handleCurrentChange = (val: number) => {
  queryParams.page = val
  fetchCategoryList()
}

// 新增分类
const handleAdd = () => {
  currentCategory.value = null
  dialogTitle.value = '新增分类'
  dialogVisible.value = true
}

// 编辑分类
const handleEdit = (row: MaterialCategory) => {
  currentCategory.value = { ...row }
  dialogTitle.value = '编辑分类'
  dialogVisible.value = true
}

// 删除分类
const handleDelete = async (row: MaterialCategory) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分类 "${row.category_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    if (row._id) {
      await deleteMaterialCategory(row._id)
      ElMessage.success('删除成功')
      fetchCategoryList()
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
      ElMessage.error(error.response?.data?.message || '删除失败')
    }
  }
}

// 查看树形视图
const handleViewTree = () => {
  treeDialogVisible.value = true
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchCategoryList()
}

// 树形视图事件处理
const handleTreeEdit = (category: MaterialCategory) => {
  currentCategory.value = { ...category }
  dialogTitle.value = '编辑分类'
  dialogVisible.value = true
  treeDialogVisible.value = false
}

const handleTreeAddChild = (parentCategory: MaterialCategory) => {
  currentCategory.value = {
    parent_category_id: parentCategory.category_id,
    level: parentCategory.level + 1,
  } as MaterialCategory
  dialogTitle.value = '新增子分类'
  dialogVisible.value = true
  treeDialogVisible.value = false
}

const handleTreeAddRoot = () => {
  currentCategory.value = {
    level: 1,
  } as MaterialCategory
  dialogTitle.value = '新增根分类'
  dialogVisible.value = true
  treeDialogVisible.value = false
}
</script>

<style scoped>
/* 页面样式 */
</style>
