import { NestFactory } from '@nestjs/core'
import { AppModule } from './app.module'
import { ValidationPipe } from '@nestjs/common'
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'

async function bootstrap() {
  const app = await NestFactory.create(AppModule)

  // 启用全局验证管道
  app.useGlobalPipes(new ValidationPipe())

  // 设置全局路由前缀
  app.setGlobalPrefix('api')
  // 配置 Swagger
  const config = new DocumentBuilder()
    .setTitle('JY API')
    .setDescription('JY 项目 API 文档')
    .setVersion('1.0')
    .addBearerAuth()
    .build()
  const document = SwaggerModule.createDocument(app, config)
  SwaggerModule.setup('swagger', app, document)

  // 启用 CORS
  app.enableCors()

  const port = process.env.PORT || 4000
  await app.listen(port)
  console.log(`服务器运行在 http://localhost:${port}`)
  console.log(`API 文档运行在 http://localhost:${port}/swagger`)
}
bootstrap()
