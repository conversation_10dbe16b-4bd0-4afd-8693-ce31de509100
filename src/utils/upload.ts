import request from './request'
import { ElMessage } from 'element-plus'
import type { UploadFile } from 'element-plus'

/**
 * API响应接口
 */
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

// 由于request.ts中的响应拦截器直接返回了response.data
// 这里我们声明一个类型，表示request方法的返回类型就是ApiResponse
declare module 'axios' {
  export interface AxiosInstance {
    post<T = any>(url: string, data?: any, config?: any): Promise<T>;
    get<T = any>(url: string, config?: any): Promise<T>;
    put<T = any>(url: string, data?: any, config?: any): Promise<T>;
    delete<T = any>(url: string, config?: any): Promise<T>;
  }
}

/**
 * 图片上传类型
 */
export enum UploadType {
  CLOTHING = 'clothing',
  OEM_CLOTHING = 'oemClothing',
  TRANSPORTATION = 'transportation',
}

/**
 * 图片信息接口
 */
export interface ImageInfo {
  url: string
  Key: string
}

/**
 * 上传图片
 * @param file 文件对象
 * @param type 上传类型
 * @returns 上传结果
 */
export async function uploadImage(file: File, type: UploadType): Promise<ImageInfo | null> {
  try {
    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      ElMessage.error('只能上传图片文件 (JPEG, PNG, GIF, WEBP)')
      return null
    }

    // 验证文件大小
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      ElMessage.error('图片大小不能超过10MB')
      return null
    }

    // 创建FormData
    const formData = new FormData()
    formData.append('file', file)

    // 发送请求
    const response = await request.post<ApiResponse<ImageInfo>>(`/api/upload/${type}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })

    if (response && response.code === 200 && response.data) {
      return response.data
    } else {
      ElMessage.error(response?.message || '上传失败')
      return null
    }
  } catch (error: any) {
    console.error('上传图片失败:', error)
    ElMessage.error(error.response?.data?.message || '上传图片失败')
    return null
  }
}

/**
 * 删除图片
 * @param keys 要删除的图片Key列表
 * @returns 是否删除成功
 */
export async function deleteImages(keys: { Key: string }[]): Promise<boolean> {
  try {
    if (!keys || keys.length === 0) {
      return true
    }

    const response = await request.post<ApiResponse>('/api/upload/delete', { keys })

    if (response && response.code === 200) {
      return true
    } else {
      ElMessage.error(response?.message || '删除图片失败')
      return false
    }
  } catch (error: any) {
    console.error('删除图片失败:', error)
    ElMessage.error(error.response?.data?.message || '删除图片失败')
    return false
  }
}

/**
 * 处理Element Plus上传组件的文件上传
 * @param options 上传选项
 * @returns 上传处理函数
 */
export function handleUpload(options: {
  type: UploadType
  onSuccess?: (file: UploadFile, imageInfo: ImageInfo) => void
  onError?: (error: any) => void
}) {
  return async (file: UploadFile) => {
    try {
      // 上传图片
      const imageInfo = await uploadImage(file.raw as File, options.type)

      if (imageInfo) {
        // 调用成功回调
        if (options.onSuccess) {
          options.onSuccess(file, imageInfo)
        }
        return imageInfo
      } else {
        // 调用错误回调
        if (options.onError) {
          options.onError(new Error('上传失败'))
        }
        return false
      }
    } catch (error) {
      console.error('上传处理失败:', error)
      // 调用错误回调
      if (options.onError) {
        options.onError(error)
      }
      return false
    }
  }
}

/**
 * 处理Element Plus上传组件的文件删除
 * @param options 删除选项
 * @returns 删除处理函数
 */
export function handleRemove(options: { onSuccess?: () => void; onError?: (error: any) => void }) {
  return async (file: UploadFile) => {
    try {
      // 获取自定义数据
      const customData = (file as any).customData as ImageInfo

      if (customData && customData.Key) {
        // 删除图片
        const success = await deleteImages([{ Key: customData.Key }])

        if (success) {
          // 调用成功回调
          if (options.onSuccess) {
            options.onSuccess()
          }
          return true
        } else {
          // 调用错误回调
          if (options.onError) {
            options.onError(new Error('删除失败'))
          }
          return false
        }
      }

      return true
    } catch (error) {
      console.error('删除处理失败:', error)
      // 调用错误回调
      if (options.onError) {
        options.onError(error)
      }
      return false
    }
  }
}
