/**
 * ObjectID 格式化工具
 * 用于将 ObjectID 转换为带 ObjectID() 包装的形式
 */

import { Types } from 'mongoose';

/**
 * 将 ObjectID 转换为带 ObjectID() 包装的形式
 * @param id ObjectID 或其字符串表示
 * @returns 带 ObjectID() 包装的字符串
 */
export function formatObjectId(id: string | Types.ObjectId | any): string {
  if (!id) return 'null';

  // 如果已经是字符串形式的 ObjectID()，直接返回
  if (typeof id === 'string' && id.startsWith('ObjectID(')) {
    return id;
  }

  // 转换为字符串
  const idStr = id.toString();

  // 检查是否是有效的 ObjectID
  if (Types.ObjectId.isValid(idStr)) {
    return `ObjectID('${idStr}')`;
  }

  // 如果不是有效的 ObjectID，原样返回
  return idStr;
}

/**
 * 将对象中的所有 _id 字段转换为带 ObjectID() 包装的形式
 * @param obj 包含 _id 字段的对象
 * @returns 转换后的对象
 */
export function formatObjectWithObjectId<T extends Record<string, any>>(obj: T): Record<string, any> {
  if (!obj) return obj;

  const result = { ...obj } as Record<string, any>;

  // 处理 _id 字段
  if ('_id' in obj) {
    result._id = formatObjectId(obj._id);
  }

  // 处理其他可能包含 ObjectID 的字段
  for (const key in obj) {
    if (key.endsWith('_id') && key !== '_id') {
      result[key] = formatObjectId(obj[key]);
    }
  }

  return result as T;
}

/**
 * 将数组中的所有对象的 _id 字段转换为带 ObjectID() 包装的形式
 * @param arr 对象数组
 * @returns 转换后的数组
 */
export function formatArrayWithObjectId<T extends Record<string, any>>(arr: T[]): Record<string, any>[] {
  if (!arr || !Array.isArray(arr)) return arr;

  return arr.map(item => formatObjectWithObjectId(item));
}
