/**
 * 日期格式化工具函数
 */

/**
 * 格式化日期为 YYYY-MM-DD 格式
 * @param date 日期对象或日期字符串
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: Date | string): string {
  if (!date) return ''
  
  const d = typeof date === 'string' ? new Date(date) : date
  
  // 检查日期是否有效
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  
  return `${year}-${month}-${day}`
}

/**
 * 格式化日期时间为 YYYY-MM-DD HH:mm:ss 格式
 * @param date 日期对象或日期字符串
 * @returns 格式化后的日期时间字符串
 */
export function formatDateTime(date: Date | string): string {
  if (!date) return ''
  
  const d = typeof date === 'string' ? new Date(date) : date
  
  // 检查日期是否有效
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

/**
 * 获取当前日期的字符串表示 (YYYY-MM-DD)
 * @returns 当前日期字符串
 */
export function getCurrentDate(): string {
  return formatDate(new Date())
}

/**
 * 获取当前日期时间的字符串表示 (YYYY-MM-DD HH:mm:ss)
 * @returns 当前日期时间字符串
 */
export function getCurrentDateTime(): string {
  return formatDateTime(new Date())
}

/**
 * 将日期字符串解析为Date对象
 * @param dateStr 日期字符串 (YYYY-MM-DD 或 YYYY/MM/DD)
 * @returns Date对象
 */
export function parseDate(dateStr: string): Date | null {
  if (!dateStr) return null
  
  // 尝试解析不同格式的日期
  const formats = [
    /^(\d{4})-(\d{2})-(\d{2})$/,
    /^(\d{4})\/(\d{2})\/(\d{2})$/,
    /^(\d{4})年(\d{2})月(\d{2})日$/
  ]
  
  for (const format of formats) {
    const match = dateStr.match(format)
    if (match) {
      const year = parseInt(match[1])
      const month = parseInt(match[2]) - 1 // 月份从0开始
      const day = parseInt(match[3])
      
      const date = new Date(year, month, day)
      
      // 验证日期是否有效
      if (
        date.getFullYear() === year &&
        date.getMonth() === month &&
        date.getDate() === day
      ) {
        return date
      }
    }
  }
  
  return null
}

/**
 * 计算两个日期之间的天数差
 * @param date1 第一个日期
 * @param date2 第二个日期
 * @returns 天数差
 */
export function daysBetween(date1: Date | string, date2: Date | string): number {
  const d1 = typeof date1 === 'string' ? new Date(date1) : date1
  const d2 = typeof date2 === 'string' ? new Date(date2) : date2
  
  // 检查日期是否有效
  if (isNaN(d1.getTime()) || isNaN(d2.getTime())) return 0
  
  // 将时间部分设置为0，只比较日期部分
  const utc1 = Date.UTC(d1.getFullYear(), d1.getMonth(), d1.getDate())
  const utc2 = Date.UTC(d2.getFullYear(), d2.getMonth(), d2.getDate())
  
  const MS_PER_DAY = 1000 * 60 * 60 * 24
  return Math.floor((utc2 - utc1) / MS_PER_DAY)
}
