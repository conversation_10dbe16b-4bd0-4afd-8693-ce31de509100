import { Readable } from 'stream';
import { ConfigService } from '@nestjs/config';
// 使用 require 导入 COS，因为它是一个 CommonJS 模块
// eslint-disable-next-line @typescript-eslint/no-var-requires
const COS = require('cos-nodejs-sdk-v5');
// 定义 COS 类型
type COSType = any;

// 获取配置服务实例
let configService: ConfigService | null = null;

/**
 * COS配置接口
 */
interface CosConfig {
  Bucket?: string;
  Region?: string;
  Prefix?: string;
}

/**
 * 上传参数接口
 */
interface PutObjectParams {
  key: string;
  buffer: Buffer | Readable;
}

/**
 * 删除文件对象接口
 */
interface DeleteObjectItem {
  Key: string;
}

/**
 * 上传结果接口
 */
interface CosResult {
  statusCode: number;
  headers: Record<string, any>;
  Location: string;
  ETag: string;
  RequestId: string;
  [key: string]: any;
}

/**
 * 腾讯云对象存储工具类
 * 用于上传和删除腾讯云COS中的文件
 */
class CosUtil {
  private cos: COSType | null = null;
  private Bucket: string = process.env.COS_BUCKET || "jygm-1307853348"; // 存储桶名称
  private Region: string = process.env.COS_REGION || "ap-shanghai"; // 存储桶区域
  private Prefix: string = ""; // 路径前缀

  /**
   * 设置配置服务
   * @param config 配置服务实例
   */
  setConfigService(config: ConfigService): void {
    configService = config;
  }

  /**
   * 初始化配置
   * @param config COS配置
   */
  init(config?: CosConfig): void {
    if (config) {
      this.Bucket = config.Bucket || this.Bucket;
      this.Region = config.Region || this.Region;
      this.Prefix = config.Prefix || this.Prefix;
    }

    // 从环境变量获取密钥
    const secretId = process.env.COS_SECRET_ID;
    const secretKey = process.env.COS_SECRET_KEY;

    if (!secretId || !secretKey) {
      throw new Error('COS密钥未配置，请检查环境变量');
    }

    this.cos = new COS({
      SecretId: secretId,
      SecretKey: secretKey,
    });
  }

  /**
   * 上传文件到腾讯云COS
   * @param param 上传参数
   * @returns 上传结果
   */
  putObject(param: PutObjectParams): Promise<CosResult> {
    return new Promise((resolve, reject) => {
      if (!this.cos) {
        reject(new Error('COS未初始化，请先调用init方法'));
        return;
      }

      this.cos.putObject(
        {
          Bucket: this.Bucket /* 必须 */,
          Region: this.Region /* 必须 */,
          Key: param.key /* 必须 */,
          Body: param.buffer /* 必须 */,
        },
        function (err: Error | null, data: CosResult) {
          if (err) {
            reject(err);
            return;
          }
          resolve(data);
        }
      );
    });
  }

  /**
   * 批量删除腾讯云COS中的文件
   * @param param 要删除的文件列表
   * @returns 删除结果
   */
  deleteMultipleObject(param: DeleteObjectItem[]): Promise<CosResult> {
    return new Promise((resolve, reject) => {
      if (!this.cos) {
        reject(new Error('COS未初始化，请先调用init方法'));
        return;
      }

      this.cos.deleteMultipleObject(
        {
          Bucket: this.Bucket /* 必须 */,
          Region: this.Region /* 必须 */,
          Objects: param /* 必须 */,
        },
        function (err: Error | null, data: CosResult) {
          if (err) {
            reject(err);
            return;
          }
          resolve(data);
        }
      );
    });
  }

  /**
   * 删除腾讯云COS中的单个文件
   * @param param 要删除的文件路径
   * @returns 删除结果
   */
  deleteObject(param: string): Promise<CosResult> {
    return new Promise((resolve, reject) => {
      if (!this.cos) {
        reject(new Error('COS未初始化，请先调用init方法'));
        return;
      }

      this.cos.deleteObject(
        {
          Bucket: this.Bucket /* 必须 */,
          Region: this.Region /* 必须 */,
          Key: param /* 必须 */,
        },
        function (err: Error | null, data: CosResult) {
          if (err) {
            reject(err);
            return;
          }
          resolve(data);
        }
      );
    });
  }
}

// 创建单例实例
const cosUtil = new CosUtil();

export default cosUtil;
