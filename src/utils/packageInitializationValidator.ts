/**
 * 包裹初始化数据验证工具
 */

export interface PackageInitializationItem {
  clothing_name: string
  original_quantity: number
  current_quantity: number
  clothing_id?: string
  oem_clothing_id?: string
  warehouse_name: string
  package_count?: number | null
  inbound_date: string
  package_type?: string
}

export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
}

export interface ValidationError {
  row: number
  field: string
  value: any
  message: string
}

export interface ValidationWarning {
  row: number
  field: string
  value: any
  message: string
}

/**
 * 解析Excel日期格式，支持多种格式
 * @param dateInput 日期输入值（字符串或数字）
 * @returns Date对象或null（解析失败）
 */
function parseExcelDate(dateInput: any): Date | null {
  if (!dateInput) return null

  // 如果是数字，可能是Excel日期序列号
  if (typeof dateInput === 'number') {
    // Excel日期序列号从1900年1月1日开始计算
    // 注意：Excel错误地认为1900年是闰年，所以需要调整
    const excelEpoch = new Date(1900, 0, 1)
    const daysSinceEpoch = dateInput - 1 // Excel从1开始计数
    const date = new Date(excelEpoch.getTime() + daysSinceEpoch * 24 * 60 * 60 * 1000)

    // 验证日期是否合理（1900-2100年之间）
    if (date.getFullYear() >= 1900 && date.getFullYear() <= 2100) {
      return date
    }
  }

  // 转换为字符串进行格式解析
  const dateStr = String(dateInput).trim()
  if (!dateStr) return null

  // 支持的日期格式模式
  const patterns = [
    // YYYY/M/D 或 YYYY/MM/DD 格式
    /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,
    // YYYY-M-D 或 YYYY-MM-DD 格式
    /^(\d{4})-(\d{1,2})-(\d{1,2})$/,
    // M/D/YYYY 或 MM/DD/YYYY 格式
    /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
    // M-D-YYYY 或 MM-DD-YYYY 格式
    /^(\d{1,2})-(\d{1,2})-(\d{4})$/
  ]

  for (let i = 0; i < patterns.length; i++) {
    const match = dateStr.match(patterns[i])
    if (match) {
      let year: number, month: number, day: number

      if (i < 2) {
        // YYYY/M/D 或 YYYY-M-D 格式
        year = parseInt(match[1])
        month = parseInt(match[2])
        day = parseInt(match[3])
      } else {
        // M/D/YYYY 或 M-D-YYYY 格式
        month = parseInt(match[1])
        day = parseInt(match[2])
        year = parseInt(match[3])
      }

      // 验证日期有效性
      const date = new Date(year, month - 1, day)
      if (date.getFullYear() === year &&
          date.getMonth() === month - 1 &&
          date.getDate() === day) {
        return date
      }
    }
  }

  // 尝试使用JavaScript原生Date解析
  const nativeDate = new Date(dateStr)
  if (!isNaN(nativeDate.getTime())) {
    return nativeDate
  }

  return null
}

/**
 * 验证包裹初始化数据（新业务逻辑）
 */
export function validatePackageInitializationData(
  data: PackageInitializationItem[]
): ValidationResult {
  const errors: ValidationError[] = []
  const warnings: ValidationWarning[] = []

  // 按package_type分组，用于混合包裹验证
  const mixedPackageGroups = new Map<string, { items: PackageInitializationItem[], rows: number[] }>()

  data.forEach((item, index) => {
    const row = index + 1

    // 验证单个项目
    validateSingleItem(item, row, errors, warnings)

    // 如果有package_type，加入混合包裹组
    if (item.package_type && item.package_type.trim() !== '') {
      const packageType = item.package_type.trim()
      if (!mixedPackageGroups.has(packageType)) {
        mixedPackageGroups.set(packageType, { items: [], rows: [] })
      }
      mixedPackageGroups.get(packageType)!.items.push(item)
      mixedPackageGroups.get(packageType)!.rows.push(row)
    }
  })

  // 验证混合包裹组
  for (const [packageType, group] of mixedPackageGroups) {
    const { items, rows } = group
    validateMixedPackageGroup(packageType, items, rows, errors, warnings)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 验证混合包裹组
 */
function validateMixedPackageGroup(
  packageType: string,
  items: PackageInitializationItem[],
  rows: number[],
  errors: ValidationError[],
  warnings: ValidationWarning[]
) {
  const firstItem = items[0]

  // 检查包裹级别信息的一致性
  const packageLevelFields = ['warehouse_name', 'inbound_date']

  for (let i = 1; i < items.length; i++) {
    const currentItem = items[i]
    const currentRow = rows[i]

    packageLevelFields.forEach(field => {
      const firstValue = (firstItem as any)[field]
      const currentValue = (currentItem as any)[field]

      // 对于可选字段，允许空值不一致，但非空值必须一致
      if (firstValue && currentValue && firstValue !== currentValue) {
        warnings.push({
          row: currentRow,
          field: field,
          value: currentValue,
          message: `混合包裹组 ${packageType} 的 ${field} 不一致，建议保持一致`
        })
      }
    })
  }

  // 检查服装ID重复（同一混合包裹内不应有重复的服装ID）
  const clothingIdSet = new Set<string>()
  items.forEach((item, itemIndex) => {
    const clothingKey = item.clothing_id || item.oem_clothing_id || ''
    if (clothingKey && clothingIdSet.has(clothingKey)) {
      errors.push({
        row: rows[itemIndex],
        field: item.clothing_id ? 'clothing_id' : 'oem_clothing_id',
        value: clothingKey,
        message: `混合包裹组 ${packageType} 中存在重复的服装ID`
      })
    } else if (clothingKey) {
      clothingIdSet.add(clothingKey)
    }
  })

  // 混合包裹警告
  if (items.length > 5) {
    warnings.push({
      row: rows[0],
      field: 'package_type',
      value: packageType,
      message: `混合包裹组 ${packageType} 包含 ${items.length} 个产品，建议控制在5个以内`
    })
  }
}

/**
 * 验证单个产品项（新业务逻辑）
 */
function validateSingleItem(
  item: PackageInitializationItem,
  row: number,
  errors: ValidationError[],
  warnings: ValidationWarning[]
) {
  // 必填字段验证
  if (!item.clothing_name || item.clothing_name.trim() === '') {
    errors.push({
      row,
      field: 'clothing_name',
      value: item.clothing_name,
      message: '服装名称不能为空'
    })
  }

  if (!item.warehouse_name || item.warehouse_name.trim() === '') {
    errors.push({
      row,
      field: 'warehouse_name',
      value: item.warehouse_name,
      message: '仓库名称不能为空'
    })
  }

  if (!item.inbound_date || item.inbound_date.trim() === '') {
    errors.push({
      row,
      field: 'inbound_date',
      value: item.inbound_date,
      message: '入库日期不能为空'
    })
  }

  // 数量验证
  if (typeof item.original_quantity !== 'number' || item.original_quantity <= 0) {
    errors.push({
      row,
      field: 'original_quantity',
      value: item.original_quantity,
      message: '初始数量必须是大于0的数字'
    })
  }

  if (typeof item.current_quantity !== 'number' || item.current_quantity < 0) {
    errors.push({
      row,
      field: 'current_quantity',
      value: item.current_quantity,
      message: '当前数量必须是大于等于0的数字'
    })
  }

  // 当前数量不能大于初始数量
  if (
    typeof item.original_quantity === 'number' &&
    typeof item.current_quantity === 'number' &&
    item.current_quantity > item.original_quantity
  ) {
    warnings.push({
      row,
      field: 'current_quantity',
      value: item.current_quantity,
      message: '当前数量大于初始数量，请确认数据正确性'
    })
  }

  // 服装ID验证（必须填写其中一个）
  if (!item.clothing_id && !item.oem_clothing_id) {
    errors.push({
      row,
      field: 'clothing_id',
      value: '',
      message: '服装ID或OEM服装ID至少填写一个'
    })
  }

  if (item.clothing_id && item.oem_clothing_id) {
    warnings.push({
      row,
      field: 'clothing_id',
      value: item.clothing_id,
      message: '不建议同时填写服装ID和OEM服装ID'
    })
  }

  // 包裹数验证（支持小数）
  if (item.package_count !== undefined && item.package_count !== null) {
    if (typeof item.package_count !== 'number' || item.package_count <= 0) {
      errors.push({
        row,
        field: 'package_count',
        value: item.package_count,
        message: '包裹数必须是大于0的数字'
      })
    } else if (item.package_count > 100) {
      warnings.push({
        row,
        field: 'package_count',
        value: item.package_count,
        message: '包裹数过大，建议控制在100以内'
      })
    } else if (item.package_count % 1 !== 0) {
      // 有小数部分的情况
      const decimalPart = item.package_count % 1
      if (decimalPart < 0.01) {
        warnings.push({
          row,
          field: 'package_count',
          value: item.package_count,
          message: '包裹数小数部分过小，可能导致部分包裹数量为0'
        })
      }
    }
  }

  // 日期格式验证（支持多种Excel日期格式）
  if (item.inbound_date && item.inbound_date.trim() !== '') {
    const parsedDate = parseExcelDate(item.inbound_date)

    if (!parsedDate) {
      errors.push({
        row,
        field: 'inbound_date',
        value: item.inbound_date,
        message: '入库日期格式不正确，支持格式：2025/7/27、2025-07-27、07/27/2025、Excel日期序列号等'
      })
    } else {
      // 检查日期是否在合理范围内
      const now = new Date()
      const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
      const oneYearLater = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate())

      if (parsedDate < oneYearAgo || parsedDate > oneYearLater) {
        warnings.push({
          row,
          field: 'inbound_date',
          value: item.inbound_date,
          message: '入库日期超出合理范围（一年前到一年后）'
        })
      }
    }
  }
}


/**
 * 格式化验证结果为可读的错误信息
 */
export function formatValidationResult(result: ValidationResult): string {
  const messages: string[] = []

  if (result.errors.length > 0) {
    messages.push('发现以下错误：')
    result.errors.forEach((error) => {
      messages.push(`第${error.row}行 ${error.field}: ${error.message}`)
    })
  }

  if (result.warnings.length > 0) {
    messages.push('发现以下警告：')
    result.warnings.forEach((warning) => {
      messages.push(`第${warning.row}行 ${warning.field}: ${warning.message}`)
    })
  }

  return messages.join('\n')
}

/**
 * 清理和标准化包裹初始化数据（新业务逻辑）
 */
export function sanitizePackageInitializationData(
  data: any[]
): PackageInitializationItem[] {
  return data.map((item) => {
    // 处理入库日期，确保格式标准化
    let inboundDate = item.inbound_date || ''
    if (inboundDate) {
      const parsedDate = parseExcelDate(inboundDate)
      if (parsedDate) {
        inboundDate = `${parsedDate.getFullYear()}/${parsedDate.getMonth() + 1}/${parsedDate.getDate()}`
      } else {
        inboundDate = String(inboundDate).trim()
      }
    }

    return {
      clothing_name: String(item.clothing_name || '').trim(),
      original_quantity: Number(item.original_quantity) || 0,
      current_quantity: Number(item.current_quantity) || 0,
      clothing_id: item.clothing_id ? String(item.clothing_id).trim() : undefined,
      oem_clothing_id: item.oem_clothing_id ? String(item.oem_clothing_id).trim() : undefined,
      warehouse_name: String(item.warehouse_name || '').trim(),
      package_count: item.package_count !== undefined && item.package_count !== '' ? Number(item.package_count) : null,
      inbound_date: inboundDate,
      package_type: item.package_type ? String(item.package_type).trim() : undefined
    }
  })
}

/**
 * 生成包裹初始化统计信息（新业务逻辑）
 */
export function generatePackageInitializationStats(data: PackageInitializationItem[]) {
  // 按package_type分组统计混合包裹
  const packageTypeGroups = new Map<string, PackageInitializationItem[]>()
  let independentPackageCount = 0

  data.forEach((item) => {
    if (item.package_type && item.package_type.trim() !== '') {
      const packageType = item.package_type.trim()
      if (!packageTypeGroups.has(packageType)) {
        packageTypeGroups.set(packageType, [])
      }
      packageTypeGroups.get(packageType)!.push(item)
    } else {
      // 独立包裹，根据包裹数计算
      const packageCount = item.package_count || 1
      independentPackageCount += packageCount
    }
  })

  const stats = {
    totalExcelRows: data.length,
    mixedPackageGroups: packageTypeGroups.size,
    independentPackages: independentPackageCount,
    totalOriginalQuantity: 0,
    totalCurrentQuantity: 0,
    warehouseCount: new Set<string>(),
    hasClothingId: 0,
    hasOemClothingId: 0,
    partiallyShippedCount: 0
  }

  // 统计信息
  data.forEach((item) => {
    stats.totalOriginalQuantity += item.original_quantity
    stats.totalCurrentQuantity += item.current_quantity
    stats.warehouseCount.add(item.warehouse_name)

    if (item.clothing_id) stats.hasClothingId++
    if (item.oem_clothing_id) stats.hasOemClothingId++

    // 检查是否为部分出货
    if (!item.package_count && item.current_quantity < item.original_quantity) {
      stats.partiallyShippedCount++
    }
  })

  return {
    ...stats,
    warehouseCount: stats.warehouseCount.size,
    totalEstimatedPackages: stats.independentPackages + stats.mixedPackageGroups,
    utilizationRate: Math.round((stats.totalCurrentQuantity / stats.totalOriginalQuantity) * 100)
  }
}
