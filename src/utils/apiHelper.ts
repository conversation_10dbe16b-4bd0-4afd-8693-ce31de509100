/**
 * API 辅助函数
 */

/**
 * 从 API 响应中提取数据
 * @param response API 响应
 * @param defaultValue 默认值
 * @returns 提取的数据
 */
export function extractData<T>(response: any, defaultValue: T): T {
  if (response && typeof response === 'object') {
    // 如果响应是数组，直接返回
    if (Array.isArray(response)) {
      return response as T
    }

    // 如果响应有 data 属性
    if ('data' in response) {
      return response.data as T
    }
  }

  return defaultValue
}
