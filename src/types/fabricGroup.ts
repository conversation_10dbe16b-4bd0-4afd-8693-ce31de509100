// 布料组相关类型定义

// 布料组记录
export interface FabricGroup {
  _id: string
  id?: string
  fabric_group_id: string
  fabric_group_year: string
  supplier: string
  group_classification: string[]
  fabrics: string[]
  settlement_state: string
  state: string
  img_url?: string
  scheduling?: string
  createTime?: string
  lastChangeTime?: string
}

// 布料组列表响应
export interface FabricGroupListResponse {
  total: number
  page: number
  limit: number
  data: FabricGroup[]
}

// 查询参数
export interface QueryParams {
  fabric_group_years: string[]
  suppliers: string[]
  classifications: string[]
  fabric_group_id: string
  page: number
  limit: number
}

// 表单数据
export interface FormData {
  fabric_group_id: string
  fabric_group_year: string
  supplier: string
  group_classification: string[]
  fabrics: string[]
  settlement_state: string
  state: string
  img_url?: string
  scheduling?: string
}
