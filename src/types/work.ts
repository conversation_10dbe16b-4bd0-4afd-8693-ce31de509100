// 工序相关类型定义

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

// 年度工价
export interface YearlyPrice {
  year: string
  price: number
  effective_date?: string | Date
}

// 工序信息
export interface Work {
  _id?: string
  id?: string
  work_id: string
  work_name: string
  work_price: number
  hourly_output?: number
  remark?: string
  yearly_prices?: YearlyPrice[]
  createTime?: string | Date
  lastChangeTime?: string | Date
}

// 工序列表响应
export interface WorkListResponse {
  total: number
  page: number
  limit: number
  workList: Work[]
}

// 查询工序参数
export interface QueryWorkParams {
  work_id?: string
  work_name?: string
  years?: string[]
  page?: number
  limit?: number
}

// 创建工序参数
export interface CreateWorkParams {
  work_id: string
  work_name: string
  work_price: number
  hourly_output?: number
  remark?: string
  yearly_prices?: YearlyPrice[]
}

// 更新工序参数
export interface UpdateWorkParams {
  work_id?: string
  work_name?: string
  work_price?: number
  hourly_output?: number
  remark?: string
  yearly_prices?: YearlyPrice[]
}

// 导入响应
export interface ImportResponse {
  success: boolean
  message: string
  count?: number
}
