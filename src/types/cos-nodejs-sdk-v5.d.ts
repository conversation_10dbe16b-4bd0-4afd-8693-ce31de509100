declare module 'cos-nodejs-sdk-v5' {
  interface CosOptions {
    SecretId: string;
    SecretKey: string;
    [key: string]: any;
  }

  interface PutObjectParams {
    Bucket: string;
    Region: string;
    Key: string;
    Body: any;
    [key: string]: any;
  }

  interface DeleteMultipleObjectParams {
    Bucket: string;
    Region: string;
    Objects: { Key: string }[];
    [key: string]: any;
  }

  interface DeleteObjectParams {
    Bucket: string;
    Region: string;
    Key: string;
    [key: string]: any;
  }

  interface CosResult {
    statusCode: number;
    headers: Record<string, any>;
    Location: string;
    ETag: string;
    RequestId: string;
    [key: string]: any;
  }

  type CosCallback = (err: Error | null, data: CosResult) => void;

  class COS {
    constructor(options: CosOptions);
    putObject(params: PutObjectParams, callback: CosCallback): void;
    deleteMultipleObject(params: DeleteMultipleObjectParams, callback: CosCallback): void;
    deleteObject(params: DeleteObjectParams, callback: CosCallback): void;
  }

  export = COS;
}
