// 布料入库相关类型定义

// 布料入库记录
export interface FabricWarehouse {
  _id: string
  fabric_warehouse_id: string
  fabric_warehouse_year: string
  date_in: string | Date
  supplier: string
  remark?: string
  total_meter?: number
  createTime?: string | Date
}

// 布料入库明细
export interface FabricWarehouseDetail {
  _id?: string
  warehouse_id?: string
  fabric_id: string
  fabric_name: string
  meter: number
  classification?: string
  remark?: string
}

// 前端表单中使用的入库明细格式
export interface FabricStockInDetail {
  fabricId: string
  fabricName: string
  category: string
  quantity: number
  remark?: string
  fabricCode?: string
}

// 查询参数
export interface QueryParams {
  years: string[]
  suppliers: string[]
  page: number
  limit: number
}

// 表单数据
export interface FormData {
  code: string
  year: string
  stockInDate: number
  supplier: string
  remark: string
  details: FabricStockInDetail[]
}

// 提交到后端的数据格式
export interface SubmitData {
  fabric_warehouse_id: string
  fabric_warehouse_year: string
  date_in: Date
  supplier: string
  remark?: string
}

// 提交到后端的明细数据格式
export interface SubmitDetailData {
  fabric_id: string
  fabric_name: string
  classification: string
  meter: number
  remark?: string
}
