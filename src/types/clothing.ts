// 服装相关类型定义

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

// 服装图片
export interface ClothingImage {
  url: string
  Key: string
}

// 颜色件数
export interface ColorPcs {
  fabric_id: string
  pcs: string
}

// 服装信息
export interface Clothing {
  _id?: string
  id?: string
  clothing_year: string
  clipping_year?: string
  make_year?: string
  clothing_id: string
  clothing_name: string
  supplier?: string
  group_classification?: string[]
  long_or_short_sleeve?: string
  size?: string
  style?: string
  pocket_type?: string
  fabric_group_id?: string
  order_quantity?: number
  clipping_pcs?: number
  fabric_usage_per_clothing?: number
  memo?: string
  printed?: string
  shipments?: number
  arrival_quantity?: number
  stock_quantity?: number
  state?: string
  craft_details?: string
  total_material_cost?: number
  img?: ClothingImage[]
  colorPcs?: ColorPcs[]
  createTime?: string | Date
  lastChangeTime?: string | Date
}

// 服装列表响应
export interface ClothingListResponse {
  total: number
  clothingList: Clothing[]
}

// 查询参数
export interface QueryClothingParams {
  page?: number
  limit?: number
  clothing_year?: string
  clothing_years?: string[]
  supplier?: string
  suppliers?: string[]
  classification?: string
  classifications?: string[]
  clothing_name?: string
  state?: string
  fabric_group_id?: string
  craft_details?: string
}

// 创建服装参数
export interface CreateClothingParams {
  clothing_year: string
  clipping_year?: string
  make_year?: string
  clothing_id: string
  clothing_name: string
  long_or_short_sleeve?: string
  size?: string
  style?: string
  pocket_type?: string
  fabric_group_id?: string
  order_quantity?: number
  clipping_pcs?: number
  fabric_usage_per_clothing?: number
  memo?: string
  printed?: string
  shipments?: number
  arrival_quantity?: number
  stock_quantity?: number
  state?: string
  craft_details?: string
  img?: ClothingImage[]
  colorPcs?: ColorPcs[]
}

// 更新服装参数
export interface UpdateClothingParams extends Partial<CreateClothingParams> {}

// 导入响应
export interface ImportResponse {
  success: boolean
  message: string
  count?: number
}
