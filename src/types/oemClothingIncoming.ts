// OEM服装入库相关类型定义

// OEM服装入库记录
export interface OemClothingIncoming {
  _id: string
  oem_clothing_incoming_id: string
  oem_clothing_incoming_year: string
  date_in: string | Date
  supplier: string
  remark?: string
  createTime?: string | Date
}

// OEM服装入库明细
export interface OemClothingIncomingDetail {
  _id?: string
  incoming_id?: string
  oem_clothing_id: string
  oem_clothing_name: string
  style?: string
  price?: number
  in_pcs: number
  money?: string
  remark?: string
}

// 前端表单中使用的入库明细格式
export interface OemClothingStockInDetail {
  oemClothingId: string
  oemClothingName: string
  style: string
  price: number
  quantity: number
  money?: string
  remark?: string
}

// 查询参数
export interface QueryParams {
  years: string[]
  suppliers: string[]
  page: number
  limit: number
}

// 表单数据
export interface FormData {
  code: string
  year: string
  stockInDate: number
  supplier: string
  remark: string
  details: OemClothingStockInDetail[]
}

// 提交到后端的数据格式
export interface SubmitData {
  oem_clothing_incoming_id: string
  oem_clothing_incoming_year: string
  date_in: Date
  supplier: string
  remark?: string
}

// 提交到后端的明细数据格式
export interface SubmitDetailData {
  oem_clothing_id: string
  oem_clothing_name: string
  style?: string
  price?: number
  in_pcs: number
  money?: string
  remark?: string
}
