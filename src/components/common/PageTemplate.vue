<template>
  <div class="page-container">
    <!-- 查询区域 -->
    <el-card class="operation-card">
      <el-form
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        class="search-form"
        :label-width="labelWidth"
      >
        <div class="search-form-left">
          <!-- 查询区域左侧插槽 -->
          <slot name="query-form-left"></slot>
          <el-form-item class="query-buttons">
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </div>
        <div class="search-form-right">
          <el-form-item>
            <!-- 查询区域右侧插槽 -->
            <slot name="query-form-right"></slot>
          </el-form-item>
        </div>
      </el-form>
    </el-card>

    <!-- 数据列表区域 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        :height="tableHeight"
        :scrollbar-always-on="true"
        :header-cell-style="headerCellStyle"
        :row-class-name="rowClassName"
        @selection-change="handleSelectionChange"
      >
        <!-- 表格列插槽 -->
        <slot name="table-columns"></slot>
      </el-table>

      <div class="pagination-container">
        <div class="pagination-left">
          <!-- 分页区域左侧插槽 -->
          <slot name="pagination-left"></slot>
        </div>
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.limit"
          :page-sizes="pageSizes"
          :layout="paginationLayout"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 其他内容插槽 -->
    <slot name="extra-content"></slot>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue'
import type { FormInstance } from 'element-plus'

// 定义属性
defineProps({
  // 查询参数
  queryParams: {
    type: Object,
    required: true
  },
  // 表格数据
  tableData: {
    type: Array,
    required: true
  },
  // 总数
  total: {
    type: Number,
    default: 0
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 表格高度
  tableHeight: {
    type: Number,
    default: 650
  },
  // 标签宽度
  labelWidth: {
    type: String,
    default: '70px'
  },
  // 分页大小选项
  pageSizes: {
    type: Array,
    default: () => [10, 20, 30, 50]
  },
  // 分页布局
  paginationLayout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  },
  // 表头单元格样式
  headerCellStyle: {
    type: Object,
    default: () => ({ background: '#f5f7fa', color: '#606266' })
  },
  // 行类名
  rowClassName: {
    type: Function,
    default: () => ''
  }
})

// 定义事件
const emit = defineEmits([
  'query',
  'reset',
  'size-change',
  'current-change',
  'selection-change'
])

// 查询表单引用
const queryFormRef = ref<FormInstance>()

// 查询方法
const handleQuery = () => {
  emit('query')
}

// 重置方法
const handleReset = () => {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields()
  }
  emit('reset')
}

// 分页大小变化
const handleSizeChange = (val: number) => {
  emit('size-change', val)
}

// 当前页变化
const handleCurrentChange = (val: number) => {
  emit('current-change', val)
}

// 表格选择变化
const handleSelectionChange = (selection: any[]) => {
  emit('selection-change', selection)
}
</script>

<style scoped>
.page-container {
  padding: 20px 20px;
}

.operation-card {
  margin-bottom: 20px;
}

.operation-card :deep(.el-card__body) {
  padding: 10px;
}

.search-form {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.search-form-left {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

.search-form-right {
  display: flex;
  gap: 10px;
  align-items: flex-start;
}

/* 确保查询区域的组件在同一水平线上 */
.search-form :deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 10px;
}

.search-form :deep(.el-form-item__label) {
  padding-right: 4px;
  font-size: 13px;
}

.search-form :deep(.el-form-item__content) {
  display: flex;
  align-items: center;
}

/* 调整选择器的高度 */
.search-form :deep(.el-select) {
  line-height: normal;
}

.search-form :deep(.el-input__wrapper) {
  padding-top: 0;
  padding-bottom: 0;
}

.query-buttons {
  margin-right: 32px !important;
}

.search-form :deep(.el-form-item:last-child) {
  margin-right: 0;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-left {
  display: flex;
  gap: 10px;
}
</style>
