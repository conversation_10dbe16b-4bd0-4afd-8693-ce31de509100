import request from '@/utils/request'
import type {
  BillOfMaterials,
  BomListResponse,
  QueryBomParams,
  CreateBomParams,
  UpdateBomParams,
  ImportResponse,
  ApiResponse,
} from '@/types/bom'

/**
 * 获取物料清单列表
 * @param params 查询参数
 * @returns 物料清单列表和总数
 */
export function getBomList(params?: QueryBomParams) {
  return request<ApiResponse<BomListResponse>>({
    url: '/api/bom',
    method: 'get',
    params,
  })
}

/**
 * 获取物料清单详情
 * @param id 物料清单ID
 * @returns 物料清单详情
 */
export function getBomById(id: string) {
  return request<ApiResponse<BillOfMaterials>>({
    url: `/api/bom/${id}`,
    method: 'get',
  })
}

/**
 * 根据服装ID获取物料清单
 * @param clothingId 服装ID
 * @param year 年份（可选）
 * @returns 物料清单详情
 */
export function getBomByClothingId(clothingId: string, year?: string) {
  return request<ApiResponse<BillOfMaterials>>({
    url: `/api/bom/clothing/${clothingId}`,
    method: 'get',
    params: { year },
  })
}

/**
 * 创建物料清单
 * @param data 物料清单数据
 * @returns 创建结果
 */
export function createBom(data: CreateBomParams) {
  return request<ApiResponse<BillOfMaterials>>({
    url: '/api/bom',
    method: 'post',
    data,
  })
}

/**
 * 更新物料清单
 * @param id 物料清单ID
 * @param data 更新数据
 * @returns 更新结果
 */
export function updateBom(id: string, data: UpdateBomParams) {
  return request<ApiResponse<BillOfMaterials>>({
    url: `/api/bom/${id}`,
    method: 'put',
    data,
  })
}

/**
 * 保存物料清单（存在则更新，不存在则创建）
 * @param data 物料清单数据
 * @returns 保存结果
 */
export function saveOrUpdateBom(data: CreateBomParams) {
  return request<ApiResponse<BillOfMaterials>>({
    url: '/api/bom/save-or-update',
    method: 'post',
    data,
  })
}

/**
 * 删除物料清单
 * @param id 物料清单ID
 * @returns 删除结果
 */
export function deleteBom(id: string) {
  return request<ApiResponse<void>>({
    url: `/api/bom/${id}`,
    method: 'delete',
  })
}

/**
 * 计算物料清单成本
 * @param bomId 物料清单ID
 * @param year 计算年份
 * @returns 成本计算结果
 */
export function calculateBomCost(bomId: string, year: string) {
  return request<ApiResponse<{ total_cost: number; item_costs: any[] }>>({
    url: `/api/bom/${bomId}/calculate-cost`,
    method: 'post',
    data: { year },
  })
}

/**
 * 复制物料清单
 * @param bomId 源物料清单ID
 * @param targetClothingId 目标服装ID
 * @param targetYear 目标年份
 * @returns 复制结果
 */
export function copyBom(bomId: string, targetClothingId: string, targetYear: string) {
  return request<ApiResponse<BillOfMaterials>>({
    url: `/api/bom/${bomId}/copy`,
    method: 'post',
    data: { targetClothingId, targetYear },
  })
}

/**
 * 批量导入物料清单数据
 * @param data 物料清单数据列表
 * @returns 导入结果
 */
export function importBomBatch(data: Partial<BillOfMaterials>[]) {
  return request<ApiResponse<ImportResponse>>({
    url: '/api/bom/import-batch',
    method: 'post',
    data,
    timeout: 60000,
  })
}

/**
 * 获取年份选项
 * @returns 年份列表
 */
export function getBomYearOptions() {
  return request<ApiResponse<string[]>>({
    url: '/api/bom/options/years',
    method: 'get',
  })
}
