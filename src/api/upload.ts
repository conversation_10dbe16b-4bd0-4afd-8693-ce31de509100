import request from '@/utils/request'
import type { UploadType, ImageInfo } from '@/utils/upload'

/**
 * API响应类型
 */
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

/**
 * 上传图片
 * @param file 文件对象
 * @param type 上传类型
 * @returns 上传结果
 */
export async function uploadImage(file: File, type: UploadType): Promise<ApiResponse<ImageInfo>> {
  const formData = new FormData()
  formData.append('file', file)

  return request.post(`/api/upload/${type}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**
 * 删除图片
 * @param keys 要删除的图片Key列表
 * @returns 删除结果
 */
export async function deleteImages(keys: { Key: string }[]): Promise<ApiResponse<any>> {
  return request.post('/api/upload/delete', { keys })
}
