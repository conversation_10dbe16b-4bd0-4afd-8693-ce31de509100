import request from '@/utils/request'

// 定义API响应类型
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

// 工序明细接口
export interface CompleteWorkDetail {
  work_id: string
  work_name: string
  pcs: number
}

// 分工完成接口
export interface DivisionWorkComplete {
  _id?: string
  id?: string
  division_work_id: string
  division_work_year: string
  staff_id: string
  staff_name: string
  totalPrice?: string
  work_detail: CompleteWorkDetail[]
  createTime?: string
  lastChangeTime?: string
}

// 创建分工完成参数
export interface CreateDivisionWorkCompleteParams {
  division_work_id: string
  division_work_year: string
  staff_id: string
  staff_name: string
  totalPrice?: string
  work_detail: CompleteWorkDetail[]
}

// 更新分工完成参数
export interface UpdateDivisionWorkCompleteParams {
  division_work_id?: string
  division_work_year?: string
  staff_id?: string
  staff_name?: string
  totalPrice?: string
  work_detail?: CompleteWorkDetail[]
}

// 查询分工完成参数
export interface QueryDivisionWorkCompleteParams {
  division_work_id?: string
  division_work_year?: string
  staff_id?: string
  staff_name?: string
  page?: number
  limit?: number
}

// 分工完成列表响应
export interface DivisionWorkCompleteListResponse {
  total: number
  page: number
  limit: number
  data: DivisionWorkComplete[]
}

/**
 * 获取分工完成列表
 * @param params 查询参数
 * @returns 分工完成列表和总数
 */
export function getDivisionWorkCompleteList(params: QueryDivisionWorkCompleteParams = {}) {
  return request<ApiResponse<DivisionWorkCompleteListResponse>>({
    url: '/api/division-work-complete',
    method: 'get',
    params,
  })
}

/**
 * 获取分工完成详情
 * @param id 分工完成ID
 * @returns 分工完成详情
 */
export function getDivisionWorkCompleteDetail(id: string) {
  return request<ApiResponse<DivisionWorkComplete>>({
    url: `/api/division-work-complete/${id}`,
    method: 'get',
  })
}

/**
 * 根据分工ID获取分工完成列表
 * @param divisionWorkId 分工ID
 * @returns 分工完成列表
 */
export function getDivisionWorkCompleteByDivisionWorkId(divisionWorkId: string) {
  return request<ApiResponse<DivisionWorkComplete[]>>({
    url: `/api/division-work-complete/division-work/${divisionWorkId}`,
    method: 'get',
  })
}

/**
 * 创建分工完成
 * @param data 分工完成数据
 * @returns 创建的分工完成
 */
export function createDivisionWorkComplete(data: CreateDivisionWorkCompleteParams) {
  return request<ApiResponse<DivisionWorkComplete>>({
    url: '/api/division-work-complete',
    method: 'post',
    data,
  })
}

/**
 * 更新分工完成
 * @param id 分工完成ID
 * @param data 更新的数据
 * @returns 更新后的分工完成
 */
export function updateDivisionWorkComplete(id: string, data: UpdateDivisionWorkCompleteParams) {
  return request<ApiResponse<DivisionWorkComplete>>({
    url: `/api/division-work-complete/${id}`,
    method: 'patch',
    data,
  })
}

/**
 * 删除分工完成
 * @param id 分工完成ID
 * @returns 删除结果
 */
export function deleteDivisionWorkComplete(id: string) {
  return request<ApiResponse<{ success: boolean; message: string }>>({
    url: `/api/division-work-complete/${id}`,
    method: 'delete',
  })
}

// 批量更新分工完成参数
export interface BatchDivisionWorkCompleteParams {
  assigns: {
    division_work_id: string
    division_work_year: string
    staff_id: string
    staff_name: string
    totalPrice: string
    work_detail: CompleteWorkDetail[]
  }[]
}

/**
 * 批量更新分工完成
 * @param data 批量更新数据
 * @returns 更新结果
 */
export function batchUpdateDivisionWorkComplete(data: BatchDivisionWorkCompleteParams) {
  return request<ApiResponse<any>>({
    url: '/api/division-work-complete/batch-update',
    method: 'post',
    data,
  })
}

/**
 * 批量创建分工完成
 * @param data 批量创建数据
 * @returns 创建结果
 */
export function batchCreateDivisionWorkComplete(data: BatchDivisionWorkCompleteParams) {
  return request<ApiResponse<any>>({
    url: '/api/division-work-complete/batch-create',
    method: 'post',
    data,
  })
}
