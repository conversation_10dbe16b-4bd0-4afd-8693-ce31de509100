import request from '@/utils/request'

// 布料入库列表
export function getFabricStockInList(params: any) {
  return request({
    url: '/api/fabric-warehouse',
    method: 'get',
    params,
  })
}

// 创建布料入库
export function createFabricStockIn(data: any) {
  return request({
    url: '/api/fabric-warehouse',
    method: 'post',
    data,
  })
}

// 更新布料入库
export function updateFabricStockIn(id: string, data: any) {
  return request({
    url: `/api/fabric-warehouse/${id}`,
    method: 'patch',
    data,
  })
}

// 删除布料入库
export function deleteFabricStockIn(id: string) {
  return request({
    url: `/api/fabric-warehouse/${id}`,
    method: 'delete',
  })
}

// 获取入库年份选项
export const getFabricStockInYearOptions = (suppliers: string = '') => {
  return request({
    url: '/api/fabric-warehouse/year-options',
    method: 'get',
    params: { supplier: suppliers },
  })
}

// 获取入库供应商选项
export const getFabricStockInSupplierOptions = (years: string = '') => {
  return request({
    url: '/api/fabric-warehouse/supplier-options',
    method: 'get',
    params: { year: years },
  })
}

// 获取单个布料入库记录
export function getFabricStockIn(id: number) {
  return request({
    url: `/api/fabric-warehouse/${id}`,
    method: 'get',
  })
}


