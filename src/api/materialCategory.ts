import request from '@/utils/request'
import type {
  MaterialCategory,
  MaterialCategoryListResponse,
  QueryMaterialCategoryParams,
  CreateMaterialCategoryParams,
  UpdateMaterialCategoryParams,
  ApiResponse,
} from '@/types/bom'

/**
 * 获取物料分类列表
 * @param params 查询参数
 * @returns 物料分类列表和总数
 */
export function getMaterialCategoryList(params?: QueryMaterialCategoryParams) {
  return request<ApiResponse<MaterialCategoryListResponse>>({
    url: '/api/material-category',
    method: 'get',
    params,
  })
}

/**
 * 获取物料分类树形结构
 * @returns 树形分类列表
 */
export function getMaterialCategoryTree() {
  return request<ApiResponse<MaterialCategory[]>>({
    url: '/api/material-category/tree',
    method: 'get',
  })
}

/**
 * 获取物料分类详情
 * @param id 分类ID
 * @returns 分类详情
 */
export function getMaterialCategoryById(id: string) {
  return request<ApiResponse<MaterialCategory>>({
    url: `/api/material-category/${id}`,
    method: 'get',
  })
}

/**
 * 创建物料分类
 * @param data 分类数据
 * @returns 创建结果
 */
export function createMaterialCategory(data: CreateMaterialCategoryParams) {
  return request<ApiResponse<MaterialCategory>>({
    url: '/api/material-category',
    method: 'post',
    data,
  })
}

/**
 * 更新物料分类
 * @param id 分类ID
 * @param data 更新数据
 * @returns 更新结果
 */
export function updateMaterialCategory(id: string, data: UpdateMaterialCategoryParams) {
  return request<ApiResponse<MaterialCategory>>({
    url: `/api/material-category/${id}`,
    method: 'put',
    data,
  })
}

/**
 * 删除物料分类
 * @param id 分类ID
 * @returns 删除结果
 */
export function deleteMaterialCategory(id: string) {
  return request<ApiResponse<void>>({
    url: `/api/material-category/${id}`,
    method: 'delete',
  })
}

/**
 * 获取子分类列表
 * @param parentId 父分类ID
 * @returns 子分类列表
 */
export function getChildCategories(parentId: string) {
  return request<ApiResponse<MaterialCategory[]>>({
    url: `/api/material-category/children/${parentId}`,
    method: 'get',
  })
}

/**
 * 获取分类选项（用于下拉选择）
 * @param level 分类级别
 * @returns 分类选项列表
 */
export function getMaterialCategoryOptions(level?: number) {
  return request<ApiResponse<MaterialCategory[]>>({
    url: '/api/material-category/options',
    method: 'get',
    params: { level },
  })
}

/**
 * 生成分类编号
 * @param params 生成参数
 * @returns 生成的分类编号
 */
export function generateCategoryId(params: { level: number; parentCategoryId?: string }) {
  return request<ApiResponse<{ category_id: string }>>({
    url: '/api/material-category/generate-id',
    method: 'get',
    params,
  })
}
