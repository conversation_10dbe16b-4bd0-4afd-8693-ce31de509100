import request from '@/utils/request'

// 布料接口参数类型
export interface FabricParams {
  fabric_year: string
  fabric_id: string
  fabric_name: string
  supplier: string
  classification: string
  in_quantity?: number
  order_quantity?: number
  remark?: string
}

// 更新布料接口参数类型
export interface UpdateFabricParams extends Partial<FabricParams> {}

// 查询布料接口参数类型
export interface QueryFabricParams {
  fabric_year?: string
  fabric_years?: string[]
  fabric_id?: string
  fabric_name?: string
  supplier?: string
  suppliers?: string[]
  classification?: string
  classifications?: string[]
  page?: number
  limit?: number
}

// 布料数据类型
export interface Fabric {
  _id: string
  id?: string
  fabric_year: string
  fabric_id: string
  fabric_name: string
  supplier: string
  classification: string
  in_quantity: number
  order_quantity: number
  remark?: string
  createTime?: string
  createdAt?: string
  updatedAt?: string
}

// 布料列表返回类型
export interface FabricListResponse {
  total: number
  page: number
  limit: number
  fabricList: Fabric[]
}

// 布料列表
export function getFabricList(params?: QueryFabricParams) {
  return request<FabricListResponse>({
    url: '/api/fabric',
    method: 'get',
    params,
  })
}

// 创建布料
export function createFabric(data: FabricParams) {
  return request({
    url: '/api/fabric',
    method: 'post',
    data,
  })
}

// 更新布料
export function updateFabric(id: string, data: UpdateFabricParams) {
  return request({
    url: `/api/fabric/${id}`,
    method: 'patch',
    data,
  })
}

// 删除布料
export function deleteFabric(id: string) {
  return request({
    url: `/api/fabric/${id}`,
    method: 'delete',
  })
}

// 已删除旧的importExcel方法，改用importJsonData方法

// 导入JSON数据
// importData包含: sheetName, data, fileName, totalRecords
export const importJsonData = async (importData: any) => {
  try {
    return request({
      url: '/api/fabric/import-json',
      method: 'post',
      data: importData,
      timeout: 60000 // 增加超时时间为60秒，防止大数据量超时
    })
  } catch (error) {
    console.error('导入数据时出错:', error)
    throw error
  }
}

// 获取布料详情
export const getFabricDetail = (id: string) => {
  return request<Fabric>({
    url: `/api/fabric/${id}`,
    method: 'get',
  })
}

export const getFabricsBySupplier = (fabric_year: string, supplier: string) => {
  return request({
    url: '/api/fabric/by-supplier',
    method: 'get',
    params: { fabric_year, supplier },
  })
}

export const getFabricCategoryOptions = (years: string = '', suppliers: string = '') => {
  return request({
    url: '/api/fabric/category-options',
    method: 'get',
    params: { year: years, supplier: suppliers },
  })
}

export const getFabricYearOptions = (suppliers: string = '', classifications: string = '') => {
  return request({
    url: '/api/fabric/year-options',
    method: 'get',
    params: { supplier: suppliers, classification: classifications },
  })
}

export const getFabricSupplierOptions = (years: string = '', classifications: string = '') => {
  return request({
    url: '/api/fabric/supplier-options',
    method: 'get',
    params: { year: years, classification: classifications },
  })
}

// 更新布料入库数量
export function updateFabricQuantity(fabricId: string) {
  return request({
    url: `/api/fabric/update-in-quantity/${fabricId}`,
    method: 'post',
  })
}

// 批量更新布料入库数量
export function updateFabricQuantityBatch(fabricIds: string[]) {
  return request({
    url: '/api/fabric/update-in-quantity-batch',
    method: 'post',
    data: { fabricIds },
  })
}
