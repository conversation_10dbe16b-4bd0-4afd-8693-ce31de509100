import request from '@/utils/request'

// 布料组接口参数类型
export interface FabricGroupParams {
  fabric_group_id: string
  fabric_group_year: string
  supplier: string
  group_classification?: string[]
  fabrics?: string[]
  settlement_state?: string
  state?: string
  img_url?: string
  scheduling?: string
}

// 更新布料组接口参数类型
export interface UpdateFabricGroupParams extends Partial<FabricGroupParams> {}

// 查询布料组接口参数类型
export interface QueryFabricGroupParams {
  fabric_group_year?: string
  fabric_group_years?: string[]
  fabric_group_id?: string
  supplier?: string
  suppliers?: string[]
  classification?: string
  classifications?: string[]
  state?: string
  page?: number
  limit?: number
}

// 布料组数据类型
export interface FabricGroup {
  _id: string
  id?: string
  fabric_group_id: string
  fabric_group_year: string
  supplier: string
  group_classification: string[]
  fabrics: string[]
  settlement_state: string
  state: string
  img_url?: string
  scheduling?: string
  createTime?: string
  lastChangeTime?: string
}

// 布料组列表返回类型
export interface FabricGroupListResponse {
  total: number
  page: number
  limit: number
  data: FabricGroup[]
}

// 布料组列表
export function getFabricGroupList(params?: QueryFabricGroupParams) {
  // 处理数组参数
  const processedParams: any = { ...params }

  // 如果有数组参数，将其转换为逗号分隔的字符串
  if (params?.fabric_group_years && Array.isArray(params.fabric_group_years) && params.fabric_group_years.length > 0) {
    processedParams.fabric_group_years = params.fabric_group_years.join(',')
  }

  if (params?.suppliers && Array.isArray(params.suppliers) && params.suppliers.length > 0) {
    processedParams.suppliers = params.suppliers.join(',')
  }

  if (params?.classifications && Array.isArray(params.classifications) && params.classifications.length > 0) {
    processedParams.classifications = params.classifications.join(',')
  }

  return request<{ data: FabricGroupListResponse }>({
    url: '/api/fabric-group',
    method: 'get',
    params: processedParams,
  })
}

// 创建布料组
export function createFabricGroup(data: FabricGroupParams) {
  return request({
    url: '/api/fabric-group',
    method: 'post',
    data,
  })
}

// 更新布料组
export function updateFabricGroup(id: string, data: UpdateFabricGroupParams) {
  return request({
    url: `/api/fabric-group/${id}`,
    method: 'patch',
    data,
  })
}

// 删除布料组
export function deleteFabricGroup(id: string) {
  return request({
    url: `/api/fabric-group/${id}`,
    method: 'delete',
  })
}

// 获取布料组详情
export function getFabricGroupDetail(id: string) {
  return request<FabricGroup>({
    url: `/api/fabric-group/${id}`,
    method: 'get',
  })
}

// 获取年份选项
export function getFabricGroupYearOptions(supplier?: string, classification?: string) {
  return request<string[]>({
    url: '/api/fabric-group/year-options',
    method: 'get',
    params: { supplier, classification },
  })
}

// 获取供应商选项
export function getFabricGroupSupplierOptions(year?: string, classification?: string) {
  return request<string[]>({
    url: '/api/fabric-group/supplier-options',
    method: 'get',
    params: { year, classification },
  })
}

// 获取分类选项
export function getFabricGroupClassificationOptions(year?: string, supplier?: string) {
  return request<string[]>({
    url: '/api/fabric-group/classification-options',
    method: 'get',
    params: { year, supplier },
  })
}

// 服装颜色件数类型
export interface ColorPcs {
  fabric_id: string
  pcs: string
}

// 服装信息类型
export interface ClothingInfo {
  _id: string
  clothing_id: string
  clothing_name: string
  colorPcs: ColorPcs[]
}

// 裁剪完成的布料组信息类型
export interface FinishedCuttingGroup {
  _id: string
  fabric_group_id: string
  fabric_group_year: string
  supplier: string
  group_classification: string[]
  clothingList: ClothingInfo[]
}

// 获取状态为"裁剪完成"的布料组及其相关服装信息
export function getFinishedCuttingGroupsWithClothing() {
  return request<{ code: number; data: FinishedCuttingGroup[]; message: string }>({
    url: '/api/fabric-group/finished-cutting/clothing',
    method: 'get',
  })
}
