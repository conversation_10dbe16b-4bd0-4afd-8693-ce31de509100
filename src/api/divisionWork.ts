import request from '@/utils/request'
// 定义API响应类型
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

export interface DivisionWork {
  _id?: string
  id?: string
  division_work_year: string
  division_work_id: string
  clothing_id: string
  clothing_name?: string
  style?: string
  pocket_type?: string
  craft_details?: string
  group_name?: string
  pcs?: number
  is_complete?: number
  createTime?: string
  lastChangeTime?: string
}

export interface CreateDivisionWorkParams {
  division_work_year: string
  division_work_id: string
  clothing_id: string
  group_name?: string
  pcs?: number
  is_complete?: number
  craft_details?: string
}

export interface UpdateDivisionWorkParams {
  division_work_year?: string
  clothing_id?: string
  group_name?: string
  pcs?: number
  is_complete?: number
  craft_details?: string
}

export interface QueryDivisionWorkParams {
  division_work_year?: string
  division_work_years?: string[]
  division_work_id?: string
  clothing_id?: string
  clothing_name?: string
  group_name?: string
  is_complete?: number
  page?: number
  limit?: number
}

export interface DivisionWorkListResponse {
  total: number
  page: number
  limit: number
  data: DivisionWork[]
}

/**
 * 获取分组工作列表
 * @param params 查询参数
 * @returns 分组工作列表和总数
 */
export function getDivisionWorkList(params: QueryDivisionWorkParams = {}) {
  return request<ApiResponse<DivisionWorkListResponse>>({
    url: '/api/division-work',
    method: 'get',
    params,
  })
}

/**
 * 获取分组工作详情
 * @param id 分组工作ID
 * @returns 分组工作详情
 */
export function getDivisionWorkDetail(id: string) {
  return request<ApiResponse<DivisionWork>>({
    url: `/api/division-work/${id}`,
    method: 'get',
  })
}

/**
 * 根据服装ID获取分组工作
 * @param clothingId 服装ID
 * @returns 分组工作列表
 */
export function getDivisionWorkByClothingId(clothingId: string) {
  return request<ApiResponse<DivisionWork[]>>({
    url: `/api/division-work/clothing/${clothingId}`,
    method: 'get',
  })
}

/**
 * 创建分组工作
 * @param data 分组工作数据
 * @returns 创建的分组工作
 */
export function createDivisionWork(data: CreateDivisionWorkParams) {
  return request<ApiResponse<DivisionWork>>({
    url: '/api/division-work',
    method: 'post',
    data,
  })
}

/**
 * 更新分组工作
 * @param id 分组工作ID
 * @param data 更新的数据
 * @returns 更新后的分组工作
 */
export function updateDivisionWork(id: string, data: UpdateDivisionWorkParams) {
  return request<ApiResponse<DivisionWork>>({
    url: `/api/division-work/${id}`,
    method: 'patch',
    data,
  })
}

/**
 * 删除分组工作
 * @param id 分组工作ID
 * @returns 删除结果
 */
export function deleteDivisionWork(id: string) {
  return request<ApiResponse<{ success: boolean; message: string }>>({
    url: `/api/division-work/${id}`,
    method: 'delete',
  })
}

/**
 * 获取年份选项
 * @returns 年份选项列表
 */
export function getDivisionWorkYearOptions() {
  return request<ApiResponse<{ years: string[] }>>({
    url: '/api/division-work/year-options',
    method: 'get',
  })
}

/**
 * 获取组名选项
 * @returns 组名选项列表
 */
export function getDivisionWorkGroupOptions() {
  return request<ApiResponse<{ groups: string[] }>>({
    url: '/api/division-work/group-options',
    method: 'get',
  })
}
