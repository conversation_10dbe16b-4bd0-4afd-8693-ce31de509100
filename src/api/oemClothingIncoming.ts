import request from '@/utils/request'

// OEM服装入库列表
export function getOemClothingIncomingList(params: any) {
  return request({
    url: '/api/oem-clothing-incoming',
    method: 'get',
    params,
  })
}

// 创建OEM服装入库
export function createOemClothingIncoming(data: any) {
  return request({
    url: '/api/oem-clothing-incoming',
    method: 'post',
    data,
  })
}

// 更新OEM服装入库
export function updateOemClothingIncoming(id: string, data: any) {
  return request({
    url: `/api/oem-clothing-incoming/${id}`,
    method: 'patch',
    data,
  })
}

// 删除OEM服装入库
export function deleteOemClothingIncoming(id: string) {
  return request({
    url: `/api/oem-clothing-incoming/${id}`,
    method: 'delete',
  })
}

// 获取OEM服装入库年份选项
export function getOemClothingIncomingYearOptions(supplier?: string) {
  return request({
    url: '/api/oem-clothing-incoming/year-options',
    method: 'get',
    params: { supplier },
  })
}

// 获取OEM服装入库供应商选项
export function getOemClothingIncomingSupplierOptions(year?: string) {
  return request({
    url: '/api/oem-clothing-incoming/supplier-options',
    method: 'get',
    params: { year },
  })
}

// 获取OEM服装入库明细
export function getOemClothingIncomingDetailsByIncomingId(incomingId: string) {
  return request({
    url: `/api/oem-clothing-incoming/detail/incoming/${incomingId}`,
    method: 'get',
  })
}

// 创建OEM服装入库明细
export function createOemClothingIncomingDetail(data: any) {
  return request({
    url: '/api/oem-clothing-incoming/detail',
    method: 'post',
    data,
  })
}

// 批量创建OEM服装入库明细
export function createOemClothingIncomingDetailBatch(incomingId: string, data: any[]) {
  return request({
    url: `/api/oem-clothing-incoming/detail/batch/${incomingId}`,
    method: 'post',
    data,
  })
}

// 更新OEM服装入库明细
export function updateOemClothingIncomingDetail(id: string, data: any) {
  return request({
    url: `/api/oem-clothing-incoming/detail/${id}`,
    method: 'patch',
    data,
  })
}

// 删除OEM服装入库明细
export function deleteOemClothingIncomingDetail(id: string) {
  return request({
    url: `/api/oem-clothing-incoming/detail/${id}`,
    method: 'delete',
  })
}


// 获取指定OEM服装的入库明细
export function getOemClothingIncomingDetailsByOemClothingId(oemClothingId: string) {
  return request({
    url: `/api/oem-clothing-incoming/detail/oem-clothing/${oemClothingId}`,
    method: 'get',
  })
}
