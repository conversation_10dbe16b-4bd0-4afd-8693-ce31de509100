import request from '@/utils/request'

export interface LoginParams {
  userName: string
  userPwd: string
}

export interface RegisterParams {
  userName: string
  userPwd: string
  email?: string
  phone?: string
}

export interface UserInfo {
  id: string
  userName: string
  email?: string
  phone?: string
  createTime?: string
  lastLoginTime?: string
}

export interface LoginResponse {
  token: string
  user: UserInfo
}

/**
 * 用户登录
 */
export const login = (data: LoginParams): Promise<LoginResponse> => {
  return request.post('/api/auth/login', data)
}

/**
 * 用户注册
 */
export const register = (data: RegisterParams): Promise<UserInfo> => {
  return request.post('/api/auth/register', data)
}

/**
 * 获取当前用户信息
 */
export const getUserInfo = (): Promise<UserInfo> => {
  return request.get('/api/auth/profile')
} 