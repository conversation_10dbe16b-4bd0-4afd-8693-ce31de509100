import request from '@/utils/request'

// 定义API响应类型
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

// 工序明细接口
export interface WorkDetail {
  work_id: string
  work_name: string
  pcs: number
}

// 分工分配接口
export interface DivisionWorkAssign {
  _id?: string
  id?: string
  division_work_id: string
  staff_id: string
  staff_name: string
  totalPrice?: string
  work_detail: WorkDetail[]
  createTime?: string
  lastChangeTime?: string
  // 关联的分工信息
  division_work_year?: string
  division_work_id_str?: string
  clothing_id?: string
  group_name?: string
  pcs?: number
  is_complete?: number
}

// 创建分工分配参数
export interface CreateDivisionWorkAssignParams {
  division_work_id: string
  staff_id: string
  staff_name: string
  totalPrice?: string
  work_detail: WorkDetail[]
}

// 更新分工分配参数
export interface UpdateDivisionWorkAssignParams {
  division_work_id?: string
  staff_id?: string
  staff_name?: string
  totalPrice?: string
  work_detail?: WorkDetail[]
}

// 查询分工分配参数
export interface QueryDivisionWorkAssignParams {
  division_work_id?: string
  staff_id?: string
  staff_name?: string
  page?: number
  limit?: number
}

// 分工分配列表响应
export interface DivisionWorkAssignListResponse {
  total: number
  page: number
  limit: number
  data: DivisionWorkAssign[]
}

/**
 * 获取分工分配列表
 * @param params 查询参数
 * @returns 分工分配列表和总数
 */
export function getDivisionWorkAssignList(params: QueryDivisionWorkAssignParams = {}) {
  return request<ApiResponse<DivisionWorkAssignListResponse>>({
    url: '/api/division-work-assign',
    method: 'get',
    params,
  })
}

/**
 * 获取分工分配详情
 * @param id 分工分配ID
 * @returns 分工分配详情
 */
export function getDivisionWorkAssignDetail(id: string) {
  return request<ApiResponse<DivisionWorkAssign>>({
    url: `/api/division-work-assign/${id}`,
    method: 'get',
  })
}

/**
 * 根据分工ID获取分工分配列表
 * @param divisionWorkId 分工ID
 * @returns 分工分配列表
 */
export function getDivisionWorkAssignByDivisionWorkId(divisionWorkId: string) {
  return request<ApiResponse<DivisionWorkAssign[]>>({
    url: `/api/division-work-assign/division-work/${divisionWorkId}`,
    method: 'get',
  })
}

/**
 * 创建分工分配
 * @param data 分工分配数据
 * @returns 创建的分工分配
 */
export function createDivisionWorkAssign(data: CreateDivisionWorkAssignParams) {
  return request<ApiResponse<DivisionWorkAssign>>({
    url: '/api/division-work-assign',
    method: 'post',
    data,
  })
}

/**
 * 更新分工分配
 * @param id 分工分配ID
 * @param data 更新的数据
 * @returns 更新后的分工分配
 */
export function updateDivisionWorkAssign(id: string, data: UpdateDivisionWorkAssignParams) {
  return request<ApiResponse<DivisionWorkAssign>>({
    url: `/api/division-work-assign/${id}`,
    method: 'patch',
    data,
  })
}

/**
 * 删除分工分配
 * @param id 分工分配ID
 * @returns 删除结果
 */
export function deleteDivisionWorkAssign(id: string) {
  return request<ApiResponse<{ success: boolean; message: string }>>({
    url: `/api/division-work-assign/${id}`,
    method: 'delete',
  })
}

// 批量更新分工分配参数
export interface BatchDivisionWorkAssignParams {
  assigns: {
    division_work_id: string
    division_work_year: string
    staff_id: string
    staff_name: string
    totalPrice: string // 修改为string类型以匹配后端期望
    work_detail: WorkDetail[]
  }[]
}

// 批量更新分工分配
export function batchUpdateDivisionWorkAssign(data: BatchDivisionWorkAssignParams) {
  return request<ApiResponse<any>>({
    url: '/api/division-work-assign/batch-update',
    method: 'post',
    data,
  })
}

//批量创建分工分配
export function batchCreateDivisionWorkAssign(data: BatchDivisionWorkAssignParams) {
  return request<ApiResponse<any>>({
    url: '/api/division-work-assign/batch-create',
    method: 'post',
    data,
  })
}
