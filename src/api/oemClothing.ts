import request from '@/utils/request'
import type {
  OemClothing,
  OemClothingListResponse,
  QueryOemClothingParams,
  CreateOemClothingParams,
  UpdateOemClothingParams,
  ImportResponse,
  ApiResponse,
} from '@/types/oemClothing'

/**
 * 获取OEM服装列表
 * @param params 查询参数
 * @returns OEM服装列表和总数
 */
export function getOemClothingList(params?: QueryOemClothingParams) {
  return request<ApiResponse<OemClothingListResponse>>({
    url: '/api/oem-clothing',
    method: 'get',
    params,
  })
}

/**
 * 获取OEM服装详情
 * @param id OEM服装ID
 * @returns OEM服装详情
 */
export function getOemClothingById(id: string) {
  return request<ApiResponse<OemClothing>>({
    url: `/api/oem-clothing/${id}`,
    method: 'get',
  })
}

/**
 * 创建OEM服装
 * @param data OEM服装数据
 * @returns 创建的OEM服装
 */
export function createOemClothing(data: CreateOemClothingParams) {
  return request<ApiResponse<OemClothing>>({
    url: '/api/oem-clothing',
    method: 'post',
    data,
  })
}

/**
 * 更新OEM服装
 * @param id OEM服装ID
 * @param data 更新的OEM服装数据
 * @returns 更新后的OEM服装
 */
export function updateOemClothing(id: string, data: UpdateOemClothingParams) {
  return request<ApiResponse<OemClothing>>({
    url: `/api/oem-clothing/${id}`,
    method: 'put',
    data,
  })
}

/**
 * 删除OEM服装
 * @param id OEM服装ID
 * @returns 删除结果
 */
export function deleteOemClothing(id: string) {
  return request<ApiResponse<{ success: boolean; message: string }>>({
    url: `/api/oem-clothing/${id}`,
    method: 'delete',
  })
}

/**
 * 获取OEM服装年份选项
 * @param params 查询参数
 * @returns 年份选项列表
 */
export function getOemClothingYearOptions(params?: { supplier?: string; classification?: string }) {
  return request<ApiResponse<string[]>>({
    url: '/api/oem-clothing/options/years',
    method: 'get',
    params,
  })
}

/**
 * 获取供应商选项
 * @param params 查询参数
 * @returns 供应商选项列表
 */
export function getSupplierOptions(params?: { year?: string; classification?: string }) {
  return request<ApiResponse<string[]>>({
    url: '/api/oem-clothing/options/suppliers',
    method: 'get',
    params,
  })
}

/**
 * 获取分类选项
 * @param params 查询参数
 * @returns 分类选项列表
 */
export function getClassificationOptions(params?: { year?: string; supplier?: string }) {
  return request<ApiResponse<string[]>>({
    url: '/api/oem-clothing/options/classifications',
    method: 'get',
    params,
  })
}

/**
 * 获取尺码、款式选项
 * @returns 尺码、款式选项列表
 */
export function getTwoOptions() {
  return request<ApiResponse<{ sizes: string[]; styles: string[] }>>({
    url: '/api/oem-clothing/options/two',
    method: 'get',
  })
}

/**
 * 获取尺码选项
 * @returns 尺码选项列表
 */
export function getSizeOptions() {
  return request<ApiResponse<string[]>>({
    url: '/api/oem-clothing/options/sizes',
    method: 'get',
  })
}

/**
 * 获取款式选项
 * @returns 款式选项列表
 */
export function getStyleOptions() {
  return request<ApiResponse<string[]>>({
    url: '/api/oem-clothing/options/styles',
    method: 'get',
  })
}

/**
 * 批量导入OEM服装数据
 * @param data OEM服装数据列表
 * @returns 导入结果
 */
export function importOemClothingBatch(data: Partial<OemClothing>[]) {
  return request<ApiResponse<ImportResponse>>({
    url: '/api/oem-clothing/import-batch',
    method: 'post',
    data,
    timeout: 60000, // 增加超时时间为60秒，防止大数据量超时
  })
}

/**
 * 导入JSON数据
 * @param importData 包含: sheetName, data, fileName, totalRecords
 * @returns 导入结果
 */
export const importJsonData = async (importData: any) => {
  try {
    return request<ApiResponse<ImportResponse>>({
      url: '/api/oem-clothing/import-json',
      method: 'post',
      data: importData,
      timeout: 60000, // 增加超时时间为60秒，防止大数据量超时
    })
  } catch (error) {
    console.error('导入数据时出错:', error)
    throw error
  }
}

/**
 * 根据服装名称和年份查找服装
 * @param data 包含服装名称数组、OEM服装名称数组和服装年份
 * @returns 查询到的服装和OEM服装列表
 */
export function findOemClothingByNames(data: { oem_clothing_names: string[]; oem_clothing_year: string }) {
  return request<
    ApiResponse<{
      success: boolean
      oemClothingList: OemClothing[]
    }>
  >({
    url: '/api/oem-clothing/find-by-names',
    method: 'post',
    data,
  })
}

/**
 * 数据校准 - 重新统计并更新指定OEM服装的入库数和出库数
 * @param calibrateData 校准数据，包含查询条件或服装ID列表
 * @returns 校准结果
 */
export function calibrateOemClothingData(calibrateData?: {
  oemClothingIds?: string[];
  queryParams?: any
}) {
  return request<ApiResponse<{ message: string; updated: number }>>({
    url: '/api/oem-clothing/calibrate-data',
    method: 'post',
    data: calibrateData,
    timeout: 120000, // 增加超时时间为2分钟，因为数据校准可能需要较长时间
  })
}
