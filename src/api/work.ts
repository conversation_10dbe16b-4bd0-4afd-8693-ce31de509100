import request from '@/utils/request'
import type {
  Work,
  WorkListResponse,
  QueryWorkParams,
  CreateWorkParams,
  UpdateWorkParams,
  ImportResponse,
  ApiResponse,
} from '@/types/work'

/**
 * 获取工序列表
 * @param params 查询参数
 * @returns 工序列表和总数
 */
export function getWorkList(params?: QueryWorkParams): Promise<ApiResponse<WorkListResponse>> {
  return request({
    url: '/api/work',
    method: 'get',
    params,
  })
}

/**
 * 获取工序详情
 * @param id 工序ID
 * @returns 工序详情
 */
export function getWorkById(id: string) {
  return request<ApiResponse<Work>>({
    url: `/api/work/${id}`,
    method: 'get',
  })
}

/**
 * 创建工序
 * @param data 工序数据
 * @returns 创建的工序
 */
export function createWork(data: CreateWorkParams) {
  return request<ApiResponse<Work>>({
    url: '/api/work',
    method: 'post',
    data,
  })
}

/**
 * 更新工序
 * @param id 工序ID
 * @param data 工序数据
 * @returns 更新后的工序
 */
export function updateWork(id: string, data: UpdateWorkParams) {
  return request<ApiResponse<Work>>({
    url: `/api/work/${id}`,
    method: 'patch',
    data,
  })
}

/**
 * 删除工序
 * @param id 工序ID
 * @returns 删除结果
 */
export function deleteWork(id: string) {
  return request<ApiResponse<Work>>({
    url: `/api/work/${id}`,
    method: 'delete',
  })
}

/**
 * 获取年份选项
 * @returns 年份选项列表
 */
export function getYearOptions() {
  return request<ApiResponse<string[]>>({
    url: '/api/work/year-options',
    method: 'get',
  })
}

/**
 * 批量导入工序数据
 * @param data 工序数据列表
 * @returns 导入结果
 */
export function importJsonData(importData: any): Promise<ApiResponse<ImportResponse>> {
  return request({
    url: '/api/work/import-json',
    method: 'post',
    data: importData,
    timeout: 60000, // 增加超时时间为60秒，防止大数据量超时
  })
}
