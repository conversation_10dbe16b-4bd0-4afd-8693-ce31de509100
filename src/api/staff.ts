import request from '@/utils/request'

// 员工接口参数类型
export interface StaffParams {
  staff_id: string
  name: string
  pinyin?: string
  gender?: string
  id_number?: string
  tel?: string
  add?: string
  bank_card_number?: string
  post?: string
  floor?: string
  clearing?: boolean
  salary?: number
  lastChangeTime?: Date | string
}

// 更新员工接口参数类型
export interface UpdateStaffParams extends Partial<StaffParams> {}

// 查询员工接口参数类型
export interface QueryStaffParams {
  staff_id?: string
  name?: string
  pinyin?: string
  gender?: string
  id_number?: string
  tel?: string
  add?: string
  post?: string
  posts?: string[]
  floor?: string
  floors?: string[]
  clearing?: boolean
  page?: number
  limit?: number
}

// 员工数据类型
export interface Staff {
  _id: string
  id?: string
  staff_id: string
  name: string
  pinyin?: string
  gender?: string
  id_number?: string
  tel?: string
  add?: string
  bank_card_number?: string
  post?: string
  floor?: string
  clearing?: boolean
  salary?: number
  createTime?: string
  lastChangeTime?: string
  createdAt?: string
  updatedAt?: string
}

// 员工列表返回类型
export interface StaffListResponse {
  total: number
  page: number
  limit: number
  staffList: Staff[]
}

// 员工列表
export function getStaffList(params?: QueryStaffParams) {
  return request<StaffListResponse>({
    url: '/api/staff',
    method: 'get',
    params,
  })
}

// 创建员工
export function createStaff(data: StaffParams) {
  return request({
    url: '/api/staff',
    method: 'post',
    data,
  })
}

// 更新员工
export function updateStaff(id: string, data: UpdateStaffParams) {
  return request({
    url: `/api/staff/${id}`,
    method: 'patch',
    data,
  })
}

// 删除员工
export function deleteStaff(id: string) {
  return request({
    url: `/api/staff/${id}`,
    method: 'delete',
  })
}

// 导入JSON数据返回类型
export interface ImportJsonResponse {
  success: boolean
  message: string
  count?: number
}

// 导入JSON数据
// importData包含: sheetName, data, fileName, totalRecords
export const importJsonData = async (importData: any) => {
  try {
    console.log('发送导入请求，数据长度:', importData.data?.length || 0)

    const response = await request<ImportJsonResponse>({
      url: '/api/staff/import-json',
      method: 'post',
      data: importData,
      timeout: 60000, // 增加超时时间为60秒，防止大数据量超时
    })

    // 详细记录响应数据
    console.log('导入响应原始数据:', response)
    console.log('响应数据类型:', typeof response)
    console.log('响应数据属性:', Object.keys(response))

    // 如果 response 是对象但没有 data 属性，则返回 response 本身
    if (response && typeof response === 'object') {
      if ('data' in response) {
        console.log('响应包含 data 属性，返回 response.data')
        return response.data
      }
      // 如果有 success 属性，说明返回的就是我们需要的数据格式
      if ('success' in response) {
        console.log('响应包含 success 属性，直接返回 response')
        return response
      }
    }

    // 如果不符合上述条件，构造一个默认的成功响应
    console.log('响应不符合预期格式，返回默认成功响应')
    return { success: true, message: '导入成功' }
  } catch (error) {
    console.error('导入数据时出错:', error)
    throw error
  }
}

// 获取员工详情
export const getStaffDetail = (id: string) => {
  return request<Staff>({
    url: `/api/staff/${id}`,
    method: 'get',
  })
}

// 获取楼层/部门选项
export const getFloorOptions = (posts: string = '') => {
  return request({
    url: '/api/staff/floor-options',
    method: 'get',
    params: { post: posts },
  })
}

// 获取职位选项
export const getPostOptions = (floors: string = '') => {
  return request({
    url: '/api/staff/post-options',
    method: 'get',
    params: { floor: floors },
  })
}

// 获取性别选项
export const getGenderOptions = () => {
  return request({
    url: '/api/staff/gender-options',
    method: 'get',
  })
}

// 员工工资导出返回类型
export interface StaffSalaryExportResponse {
  staffSalaryData: any[]
  totalData: any[]
}

// 导出员工工资
export const exportStaffSalary = (year: string, staff_id?: string) => {
  return request<StaffSalaryExportResponse>({
    url: '/api/staff/export-salary',
    method: 'get',
    params: { year, staff_id },
    timeout: 60000, // 增加超时时间为60秒，防止大数据量超时
  })
}

// 导出所有员工工资为ZIP
export const exportStaffSalaryZip = (year: string) => {
  // 使用window.open直接下载文件
  window.open(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:4000'}/api/staff/export-salary-zip?year=${year}`, '_blank')
}
