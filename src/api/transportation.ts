import request from '@/utils/request'

// 发货信息列表
export function getTransportationList(params: any) {
  return request({
    url: '/api/transportation',
    method: 'get',
    params,
  })
}

// 创建发货信息
export function createTransportation(data: any) {
  return request({
    url: '/api/transportation',
    method: 'post',
    data,
  })
}

// 更新发货信息
export function updateTransportation(id: string, data: any) {
  return request({
    url: `/api/transportation/${id}`,
    method: 'patch',
    data,
  })
}

// 更新发货信息及其明细
export function updateTransportationWithDetails(id: string, transportation: any, details: any[]) {
  return request({
    url: `/api/transportation/${id}/with-details`,
    method: 'patch',
    data: {
      transportation,
      details,
    },
  })
}

// 删除发货信息
export function deleteTransportation(id: string) {
  return request({
    url: `/api/transportation/${id}`,
    method: 'delete',
  })
}

// 获取发货信息年份选项
export function getTransportationYearOptions(supplier?: string) {
  return request({
    url: '/api/transportation/year-options',
    method: 'get',
    params: { supplier },
  })
}

// 获取发货信息运输公司选项
export function getTransportationSupplierOptions(year?: string) {
  return request({
    url: '/api/transportation/supplier-options',
    method: 'get',
    params: { year },
  })
}

// 获取发货信息明细
export function getTransportationDetailsByTransportationId(transportationId: string) {
  return request({
    url: `/api/transportation/detail/transportation/${transportationId}`,
    method: 'get',
  })
}

// 创建发货信息明细
export function createTransportationDetail(data: any) {
  return request({
    url: '/api/transportation/detail',
    method: 'post',
    data,
  })
}

// 批量创建发货信息明细
export function createTransportationDetailBatch(transportationId: string, data: any[]) {
  return request({
    url: `/api/transportation/detail/batch/${transportationId}`,
    method: 'post',
    data,
  })
}

// 更新发货信息明细
export function updateTransportationDetail(id: string, data: any) {
  return request({
    url: `/api/transportation/detail/${id}`,
    method: 'patch',
    data,
  })
}

// 删除发货信息明细
export function deleteTransportationDetail(id: string) {
  return request({
    url: `/api/transportation/detail/${id}`,
    method: 'delete',
  })
}

// 获取指定服装的发货明细
export function getTransportationDetailsByClothingId(clothingId: string) {
  return request({
    url: `/api/transportation/detail/clothing/${clothingId}`,
    method: 'get',
  })
}

// 获取最新的发货编码
export function getLatestTransportationId() {
  return request({
    url: '/api/transportation/latest-id',
    method: 'get',
  })
}

// 获取发货信息详情
export function getTransportationById(id: string) {
  return request({
    url: `/api/transportation/${id}`,
    method: 'get',
  })
}

// 获取发货明细导出数据
export function getTransportationDetailsExportData(transportationIds: string[]) {
  return request({
    url: '/api/transportation/export-selected-details',
    method: 'post',
    data: { transportationIds },
    timeout: 30000, // 增加超时时间为30秒，因为可能需要处理大量数据
  })
}

// 包裹初始化
export function initializePackages(data: any) {
  return request({
    url: '/api/transportation/initialize-packages',
    method: 'post',
    data,
    timeout: 60000, // 增加超时时间为60秒，因为可能需要处理大量数据
  })
}
