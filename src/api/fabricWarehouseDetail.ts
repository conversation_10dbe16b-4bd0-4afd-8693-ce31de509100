import request from '@/utils/request'

// 布料入库明细接口参数类型
export interface FabricWarehouseDetailParams {
  warehouse_id: string
  fabric_id: string
  fabric_name: string
  classification: string
  meter: number
  remark?: string
}

// 获取指定仓库的所有入库明细
export function getFabricWarehouseDetailsByWarehouseId(warehouseId: string) {
  return request({
    url: `/api/fabric-warehouse-detail/warehouse/${warehouseId}`,
    method: 'get',
  }).then(response => {
    // 确保返回的是数组数据
    console.log('获取入库明细原始响应:', response)
    if (Array.isArray(response)) {
      return response
    } else if (response && typeof response === 'object') {
      if (Array.isArray(response.data)) {
        return response.data
      } else {
        return []
      }
    } else {
      return []
    }
  })
}

// 获取指定布料的入库明细
export function getFabricWarehouseDetailsByFabricId(fabricId: string) {
  return request({
    url: `/api/fabric-warehouse-detail/fabric/${fabricId}`,
    method: 'get',
  }).then(response => {
    // 确保返回的是数组数据
    console.log('获取布料入库明细原始响应:', response)
    if (Array.isArray(response)) {
      return response
    } else if (response && typeof response === 'object') {
      if (Array.isArray(response.data)) {
        return response.data
      } else {
        return []
      }
    } else {
      return []
    }
  })
}

// 获取单个入库明细
export function getFabricWarehouseDetail(id: string) {
  return request({
    url: `/api/fabric-warehouse-detail/${id}`,
    method: 'get',
  })
}

// 创建入库明细
export function createFabricWarehouseDetail(data: FabricWarehouseDetailParams) {
  return request({
    url: '/api/fabric-warehouse-detail',
    method: 'post',
    data,
  })
}

// 批量创建入库明细
export function createFabricWarehouseDetailBatch(warehouseId: string, details: Omit<FabricWarehouseDetailParams, 'warehouse_id'>[]) {
  return request({
    url: `/api/fabric-warehouse-detail/batch/${warehouseId}`,
    method: 'post',
    data: details,
  })
}

// 更新入库明细
export function updateFabricWarehouseDetail(id: string, data: Partial<FabricWarehouseDetailParams>) {
  return request({
    url: `/api/fabric-warehouse-detail/${id}`,
    method: 'patch',
    data,
  })
}

// 删除入库明细
export function deleteFabricWarehouseDetail(id: string) {
  return request({
    url: `/api/fabric-warehouse-detail/${id}`,
    method: 'delete',
  })
}
