# 依赖
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.pnpm-store/

# 编译输出
/dist
/build

# 环境变量
.env
.env.*
!.env.example
.ecosystem.config.js

# 编辑器目录和文件
.idea
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 系统文件
.DS_Store
Thumbs.db

# 日志
logs
*.log

# 临时文件
*.tmp
*.temp
*.bak
*.swp
*.swo
*.zip
*.rar
*.7z
*.gz
*.tar
*.tgz
*.bz2
*.xz
*.lzma
*.lz
*.cab
*.iso
*.dmg
*.img
*.bin
*.exe
*.dll
*.msi
*.deb
*.rpm
*.pkg
*.apk

# 开发工具和脚本（生产环境不需要）
/tools/
/scripts/

# 开发文档（生产环境不需要）
*.md
!README.md

# 迁移和分析报告
migration-reports/
validation-reports/
analysis/

# 部署指南
deployment-guide/

# 上传文件（根据需要调整）
uploads/
!uploads/.gitkeep

# 备份文件
*.backup
*~

# 测试覆盖率
coverage

# 锁定文件
# package-lock.json
# yarn.lock
# pnpm-lock.yaml
