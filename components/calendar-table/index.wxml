<!-- components/calendar-table/index.wxml -->
<view class="calendar-container">
  <!-- 日历头部 -->
  <view class="calendar-header">
    <view class="header-controls">
      <!-- 上一月按钮 -->
      <view class="nav-button" bindtap="onPrevMonth">
        <view class="nav-icon">‹</view>
      </view>
      <!-- 年月显示和选择 -->
      <view class="date-selector">
        <view class="year-month-display">
          <view class="year-view" bindtap="onShowYearPicker">{{currentYear}}年</view>
          <view class="month-view" bindtap="onShowMonthPicker">{{currentMonth}}月</view>
        </view>
      </view>
      <!-- 下一月按钮 -->
      <view class="nav-button" bindtap="onNextMonth">
        <view class="nav-icon">›</view>
      </view>
    </view>
    <!-- 今天按钮 -->
    <view class="today-button" bindtap="selectToday">
      <view>选择今日</view>
    </view>
  </view>
  <!-- 星期标题 -->
  <view class="week-header">
    <view class="week-day" wx:for="{{weekDays}}" wx:key="index">
      <view>{{item}}</view>
    </view>
  </view>
  <!-- 日历主体 -->
  <view class="calendar-body" bindtouchstart="onTouchStart" bindtouchmove="onTouchMove" bindtouchend="onTouchEnd">
    <!-- 使用单层循环，通过CSS Grid布局 -->
    <view class="calendar-grid">
      <view class="calendar-cell {{item.isCurrentMonth ? 'current-month' : 'other-month'}} {{item.isToday ? 'today' : ''}} {{item.isSelected ? 'selected' : ''}} {{item.isInRange ? 'in-range' : ''}} {{item.isRangeStart ? 'range-start' : ''}} {{item.isRangeEnd ? 'range-end' : ''}}" wx:for="{{calendarDays}}" wx:for-item="item" wx:for-index="index" wx:key="fullDate" data-index="{{index}}" bindtap="onDateTap">
        <!-- 日期数字 -->
        <view class="date-number">
          <view>{{item.date}}</view>
        </view>
        <!-- 出库数量显示 -->
        <view class="outbound-count" wx:if="{{item.isCurrentMonth && item.outboundCount > 0}}">
          <view>{{item.outboundCount}}</view>
        </view>
      </view>
    </view>
  </view>
  <!-- 选中信息和查询按钮 -->
  <view class="calendar-footer" wx:if="{{selectedDates.length > 0 && showQueryButton}}">
    <view class="selected-info">
      <view class="date-info">
        <view wx:if="{{selectedDates.length === 1}}">已选择: {{selectedDates[0]}}</view>
        <view wx:else>
          已选择: {{selectedDates[0]}} 至 {{selectedDates[selectedDates.length - 1]}} ({{selectedDates.length}}天)
        </view>
      </view>
      <view class="outbound-total" wx:if="{{selectedDatesTotal > 0}}">
        <view>出库总数: {{selectedDatesTotal}}包</view>
      </view>
    </view>
    <view class="query-button" bindtap="onQuery">
      <view>查询</view>
    </view>
  </view>
  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <van-loading type="spinner" size="24px" color="#2c9678">加载中...</van-loading>
  </view>
</view>
<!-- 年份选择器弹窗 -->
<van-popup show="{{showYearPicker}}" position="bottom" bind:close="onCloseYearPicker" z-index="9999">
  <view class="picker-header">
    <view class="picker-cancel" bindtap="onCloseYearPicker">取消</view>
    <view class="picker-title">选择年份</view>
    <view class="picker-confirm" bindtap="onConfirmYear">确定</view>
  </view>
  <van-picker columns="{{yearOptions}}" bind:change="onYearChange" default-index="{{currentYearIndex}}" />
</van-popup>
<!-- 月份选择器弹窗 -->
<van-popup show="{{showMonthPicker}}" position="bottom" bind:close="onCloseMonthPicker" z-index="9999">
  <view class="picker-header">
    <view class="picker-cancel" bindtap="onCloseMonthPicker">取消</view>
    <view class="picker-title">选择月份</view>
    <view class="picker-confirm" bindtap="onConfirmMonth">确定</view>
  </view>
  <van-picker columns="{{monthOptions}}" bind:change="onMonthChange" default-index="{{currentMonth - 1}}" />
</van-popup>