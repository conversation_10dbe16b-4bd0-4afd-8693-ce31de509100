Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 服装信息
    clothingInfo: Object,
    // OEM服装信息
    oemClothingInfo: Object,
    // 是否为OEM服装
    isOem: {
      type: Boolean,
      value: false
    },
    // 是否显示详情信息（到货数和库存数）
    showDetailInfo: {
      type: Boolean,
      value: false
    },
    // 是否禁用点击跳转（在详情页面中使用）
    disableNavigation: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    totalPcs: 0,
    shipments: 0
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 预览图片
     */
    showImg(e) {
     
      const info = this.data.isOem ? this.data.oemClothingInfo : this.data.clothingInfo;
      console.log("info",info)
      let images = [];
      images = info.img.filter(img => img && img.url).map(img => img.url);

      
      if (images.length > 0) {
        wx.previewImage({
          urls: images,
          current: images[0]
        });
      }
    },

    /**
     * 点击卡片事件
     */
    onCardTap() {
      const info = this.data.isOem ? this.data.oemClothingInfo : this.data.clothingInfo;
      console.log("info", info)

      // 如果禁用导航，则不执行跳转逻辑
      if (this.data.disableNavigation) {
        console.log('导航已禁用，不执行跳转');
        // 仍然触发事件，保持兼容性
        this.triggerEvent('cardTap', {
          info: info,
          isOem: this.data.isOem
        });
        return;
      }

      // 跳转到服装详情页面，携带完整的服装信息
      if (this.data.isOem && info.oem_clothing_id) {
        // 将服装信息存储到全局数据中，供详情页面使用
        getApp().globalData.clothingDetailInfo = {
          clothingInfo: info,
          isOem: true
        };

        wx.navigateTo({
          url: `/pages/clothing-detail/clothing-detail?oemClothingId=${info.oem_clothing_id}&isOem=true&hasData=true`
        });
      } else if (!this.data.isOem && info.clothing_id) {
        // 将服装信息存储到全局数据中，供详情页面使用
        getApp().globalData.clothingDetailInfo = {
          clothingInfo: info,
          isOem: false
        };

        wx.navigateTo({
          url: `/pages/clothing-detail/clothing-detail?clothingId=${info.clothing_id}&isOem=false&hasData=true`
        });
      } else {
        wx.showToast({
          title: '服装信息不完整',
          icon: 'none'
        });
      }

      // 同时触发原有事件，保持兼容性
      this.triggerEvent('cardTap', {
        info: info,
        isOem: this.data.isOem
      });
    },

    /**
     * 修改价格
     */
    onChangePrice(e) {
      e.stopPropagation(); // 阻止事件冒泡
      const info = this.data.isOem ? this.data.oemClothingInfo : this.data.clothingInfo;
      this.triggerEvent('changePrice', {
        info: info,
        isOem: this.data.isOem
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
  },

}); 