<view class="clothing-card" >
  <!-- 普通服装卡片 -->
  <view wx:if="{{!isOem}}" class="card-container">
    <!-- 服装属性标签 -->
    <view class="card-box-content">
      <view class="card-content-a">{{clothingInfo.clothing_name}}</view>
      <view class="card-content-b">
        <van-tag wx:if="{{clothingInfo.img.length>0}}" bindtap="showImg" size="large" color="#7aa386" type="primary" class="card-tag">
          图片
        </van-tag>
        <van-tag color="#98281e99" wx:if="{{clothingInfo.price}}" type="primary" size="large" class="card-tag">
          ￥{{clothingInfo.price}}
        </van-tag>
      </view>
    </view>
    <view class="card-box-card-tag">
      <van-tag wx:if="{{clothingInfo.supplier}}" size="medium" type="primary" color="#2775b699" class="card-tag">
        {{clothingInfo.supplier}}
      </van-tag>
      <van-tag wx:if="{{clothingInfo.group_classification}}" wx:for="{{clothingInfo.group_classification}}" wx:key="index" size="medium" type="primary" color="#2775b699" class="card-tag">
        {{item}}
      </van-tag>
    </view>
    <view class="card-box-card-tag">
      <van-tag wx:if="{{clothingInfo.style}}" size="medium" type="success" color="#45b78799" class="card-tag">
        {{clothingInfo.style}}
      </van-tag>
      <van-tag wx:if="{{clothingInfo.long_or_short_sleeve}}" size="medium" type="success" color="#45b78799" class="card-tag">
        {{clothingInfo.long_or_short_sleeve}}
      </van-tag>
      <van-tag wx:if="{{clothingInfo.size}}" size="medium" type="success" color="#45b78799" class="card-tag">
        {{clothingInfo.size}}
      </van-tag>
      <van-tag wx:if="{{clothingInfo.pocket_type}}" size="medium" type="success" color="#45b78799" class="card-tag">
        {{clothingInfo.pocket_type}}
      </van-tag>
    </view>
    <!-- 数量信息 -->
    <view class="quantity-info" bindtap="onCardTap">
      <!-- 详情页面显示的信息 -->
      <view class="detail-quantity-info">
        <view class="quantity-row">
          <view class="quantity-item">
            <text class="quantity-label">裁剪数</text>
            <text class="quantity-value">{{clothingInfo.clipping_pcs || 0}}</text>
          </view>
          <view class="quantity-item">
            <text class="quantity-label">发货数</text>
            <text class="quantity-value">{{clothingInfo.shipments || 0}}</text>
          </view>
          <view class="quantity-item">
            <text class="quantity-label">到货数</text>
            <text class="quantity-value">{{clothingInfo.arrival_quantity || 0}}</text>
          </view>
          <view class="quantity-item">
            <text class="quantity-label">库存数</text>
            <text class="quantity-value">{{clothingInfo.stock_quantity || 0}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- OEM服装卡片 -->
  <view wx:else class="card-container">
    <view class="card-box-content">
      <view class="card-content-a">{{oemClothingInfo.oem_clothing_name}}</view>
      <view class="card-content-b">
        <van-tag wx:if="{{oemClothingInfo.img.length>0}}" bindtap="showImg" size="large" color="#7aa386" type="primary" class="card-tag">
          图片
        </van-tag>
        <van-tag color="#98281e99" wx:if="{{oemClothingInfo.price}}" type="primary" size="large" class="card-tag">
          ￥{{oemClothingInfo.price}}
        </van-tag>
      </view>
    </view>
    <view class="card-box-card-tag">
      <van-tag wx:if="{{oemClothingInfo.oem_supplier}}" size="medium" type="primary" color="#2775b699" class="card-tag">
        {{oemClothingInfo.oem_supplier}}
      </van-tag>
      <van-tag wx:if="{{oemClothingInfo.classification}}" size="medium" type="primary" color="#2775b699" class="card-tag">
        {{oemClothingInfo.classification}}
      </van-tag>
    </view>
    <view class="card-box-card-tag">
      <van-tag wx:if="{{oemClothingInfo.style}}" size="medium" type="success" color="#45b78799" class="card-tag">
        {{oemClothingInfo.style}}
      </van-tag>
      <van-tag wx:if="{{oemClothingInfo.size}}" size="medium" type="success" color="#45b78799" class="card-tag">
        {{oemClothingInfo.size}}
      </van-tag>
    </view>
    <!-- 数量信息 -->
    <view class="quantity-info" bindtap="onCardTap">
      <!-- 详情页面显示的信息 -->
      <view class="detail-quantity-info">
        <view class="quantity-row">
          <view class="quantity-item">
            <text class="quantity-label">进货数</text>
            <text class="quantity-value">{{oemClothingInfo.in_pcs || 0}}</text>
          </view>
          <view class="quantity-item">
            <text class="quantity-label">发货数</text>
            <text class="quantity-value">{{oemClothingInfo.shipments || 0}}</text>
          </view>
          <view class="quantity-item">
            <text class="quantity-label">到货数</text>
            <text class="quantity-value">{{oemClothingInfo.arrival_quantity || 0}}</text>
          </view>
          <view class="quantity-item">
            <text class="quantity-label">库存数</text>
            <text class="quantity-value">{{oemClothingInfo.stock_quantity || 0}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>