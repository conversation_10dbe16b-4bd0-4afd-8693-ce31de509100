// components/loading-state/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否正在加载
    loading: {
      type: Boolean,
      value: false
    },
    // 是否已加载完所有数据
    loadAll: {
      type: Boolean,
      value: false
    },
    // 加载中文本
    loadingText: {
      type: String,
      value: '加载中...'
    },
    // 加载完成文本
    loadAllText: {
      type: String,
      value: '到底了'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {

  }
})
