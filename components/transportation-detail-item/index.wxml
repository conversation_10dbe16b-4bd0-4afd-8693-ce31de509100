<view class="container">
  <view class="f">{{detail.index+1}}</view>
  <view class="a">
    <view class="b" data-clothing_id="{{item.clothing_id}}" data-oem="{{item.oem}}" bindtap="onGoToDetail" bindlongtap="longtap" bindtouchstart="touchStart" bindtouchEnd="touchEnd" wx:for="{{detail.bag}}" wx:key="index">
      <view class="c" style="{{item.img.length>0?'color:green':'color:black'}}">
        {{item.clothing_name}}
      </view>
      <view class="d">{{item.out_pcs}} 件</view>
    </view>
  </view>
  <view class="e">{{detail.QUP}} * {{detail.package_quantity}} 包</view>
</view>