// components/transport-detail-item/index.js
import Api from "../../utils/api.js";
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    detail: Object,
  },

  /**
   * 组件的初始数据
   */
  data: {
    taplock: false, //单击锁，当此值为false时，单击不生效
    longtap: false, //是否触发了长按
  },

  /**
   * 组件的方法列表
   */
  methods: {
    //获取款号信息
    async onGoToDetail(e) {
      if (this.data.taplock) {
        console.log(this.properties.detail)
        console.log('e',e)
      }
        const info =e
      this.triggerEvent("monitor", info);
    },
    // 长按事件
    longtap(e) {
      const list = this.properties.detail.bag
      const clothing_id = e.currentTarget.dataset.clothing_id
      const obj = list.find(e => e.clothing_id == clothing_id)

      wx.previewImage({
        urls: obj.img,
      });
      this.setData({
        taplock: false,
        longtap: true
      });
    },
    // 触摸开始
    touchStart() {
      this.setData({
        taplock: true,
        longtap: false
      });

    },
    // 触摸结束
    touchEnd() {
      if (this.data.longtap) {} else
        this.setData({
          taplock: true,
        });

    },
  },
});