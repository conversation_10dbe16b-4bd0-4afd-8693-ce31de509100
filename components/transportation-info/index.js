// components/transportation/index.js
import Api from "../../utils/api.js";
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    transportation: Object,
  },

  /**
   * 组件的初始数据
   */
  data: {
    price: "",
    transportation_id: "",
  },

  lifetimes: {
    attached() {
      this.setData({
        price: this.properties.transportation.price || "",
      });
      // 在组件实例进入页面节点树时执行
    },
  },

  /**
   * 监听属性变化
   */
  observers: {
    "transportation.price": function (newPrice) {
      // 防止递归更新，只有当价格真的改变时才更新
      const currentPrice = this.data.price;
      const formattedNewPrice = newPrice || "";

      if (currentPrice !== formattedNewPrice) {
        this.setData({
          price: formattedNewPrice,
        });
      }
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    //显示原图
    showImg() {
      const imgList = [];
      for (
        let i = 0;
        i < this.properties.transportation.transportation_img.length;
        i++
      ) {
        imgList.push(this.properties.transportation.transportation_img[i].url);
      }
      wx.previewImage({
        urls: imgList,
      });
    },
    showPopup() {
      console.log("showPopup", this.properties.transportation);
      this.showNativeInput();
    },

    // 微信原生输入框方案
    showNativeInput() {
      const that = this;
      wx.showModal({
        title: "编辑运费单价",
        placeholderText: "请输入运费单价（美元）",
        editable: true,
        content: this.data.price || "",
        confirmText: "确定",
        cancelText: "取消",
        success: function (res) {
          if (res.confirm) {
            const inputValue = res.content || "";
            // 简单的数字验证
            if (inputValue && isNaN(Number(inputValue))) {
              wx.showToast({
                title: "请输入有效的数字",
                icon: "none",
              });
              return;
            }
            that.setData({
              price: inputValue,
              transportation_id:
                that.properties.transportation.transportation_id,
            });
            that.changePrice();
          }
        },
        fail: function (err) {
          console.error("显示输入框失败:", err);
        },
      });
    },

    async changePrice() {
      const price = this.data.price || "";
      const transportation_id = this.data.transportation_id;

      let params = {};
      params.price = price;
      params.transportation_id = transportation_id;
      console.log("paramshhh", params);

      try {
        const res = await Api.changePrice(params);
        console.log("reshhh", res);
        if (res.statusCode == 500) {
          wx.showToast({
            title: "修改失败",
            icon: "none",
          });
        } else {
          wx.showToast({
            title: "修改成功",
            icon: "success",
          });
        }
      } catch (error) {
        console.error("修改价格失败:", error);
        wx.showToast({
          title: "网络错误",
          icon: "none",
        });
      }
    },
  },
});
