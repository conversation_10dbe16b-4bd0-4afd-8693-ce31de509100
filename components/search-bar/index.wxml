<view class="search-bar-container">
  <!-- 年份步进器 -->
  <view class="stepper-container" wx:if="{{ showYearStepper }}">
    <van-stepper
      input-width="100rpx"
      button-size="60rpx"
      value="{{ internalYear }}"
      min="{{ minYear }}"
      max="{{ maxYear }}"
      bind:change="onYearChange"
      integer
      disabled="{{ disabled }}"
    />
  </view>

  <!-- 搜索框 -->
  <view class="search-container">
    <van-search
      custom-class="search"
      field-class="search-field"
      background="transparent"
      value="{{ internalSearchValue }}"
      placeholder="{{ placeholder }}"
      input-align="left"
      show-action="{{ showAction }}"
      action-text="{{ actionText }}"
      disabled="{{ disabled }}"
      bind:change="onSearchInput"
      bind:search="onSearchConfirm"
      bind:confirm="onSearchConfirm"
      bind:clear="onSearchClear"
    >

    </van-search>
  </view>
</view>