/* components/custom-dropdown/index.wxss */
.custom-dropdown {
  position: relative;
  width: 100%;
}

.dropdown-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background-color: #fff;
  border: 2rpx solid #ebedf0;
  border-radius: 8rpx;
  min-height: 88rpx;
  box-sizing: border-box;
}

.dropdown-trigger.disabled {
  background-color: #f7f8fa;
  color: #c8c9cc;
  cursor: not-allowed;
}

.selected-text {
  flex: 1;
  font-size: 28rpx;
  color: #323233;
}

.arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  transition: transform 0.3s ease;
}

.arrow-up {
  transform: rotate(180deg);
}

.arrow-down {
  transform: rotate(0deg);
}

.arrow-icon {
  font-size: 24rpx;
  color: #969799;
}

.dropdown-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #fff;
  border: 2rpx solid #ebedf0;
  border-top: none;
  border-radius: 0 0 8rpx 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(50, 50, 51, 0.12);
  z-index: 1000;
  max-height: 400rpx;
  overflow-y: auto;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  font-size: 28rpx;
  color: #323233;
  border-bottom: 1rpx solid #ebedf0;
  transition: background-color 0.2s ease;
}

.option-item:last-child {
  border-bottom: none;
}

.option-item:hover {
  background-color: #f7f8fa;
}

.option-item.selected {
  color: #323233;
  background-color: #2c967854;
}

.option-text {
  flex: 1;
}

.check-icon {
  font-size: 32rpx;
  color: #2c9678;
  font-weight: bold;
}

.dropdown-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background-color: transparent;
}
