<!-- components/custom-dropdown/index.wxml -->
<view class="custom-dropdown">
  <!-- 选择器触发区域 -->
  <view 
    class="dropdown-trigger {{ disabled ? 'disabled' : '' }}" 
    bindtap="toggleDropdown"
  >
    <view class="selected-text">{{ selectedText }}</view>
    <view class="arrow {{ showDropdown ? 'arrow-up' : 'arrow-down' }}">
      <text class="arrow-icon">{{ showDropdown ? '▲' : '▼' }}</text>
    </view>
  </view>

  <!-- 下拉选项列表 -->
  <view 
    wx:if="{{ showDropdown }}" 
    class="dropdown-options"
    catchtap="stopPropagation"
  >
    <view 
      wx:for="{{ options }}" 
      wx:key="value"
      class="option-item {{ item.value === value ? 'selected' : '' }}"
      data-option="{{ item }}"
      bindtap="selectOption"
    >
      <text class="option-text">{{ item.text }}</text>
      <view wx:if="{{ item.value === value }}" class="check-icon">✓</view>
    </view>
  </view>

  <!-- 遮罩层 -->
  <view 
    wx:if="{{ showDropdown }}" 
    class="dropdown-mask"
    bindtap="closeDropdown"
  ></view>
</view>
