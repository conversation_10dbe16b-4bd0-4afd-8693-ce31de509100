// components/custom-dropdown/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 当前选中的值
    value: {
      type: String,
      value: ""
    },
    // 选项列表
    options: {
      type: Array,
      value: []
    },
    // 占位符文本
    placeholder: {
      type: String,
      value: "请选择"
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    showDropdown: false,
    selectedText: ""
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.updateSelectedText();
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'value, options': function() {
      this.updateSelectedText();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 更新显示的文本
    updateSelectedText() {
      const { value, options, placeholder } = this.properties;
      
      if (!value) {
        this.setData({
          selectedText: placeholder
        });
        return;
      }

      const selectedOption = options.find(option => option.value === value);
      this.setData({
        selectedText: selectedOption ? selectedOption.text : placeholder
      });
    },

    // 切换下拉菜单显示状态
    toggleDropdown() {
      if (this.properties.disabled) {
        return;
      }
      
      this.setData({
        showDropdown: !this.data.showDropdown
      });
    },

    // 选择选项
    selectOption(e) {
      const { option } = e.currentTarget.dataset;
      
      this.setData({
        showDropdown: false
      });

      // 触发change事件
      this.triggerEvent('change', option.value);
    },

    // 关闭下拉菜单
    closeDropdown() {
      this.setData({
        showDropdown: false
      });
    },

    // 阻止事件冒泡
    stopPropagation() {
      // 阻止事件冒泡
    }
  }
});
