/* components/warehouse-package-item/index.wxss */

.package-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.clothing-name {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  cursor: pointer;
  transition: color 0.2s;
}

.clothing-name:active {
  color: #98281e;
}

.package-info {
  flex: 2;
  margin: 0 24rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 26rpx;
  color: #969799;
}

.value {
  font-size: 26rpx;
  font-weight: 500;
  color: #323233;
}

.action-section {
  flex-shrink: 0;
}
