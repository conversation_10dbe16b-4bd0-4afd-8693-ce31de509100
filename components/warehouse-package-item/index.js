// components/warehouse-package-item/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 包裹数据
    packageData: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击服装名称
     */
    onClothingTap() {
      this.triggerEvent('clothingTap', {
        clothingInfo: this.data.packageData.clothingInfo,
        clothingId: this.data.packageData.clothingId
      });
    },

    /**
     * 点击出库按钮
     */
    onOutboundTap() {
      this.triggerEvent('outboundTap', {
        packageData: this.data.packageData
      });
    }
  }
});
