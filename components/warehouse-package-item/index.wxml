<!-- components/warehouse-package-item/index.wxml -->
<view class="package-item">
  <!-- 服装名称 - 可点击 -->
  <view class="clothing-name" bindtap="onClothingTap">
    {{ packageData.clothingName }}
  </view>
  
  <!-- 包裹信息 -->
  <view class="package-info">
    <view class="info-row">
      <text class="label">总件数:</text>
      <text class="value">{{ packageData.totalPieces }}</text>
    </view>
    <view class="info-row">
      <text class="label">每包件数:</text>
      <text class="value">{{ packageData.piecesPerPackage }}</text>
    </view>
    <view class="info-row">
      <text class="label">库存总包数:</text>
      <text class="value">{{ packageData.totalPackages }}</text>
    </view>
  </view>
  
  <!-- 出库按钮 -->
  <view class="action-section">
    <van-button
      size="small"
      type="primary"
      bindtap="onOutboundTap"
    >
      出库
    </van-button>
  </view>
</view>
