# JY前后端项目

## 项目架构

本项目采用前后端分离的架构，包含两个主要部分：

### 1. 后端 (JY-MONGO)
- **技术栈**：NestJS + MongoDB + Mongoose
- **功能**：提供RESTful API接口，处理用户认证、数据存储等
- **目录结构**：
  - `src/models`：数据模型
  - `src/modules`：业务模块
  - `src/database`：数据库配置

### 2. 前端 (JY-VUE3)
- **技术栈**：Vue3 + Vite + TypeScript + Pinia
- **功能**：用户界面，与后端API交互
- **目录结构**：
  - `src/views`：页面组件
  - `src/api`：API请求
  - `src/stores`：状态管理
  - `src/utils`：工具函数

## 启动项目

### 后端

```bash
cd JY-MONGO
pnpm install
pnpm run dev
```

后端将运行在 http://localhost:4000

### 前端

```bash
cd JY-VUE3
pnpm install
pnpm run dev
```

前端将运行在 http://localhost:5173

## API文档

启动后端后，可以访问 http://localhost:4000/swagger 查看API文档

## 功能列表

- 用户认证
  - 注册
  - 登录
  - 获取用户信息
  
## 环境要求

- Node.js >= 20.10.0（LTS）
- MongoDB >= 7.0.5
- PNPM >= 8.15.1
