# 系统前端项目

本项目是使用Vue 3 + TypeScript + Vite构建的系统前端项目。

## 项目技术栈

- Vue 3
- TypeScript
- Vite
- Vue Router
- Pinia
- Element Plus
- Axios

## 功能模块

### 用户认证

#### 登录功能

登录页面提供以下功能：
- 用户名和密码登录
- 表单验证
- 登录状态保持
- 登录错误提示

#### 注册功能

注册页面提供以下功能：
- 用户信息注册（用户名、密码、邮箱、手机号）
- 表单验证（包括密码复杂度验证和两次密码一致性验证）
- 注册成功后跳转到登录页

### 路由管理

- 基于Vue Router的路由管理
- 路由守卫实现身份验证
- 根据登录状态重定向

### 状态管理

- 使用Pinia管理用户状态
- 持久化存储用户令牌
- 自动恢复用户会话

## 开发指南

### 环境设置

1. 安装依赖：
```
pnpm install
```

2. 开发环境运行：
```
pnpm run dev
```

3. 生产环境构建：
```
pnpm run build
```

## API接口

### 用户相关

- 登录: `/auth/login`
- 注册: `/auth/register`
- 获取用户信息: `/auth/profile`

请参考API文档了解更多接口详情。
