{"name": "jy-mongo", "version": "1.0.0", "description": "JY 项目后端服务", "main": "dist/index.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "migrate:staff-to-new": "ts-node src/scripts/migrate-staff-to-new.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@nestjs/common": "^11.0.17", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.17", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.17", "@nestjs/swagger": "^11.1.3", "@types/mongoose": "^5.11.97", "@types/multer": "^1.4.12", "archiver": "^7.0.1", "argon2": "^0.41.1", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cors": "^2.8.5", "cos-nodejs-sdk-v5": "^2.14.7", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "lunar-typescript": "^1.7.9", "mongodb": "^6.16.0", "mongoose": "^8.13.2", "morgan": "^1.10.0", "multer": "1.4.5-lts.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^11.0.6", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jest": "^29.5.12", "@types/morgan": "^1.9.9", "@types/node": "^22.14.1", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "axios": "^1.8.4", "eslint": "^9.24.0", "jest": "^29.7.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}