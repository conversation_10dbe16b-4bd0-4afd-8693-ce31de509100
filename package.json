{"name": "jy-vue3", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.8.4", "element-plus": "^2.9.7", "lunar-typescript": "^1.7.9", "pinia": "^3.0.2", "pinyin-match": "^1.2.8", "vue": "^3.5.13", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^22.14.1", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.24.0", "eslint-plugin-vue": "^10.0.0", "prettier": "^3.5.3", "terser": "^5.43.1", "typescript": "~5.7.2", "vite": "^6.2.0", "vue-tsc": "^2.2.4"}}