{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}