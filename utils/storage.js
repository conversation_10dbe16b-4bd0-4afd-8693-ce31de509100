// utils/storage.js - 微信小程序临时存储工具类

/**
 * 临时存储管理工具
 * 用于防止用户在操作过程中意外退出导致数据丢失
 */
class TempStorage {
  // 存储键名常量
  static STORAGE_KEYS = {
    OUTBOUND_CART: 'temp_outbound_cart',      // 待出库清单（仅保留出库操作的临时存储）
  };

  /**
   * 保存临时数据
   * @param {string} key - 存储键名
   * @param {any} data - 要保存的数据
   * @param {Object} metadata - 元数据（如页面信息、时间戳等）
   */
  static save(key, data, metadata = {}) {
    try {
      const storageData = {
        data: data,
        metadata: {
          ...metadata,
          timestamp: Date.now(),
          version: '1.0'
        }
      };
      
      wx.setStorageSync(key, JSON.stringify(storageData));
      console.log(`临时数据已保存: ${key}`, storageData);
    } catch (error) {
      console.error(`保存临时数据失败: ${key}`, error);
    }
  }

  /**
   * 获取临时数据
   * @param {string} key - 存储键名
   * @param {number} maxAge - 最大有效期（毫秒），默认24小时
   * @returns {Object|null} 返回数据对象或null
   */
  static get(key, maxAge = 24 * 60 * 60 * 1000) {
    try {
      const storageString = wx.getStorageSync(key);
      if (!storageString) {
        return null;
      }

      const storageData = JSON.parse(storageString);
      const { data, metadata } = storageData;

      // 检查数据是否过期
      if (metadata.timestamp && (Date.now() - metadata.timestamp > maxAge)) {
        console.log(`临时数据已过期，自动清除: ${key}`);
        this.remove(key);
        return null;
      }

      console.log(`临时数据已恢复: ${key}`, storageData);
      return { data, metadata };
    } catch (error) {
      console.error(`获取临时数据失败: ${key}`, error);
      return null;
    }
  }

  /**
   * 删除临时数据
   * @param {string} key - 存储键名
   */
  static remove(key) {
    try {
      wx.removeStorageSync(key);
      console.log(`临时数据已清除: ${key}`);
    } catch (error) {
      console.error(`清除临时数据失败: ${key}`, error);
    }
  }

  /**
   * 清除所有临时数据
   */
  static clearAll() {
    Object.values(this.STORAGE_KEYS).forEach(key => {
      this.remove(key);
    });
  }

  /**
   * 检查是否有有效的临时数据
   * @param {string} key - 存储键名
   * @param {number} maxAge - 最大有效期（毫秒），默认24小时
   * @returns {boolean}
   */
  static has(key, maxAge = 24 * 60 * 60 * 1000) {
    try {
      const storageString = wx.getStorageSync(key);
      if (!storageString) {
        return false;
      }

      // 解析数据并检查有效性
      const storageData = JSON.parse(storageString);
      const { data, metadata } = storageData;

      // 检查数据是否过期
      if (metadata.timestamp && (Date.now() - metadata.timestamp > maxAge)) {
        console.log(`临时数据已过期，自动清除: ${key}`);
        this.remove(key);
        return false;
      }

      // 检查数据是否为空或无效
      if (!data || (Array.isArray(data) && data.length === 0)) {
        console.log(`临时数据为空，自动清除: ${key}`);
        this.remove(key);
        return false;
      }

      return true;
    } catch (error) {
      console.error(`检查临时数据失败: ${key}`, error);
      // 如果解析失败，清除损坏的数据
      this.remove(key);
      return false;
    }
  }

  // ==================== 业务特定方法 ====================

  /**
   * 保存出库清单
   * @param {Array} outboundList - 出库清单数据
   * @param {Object} metadata - 元数据
   */
  static saveOutboundCart(outboundList, metadata = {}) {
    this.save(this.STORAGE_KEYS.OUTBOUND_CART, outboundList, {
      ...metadata,
      type: 'outbound',
      count: outboundList.length
    });
  }

  /**
   * 获取出库清单
   * @returns {Array|null}
   */
  static getOutboundCart() {
    const result = this.get(this.STORAGE_KEYS.OUTBOUND_CART);
    return result ? result.data : null;
  }

  /**
   * 清除出库清单
   */
  static clearOutboundCart() {
    console.log('开始清除出库临时数据...');

    // 检查清除前的状态
    const beforeClear = this.has(this.STORAGE_KEYS.OUTBOUND_CART);
    console.log('清除前临时数据存在:', beforeClear);

    this.remove(this.STORAGE_KEYS.OUTBOUND_CART);

    // 检查清除后的状态
    const afterClear = this.has(this.STORAGE_KEYS.OUTBOUND_CART);
    console.log('清除后临时数据存在:', afterClear);

    if (afterClear) {
      console.error('警告：临时数据清除失败！');
    } else {
      console.log('出库临时数据已成功清除');
    }
  }

  /**
   * 显示恢复数据的确认对话框
   * @param {string} type - 数据类型
   * @param {Function} onConfirm - 确认回调
   * @param {Function} onCancel - 取消回调
   */
  static showRestoreDialog(type, onConfirm, onCancel) {
    const typeNames = {
      outbound: '出库'
    };

    wx.showModal({
      title: '恢复数据',
      content: `检测到您有未完成的${typeNames[type] || '操作'}数据，是否恢复？`,
      confirmText: '恢复',
      cancelText: '清除',
      success: (res) => {
        if (res.confirm) {
          onConfirm && onConfirm();
        } else if (res.cancel) {
          onCancel && onCancel();
        }
      }
    });
  }
}

export default TempStorage;
