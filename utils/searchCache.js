// utils/searchCache.js
// 搜索选项缓存管理工具

class SearchCache {
  constructor() {
    this.CACHE_KEY = 'search_clothing_names_cache';
    this.CACHE_DURATION = 24 * 60 * 60 * 1000; // 24小时，单位毫秒
  }

  /**
   * 获取缓存的服装名称列表
   * @returns {Object|null} 缓存数据或null
   */
  getCachedClothingNames() {
    try {
      const cachedData = wx.getStorageSync(this.CACHE_KEY);
      if (!cachedData) {
        console.log('搜索缓存：无缓存数据');
        return null;
      }

      const { data, timestamp } = cachedData;
      const now = new Date().getTime();
      
      // 检查缓存是否过期
      if (now - timestamp > this.CACHE_DURATION) {
        console.log('搜索缓存：缓存已过期，清除缓存');
        this.clearCache();
        return null;
      }

      console.log(`搜索缓存：使用缓存数据，包含 ${data.length} 个服装名称`);
      return data;
    } catch (error) {
      console.error('获取搜索缓存失败:', error);
      return null;
    }
  }

  /**
   * 缓存服装名称列表
   * @param {Array} clothingNames 服装名称列表
   */
  setCachedClothingNames(clothingNames) {
    try {
      const cacheData = {
        data: clothingNames,
        timestamp: new Date().getTime()
      };
      
      wx.setStorageSync(this.CACHE_KEY, cacheData);
      console.log(`搜索缓存：已缓存 ${clothingNames.length} 个服装名称`);
    } catch (error) {
      console.error('设置搜索缓存失败:', error);
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    try {
      wx.removeStorageSync(this.CACHE_KEY);
      console.log('搜索缓存：已清除缓存');
    } catch (error) {
      console.error('清除搜索缓存失败:', error);
    }
  }

  /**
   * 检查缓存是否有效
   * @returns {boolean} 缓存是否有效
   */
  isCacheValid() {
    try {
      const cachedData = wx.getStorageSync(this.CACHE_KEY);
      if (!cachedData) {
        return false;
      }

      const { timestamp } = cachedData;
      const now = new Date().getTime();
      
      return (now - timestamp) <= this.CACHE_DURATION;
    } catch (error) {
      console.error('检查搜索缓存有效性失败:', error);
      return false;
    }
  }

  /**
   * 获取缓存剩余时间（毫秒）
   * @returns {number} 剩余时间，-1表示无缓存或已过期
   */
  getCacheRemainingTime() {
    try {
      const cachedData = wx.getStorageSync(this.CACHE_KEY);
      if (!cachedData) {
        return -1;
      }

      const { timestamp } = cachedData;
      const now = new Date().getTime();
      const elapsed = now - timestamp;
      
      if (elapsed > this.CACHE_DURATION) {
        return -1; // 已过期
      }
      
      return this.CACHE_DURATION - elapsed;
    } catch (error) {
      console.error('获取搜索缓存剩余时间失败:', error);
      return -1;
    }
  }

  /**
   * 根据关键词筛选缓存的服装名称
   * @param {string} keyword 搜索关键词
   * @param {number} limit 返回数量限制，默认8
   * @returns {Array} 筛选后的服装名称列表
   */
  filterCachedNames(keyword, limit = 8) {
    const cachedNames = this.getCachedClothingNames();
    if (!cachedNames || !keyword) {
      return [];
    }

    const filtered = cachedNames.filter(item => 
      item.name && item.name.toLowerCase().includes(keyword.toLowerCase())
    );

    return filtered.slice(0, limit);
  }
}

// 创建单例实例
const searchCache = new SearchCache();

export default searchCache;
