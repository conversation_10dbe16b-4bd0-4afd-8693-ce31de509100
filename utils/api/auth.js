import http from "../http.js"; // 引入封装好的请求方法

// ==================== 认证相关 API ====================

// 获取微信OpenID
export const getOpenId = (params) => {
  return http({
    url: "/miniprogram/openid",
    method: "post",
    data: params,
  });
};

// 微信小程序登录
export const login = (params) => {
  return http({
    url: "/miniprogram/login",
    method: "post",
    data: params,
  });
};

// 用户名密码登录 (复用JY-VUE3后端接口)
export const userLogin = (params) => {
  return http({
    url: "/auth/login",
    method: "post",
    data: params,
  });
};

// 添加新用户
export const addNewUser = (params) => {
  return http({
    url: "/miniprogram/operate",
    method: "post",
    data: params,
  });
};

// 用户注册 (复用JY-VUE3后端接口)
export const register = (params) => {
  return http({
    url: "/auth/register",
    method: "post",
    data: params,
  });
};
