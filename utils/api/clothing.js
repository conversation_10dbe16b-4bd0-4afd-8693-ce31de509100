import http from "../http.js"; // 引入封装好的请求方法

// ==================== 服装相关 API ====================

// 获取普通服装信息
export const getClothingInfo = (params) => {
  return http({
    url: "/miniprogram/clothingInfo",
    method: "get",
    data: params,
  });
};

// 获取OEM服装信息
export const getOemClothingInfo = (params) => {
  return http({
    url: "/miniprogram/OemClothingInfo",
    method: "get",
    data: params,
  });
};

// 搜索普通服装
export const searchClothing = (params) => {
  return http({
    url: "/miniprogram/searchClothing",
    method: "get",
    data: params,
  });
};

// 搜索OEM服装
export const searchOemClothing = (params) => {
  return http({
    url: "/miniprogram/searchOemClothing",
    method: "get",
    data: params,
  });
};

// 修改价格
export const changePrice = (params) => {
  return http({
    url: "/miniprogram/changePrice",
    method: "post",
    data: params,
  });
};

// 搜索已出库服装（用于盘盈）
export const searchShippedClothing = (params) => {
  return http({
    url: "/warehouse-management/search-shipped-clothing",
    method: "get",
    data: params,
  });
};

// 获取服装详情
export const getClothingDetail = (params) => {
  return http({
    url: "/miniprogram/getClothingDetail",
    method: "get",
    data: params,
  });
};

// 获取OEM服装详情
export const getOemClothingDetail = (params) => {
  return http({
    url: "/miniprogram/getOemClothingDetail",
    method: "get",
    data: params,
  });
};

// 获取服装库存明细
export const getClothingInventory = (params) => {
  return http({
    url: "/miniprogram/getClothingInventory",
    method: "get",
    data: params,
  });
};

// 获取服装仓库日志
export const getClothingLogs = (params) => {
  return http({
    url: "/miniprogram/getClothingLogs",
    method: "get",
    data: params,
  });
};

// 获取服装年份选项
export const getClothingYearOptions = (params) => {
  return http({
    url: "/clothing/options/years",
    method: "get",
    data: params,
  });
};

// 获取服装四个选项（袖长、尺码、款式、口袋类型）
export const getClothingFourOptions = () => {
  return http({
    url: "/clothing/options/four",
    method: "get",
  });
};

// 获取服装供应商选项
export const getClothingSupplierOptions = (params) => {
  return http({
    url: "/clothing/options/suppliers",
    method: "get",
    data: params,
  });
};

// 获取服装分类选项
export const getClothingClassificationOptions = (params) => {
  return http({
    url: "/clothing/options/classifications",
    method: "get",
    data: params,
  });
};

// 获取OEM服装年份选项
export const getOemClothingYearOptions = (params) => {
  return http({
    url: "/oem-clothing/options/years",
    method: "get",
    data: params,
  });
};

// 获取OEM服装供应商选项
export const getOemSupplierOptions = (params) => {
  return http({
    url: "/oem-clothing/options/suppliers",
    method: "get",
    data: params,
  });
};

// 获取OEM服装分类选项
export const getOemClassificationOptions = (params) => {
  return http({
    url: "/oem-clothing/options/classifications",
    method: "get",
    data: params,
  });
};

// 获取OEM服装两个选项（尺码、款式）
export const getOemTwoOptions = () => {
  return http({
    url: "/oem-clothing/options/two",
    method: "get",
  });
};

// ==================== 综合搜索相关 API ====================

// 获取服装年份（综合搜索用）
export const getClothingYears = () => {
  return http({
    url: "/comprehensive-search/years",
    method: "get",
  });
};

// 获取综合搜索所有选项（新的统一接口）
export const getComprehensiveSearchOptions = (params) => {
  return http({
    url: "/comprehensive-search/options",
    method: "get",
    data: params,
  });
};

// 获取供应商选项（综合搜索用 - 兼容旧接口）
export const getSupplierOptions = (params) => {
  return http({
    url: "/comprehensive-search/suppliers",
    method: "get",
    data: params,
  });
};

// 获取布料分类选项（综合搜索用 - 兼容旧接口）
export const getClassificationOptions = (params) => {
  return http({
    url: "/comprehensive-search/classifications",
    method: "get",
    data: params,
  });
};

// 获取款式选项（综合搜索用 - 兼容旧接口）
export const getStyleOptions = (params) => {
  return http({
    url: "/comprehensive-search/styles",
    method: "get",
    data: params,
  });
};

// 获取汇总查询结果
export const getSummaryResults = (params) => {
  return http({
    url: "/comprehensive-search/summary-results",
    method: "get",
    data: params,
  });
};
