import http from "../http.js"; // 引入封装好的请求方法

// ==================== 仓库管理相关 API ====================

// 获取仓库列表
export const getWarehouseList = (params) => {
  return http({
    url: "/miniprogram/warehouse-management/warehouses-with-stats",
    method: "get",
    data: params,
  });
};

// 获取仓库详情
export const getWarehouseDetail = (params) => {
  return http({
    url: "/miniprogram/warehouseDetail",
    method: "get",
    data: params,
  });
};

// 创建仓库
export const createWarehouse = (params) => {
  return http({
    url: "/miniprogram/createWarehouse",
    method: "post",
    data: params,
  });
};

// 更新仓库信息
export const updateWarehouse = (params) => {
  return http({
    url: "/miniprogram/updateWarehouse",
    method: "post",
    data: params,
  });
};

// 删除仓库
export const deleteWarehouse = (params) => {
  return http({
    url: "/miniprogram/deleteWarehouse",
    method: "post",
    data: params,
  });
};

// 确认出库
export const confirmOutbound = (params) => {
  return http({
    url: "/miniprogram/confirmOutbound",
    method: "post",
    data: params,
  });
};

// ==================== 新仓库管理系统 API ====================

// 批量出库操作 - 统一接口
export const warehouseBatchOutbound = (params) => {
  return http({
    url: "/miniprogram/warehouse-management/batch-outbound",
    method: "post",
    data: params,
  });
};

// 移库操作
export const warehouseTransfer = (params) => {
  return http({
    url: "/miniprogram/warehouse-management/transfer",
    method: "post",
    data: params,
  });
};

// 盘存操作
export const warehouseInventoryCheck = (params) => {
  return http({
    url: "/miniprogram/warehouse-management/inventory-check",
    method: "post",
    data: params,
  });
};

// 查看库存（新系统）
export const getNewWarehouseInventory = (params) => {
  return http({
    url: "/miniprogram/warehouse-management/inventory",
    method: "get",
    data: params,
  });
};

// 获取全局库存汇总统计
export const getGlobalInventorySummary = () => {
  return http({
    url: "/miniprogram/warehouse-management/inventory-summary",
    method: "get",
  });
};

// 获取操作日志明细
export const getWarehouseOperationLogsDetail = (params) => {
  return http({
    url: "/miniprogram/warehouse-management/operation-logs-detail",
    method: "get",
    data: params,
  });
};

// 获取操作日志汇总
export const getWarehouseOperationLogsSummary = (params) => {
  return http({
    url: "/miniprogram/warehouse-management/operation-logs-summary",
    method: "get",
    data: params,
  });
};

// 获取操作日志（默认汇总，保持向后兼容）
export const getWarehouseOperationLogs = (params) => {
  return http({
    url: "/miniprogram/warehouse-management/operation-logs",
    method: "get",
    data: params,
  });
};

// 获取已入库统计数据
export const getInboundStatistics = (params) => {
  return http({
    url: "/miniprogram/warehouse-management/inbound-statistics",
    method: "get",
    data: params,
  });
};

// 搜索仓库库存
export const searchWarehouseInventory = (params) => {
  return http({
    url: "/miniprogram/warehouse-management/inventory",
    method: "get",
    data: params,
  });
};

// 获取所有仓库汇总统计
export const getWarehouseSummary = () => {
  return http({
    url: "/miniprogram/warehouse-management/inventory-summary",
    method: "get",
  });
};

// 获取包含统计数据的仓库列表
export const getWarehousesWithStats = (params) => {
  return http({
    url: "/miniprogram/warehouse-management/warehouses-with-stats",
    method: "get",
    data: params,
  });
};

// 冲正操作日志
export const reverseOperationLog = (params) => {
  return http({
    url: "/miniprogram/warehouse-management/reverse-operation-log",
    method: "post",
    data: params,
  });
};

// 移库操作专用冲正
export const reverseTransferOperation = (params) => {
  return http({
    url: "/miniprogram/warehouse-management/reverse-transfer-operation",
    method: "post",
    data: params,
  });
};

// ==================== 日历相关 API ====================

// 获取每日出库汇总数据
export const getDailyOutboundSummary = (params) => {
  return http({
    url: "/miniprogram/warehouse-management/daily-outbound-summary",
    method: "get",
    data: params,
  });
};
