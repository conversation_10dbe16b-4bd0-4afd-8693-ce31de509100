import http from "../http.js"; // 引入封装好的请求方法

// ==================== 入库管理相关 API ====================

// 创建入库单
export const createInboundOrder = (params) => {
  return http({
    url: "/miniprogram/createInboundOrder",
    method: "post",
    data: params,
  });
};

// 获取入库单列表
export const getInboundOrderList = (params) => {
  return http({
    url: "/miniprogram/inboundOrderList",
    method: "get",
    data: params,
  });
};

// 获取入库单详情
export const getInboundOrderDetail = (params) => {
  return http({
    url: "/miniprogram/inboundOrderDetail",
    method: "get",
    data: params,
  });
};

// 获取包裹内容详情
export const getPackageContents = (params) => {
  return http({
    url: "/miniprogram/packageContents",
    method: "get",
    data: params,
  });
};
