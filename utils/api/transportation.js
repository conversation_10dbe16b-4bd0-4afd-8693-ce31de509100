import http from "../http.js"; // 引入封装好的请求方法

// ==================== 货运相关 API ====================

// 获取货运单列表（支持分页和筛选）
export const getTransportationList = (params) => {
  return http({
    url: "/miniprogram/transportationList",
    method: "get",
    data: params,
  });
};

// 获取货运单详情
export const getTransportationDetail = (params) => {
  return http({
    url: "/miniprogram/transportationDetail",
    method: "get",
    data: params,
  });
};

// 获取入库页面的货运单详情（保持bag结构）
export const getInboundTransportationDetail = (params) => {
  return http({
    url: "/miniprogram/inboundTransportationDetail",
    method: "get",
    data: params,
  });
};

// 获取货运公司选项列表
export const getTransportationSupplierOptions = () => {
  return http({
    url: "/miniprogram/transportationSupplierOptions",
    method: "get",
  });
};

// 更新到货日期
export const updateArrivedDate = (params) => {
  return http({
    url: "/miniprogram/updateArrivedDate",
    method: "post",
    data: params,
  });
};

// 获取待入库货运信息列表
export const getPendingInboundTransportations = (params) => {
  return http({
    url: "/miniprogram/transportationList",
    method: "get",
    data: params,
  });
};
